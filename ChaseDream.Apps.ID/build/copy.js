const fse = require('fs-extra')

process.chdir( __dirname )

fse.copySync('../app', '../dist/app')
fse.copySync('../config', '../dist/config')
fse.copySync('../app.js', '../dist/app.js')
fse.copySync('../favicon.ico', '../dist/favicon.ico')
fse.copySync('../package.json', '../dist/package.json')
fse.moveSync('../dist/dist', '../dist/app/public/dist')
fse.removeSync('../dist/app/view/login.html')
fse.copySync('../dist/index.html', '../dist/app/view/index.html')
fse.moveSync('../dist/index.html', '../dist/app/view/login.html')
fse.copySync('../pm2.json', '../dist/pm2.json')
fse.copySync('../server.js', '../dist/server.js')
fse.copySync('../zrole_model.conf', '../dist/zrole_model.conf')
fse.copySync('../AuthKey_46FK6Z7HGD.p8', '../dist/AuthKey_46FK6Z7HGD.p8')
fse.copySync('../jiguang_identity.pem', '../dist/jiguang_identity.pem')
fse.copySync('../apple-app-site-association', '../dist/apple-app-site-association')
