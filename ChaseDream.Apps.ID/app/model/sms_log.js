module.exports = app => {
    const { STRING, BIGINT, INTEGER, TEXT, DATEONLY, BOOLEAN, NOW } = app.Sequelize

    const SmsLog = app.model.define("smsLog", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        area_code: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        mobile: {
            type: BIGINT.UNSIGNED,
            allowNull: false,
        },
        captcha: {
            type: STRING(6),
            allowNull: false,
        },
        type: {
            type: STRING(20),
            allowNull: false,
        },
        token: {
            type: STRING(255),
            defaultValue: '',
        },
        used: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: 0
        },
        platform: STRING(20),
        ip: STRING(20),
        platform_response: TEXT,
        success: BOOLEAN,
        timestamp: BIGINT.UNSIGNED,
        created_at: {
            type: DATEONLY,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_sms_log',
        indexes: [
            {
                fields: ['area_code', 'mobile']
            }
        ],
        timestamps: false,
    })

    return SmsLog
}
