module.exports = app => {
    const { BIGINT } = app.Sequelize

    const SchoolAreaSchool = app.model.define("SchoolAreaSchool", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        school_area_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        school_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_school_area_school',
        indexes: [
            {
                fields: ['school_id']
            }
        ],
        timestamps: false,
    })

    return SchoolAreaSchool
}
