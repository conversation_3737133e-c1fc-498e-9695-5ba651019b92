module.exports = app => {
    const { BIGINT, INTEGER, STRING, BOOLEAN, DATE, NOW } = app.Sequelize

    const RecruitForm = app.model.define("RecruitForm", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        subject: {
            type: STRING(255),
            defaultValue: ''
        },
        type: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        wechat: {
            type: BOOLEAN,
        },
        qq: {
            type: BOOLEAN,
        },
        test_date: {
            type: BOOLEAN,
        },
        test_plan: {
            type: BOOLEAN,
        },
        city: {
            type: BOOLEAN,
        },
        status: {
            type: BOOLEAN,
        },
        ps: {
            type: BOOLEA<PERSON>,
        },
        bachelor: {
            type: BOOLEAN,
        },
        industry: {
            type: BOOLEAN,
        },
        appeal: {
            type: BOOLEAN,
        },
        total: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        new: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_recruit_form',
        timestamps: false,
    })

    return RecruitForm
}
