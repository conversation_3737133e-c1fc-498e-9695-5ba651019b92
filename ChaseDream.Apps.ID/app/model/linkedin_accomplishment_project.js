module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, NOW } = app.Sequelize

    const LinkedinAccomplishmentProject = app.model.define("LinkedinAccomplishmentProject", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        begin_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        begin_month: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_month: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        website: {
            type: STRING(255),
            defaultValue: '',
        },
        desc: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_accomplishment_project',
        timestamps: false,
    })

    return LinkedinAccomplishmentProject
}
