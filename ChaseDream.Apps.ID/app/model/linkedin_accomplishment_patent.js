module.exports = app => {
    const { BIGINT, DATE, STRING, NOW } = app.Sequelize

    const LinkedinAccomplishmentPatent = app.model.define("LinkedinAccomplishmentPatent", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        number: {
            type: STRING(255),
            defaultValue: '',
        },
        user: {
            type: STRING(255),
            defaultValue: '',
        },
        status: {
            type: STRING(255),
            defaultValue: '',
        },
        dateline: {
            type: STRING(255),
            defaultValue: '',
        },
        website: {
            type: STRING(255),
            defaultValue: '',
        },
        desc: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_accomplishment_patent',
        timestamps: false,
    })

    return LinkedinAccomplishmentPatent
}
