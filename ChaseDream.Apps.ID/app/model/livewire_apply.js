module.exports = app => {
    const { BIGINT, INTEGER, STRING,BOOLEAN, DATE } = app.Sequelize

    const LivewireApply = app.model.define("LivewireApply", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        iid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        progress: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        school: {
            type: STRING(50),
            defaultValue: ''
        },
        round: {
            type: STRING(20),
            defaultValue: ''
        },
        enroll_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        enroll_season: {
            type: STRING(100),
            defaultValue: ''
        },
        re_applicant: {
            type: BOOLEAN,
            defaultValue: false,
        },
        apply_begin_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        apply_begin_start_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        apply_begin_method: {
            type: STRING(50),
            defaultValue: ''
        },
        material_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        material_finish_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        material_finish_mode: {
            type: STRING(50),
            defaultValue: ''
        },
        interview_invitation_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        interview_invitation_notice_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        interview_invitation_mode: {
            type: STRING(50),
            defaultValue: ''
        },
        interview_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        interview_receive_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        interview_receive_mode: {
            type: STRING(50),
            defaultValue: ''
        },
        interview_scholarship: {
            type: STRING(50),
            defaultValue: ''
        },
        waitlist_notice_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        waitlist_notice_receive_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        waitlist_notice_receive_mode: {
            type: STRING(50),
            defaultValue: ''
        },
        waitlist_regular_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        waitlist_regular_receive_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        waitlist_regular_receive_mode: {
            type: STRING(50),
            defaultValue: ''
        },
        refused_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        refused_receive_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        refused_receive_mode: {
            type: STRING(50),
            defaultValue: ''
        },
        defer_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        defer_apply_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        defer_reason: {
            type: STRING(50),
            defaultValue: ''
        },
        enrol_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        enrol_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        abandon_submit_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        abandon_apply_time: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        abandon_reason: {
            type: STRING(50),
            defaultValue: ''
        },
        created_at: DATE,
        updated_at: DATE
    }, {
        tableName: 'tbl_livewire_apply'
    })

    return LivewireApply
}
