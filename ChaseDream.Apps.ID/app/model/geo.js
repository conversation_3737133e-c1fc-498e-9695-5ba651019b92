module.exports = app => {
    const {BIGINT, INTEGER, STRING} = app.Sequelize

    const Geo = app.model.define("Geo", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(200),
            defaultValue: ''
        },
        parent_id: {
            type: INTEGER.UNSIGNED,
        },
        order: {
            type: INTEGER.UNSIGNED,
            defaultValue: 1,
        },
        en_name: {
            type: STRING(200),
            defaultValue: ''
        },
        keyword: {
            type: STRING(255),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_geo',
        timestamps: false,
    })

    return Geo
}
