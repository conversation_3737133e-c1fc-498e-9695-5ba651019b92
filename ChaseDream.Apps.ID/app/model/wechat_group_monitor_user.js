module.exports = app => {
    const { BIGINT, INTEGER, TEXT, BOOLEAN, STRING } = app.Sequelize

    const WechatGroupMonitorUser = app.model.define("WechatGroupMonitorUser", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        wx_id: {
            type: STRING(255),
            defaultValue: '',
        },
        avatar: {
            type: STRING(255),
            defaultValue: '',
        },
        wx: {
            type: STRING(255),
            defaultValue: '',
        },
        wx_nickname: {
            type: STRING(255),
            defaultValue: '',
        },
        gender: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        forum_uid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        forum_username: {
            type: STRING(20),
            defaultValue: '',
        },
        forum_mobile: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        forum_gender: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        forum_regip: {
            type: STRING(15),
            defaultValue: '',
        },
        area: {
            type: STRING(50),
            defaultValue: '',
        },
        note: {
            type: STRING(255),
            defaultValue: '',
        },
        label: {
            type: STRING(255),
            defaultValue: '',
        },
        desc: {
            type: STRING(255),
            defaultValue: '',
        },
        sign: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_wechat_group_monitor_user',
        timestamps: false,
        indexes: [{
            name: 'wx_id',
            fields: ['wx_id']
        }],
    })

    return WechatGroupMonitorUser
}
