module.exports = app => {
    const { BIGINT, INTEGER, STRING, BOOLEAN, TEXT } = app.Sequelize

    const WechatGroupMonitor = app.model.define("WechatGroupMonitor", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        chatroom_id: {
            type: STRING(100),
            defaultValue: '',
        },
        chatroom_name: {
            type: STRING(100),
            defaultValue: '',
        },
        tid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        fid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        qr_code: {
            type: STRING(255),
            defaultValue: '',
        },
        member_count: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        admin: {
            type: STRING(200),
            defaultValue: '',
        },
        announcement: {
            type: STRING(1000),
            defaultValue: '',
        },
        note: {
            type: STRING(1000),
            defaultValue: '',
        },
        last_message_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        status: {
            type: INTEGER,
            defaultValue: 0,
        },
        send_post: {
            type: BOOLEAN,
            defaultValue: false,
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_wechat_group_monitor',
        timestamps: false,
        indexes: [{
            name: 'chatroom_id',
            fields: ['chatroom_id']
        }, {
            name: 'status',
            fields: ['status']
        }],
    })

    WechatGroupMonitor.associate = function () {
        WechatGroupMonitor.hasOne(app.model.WechatGroupMonitorMessage, { as: 'message', foreignKey: 'id', sourceKey: 'last_message_id' })
    }

    return WechatGroupMonitor
}
