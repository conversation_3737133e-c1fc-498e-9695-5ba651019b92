module.exports = app => {
    const {BIGINT, STRING} = app.Sequelize

    const MajorCategoryDic = app.model.define("MajorCategoryDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        type: {
            type: STRING(50),
            defaultValue: ''
        },
        field: {
            type: STRING(200),
            defaultValue: ''
        },
        display_name: {
            type: STRING(200),
            defaultValue: ''
        },
        english_name: {
            type: STRING(200),
            defaultValue: ''
        },
        introduction: {
            type: STRING(1000),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_major_category_dic',
        timestamps: false,
    })

    return MajorCategoryDic
}
