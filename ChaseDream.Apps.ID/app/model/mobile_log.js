module.exports = app => {
    const { STRING, BIGINT, INTEGER, DATE, NOW } = app.Sequelize

    const MobileLog = app.model.define("MobileLog", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        area_code: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        mobile: {
            type: BIGINT.UNSIGNED,
            allowNull: false
        },
        ip: {
            type: STRING(15),
            defaultValue: '',
        },
        type: {
            type: STRING(50),
            defaultValue: '',
        },
        useragent: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_mobile_log',
        timestamps: false,
        indexes: [
            {
                fields: ['uid']
            }
        ],
    })

    return MobileLog
}
