module.exports = app => {
    const { BIGINT, INTEGER, BOOLEAN, STRING } = app.Sequelize

    const Tag = app.model.define("Tag", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(100),
            defaultValue: '',
        },
        synonym_id: {
            type: STRING(32),
            defaultValue: '',
        },
        main: {
            type: BOOLEAN,
            defaultValue: false,
        },
        property: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        order: {
            type: STRING(10),
            defaultValue: '',
        }
    }, {
        tableName: 'tbl_tag',
    })

    return Tag
}
