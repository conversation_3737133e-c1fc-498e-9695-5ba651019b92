module.exports = app => {
    const { BIGINT } = app.Sequelize

    const TagThread = app.model.define("TagThread", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        tagid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        tid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        fid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_tag_thread',
        indexes: [
            {
                fields: ['tid']
            }
        ],
        timestamps: false,
    })

    return TagThread
}
