module.exports = app => {
    const { BIGINT, STRING } = app.Sequelize

    const ThreadTag = app.model.define("ThreadTag", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        tid: {
            type: BIGINT.UNSIGNED,
        },
        tags: {
            type: STRING(1000),
            defaultValue: '',
        },
    }, {
        tableName: 'tbl_thread_tag',
        indexes: [
            {
                fields: ['tid']
            }
        ],
        timestamps: false,
    })

    return ThreadTag
}
