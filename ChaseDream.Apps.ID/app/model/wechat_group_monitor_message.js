module.exports = app => {
    const { BIGINT, INTEGER, TEXT, STRING, DATE, NOW, BOOLEAN } = app.Sequelize

    const WechatGroupMonitorMessage = app.model.define("WechatGroupMonitorMessage", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        tid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        fid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        pid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        chatroom_id: {
            type: STRING(100),
            defaultValue: '',
        },
        wx_id: {
            type: STRING(255),
            defaultValue: '',
        },
        wx_nickname: {
            type: STRING(100),
            defaultValue: '',
        },
        text: {
            type: TEXT,
            defaultValue: '',
        },
        type: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        filename: {
            type: STRING(255),
            defaultValue: '',
        },
        filesize: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        attachment: {
            type: STRING(255),
            defaultValue: '',
        },
        isimage: {
            type: BOOLEAN,
            defaultValue: false,
        },
        width: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        height: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        thumb: {
            type: BOOLEAN,
            defaultValue: false,
        },
        thumbwidth: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        thumbheight: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        status: {
            type: INTEGER,
            defaultValue: 0,
        },
        error_message: {
            type: TEXT,
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_wechat_group_monitor_message',
        indexes: [{
            name: 'chatroom_id',
            fields: ['chatroom_id']
        }, {
            name: 'wx_id',
            fields: ['wx_id']
        }, {
            name: 'status',
            fields: ['status']
        }],
        timestamps: false,
    })

    return WechatGroupMonitorMessage
}
