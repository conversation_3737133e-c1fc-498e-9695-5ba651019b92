module.exports = app => {
    const {BIGINT, INTEGER, STRING, DATE, TEXT, NOW} = app.Sequelize

    const Thread = app.model.define("thread", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        subject: {
            type: STRING(200),
            allowNull: false,
        },
        content: {
            type: TEXT,
            allowNull: false,
        },
        opt_id: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        send_username: {
            type: STRING(50),
            allowNull: false,
        },
        tid: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        pid: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        fid: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        forum: {
            type: STRING(200),
            defaultValue: '',
            allowNull: false,
        },
        uid: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        typeid: {
            type: INTEGER.UNSIGNED,
            allowNull: false,
        },
        highlight: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        digest: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        stick: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        htmlon: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        status: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        schedule: {
            type: STRING(200),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_thread',
        timestamps: false,
    })

    return Thread
}
