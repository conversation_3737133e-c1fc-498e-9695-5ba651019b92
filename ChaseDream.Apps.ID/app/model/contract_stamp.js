module.exports = app => {
    const { BIGINT, INTEGER, STRING, TEXT } = app.Sequelize

    const ContractStamp = app.model.define("ContractStamp", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        apply_uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        apply_username: {
            type: STRING(20),
            defaultValue: '',
        },
        approve_uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        approve_username: {
            type: STRING(20),
            defaultValue: '',
        },
        contract_name: {
            type: STRING(255),
            defaultValue: '',
        },
        raw_aid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        final_aid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        stamp: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        stamp_positons: {
            type: STRING(2000),
            defaultValue: '',
        },
        cross_page_y: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        //-2:请修改 -1:已拒绝 0:待系统预处理 1:系统预处理中 2:申请盖章 3:待审核 4:已批准 5:审核中 6:已盖章
        status: {
            type: INTEGER,
            defaultValue: 0,
        },
        department: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        message: {
            type: STRING(255),
            defaultValue: '',
        },
        remark: {
            type: TEXT,
            defaultValue: '',
        },
        approve_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_contract_stamp',
        indexes: [{
            fields: ['status']
        }],
        timestamps: false,
    })

    ContractStamp.associate = function () {
        ContractStamp.hasOne(app.model.ContractStampAttachment, { as: 'raw_att', foreignKey: 'id', sourceKey: 'raw_aid' })
        ContractStamp.hasOne(app.model.ContractStampAttachment, { as: 'final_att', foreignKey: 'id', sourceKey: 'final_aid' })
    }

    return ContractStamp
}
