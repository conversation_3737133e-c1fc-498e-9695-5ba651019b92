module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE, NOW } = app.Sequelize

    const SchoolMajorDic = app.model.define("SchoolMajorDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        program_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        school_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        //项目学位ID
        program_degrees_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        //项目分类ID
        major_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        //项目类型
        program_type: {
            type: STRING(20),
            defaultValue: ''
        },
        //项目时长
        program_duration: {
            type: STRING(20),
            defaultValue: ''
        },
        //开学时间
        school_start_time: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        fullname_chinese: {
            type: STRING(255),
            defaultValue: ''
        },
        fullname_english: {
            type: STRING(255),
            defaultValue: ''
        },
        short: {
            type: STRING(255),
            defaultValue: ''
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_school_major_dic',
        timestamps: false,
    })

    SchoolMajorDic.associate = function () {
        SchoolMajorDic.hasOne(app.model.MajorCategoryDic, { as: 'event_major', foreignKey: 'id', sourceKey: 'major_id' })
    }

    return SchoolMajorDic
}
