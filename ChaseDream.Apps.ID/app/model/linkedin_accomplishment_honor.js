module.exports = app => {
    const { BIGINT, DATE, STRING, NOW } = app.Sequelize

    const LinkedinAccomplishmentHonor = app.model.define("LinkedinAccomplishmentHonor", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        issue_authority: {
            type: STRING(255),
            defaultValue: '',
        },
        dateline: {
            type: STRING(255),
            defaultValue: '',
        },
        desc: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_accomplishment_honor',
        timestamps: false,
    })

    return LinkedinAccomplishmentHonor
}
