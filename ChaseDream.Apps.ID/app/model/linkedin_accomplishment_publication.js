module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, NOW } = app.Sequelize

    const LinkedinAccomplishmentPublication = app.model.define("LinkedinAccomplishmentPublication", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        company: {
            type: STRING(255),
            defaultValue: '',
        },
        dateline: {
            type: STRING(255),
            defaultValue: '',
        },
        website: {
            type: STRING(255),
            defaultValue: '',
        },
        desc: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_accomplishment_publication',
        timestamps: false,
    })

    return LinkedinAccomplishmentPublication
}
