const moment = require('moment-timezone')

module.exports = app => {
    const { INTEGER, BIGINT, CHAR, STRING, DATE, NOW } = app.Sequelize

    const Mobile = app.model.define("mobile", {
        uid: {
            type: BIGINT.UNSIGNED,
            primaryKey: true,
            unique: true,
            allowNull: false
        },
        area_code: {
            type: INTEGER.UNSIGNED,
            unique: "idx_mobile",
            allowNull: false
        },
        mobile: {
            type: BIGINT.UNSIGNED,
            allowNull: false
        },
        mobile_encrypt: {
            type: CHAR(32),
            unique: "idx_mobile",
            defaultValue: ''
        },
        mobile_mask: {
            type: CHAR(20),
            defaultValue: ''
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_mobile',
        timestamps: false,
        hooks: {
            afterCreate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: `${instance.area_code}`,
                    table: app.model.Mobile.tableName,
                    type: 'create',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
            afterUpdate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: `${instance.area_code}`,
                    table: app.model.Mobile.tableName,
                    type: 'update',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
        },
    })

    return Mobile
}
