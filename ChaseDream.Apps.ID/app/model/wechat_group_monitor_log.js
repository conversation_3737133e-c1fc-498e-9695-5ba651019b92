module.exports = app => {
    const { BIGINT, INTEGER, STRING, BOOLEAN } = app.Sequelize

    const WechatGroupMonitorLog = app.model.define("WechatGroupMonitorLog", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        chatroom_id: {
            type: STRING(100),
            defaultValue: '',
        },
        wx_id: {
            type: STRING(255),
            defaultValue: '',
        },
        in_out: {
            type: BOOLEAN,
            defaultValue: false,
        },
        method: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_wechat_group_monitor_log',
        timestamps: false,
        indexes: [{
            name: 'chatroom_id',
            fields: ['chatroom_id']
        }],
    })

    WechatGroupMonitorLog.associate = function () {
        WechatGroupMonitorLog.hasOne(app.model.WechatGroupMonitorUser, { as: 'wx_group_method', foreignKey: 'id', sourceKey: 'method' })
        WechatGroupMonitorLog.hasOne(app.model.WechatGroupMonitorUser, { as: 'wx_group_user', foreignKey: 'wx_id', sourceKey: 'wx_id' })
        WechatGroupMonitorLog.hasOne(app.model.WechatGroupMonitor, { as: 'wechat_group_monitor', foreignKey: 'chatroom_id', sourceKey: 'chatroom_id' })
    }

    return WechatGroupMonitorLog
}
