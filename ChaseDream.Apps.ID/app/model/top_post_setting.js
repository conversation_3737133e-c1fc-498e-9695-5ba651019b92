module.exports = app => {
    const {BIGINT, INTEGER, DATE, NOW} = app.Sequelize

    const TopPostSetting = app.model.define("TopPostSetting", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        min: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        max: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_top_post_setting',
        timestamps: false,
    })

    return TopPostSetting
}
