module.exports = app => {
    const { BIGINT, STRING, BOOLEAN, FLOAT } = app.Sequelize

    const ProgramDic = app.model.define("ProgramDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        program_direction_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        program_degree_category_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        program_duration: {
            type: STRING(50),
            defaultValue: ''
        },
        program_name_en: {
            type: STRING(255),
            defaultValue: ''
        },
        program_name_cn: {
            type: STRING(255),
            defaultValue: ''
        },
        program_type: {
            type: STRING(255),
            defaultValue: ''
        },
        teaching_method: {
            type: STRING(255),
            defaultValue: ''
        },
        display_name: {
            type: STRING(255),
            defaultValue: ''
        },
        introduction: {
            type: STRING(1000),
            defaultValue: ''
        },
        application_fee: {
            type: STRING(20),
            defaultValue: ''
        },
        tuition: {
            type: STRING(20),
            defaultValue: ''
        },
        living_expenses: {
            type: STRING(20),
            defaultValue: ''
        },
        insurance_premiums: {
            type: STRING(20),
            defaultValue: ''
        },
        gmat_code: {
            type: STRING(100),
            defaultValue: ''
        },
        ea_code: {
            type: STRING(100),
            defaultValue: ''
        },
        gre_code: {
            type: STRING(100),
            defaultValue: ''
        },
        gre_major_code: {
            type: STRING(100),
            defaultValue: ''
        },
        ielts_code: {
            type: STRING(100),
            defaultValue: ''
        },
        toefl_code: {
            type: STRING(100),
            defaultValue: ''
        },
        pte_code: {
            type: STRING(100),
            defaultValue: ''
        },
        school_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        multi_school: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        abbr_cn: {
            type: STRING(100),
            defaultValue: ''
        },
        abbr_en: {
            type: STRING(100),
            defaultValue: ''
        },
        keyword: {
            type: STRING(255),
            defaultValue: ''
        },
        school_starttime: {
            type: STRING(100),
            defaultValue: ''
        },
        timezone: {
            type: STRING(50),
            defaultValue: ''
        },
        gmat_score: {
            type: FLOAT,
            defaultValue: 0,
        },
        ea_score: {
            type: FLOAT,
            defaultValue: 0,
        },
        gre_score: {
            type: FLOAT,
            defaultValue: 0,
        },
        toefl_score: {
            type: FLOAT,
            defaultValue: 0,
        },
        ielts_score: {
            type: FLOAT,
            defaultValue: 0,
        },
        pte_score: {
            type: FLOAT,
            defaultValue: 0,
        },
        gmat_remark: {
            type: STRING(500),
            defaultValue: '',
        },
        ea_remark: {
            type: STRING(500),
            defaultValue: '',
        },
        gre_remark: {
            type: STRING(500),
            defaultValue: '',
        },
        toefl_remark: {
            type: STRING(500),
            defaultValue: '',
        },
        ielts_remark: {
            type: STRING(500),
            defaultValue: '',
        },
        pte_remark: {
            type: STRING(500),
            defaultValue: '',
        },
        enrollment: {
            type: STRING(20),
            defaultValue: '',
        },
    }, {
        tableName: 'tbl_program_dic',
    })

    return ProgramDic
}
