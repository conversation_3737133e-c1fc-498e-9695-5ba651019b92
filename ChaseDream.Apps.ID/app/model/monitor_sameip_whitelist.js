module.exports = app => {
    const { CHAR, BIGINT, INTEGER, STRING } = app.Sequelize

    return app.model.define("MonitorSameipWhitelist", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        ip: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
        },
        remark: {
            type: STRING(100),
            defaultValue: ''
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
    }, {
        tableName: 'tbl_adv_monitor_sameip_whitelist',
        timestamps: false,
    })
}
