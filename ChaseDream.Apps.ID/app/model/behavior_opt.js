module.exports = app => {
    const { BIGINT, INTEGER, STRING } = app.Sequelize

    const BehaviorOpt = app.model.define("BehaviorOpt", {
        behavior_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        opt_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        opt_text: {
            type: STRING(50),
            defaultValue: '',
        },
    }, {
        tableName: 'tbl_user_behavior_opt',
        indexes: [
            {
                fields: ['behavior_id']
            }
        ],
        timestamps: false,
    })

    return BehaviorOpt
}
