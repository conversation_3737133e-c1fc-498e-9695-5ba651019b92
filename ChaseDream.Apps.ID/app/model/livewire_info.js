module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE } = app.Sequelize

    const LivewireInfo = app.model.define("LivewireInfo", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        forum_uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        gender: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        age: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        area: {
            type: STRING(50),
            defaultValue: ''
        },
        exam_type: {
            type: STRING(20),
            defaultValue: ''
        },
        bachelor_school: {
            type: STRING(100),
            defaultValue: ''
        },
        bachelor_major: {
            type: STRING(100),
            defaultValue: ''
        },
        bachelor_gpa: {
            type: STRING(100),
            defaultValue: ''
        },
        master_school: {
            type: STRING(100),
            defaultValue: ''
        },
        master_major: {
            type: STRING(100),
            defaultValue: ''
        },
        master_gpa: {
            type: STRING(100),
            defaultValue: ''
        },
        doctor_school: {
            type: STRING(100),
            defaultValue: ''
        },
        doctor_major: {
            type: STRING(100),
            defaultValue: ''
        },
        doctor_gpa: {
            type: STRING(100),
            defaultValue: ''
        },
        industry: {
            type: STRING(100),
            defaultValue: ''
        },
        job: {
            type: STRING(100),
            defaultValue: ''
        },
        we: {
            type: STRING(100),
            defaultValue: ''
        },
        created_at: DATE,
        updated_at: DATE
    }, {
        tableName: 'tbl_livewire_info'
    })

    return LivewireInfo
}
