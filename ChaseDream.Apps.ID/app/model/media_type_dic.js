module.exports = app => {
    const { BIGINT, STRING } = app.Sequelize

    const MediaTypeDic = app.model.define("MediaTypeDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(255),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_media_type_dic',
    })

    return MediaTypeDic
}
