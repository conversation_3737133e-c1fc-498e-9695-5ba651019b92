module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE, BOOLEAN, TEXT, NOW } = app.Sequelize

    const EventRelease = app.model.define("EventRelease", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        event_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        event_begin_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        event_end_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        push_begin_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        push_end_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        new_flag_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        country_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        province_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        location_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        // event_location表主键
        lid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        subject: {
            type: TEXT,
            defaultValue: ''
        },
        url1: {
            type: STRING(500),
            defaultValue: ''
        },
        url2: {
            type: STRING(500),
            defaultValue: ''
        },
        //推2的封面
        image: {
            type: STRING(500),
            defaultValue: ''
        },
        //推1-3
        type: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        //推2显示在第几行
        level: {
            type: INTEGER,
            defaultValue: 0
        },
        //推3文字链接颜色
        color: {
            type: STRING(50),
            defaultValue: ''
        },
        html: {
            type: BOOLEAN,
            defaultValue: false,
        },
        order: {
            type: INTEGER,
            defaultValue: 0
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event_release',
        indexes: [
            {
                fields: ['status', 'order']
            }
        ],
        timestamps: false,
    })

    EventRelease.associate = function () {
        EventRelease.hasOne(app.model.Geo, { as: 'event_geo', foreignKey: 'id', sourceKey: 'location_id' })
    }

    return EventRelease
}
