module.exports = app => {
    const { STRING, BIGINT, INTEGER, DATE, NOW } = app.Sequelize

    const SchoolAreaPhoto = app.model.define("SchoolAreaPhoto", {
        id: {
            type: BIGINT,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        school_area_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        original: {
            type: STRING(100),
            allowNull: false
        },
        fullpath: {
            type: STRING(4000),
            allowNull: false,
        },
        type: {
            type: STRING(100),
            allowNull: false,
        },
        order: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_school_area_photo',
        timestamps: false,
    })

    return SchoolAreaPhoto
}
