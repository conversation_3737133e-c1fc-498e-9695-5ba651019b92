module.exports = app => {
    const { BIGINT, INTEGER, DATE, NOW } = app.Sequelize

    const EventLocation = app.model.define("EventLocation", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        event_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        country_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        province_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        city_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        begin_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        app_join_event: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event_location',
        timestamps: false,
    })

    return EventLocation
}
