module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, NOW } = app.Sequelize

    const LinkedinEducation = app.model.define("LinkedinEducation", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        school_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        school_name: {
            type: STRING(255),
            defaultValue: '',
        },
        major: {
            type: STRING(255),
            defaultValue: '',
        },
        degree: {
            type: STRING(255),
            defaultValue: '',
        },
        enrollment_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        graduation_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        school_record: {
            type: STRING(255),
            defaultValue: '',
        },
        club_activity: {
            type: STRING(255),
            defaultValue: '',
        },
        other: {
            type: STRING(500),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_education',
        timestamps: false,
    })

    LinkedinEducation.associate = function () {
        LinkedinEducation.hasOne(app.model.LinkedinSchool, { as: 'school', foreignKey: 'id', sourceKey: 'school_id' })
    }

    return LinkedinEducation
}
