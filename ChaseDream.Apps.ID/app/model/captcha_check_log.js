module.exports = app => {
    const { CHAR, INTEGER } = app.Sequelize

    const CaptchaCheckLog = app.model.define("CaptchaCheckLog", {
        area_code: {
            type: INTEGER.UNSIGNED,
            unique: "idx_mobile",
            allowNull: false
        },
        mobile: {
            type: CHAR(32),
            unique: "idx_mobile",
            allowNull: false
        },
        count: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
    }, {
        tableName: 'tbl_captcha_check_log',
        timestamps: false,
    })

    CaptchaCheckLog.removeAttribute('id')

    return CaptchaCheckLog
}
