module.exports = app => {
    const {STRING, BIGINT, INTEGER, DATE, FLOAT} = app.Sequelize

    const Menu = app.model.define("menu", {
        id: {
            type: BIGINT,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(50),
            allowNull: false,
        },
        icon: {
            type: STRING(50),
            allowNull: false,
        },
        text: {
            type: STRING(50),
            allowNull: false,
        },
        router: {
            type: STRING(255),
            allowNull: false,
        },
        pid: {
            type: INTEGER,
            defaultValue: 0
        },
        is_show: {
            type: INTEGER,
            defaultValue: 0
        },
        component: {
            type: STRING(50),
            allowNull: false,
        },
        order: {
            type: FLOAT,
            allowNull: false,
        },
        created_at: DATE,
        updated_at: DATE
    }, {
        tableName: 'tbl_menu'
    })

    return Menu
}
