const moment = require('moment-timezone')

module.exports = app => {
    const { STRING, BIGINT, DATE, NOW } = app.Sequelize

    const WeChat = app.model.define("WeChat", {
        uid: {
            type: BIGINT.UNSIGNED,
            primaryKey: true,
            unique: true,
            allowNull: false
        },
        nickname: {
            type: STRING(32),
            defaultValue: '',
        },
        openid: {
            type: STRING(32),
            unique: true,
            defaultValue: '',
            allowNull: false
        },
        unionid: {
            type: STRING(32),
            defaultValue: '',
            allowNull: false
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_wechat',
        timestamps: false,
        indexes: [
            {
                fields: ['openid', 'unionid']
            }
        ],
        hooks: {
            afterCreate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: instance.openid,
                    table: app.model.Wechat.tableName,
                    type: 'create',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
            afterUpdate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: instance.openid,
                    table: app.model.Wechat.tableName,
                    type: 'update',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
            afterDestroy: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: instance.openid,
                    table: app.model.Wechat.tableName,
                    type: 'delete',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
            beforeUpsert: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: instance.openid,
                    table: app.model.Wechat.tableName,
                    type: 'upsert',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
        },
    })

    return WeChat
}
