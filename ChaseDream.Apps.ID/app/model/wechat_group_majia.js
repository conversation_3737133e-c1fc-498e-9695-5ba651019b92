module.exports = app => {
    const { BIGINT, INTEGER, STRING } = app.Sequelize

    const WechatGroupMajia = app.model.define("WechatGroupMajia", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        wx_id: {
            type: STRING(100),
            defaultValue: '',
        },
        uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        remark: {
            type: STRING(100),
            defaultValue: '',
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_wechat_group_majia',
        timestamps: false,
    })

    return WechatGroupMajia
}
