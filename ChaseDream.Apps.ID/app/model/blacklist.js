module.exports = app => {
    const {INTEGER, BIGINT, DATE} = app.Sequelize

    const Blacklist = app.model.define("blackList", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        area_code: {
            type: INTEGER.UNSIGNED,
            unique: "idx_mobile",
            allowNull: false,
        },
        mobile: {
            type: BIGINT.UNSIGNED,
            unique: "idx_mobile",
            allowNull: false,
        },
        created_at: DATE,
        updated_at: DATE
    }, {
        tableName: 'tbl_blacklist'
    })

    return Blacklist
}
