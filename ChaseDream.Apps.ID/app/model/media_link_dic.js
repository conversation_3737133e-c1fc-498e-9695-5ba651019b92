module.exports = app => {
    const { BIGINT, STRING, INTEGER, NOW } = app.Sequelize

    const MediaLinkDic = app.model.define("MediaLinkDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        media_type_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        url: {
            type: STRING(255),
            defaultValue: ''
        },
        link_type: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_media_link_dic',
    })

    return MediaLinkDic
}
