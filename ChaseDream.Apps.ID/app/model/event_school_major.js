module.exports = app => {
    const { BIGINT, STRING, DATE, NOW } = app.Sequelize

    const EventSchoolMajor = app.model.define("EventSchoolMajor", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        event_id: {
            type: BIGINT.UNSIGNED,
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        school_major_id: {
            type: BIGINT.UNSIGNED,
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event_school_major',
        indexes: [
            {
                fields: ['event_id', 'lid']
            }
        ],
        timestamps: false,
    })

    return EventSchoolMajor
}
