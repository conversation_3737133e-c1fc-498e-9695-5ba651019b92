module.exports = app => {
    const {BIGINT, INTEGER, STRING, DATE, NOW} = app.Sequelize

    const TopPostUser = app.model.define("TopPostUser", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(200),
            allowNull: false,
        },
        nickname: {
            type: STRING(200),
            defaultValue: '',
            allowNull: false,
        },
        password: {
            type: STRING(200),
            allowNull: false,
        },
        status: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_top_post_user',
        timestamps: false,
    })

    return TopPostUser
}
