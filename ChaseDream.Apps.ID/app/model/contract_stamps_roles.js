module.exports = app => {
    const { BIGINT, INTEGER, BOOLEAN } = app.Sequelize

    const ContractStampsRoles = app.model.define("ContractStampsRoles", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        uid: {
            type: BIGINT.UNSIGNED,
            unique: "idx_uid_stamp",
            defaultValue: 0,
        },
        stamp_id: {
            type: BIGINT.UNSIGNED,
            unique: "idx_uid_stamp",
            defaultValue: 0,
        },
        apply: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        approve: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_contract_stamps_roles',
        indexes: [{
            fields: ['uid', 'stamp_id']
        }],
        timestamps: false,
    })

    return ContractStampsRoles
}
