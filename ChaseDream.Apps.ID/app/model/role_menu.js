module.exports = app => {
    const {BIGINT} = app.Sequelize

    const RoleMenu = app.model.define("RoleMenu", {
        id: {
            type: BIGINT,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        role_id: {
            type: BIGINT,
            unique:"uk_t_role_menu",
            allowNull: false
        },
        menu_id: {
            type: BIGINT,
            unique:"uk_t_role_menu",
            allowNull: false
        }
    }, {
        timestamps: false,
        tableName: 'tbl_role_menu'
    })

    return RoleMenu
}
