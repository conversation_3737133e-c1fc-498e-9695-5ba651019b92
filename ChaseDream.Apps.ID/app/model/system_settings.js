module.exports = app => {
    const { STRING, BIGINT } = app.Sequelize

    const SystemSettings = app.model.define("SystemSettings", {
        id: {
            type: BIGINT,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        value: {
            type: STRING(2000),
            defaultValue: '',
        }
    }, {
        tableName: 'tbl_system_settings',
        timestamps: false,
        indexes: [
            {
                fields: ['name']
            }
        ],
    })

    return SystemSettings
}
