module.exports = app => {
    const { BIGINT, DATE, STRING, NOW } = app.Sequelize

    const LinkedinAccomplishmentCourse = app.model.define("LinkedinAccomplishmentCourse", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        number: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_accomplishment_course',
        timestamps: false,
    })

    return LinkedinAccomplishmentCourse
}
