module.exports = app => {
    const { BIGINT, DATE, STRING, B<PERSON><PERSON>EA<PERSON>, NOW } = app.Sequelize

    const LinkedinAccomplishmentLanguage = app.model.define("LinkedinAccomplishmentLanguage", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        level: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_accomplishment_language',
        timestamps: false,
    })

    return LinkedinAccomplishmentLanguage
}
