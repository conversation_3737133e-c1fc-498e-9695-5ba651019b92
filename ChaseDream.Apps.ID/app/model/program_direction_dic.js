module.exports = app => {
    const { BIGINT, STRING, DATE, NOW } = app.Sequelize

    const ProgramDirectionDic = app.model.define("ProgramDirectionDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        organization_type_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        program_direction_type_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        program_direction: {
            type: STRING(50),
            defaultValue: ''
        },  
        display_name: {
            type: STRING(50),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_program_direction_dic',
    })

    ProgramDirectionDic.associate = function () {
        ProgramDirectionDic.hasOne(app.model.ProgramDirectionOrgTypeDic, { as: 'organization_type', foreignKey: 'id', sourceKey: 'organization_type_id' })
        ProgramDirectionDic.hasOne(app.model.ProgramDirectionOrgTypeDic, { as: 'program_direction_type', foreignKey: 'id', sourceKey: 'program_direction_type_id' })
    }

    return ProgramDirectionDic
}
