const moment = require('moment-timezone')

module.exports = app => {
    const { BIGINT, INTEGER, BOOLEAN, STRING } = app.Sequelize

    const Apple = app.model.define("apple", {
        uid: {
            type: BIGINT.UNSIGNED,
            primaryKey: true,
            unique: true,
            allowNull: false
        },
        user: {
            type: STRING(50),
            allowNull: false,
            unique: true,
        },
        email: {
            type: STRING(100),
            defaultValue: '',
        },
        auth_time: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
    }, {
        tableName: 'tbl_apple',
        timestamps: false,
        hooks: {
            afterCreate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: instance.email,
                    table: app.model.Email.tableName,
                    type: 'create',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
            afterUpdate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.uid,
                    value: instance.email,
                    table: app.model.Email.tableName,
                    type: 'update',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
        },
    })

    return Apple
}
