module.exports = app => {
    const { BIGINT, STRING, DATE, NOW } = app.Sequelize

    const EventMajor = app.model.define("EventMajor", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        event_id: {
            type: BIGINT.UNSIGNED,
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        major_id: {
            type: BIGINT.UNSIGNED,
        },
        major_name: {
            type: STRING(255),
            defaultValue: ''
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event_major',
        indexes: [
            {
                fields: ['event_id', 'lid']
            }
        ],
        timestamps: false,
    })

    return EventMajor
}
