module.exports = app => {
    const { BIGINT, INTEGER, STRING } = app.Sequelize

    const SchoolDic = app.model.define("SchoolDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        university_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        display_name: {
            type: STRING(255),
            defaultValue: ''
        },
        school_name: {
            type: STRING(255),
            defaultValue: ''
        },
        university_name: {
            type: STRING(255),
            defaultValue: ''
        },
        keyword: {
            type: STRING(255),
            defaultValue: ''
        },
        website: {
            type: STRING(255),
            defaultValue: ''
        },
        logo_url: {
            type: STRING(255),
            defaultValue: ''
        },
        area: {
            type: STRING(255),
            defaultValue: ''
        },
        country: {
            type: STRING(255),
            defaultValue: ''
        },
        short: {
            type: STRING(255),
            defaultValue: ''
        },
        directory: {
            type: STRING(255),
            defaultValue: ''
        },
        school_name_cn: {
            type: STRING(255),
            defaultValue: ''
        },
        university_name_cn: {
            type: STRING(255),
            defaultValue: ''
        },
        introduction: {
            type: STRING(1000),
            defaultValue: ''
        },
        type: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
    }, {
        tableName: 'tbl_school_dic',
    })

    SchoolDic.associate = function () {
        SchoolDic.hasMany(app.model.ProgramDic, { as: 'program', foreignKey: 'school_id', sourceKey: 'id' })
    }

    return SchoolDic
}
