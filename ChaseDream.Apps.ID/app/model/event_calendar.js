module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE, TEXT, NOW } = app.Sequelize

    const EventCalendar = app.model.define("EventCalendar", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        event_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        event_begin_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        event_end_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        push_begin_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        push_end_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        new_flag_date: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        school_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        location_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        // event_location表主键
        lid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        subject: {
            type: STRING(255),
            defaultValue: ''
        },
        url1: {
            type: STRING(500),
            defaultValue: ''
        },
        url2: {
            type: STRING(500),
            defaultValue: ''
        },
        url3: {
            type: STRING(500),
            defaultValue: ''
        },
        image: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        position: {
            type: INTEGER,
            defaultValue: 0
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        type: {
            type: INTEGER
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event_calendar',
        indexes: [
            {
                fields: ['status', 'event_end_date']
            }
        ],
        timestamps: false,
    })

    EventCalendar.associate = function () {
        EventCalendar.hasOne(app.model.SchoolDic, { as: 'event_school', foreignKey: 'id', sourceKey: 'school_id' })
        EventCalendar.hasOne(app.model.Upload, { as: 'event_image', foreignKey: 'id', sourceKey: 'image' })
        EventCalendar.hasOne(app.model.EventLocation, { as: 'event_location', foreignKey: 'id', sourceKey: 'lid' })
        EventCalendar.hasOne(app.model.Geo, { as: 'event_geo', foreignKey: 'id', sourceKey: 'location_id' })
        EventCalendar.hasMany(app.model.EventType, { as: 'event_type', foreignKey: 'lid', sourceKey: 'lid' })
        EventCalendar.hasMany(app.model.EventMajor, { as: 'event_major', foreignKey: 'lid', sourceKey: 'lid' })
        EventCalendar.hasMany(app.model.EventSchoolMajor, { as: 'event_school_major', foreignKey: 'lid', sourceKey: 'lid' })
    }

    return EventCalendar
}
