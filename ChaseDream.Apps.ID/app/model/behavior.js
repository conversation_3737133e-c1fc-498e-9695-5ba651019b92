module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE, TEXT, NOW } = app.Sequelize

    const Behavior = app.model.define("Behavior", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(20),
            defaultValue: '',
        },
        mobile: {
            type: STRING(50),
            defaultValue: '',
        },
        email: {
            type: STRING(50),
            defaultValue: '',
        },
        record_type: {
            type: STRING(20),
            defaultValue: '',
        },
        record_code: {
            type: STRING(10),
            defaultValue: '',
        },
        desc: {
            type: TEXT,
            defaultValue: '',
        },
        attach: {
            type: STRING(255),
            defaultValue: '',
        },
        operator: {
            type: STRING(50),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_user_behavior',
        indexes: [
            {
                fields: ['username']
            }
        ],
        timestamps: false,
    })

    Behavior.associate = function () {
        Behavior.hasMany(app.model.BehaviorOpt, {as: 'opt', foreignKey: 'behavior_id', sourceKey: 'id'})
    }

    return Behavior
}
