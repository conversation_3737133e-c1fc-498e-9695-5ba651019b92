module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, NOW } = app.Sequelize

    const LinkedinInfo = app.model.define("LinkedinInfo", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        uid: {
            type: STRING(255),
            defaultValue: '',
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        gender: {
            type: STRING(255),
            defaultValue: '',
        },
        nationality: {
            type: STRING(255),
            defaultValue: '',
        },
        avatar_small: {
            type: STRING(255),
            defaultValue: '',
        },
        avatar_big: {
            type: STRING(255),
            defaultValue: '',
        },
        url: {
            type: STRING(255),
            defaultValue: '',
        },
        friends: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        followers: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        summary: {
            type: STRING(255),
            defaultValue: '',
        },
        location: {
            type: STRING(255),
            defaultValue: '',
        },
        resume: {
            type: STRING(500),
            defaultValue: '',
        },
        website: {
            type: STRING(1000),
            defaultValue: '',
        },
        mobile: {
            type: STRING(255),
            defaultValue: '',
        },
        address: {
            type: STRING(255),
            defaultValue: '',
        },
        email: {
            type: STRING(255),
            defaultValue: '',
        },
        social1: {
            type: STRING(255),
            defaultValue: '',
        },
        social2: {
            type: STRING(255),
            defaultValue: '',
        },
        social3: {
            type: STRING(255),
            defaultValue: '',
        },
        birthday: {
            type: STRING(255),
            defaultValue: '',
        },
        cur_work_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        cur_edu_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        update_by: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        updated_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_linkedin_info',
        indexes: [
            {
                fields: ['uid']
            }
        ],
        timestamps: false,
    })

    LinkedinInfo.associate = function () {        
        LinkedinInfo.hasMany(app.model.LinkedinWorkExperience, {as: 'work_experience', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinEducation, {as: 'education', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinCertification, {as: 'certification', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinVolunteer, {as: 'volunteer', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinSkill, {as: 'skill', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentLanguage, {as: 'accomplishment_language', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentCourse, {as: 'accomplishment_course', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentPatent, {as: 'accomplishment_patent', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentHonor, {as: 'accomplishment_honor', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentOrganization, {as: 'accomplishment_organization', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentProject, {as: 'accomplishment_project', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentPublication, {as: 'accomplishment_publication', foreignKey: 'lid', sourceKey: 'id'})
        LinkedinInfo.hasMany(app.model.LinkedinAccomplishmentTest, {as: 'accomplishment_test', foreignKey: 'lid', sourceKey: 'id'})
    }

    return LinkedinInfo
}
