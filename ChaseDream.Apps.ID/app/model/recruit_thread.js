module.exports = app => {
    const { BIGINT, INTEGER, STRING, TEXT, DATE, NOW } = app.Sequelize

    const RecruitThread = app.model.define("RecruitThread", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        tid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        pid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        fid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        subject: {
            type: STRING(255),
            defaultValue: ''
        },
        sender_uid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        thread_html: {
            type: TEXT,
            defaultValue: '',
        },
        form_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        form_subject: {
            type: STRING(255),
            defaultValue: ''
        },
        owner_uid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        owner_name: {
            type: STRING(25),
            defaultValue: ''
        },
        owner_html: {
            type: TEXT,
            defaultValue: '',
        },
        count: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_recruit_thread',
        indexes: [{
            fields: ['form_id']
        }],
        timestamps: false,
    })

    return RecruitThread
}
