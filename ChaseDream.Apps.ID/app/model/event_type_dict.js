module.exports = app => {
    const {BIGINT, STRING} = app.Sequelize

    const EventTypeDict = app.model.define("EventTypeDict", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(50),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_event_type_dict',
        timestamps: false,
    })

    return EventTypeDict
}
