module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, NOW } = app.Sequelize

    const LinkedinVolunteer = app.model.define("LinkedinVolunteer", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        org_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        org_name: {
            type: STRING(255),
            defaultValue: '',
        },
        focus: {
            type: STRING(255),
            defaultValue: '',
        },
        begin_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        begin_month: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_month: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        desc: {
            type: STRING(1000),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_volunteer',
        timestamps: false,
    })

    return LinkedinVolunteer
}
