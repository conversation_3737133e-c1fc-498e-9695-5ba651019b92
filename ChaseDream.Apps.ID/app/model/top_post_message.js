module.exports = app => {
    const {BIGINT, STRING, DATE, NOW} = app.Sequelize

    const TopPostMessage = app.model.define("TopPostMessage", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        message: {
            type: STRING(1000),
            allowNull: false,
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_top_post_message',
        timestamps: false,
    })

    return TopPostMessage
}
