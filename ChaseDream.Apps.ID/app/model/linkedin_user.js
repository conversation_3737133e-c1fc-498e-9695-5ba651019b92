module.exports = app => {
    const { BIGINT, INTEGER, STRING, TEXT, DATE, NOW } = app.Sequelize

    const LinkedinUser = app.model.define("LinkedinUser", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(255),
            allowNull: false,
        },
        password: {
            type: STRING(255),
            allowNull: false,
        },
        pin: {
            type: STRING(255),
            defaultValue: ''
        },
        cookie: {
            type: TEXT,
            defaultValue: '',
        },
        status: {
            type: INTEGER,
            defaultValue: 0,
        },
        error_message: {
            type: STRING(1000),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_user',
        timestamps: false,
    })

    return LinkedinUser
}
