module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, NOW } = app.Sequelize

    const LinkedinWorkExperience = app.model.define("LinkedinWorkExperience", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        org_id: {
            type: BIGINT.UNSIGNED,
        },
        org_name: {
            type: STRING(100),
            defaultValue: '',
        },
        title: {
            type: STRING(100),
            defaultValue: '',
        },
        job_category: {
            type: STRING(200),
            defaultValue: '',
        },
        city: {
            type: STRING(200),
            defaultValue: '',
        },
        job_desc: {
            type: STRING(1000),
            defaultValue: '',
        },
        begin_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        begin_month: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_year: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        end_month: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_work_experience',
        timestamps: false,
    })

    LinkedinWorkExperience.associate = function () {
        LinkedinWorkExperience.hasOne(app.model.LinkedinOrganization, { as: 'org', foreignKey: 'id', sourceKey: 'org_id' })
    }

    return LinkedinWorkExperience
}
