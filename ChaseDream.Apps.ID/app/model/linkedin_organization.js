module.exports = app => {
    const { BIGINT, INTEGER, DATE, STRING, TEXT, NOW } = app.Sequelize

    const LinkedinOrganization = app.model.define("LinkedinOrganization", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        linkedin_org_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        logo: {
            type: STRING(255),
            defaultValue: '',
        },
        summary: {
            type: STRING(255),
            defaultValue: '',
        },
        url: {
            type: STRING(255),
            defaultValue: '',
        },
        desc: {
            type: TEXT,
            defaultValue: '',
        },
        location: {
            type: STRING(255),
            defaultValue: '',
        },
        industry: {
            type: STRING(255),
            defaultValue: '',
        },
        website: {
            type: STRING(255),
            defaultValue: '',
        },
        staff_size: {
            type: STRING(255),
            defaultValue: '',
        },
        company_type: {
            type: STRING(255),
            defaultValue: '',
        },
        company_location: {
            type: STRING(255),
            defaultValue: '',
        },
        established_time: {
            type: STRING(255),
            defaultValue: '',
        },
        focus_areas: {
            type: TEXT,
            defaultValue: '',
        },
        linkedin_staff_size: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        linkedin_followers: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_organization',
        indexes: [{
            fields: ['url']
        }, {
            fields: ['linkedin_org_id']
        }],
        timestamps: false,
    })

    return LinkedinOrganization
}
