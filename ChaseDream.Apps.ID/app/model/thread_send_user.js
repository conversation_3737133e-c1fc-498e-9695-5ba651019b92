module.exports = app => {
    const {BIGINT, STRING, DATE, NOW} = app.Sequelize

    const ThreadSendUser = app.model.define("ThreadSendUser", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(200),
            allowNull: false,
        },
        nickname: {
            type: STRING(200),
            allowNull: false,
        },
        password: {
            type: STRING(200),
            allowNull: false,
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_thread_send_user',
        timestamps: false,
    })

    return ThreadSendUser
}
