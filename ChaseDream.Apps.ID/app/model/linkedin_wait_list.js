module.exports = app => {
    const { BIGINT, INTEGER, STRING, TEXT, DATE, NOW } = app.Sequelize

    const LinkedinWaitList = app.model.define("LinkedinWaitList", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        url: {
            type: STRING(200),
            allowNull: false,
        },
        status: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        lid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        error_message: {
            type: STRING(1000),
            defaultValue: '',
        },
        html: {
            type: TEXT,
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_wait_list',
        timestamps: false,
    })

    return LinkedinWaitList
}
