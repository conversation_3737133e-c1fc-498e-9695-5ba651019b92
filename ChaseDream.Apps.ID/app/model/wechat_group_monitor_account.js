module.exports = app => {
    const { BIGINT, INTEGER, STRING, TEXT, BOOLEAN } = app.Sequelize

    const WechatGroupMonitorAccount = app.model.define("WechatGroupMonitorAccount", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        chatroom_id: {
            type: STRING(100),
            defaultValue: '',
        },
        wx_id: {
            type: STRING(255),
            defaultValue: '',
        },
        wx: {
            type: STRING(255),
            defaultValue: '',
        },
        wx_nickname: {
            type: STRING(255),
            defaultValue: '',
        },
        wx_group_nickname: {
            type: STRING(255),
            defaultValue: '',
        },
        join_time: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        join_method: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        last_message_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        status: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        cd_worker: {
            type: BOOLEAN,
            defaultValue: false,
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_wechat_group_monitor_account',
        timestamps: false,
        indexes: [{
            name: 'chatroom_id',
            fields: ['chatroom_id']
        }, {
            name: 'wx_id',
            fields: ['wx_id']
        }],
    })

    WechatGroupMonitorAccount.associate = function () {
        WechatGroupMonitorAccount.hasOne(app.model.WechatGroupMonitorMessage, { as: 'message', foreignKey: 'id', sourceKey: 'last_message_id' })
        WechatGroupMonitorAccount.hasOne(app.model.WechatGroupMonitor, { as: 'wechat_group_monitor', foreignKey: 'chatroom_id', sourceKey: 'chatroom_id' })
    }

    return WechatGroupMonitorAccount
}
