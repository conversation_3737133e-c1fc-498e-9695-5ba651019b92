module.exports = app => {
    const { STRING, BIGINT, INTEGER, DATE, NOW } = app.Sequelize

    const User = app.model.define("user", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(32),
            defaultValue: '',
        },
        realname: {
            type: STRING(32),
            defaultValue: '',
        },
        department: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        password: {
            type: STRING(32),
            defaultValue: '',
        },
        role: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        permissions: {
            type: STRING(2000),
            defaultValue: ''
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_user',
        timestamps: false,
        indexes: [
            {
                fields: ['username']
            }
        ]
    })

    return User
}
