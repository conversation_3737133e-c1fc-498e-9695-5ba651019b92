module.exports = app => {
    const { STRING, BIGINT, INTEGER, DATE, NOW } = app.Sequelize

    const Upload = app.model.define("upload", {
        id: {
            type: BIGINT,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        school_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        category: {
            type: STRING(10),
            defaultValue: ''
        },
        digest: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        original: {
            type: STRING(255),
            allowNull: false
        },
        fullpath: {
            type: STRING(4000),
            allowNull: false,
        },
        type: {
            type: STRING(100),
            allowNull: false,
        },
        order: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_upload',
        indexes: [
            {
                fields: ['digest']
            }
        ],
        timestamps: false,
    })

    return Upload
}
