module.exports = app => {
    const { BIGINT, STRING, DATE, NOW } = app.Sequelize

    const ProgramDegreeCategoryDic = app.model.define("ProgramDegreeCategoryDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        education_level_cn: {
            type: STRING(50),
            defaultValue: ''
        },
        education_level_en: {
            type: STRING(50),
            defaultValue: ''
        },
        degree_cn: {
            type: STRING(50),
            defaultValue: ''
        },
        degree_en: {
            type: STRING(50),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_program_degree_category_dic',
    })

    return ProgramDegreeCategoryDic
}
