module.exports = app => {
    const { BIGINT, STRING, DATE, NOW } = app.Sequelize

    const EventType = app.model.define("EventType", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        event_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        event_type_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        event_type_name: {
            type: STRING(255),
            defaultValue: ''
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event_type',
        indexes: [
            {
                fields: ['event_id', 'lid']
            }
        ],
        timestamps: false,
    })

    return EventType
}
