module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE, NOW } = app.Sequelize

    const TopPostThread = app.model.define("TopPostThread", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        tid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        subject: {
            type: STRING(255),
            defaultValue: '',
        },
        next_tick: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        min: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        max: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        total: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_top_post_thread',
        timestamps: false,
    })

    return TopPostThread
}
