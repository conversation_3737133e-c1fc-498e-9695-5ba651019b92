module.exports = app => {
    const {BIGINT, INTEGER, STRING, TEXT, BOOLEAN, DATE, NOW} = app.Sequelize

    const Event = app.model.define("Event", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        school_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        subject: {
            type: STRING(255),
            defaultValue: ''
        },
        content: {
            type: TEXT,
            defaultValue: ''
        },
        www_url: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        www_url_senduser: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        forum_url: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        iese_url: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        forum_url_fid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        forum_url_pid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        forum_url_typeid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        forum_url_senduser: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        highlight: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        digest: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        stick: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        push_0: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        push_1: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        push_2: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        push_3: {
            type: BOOLEAN,
            defaultValue: 0,
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_event',
        timestamps: false,
    })

    Event.associate = function () {
        Event.hasOne(app.model.SchoolDic, {as: 'event_school', foreignKey: 'id', sourceKey: 'school_id'})        
        Event.hasMany(app.model.EventLocation, {as: 'event_location', foreignKey: 'event_id', sourceKey: 'id'})        
    }

    return Event
}
