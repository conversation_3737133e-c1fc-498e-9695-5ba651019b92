module.exports = app => {
    const {STRING, BIGINT, INTEGER} = app.Sequelize

    const MemberLog = app.model.define("MemberLog", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        value: {
            type: STRING(32),
            defaultValue: '',
        },
        table: {
            type: STRING(32),
            defaultValue: '',
        },
        type: {
            type: STRING(10),
            defaultValue: '',
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_member_log',
        timestamps: false,
    })

    return MemberLog
}
