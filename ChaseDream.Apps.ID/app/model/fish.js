module.exports = app => {
    const { BIGINT, INTEGER, CHAR } = app.Sequelize

    return app.model.define("fish", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: '',            
        },
        ip: {
            type: CHAR(15),            
            defaultValue: ''
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
    }, {
        tableName: 'tbl_adv_fish',
        timestamps: false,
    })    
}
