module.exports = app => {
    const { BIGINT, INTEGER, STRING } = app.Sequelize

    const ContractStampAttachment = app.model.define("ContractStampAttachment", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        original: {
            type: STRING(255),
            defaultValue: '',
        },
        fullpath: {
            type: STRING(1000),
            defaultValue: '',
        },
        ext: {
            type: STRING(10),
            defaultValue: '',
        },
        type: {
            type: STRING(100),
            defaultValue: '',
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_contract_stamp_attachment',
        timestamps: false,
    })

    return ContractStampAttachment
}
