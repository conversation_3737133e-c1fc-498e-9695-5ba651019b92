module.exports = app => {
    const { BIGINT, STRING, INTEGER, DATE, NOW } = app.Sequelize

    const RecruitOwner = app.model.define("RecruitOwner", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(25),
            defaultValue: ''
        },
        type: {
            type: INTEGER.UNSIGNED,
        },
        account: {
            type: STRING(50),
            defaultValue: ''
        },
        qr_code: {
            type: STRING(255),
            defaultValue: ''
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_recruit_owner',
        timestamps: false,
    })

    return RecruitOwner
}
