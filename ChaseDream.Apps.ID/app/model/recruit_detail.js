module.exports = app => {
    const { BIGINT, INTEGER, STRING, TEXT, DATE, NOW } = app.Sequelize

    const RecruitDetail = app.model.define("RecruitDetail", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        tid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        pid: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        form_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        thread_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        form_subject: {
            type: STRING(255),
            defaultValue: ''
        },
        type: {
            type: INTEGER.UNSIGNED,
        },
        uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0,
        },
        username: {
            type: STRING(50),
            defaultValue: ''
        },
        mobile: {
            type: STRING(50),
            defaultValue: ''
        },
        wechat: {
            type: STRING(50),
            defaultValue: ''
        },
        qq: {
            type: STRING(50),
            defaultValue: ''
        },
        test_date: {
            type: STRING(50),
            defaultValue: ''
        },
        test_plan: {
            type: STRING(255),
            defaultValue: ''
        },
        city: {
            type: STRING(50),
            defaultValue: ''
        },
        country_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        province_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        city_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        status1: {
            type: STRING(255),
            defaultValue: ''
        },
        status2: {
            type: STRING(255),
            defaultValue: ''
        },
        status3: {
            type: STRING(255),
            defaultValue: ''
        },
        status4: {
            type: STRING(255),
            defaultValue: ''
        },
        ps: {
            type: STRING(255),
            defaultValue: ''
        },
        bachelor: {
            type: STRING(255),
            defaultValue: ''
        },
        industry: {
            type: STRING(255),
            defaultValue: ''
        },
        appeal: {
            type: STRING(255),
            defaultValue: ''
        },
        ip: {
            type: STRING(15),
            defaultValue: ''
        },
        ip_location: {
            type: STRING(100),
            defaultValue: ''
        },
        opt: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        reason_code: {
            type: STRING(10),
            defaultValue: ''
        },
        reason: {
            type: TEXT,
            defaultValue: ''
        },
        note: {
            type: STRING(1000),
            defaultValue: ''
        },
        forum_data: {
            type: STRING(1000),
            defaultValue: ''
        },
        report: {
            type: TEXT,
            defaultValue: ''
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_recruit_detail',
        indexes: [{
            fields: ['form_id']
        }, {
            fields: ['thread_id']
        }, {
            fields: ['reason_code']
        }],
        timestamps: false,
    })

    return RecruitDetail
}
