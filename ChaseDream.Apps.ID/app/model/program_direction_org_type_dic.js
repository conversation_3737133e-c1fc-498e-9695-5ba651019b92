module.exports = app => {
    const { BIGINT, STRING, DATE, NOW } = app.Sequelize

    const ProgramDirectionOrgTypeDic = app.model.define("ProgramDirectionOrgTypeDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        parent_id: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        name: {
            type: STRING(150),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_program_direction_org_type_dic',
    })

    return ProgramDirectionOrgTypeDic
}
