module.exports = app => {
    const {BIGINT, STRING} = app.Sequelize

    const Role = app.model.define("role", {
        id: {
            type: BIGINT,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        name: {
            type: STRING(255),
            allowNull: false
        },
    }, {
        tableName: 'tbl_role'
    })

    return Role
}
