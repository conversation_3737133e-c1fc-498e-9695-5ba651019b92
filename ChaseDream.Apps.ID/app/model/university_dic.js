module.exports = app => {
    const {BIGINT, STRING} = app.Sequelize

    const UniversityDic = app.model.define("UniversityDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        university_name_en: {
            type: STRING(255),
            defaultValue: ''
        },
        university_name_cn: {
            type: STRING(255),
            defaultValue: ''
        },
        display_name: {
            type: STRING(255),
            defaultValue: ''
        },
        keyword: {
            type: STRING(255),
            defaultValue: ''
        },
        website: {
            type: STRING(255),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_university_dic',        
    })

    return UniversityDic
}
