module.exports = app => {
    const { B<PERSON>IN<PERSON>, DATE, STRING, <PERSON>OOLEAN, NOW } = app.Sequelize

    const LinkedinSkill = app.model.define("LinkedinSkill", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        lid: {
            type: BIGINT.UNSIGNED,
        },
        name: {
            type: STRING(255),
            defaultValue: '',
        },
        top: {
            type: BOOLEAN,
        },
        type: {
            type: STRING(255),
            defaultValue: '',
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_linkedin_skill',
        timestamps: false,
    })

    return LinkedinSkill
}
