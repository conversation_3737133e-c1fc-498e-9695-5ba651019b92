module.exports = app => {
    const { BIGINT, INTEGER, STRING, DATE, NOW } = app.Sequelize

    const MockUser = app.model.define("MockUser", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(255),
            defaultValue: ''
        },
        status: {
            type: INTEGER,
            defaultValue: 0
        },
        created_at: {
            type: DATE,
            defaultValue: NOW,
        },
    }, {
        tableName: 'tbl_mock_user',
        timestamps: false,
    })

    return MockUser
}
