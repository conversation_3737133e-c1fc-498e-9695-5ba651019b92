module.exports = app => {
    const { BIGINT, INTEGER, STRING } = app.Sequelize

    const LinkedinCrawlLog = app.model.define("LinkedinCrawlLog", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        username: {
            type: STRING(255),
            defaultValue: '',
        },
        message: {
            type: STRING(1000),
            defaultValue: '',
        },
        screenshot: {
            type: STRING(1000),
            defaultValue: '',
        },
        created_at: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
    }, {
        tableName: 'tbl_linkedin_crawl_log',
        timestamps: false,
    })

    return LinkedinCrawlLog
}
