module.exports = app => {
    const { BIGINT, INTEGER, STRING } = app.Sequelize

    const SchoolAreaDic = app.model.define("SchoolAreaDic", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        university_id: {
            type: BIGINT.UNSIGNED,            
            defaultValue: 0,            
        },
        order: {
            type: STRING(10),
            defaultValue: ''
        },
        name: {
            type: STRING(255),
            defaultValue: ''
        },
        address: {
            type: STRING(500),
            defaultValue: ''
        },
        postcode: {
            type: STRING(50),
            defaultValue: ''
        },
        area: {
            type: STRING(255),
            defaultValue: ''
        },
        country_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        province_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        city_id: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0,
        },
        timezone: {
            type: STRING(100),
            defaultValue: ''
        },
        longitude: {
            type: STRING(50),
            defaultValue: ''
        },
        latitude: {
            type: STRING(50),
            defaultValue: ''
        },
        phone: {
            type: STRING(50),
            defaultValue: ''
        },
        google_map: {
            type: STRING(500),
            defaultValue: ''
        },
        baidu_map: {
            type: STRING(500),
            defaultValue: ''
        },
        bing_map: {
            type: STRING(500),
            defaultValue: ''
        },
    }, {
        tableName: 'tbl_school_area_dic',  
    })

    SchoolAreaDic.associate = function () {
        SchoolAreaDic.hasMany(app.model.SchoolAreaPhoto, {as: 'school_area_photo', foreignKey: 'school_area_id', sourceKey: 'id'})
    }

    return SchoolAreaDic
}
