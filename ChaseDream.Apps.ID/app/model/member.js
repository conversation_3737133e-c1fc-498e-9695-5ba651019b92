const moment = require('moment-timezone')

module.exports = app => {
    const { BIGINT, INTEGER, CHAR, STRING } = app.Sequelize

    const Member = app.model.define("member", {
        id: {
            type: BIGINT.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
            unique: true
        },
        forum_uid: {
            type: BIGINT.UNSIGNED,
            defaultValue: 0
        },
        username: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        password: {
            type: CHAR(32),
            allowNull: false,
            defaultValue: ''
        },
        salt: {
            type: CHAR(6),
            allowNull: false
        },
        status: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: 0
        },
        regip: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
        },
        regdate: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
        phonestamp: {
            type: STRING(11),
            defaultValue: '',
        },
        phonestamp2: {
            type: STRING(11),
            defaultValue: '',
        },
        create_at: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        },
    }, {
        tableName: 'tbl_member',
        timestamps: false,
        indexes: [
            {
                fields: ['forum_uid']
            }
        ],
        hooks: {
            afterCreate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.id,
                    value: instance.username,
                    table: app.model.Member.tableName,
                    type: 'create',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
            afterUpdate: async (instance, options) => {
                await app.model.MemberLog.create({
                    uid: instance.id,
                    value: instance.username,
                    table: app.model.Member.tableName,
                    type: 'update',
                    dateline: moment().tz("Asia/Shanghai").unix()
                })
            },
        },
    })

    Member.associate = function () {
        Member.hasOne(app.model.Mobile, { as: 'mobile', foreignKey: 'uid', sourceKey: 'id' })
        Member.hasOne(app.model.Wechat, { as: 'wechat', foreignKey: 'uid', sourceKey: 'id' })
        Member.hasOne(app.model.MobileLog, { as: 'mobile_log', foreignKey: 'uid', sourceKey: 'id' })
    }

    return Member
}
