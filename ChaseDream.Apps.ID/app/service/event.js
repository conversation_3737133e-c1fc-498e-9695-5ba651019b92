const _ = require('lodash')
const Service = require('egg').Service

class EventService extends Service {
    async event_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Event.update(body, {
            where: { id: body.id }
        })

        const event = await ctx.model.Event.findOne({
            where: { id: body.id },
            raw: true,
        })

        if (event.www_url) {
            await ctx.wwwModel.IwmsNews.update({
                title: body.subject,
                content: body.content,
            }, {
                where: {
                    articleid: event.www_url
                }
            })

            await ctx.service.www.clear_cache()
        }

        if (event.forum_url) {
            const chasedream = await ctx.model.EventUser.findOne({
                where: {
                    username: '<EMAIL>'
                },
                raw: true,
            })

            await ctx.service.thread.update({
                username: chasedream.username,
                password: ctx.helper.decode_dz_string(chasedream.password),
                tid: event.forum_url,
                fid: event.forum_url_fid,
                pid: event.forum_url_pid,
                subject: body.subject,
                content: body.content,
                typeid: event.forum_url_typeid,
                htmlon: 1,
            })
        }

        await ctx.model.EventCalendar.update({
            school_id: body.school_id
        }, {
            where: { event_id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async event_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const event = await ctx.model.Event.findOne({
            where: {
                id: body.id
            },
            raw: true,
        })

        let tran = await ctx.model.transaction()

        try {
            await ctx.model.Event.update({
                status: -1,
                push_0: false,
                push_1: false,
                push_2: false,
                push_3: false,
                www_url: 0,
                forum_url: 0,
                highlight: 0,
                digest: 0,
                stick: 0,
            }, {
                where: { id: body.id },
                transaction: tran,
            })

            await ctx.model.EventCalendar.update({
                status: -1,
            }, {
                where: { event_id: body.id },
                transaction: tran,
            })

            await ctx.model.EventRelease.update({
                status: -1,
            }, {
                where: { event_id: body.id },
                transaction: tran,
            })

            if (event.www_url) {
                await ctx.wwwModel.IwmsNews.destroy({
                    where: {
                        articleid: event.www_url
                    },
                    force: true,
                })
            }

            if (event.forum_url) {
                const user = await ctx.model.EventUser.findOne({
                    where: {
                        id: event.forum_url_senduser
                    },
                    raw: true,
                })

                const body = {
                    username: user.username,
                    password: ctx.helper.decode_dz_string(user.password),
                    tid: event.forum_url,
                    fid: event.forum_url_fid,
                }

                await ctx.service.thread.delete(body)
            }

            await tran.commit()

        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async event_location_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventLocation.update(body, {
            where: { id: body.id }
        })

        await ctx.model.EventCalendar.update({
            location_id: body.city_id || body.province_id
        }, {
            where: { lid: body.id }
        })

        const ets = await ctx.model.EventType.findAll({
            attributes: ['event_type_id'],
            where: {
                event_id: body.event_id,
                lid: body.id,
            },
            raw: true,
        }).map(row => { return row.event_type_id })

        let current_et = new Set(ets)
        let new_et = new Set(body.event_type)
        let all_et = new Set([...current_et, ...new_et])

        let need_add = new Set([...all_et].filter(x => !current_et.has(x)))
        let need_del = new Set([...all_et].filter(x => !new_et.has(x)))

        if (need_add.size) {
            need_add = Array.from(need_add)

            for (const event_type_id of need_add) {
                const et = await ctx.model.EventTypeDict.findOne({
                    where: {
                        id: event_type_id
                    }
                })
                await ctx.model.EventType.create({
                    event_id: body.event_id,
                    lid: body.id,
                    event_type_id,
                    event_type_name: et.name,
                })
            }
        }

        if (need_del.size) {
            need_del = Array.from(need_del)

            for (const event_type_id of need_del) {
                await ctx.model.EventType.destroy({
                    where: {
                        event_id: body.event_id,
                        lid: body.id,
                        event_type_id
                    }
                })
            }
        }

        const ems = await ctx.model.EventMajor.findAll({
            attributes: ['major_id'],
            where: {
                event_id: body.event_id,
                lid: body.id,
            },
            raw: true,
        }).map(row => { return row.major_id })

        let current_em = new Set(ems)
        let new_em = new Set(body.major)
        let all_em = new Set([...current_em, ...new_em])

        need_add = new Set([...all_em].filter(x => !current_em.has(x)))
        need_del = new Set([...all_em].filter(x => !new_em.has(x)))

        if (need_add.size) {
            need_add = Array.from(need_add)

            for (const major_id of need_add) {
                const em = await ctx.model.MajorCategoryDic.findOne({
                    where: {
                        id: major_id
                    }
                })
                await ctx.model.EventMajor.create({
                    event_id: body.event_id,
                    lid: body.id,
                    major_id,
                    major_name: em.display_name,
                })
            }
        }

        if (need_del.size) {
            need_del = Array.from(need_del)

            for (const major_id of need_del) {
                await ctx.model.EventMajor.destroy({
                    where: {
                        event_id: body.event_id,
                        lid: body.id,
                        major_id
                    }
                })
            }
        }

        const esm = await ctx.model.EventSchoolMajor.findAll({
            attributes: ['school_major_id'],
            where: {
                event_id: body.event_id,
                lid: body.id,
            },
            raw: true,
        }).map(row => { return row.school_major_id })

        let current_esm = new Set(esm)
        let new_esm = new Set(body.school_major)
        let all_esm = new Set([...current_esm, ...new_esm])

        need_add = new Set([...all_esm].filter(x => !current_esm.has(x)))
        need_del = new Set([...all_esm].filter(x => !new_esm.has(x)))

        if (need_add.size) {
            need_add = Array.from(need_add)

            for (const school_major_id of need_add) {
                await ctx.model.EventSchoolMajor.create({
                    event_id: body.event_id,
                    lid: body.id,
                    school_major_id,
                })
            }
        }

        if (need_del.size) {
            need_del = Array.from(need_del)

            for (const school_major_id of need_del) {
                await ctx.model.EventSchoolMajor.destroy({
                    where: {
                        event_id: body.event_id,
                        lid: body.id,
                        school_major_id
                    }
                })
            }
        }

        await ctx.model.Event.update({
            status: 0
        }, {
            where: {
                id: body.event_id
            }
        })
    }

    async event_release_clean() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const events = await ctx.model.EventRelease.findAll({
            where: {
                status: {
                    [Op.gte]: 0,
                },
            },
            raw: true
        })

        for (const event of events) {
            if (event.event_end_date < ctx.helper.now() && event.push_end_date < ctx.helper.now()) {
                await ctx.model.EventRelease.update({
                    status: -1,
                }, {
                    where: {
                        id: event.id,
                    }
                })
            }
        }

        await this.load_event_release_to_cache()
    }

    async event_calendar_clean() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const events = await ctx.model.EventCalendar.findAll({
            where: {
                status: {
                    [Op.gte]: 0,
                },
            },
            raw: true
        })

        for (const event of events) {
            if (event.event_end_date < ctx.helper.now()) {
                await ctx.model.EventCalendar.update({
                    status: -1,
                }, {
                    where: {
                        id: event.id,
                    }
                })
            }
        }
    }

    async load_event_release_to_cache() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let push1 = await ctx.model.EventRelease.findAll({
            include: [{
                model: ctx.model.Geo,
                required: false,
                as: 'event_geo',
                attributes: ['id', 'name']
            }],
            where: {
                status: {
                    [Op.gte]: 0,
                },
                type: 1,
                [Op.and]: [{
                    push_begin_date: {
                        [Op.lte]: ctx.helper.now()
                    }
                }, {
                    push_end_date: {
                        [Op.gte]: ctx.helper.now()
                    }
                }],
            },
            order: [
                ['event_begin_date', 'ASC'],
            ],
        })

        for (let el of push1) {
            if (el.event_geo && el.event_geo.name === '申请截止') {
                el.event_geo.name = 'Deadline'
            }
        }

        const push2_3 = await ctx.model.EventRelease.findAll({
            include: [{
                model: ctx.model.Geo,
                required: false,
                as: 'event_geo',
                attributes: ['id', 'name']
            }],
            where: {
                status: {
                    [Op.gte]: 0,
                },
                type: [2, 3],
                [Op.and]: [{
                    event_begin_date: {
                        [Op.lte]: ctx.helper.now()
                    }
                }, {
                    event_end_date: {
                        [Op.gte]: ctx.helper.now()
                    }
                }],
            }
        })

        let index_image = _.filter(push2_3, { 'type': 2 })
        let headlines = _.filter(push2_3, { 'type': 3 })

        let index_image_level1 = _.filter(index_image, { 'level': 1 })
        let index_image_level2 = _.filter(index_image, { 'level': 2 })
        let index_image_level3 = _.filter(index_image, { 'level': 3 })

        headlines = _.orderBy(headlines, ['order'], ['desc'])

        let event_list = {
            events: push1,
            index_image: {
                level1: _.orderBy(index_image_level1, ['order'], ['desc']),
                level2: _.orderBy(index_image_level2, ['order'], ['desc']),
                level3: _.orderBy(index_image_level3, ['order'], ['desc']),
            },
            headlines
        }

        await app.redis.set(`${config.redis_key.event_list}`, JSON.stringify(event_list))

        return event_list
    }

    async clear_cache() {
        const { app, config } = this

        await app.redis.del(`${config.redis_key.event_list}`)
    }
}

module.exports = EventService
