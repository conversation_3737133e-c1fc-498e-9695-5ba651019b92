const Service = require('egg').Service

class ServerService extends Service {
    async clear() {
        const { ctx, app } = this

        const Op = app.Sequelize.Op

        await ctx.model.CaptchaCheckLog.destroy({
            where: {
                dateline: {
                    [Op.lt]: ctx.helper.today_begin_timestamp()
                }
            },
            force: true
        })

        await ctx.model.CaptchaCheckLog.update({
            count: 0
        }, {
            where: {}
        })
    }

    async monitor_sameip_register() {
        const { ctx, app, config } = this

        const notices = []
        const ips = new Map
        const timestamp = ctx.helper.subtract_timestamp(7)

        const ip_whitelist = await ctx.model.MonitorSameipWhitelist.findAll({
            attributes: ['ip'],
            where: {},
            raw: true,
        }).map(row => { return row.ip })

        const items = await ctx.forumModel.query(`SELECT a.uid,a.regip from cc_common_member_status as a left outer join cc_common_member as b on a.uid=b.uid where a.regip in(SELECT regip from cc_common_member_status where uid in (SELECT uid from cc_common_member where status=0 and regdate > ${timestamp}) GROUP BY regip HAVING count(regip) >= 3) and b.status=0 and b.regdate > ${timestamp}`, { type: app.Sequelize.QueryTypes.SELECT })

        for (const item of items) {
            if (!ip_whitelist.includes(item.regip)) {
                const key = `reg-monitor:${item.regip}`
                if (ips.has(key)) {
                    let val = ips.get(key)
                    ips.set(key, [...val, item.uid])
                } else {
                    ips.set(key, [item.uid])
                }
            }
        }

        for (let [key, val] of ips.entries()) {
            let o_val = await app.redis.get(key) || []
            if (o_val.length === 0 || val.length > o_val.split(',').length) notices.push({ key, val })
        }

        if (notices.length) {
            const subject = '相同IP注册量过多提醒'
            let message = '检测发现相同IP注册量已到报警阈值<br/><br/>'

            for (const notice of notices) {
                const ip = notice.key.replace('reg-monitor:', '')
                message += `IP: <a href='https://forum.chasedream.com/admin.php?action=mynav&operation=userlist&frames=yes&txtregip=${ip}&rdostatus=1' target='_blank'>${ip}</a><br/>`

                const ip_data = await ctx.curl(`${config.ip_location.aliyun.url}${ip}`, {
                    method: 'GET',
                    dataType: 'json',
                    headers: {
                        'Authorization': `APPCODE ${config.ip_location.aliyun.APPCODE}`,
                    },
                    timeout: 2000,
                })

                const nation = ip_data.data.ENTITY.INPUT_IP_ADDRESS.NATION || ''
                const province = ip_data.data.ENTITY.INPUT_IP_ADDRESS.PROVINCE || ''
                const city = ip_data.data.ENTITY.INPUT_IP_ADDRESS.CITY || ''
                const ip_location = `${nation}-${province}-${city}`

                message += `归属地: ${ip_location}<br/>`
                message += 'UID | 用户名 | 注册时间<br/>'

                const users = await ctx.forumModel.query(`SELECT uid,username,regdate from cc_common_member where uid in (${notice.val.join(',')}) order by uid desc`, { type: app.Sequelize.QueryTypes.SELECT })
                for (const user of users) {
                    message += `${user.uid} | ${user.username} | ${ctx.helper.timestamp_to_date(user.regdate, 'YYYY-MM-DD HH:mm:ss')} <br/>`
                }

                console.log(message)

                message += '---------------------------<br/>'
            }

            await ctx.curl(config.mail.url, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: {
                    toemail: '<EMAIL>',
                    // toemail: '<EMAIL>',
                    subject,
                    message,
                    token: config.mail.token,
                    mailfrom: config.mail.mailfrom,
                },
                timeout: 10000,
            })

            for (let [key, val] of ips.entries()) {
                await app.redis.setex(key, config.reg_monitor_timeout, val.join(','))
            }
        }
    }
}

module.exports = ServerService
