const _ = require('lodash')
const fs = require('fs').promises
const path = require('path')
const fse = require('fs-extra')
const { promisify } = require('util')
const sizeof = promisify(require('image-size'))
const { Wechaty } = require('wechaty')
const { PuppetPadlocal } = require('wechaty-puppet-padlocal')
const Service = require('egg').Service

class WechatGroupService extends Service {
    async start() {
        const { ctx, app, config } = this

        // if (global.wechat_bot) return

        // global.wechat_bot = undefined

        // const name = config.wechat_group.token
        // let wechaty_opt = {
        //     name,
        // }

        // wechaty_opt.puppet = new PuppetPadlocal({
        //     token: config.wechat_group.token,
        // })

        // const bot = new Wechaty(wechaty_opt)

        // global.wechat_bot = bot

        // bot
        //     .on('scan', this.on_scan.bind(this))
        //     .on('login', this.on_login.bind(this))
        //     .on('logout', this.on_logout.bind(this))
        //     .on('message', this.on_message.bind(this))
        //     .on("room-topic", this.on_room_topic.bind(this))
        //     .on("room-join", this.on_room_join.bind(this))
        //     .on("room-leave", this.on_room_leave.bind(this))

        // await bot.start()
        // await bot.ready()

        // const rooms = await this.self_group_list()
        // for (const room of rooms) {
        //     let group = await ctx.model.WechatGroupMonitor.findOne({
        //         where: {
        //             chatroom_id: room.id,
        //         },
        //         raw: true,
        //     })
        //     if (!group) {
        //         await this.wechat_group_monitor_create(room.payload)
        //     }
        // }
    }

    async on_scan(
        qrcode,
        status,
    ) {
        const { ctx, app } = this

        const qr_code = 'https://wechaty.js.org/qrcode/' + encodeURIComponent(qrcode)
        console.log(qr_code)

        await ctx.model.WechatGroupMonitor.update({
            qr_code
        }, {
            where: {
                status: 0,
            }
        })
    }

    async on_login(bot) {
        const { ctx, app } = this

        await ctx.model.WechatGroupMonitor.update({
            qr_code: '',
        }, {
            where: {
                status: 0,
            }
        })

        console.log(`${bot} 已登录`)
    }

    async on_logout(user) {
        const { ctx, app } = this

        global.wechat_bot = undefined

        await ctx.helper.bark_message(`微信群助手 ${user.name()} 已下线`, `微信群助手已在其他iPad登录，请及时处理。`)

        console.log(`${user.name()} 已登出`)
    }

    async on_message(msg) {
        const { ctx } = this

        const contact = msg.talker()
        const text = msg.text()
        const room = msg.room()
        const type = msg.type()
        const wx_name = contact.name()
        const wx_id = contact.id

        if (!room) return

        const topic = await room.topic()
        const chatroom_id = room.id

        let group = await ctx.model.WechatGroupMonitor.findOne({
            where: {
                chatroom_id,
            },
            raw: true,
        })

        if (!group) {
            const room_info = await this.get_room_by_id(chatroom_id)
            group = await this.wechat_group_monitor_create(room_info.payload)
        }

        if (group && group.status === 0) {
            await this.group_monitor_message_handler(msg, chatroom_id, type, topic, wx_id, wx_name, text, group)
        }
    }

    async group_monitor_message_handler(msg, chatroom_id, type, topic, wx_id, wx_name, text, group) {
        const { ctx, config } = this
        let wgmm = undefined

        try {
            switch (type) {
                case global.wechat_bot.Message.Type.Text: {
                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text,
                        type,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })
                    break
                }

                case global.wechat_bot.Message.Type.Emoticon: {
                    const emotionFile = await msg.toFileBox()
                    const emotionData = await emotionFile.toBuffer()

                    const path = `${config.forum_attachment}/${ctx.helper.today('YYYYMM/DD')}`

                    await fse.mkdirp(`${config.baseDir}${path}`)

                    const filename = `${ctx.helper.today('HHmmss')}${ctx.helper.randomstring(16)}.jpg`
                    const attachment = `${ctx.helper.today('YYYYMM/DD')}/${filename}`

                    await fs.writeFile(`${config.baseDir}${path}/${filename}`, emotionData)

                    let thumb = undefined
                    if (emotionFile.metadata.width > config.wechat_group.thumbwidth) {
                        thumb = await ctx.helper.resize_spec(emotionData, `${config.baseDir}${path}/${filename}.thumb`, [{
                            w: config.wechat_group.thumbwidth,
                            h: null
                        }], 'jpg')
                    }

                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text,
                        type,
                        filename: `${emotionFile.name.substring(0, emotionFile.name.lastIndexOf('.'))}.jpg`,
                        filesize: emotionFile.metadata.len,
                        attachment,
                        isimage: true,
                        width: emotionFile.metadata.width,
                        height: emotionFile.metadata.height,
                        thumb: emotionFile.metadata.width > config.wechat_group.thumbwidth,
                        thumbwidth: thumb ? thumb.width : 0,
                        thumbheight: thumb ? thumb.height : 0,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })

                    break
                }

                case global.wechat_bot.Message.Type.Url: {
                    const urlLink = await msg.toUrlLink()

                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text: urlLink.payload.url,
                        type,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })

                    break
                }

                case global.wechat_bot.Message.Type.Image: {
                    const messageImage = await msg.toImage()
                    const artworkImage = await messageImage.artwork()
                    const artworkImageData = await artworkImage.toBuffer()

                    const path = `${config.forum_attachment}/${ctx.helper.today('YYYYMM/DD')}`

                    await fse.mkdirp(`${config.baseDir}${path}`)

                    const filename = `${ctx.helper.today('HHmmss')}${ctx.helper.randomstring(16)}.jpg`
                    const attachment = `${ctx.helper.today('YYYYMM/DD')}/${filename}`

                    await fs.writeFile(`${config.baseDir}${path}/${filename}`, artworkImageData)

                    const dimensions = await sizeof(`${config.baseDir}${path}/${filename}`)
                    const stat = await fs.stat(`${config.baseDir}${path}/${filename}`)

                    let thumb = undefined
                    if (dimensions.width > config.wechat_group.thumbwidth) {
                        thumb = await ctx.helper.resize_spec(artworkImageData, `${config.baseDir}${path}/${filename}.thumb`, [{
                            w: config.wechat_group.thumbwidth,
                            h: null
                        }], 'jpg')
                    }

                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text,
                        type,
                        filename: `${artworkImage.name.substring(0, artworkImage.name.lastIndexOf('.'))}.jpg`,
                        filesize: stat.size,
                        attachment,
                        isimage: true,
                        width: dimensions.width,
                        height: dimensions.height,
                        thumb: dimensions.width > config.wechat_group.thumbwidth,
                        thumbwidth: thumb ? thumb.width : 0,
                        thumbheight: thumb ? thumb.height : 0,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })

                    break
                }

                case global.wechat_bot.Message.Type.Attachment: {
                    const attachFileBox = await msg.toFileBox()
                    const attachData = await attachFileBox.toBuffer()

                    const path = `${config.forum_attachment}/${ctx.helper.today('YYYYMM/DD')}`

                    await fse.mkdirp(`${config.baseDir}${path}`)

                    const filename = `${ctx.helper.today('HHmmss')}${ctx.helper.randomstring(16)}.attach`
                    const attachment = `${ctx.helper.today('YYYYMM/DD')}/${filename}`

                    await fs.writeFile(`${config.baseDir}${path}/${filename}`, attachData)

                    const stat = await fs.stat(`${config.baseDir}${path}/${filename}`)

                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text,
                        type,
                        filename: attachFileBox.name,
                        filesize: stat.size,
                        attachment,
                        isimage: false,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })

                    break
                }

                case global.wechat_bot.Message.Type.Audio: {
                    const audioFileBox = await msg.toFileBox()
                    const audioData = await audioFileBox.toBuffer()

                    const ext = ctx.helper.get_extension(audioFileBox.name)

                    const filepath = `${config.forum_attachment}/${ctx.helper.today('YYYYMM/DD')}`

                    await fse.mkdirp(`${config.baseDir}${filepath}`)

                    let filename = `${ctx.helper.today('HHmmss')}${ctx.helper.randomstring(16)}`
                    const attachment = `${ctx.helper.today('YYYYMM/DD')}/${filename}`

                    await fs.writeFile(`${config.baseDir}${filepath}/${filename}${ext}`, audioData)

                    try {
                        await ctx.helper.shell_exec(`slk2mp3 ${config.baseDir}${filepath}/${filename}${ext} ${config.baseDir}${filepath}/${filename}.mp3`)
                    } catch (e) {
                        await ctx.helper.shell_exec(`ffmpeg -y -f s16be -ac 2 -ar 16000 -acodec pcm_s16le -i ${config.baseDir}${filepath}/${filename}${ext}.pcm ${config.baseDir}${filepath}/${filename}.mp3`)
                        await ctx.helper.shell_exec(`rm -rf ${config.baseDir}${filepath}/${filename}${ext}.pcm`)
                    } finally {
                        await ctx.helper.shell_exec(`mv ${config.baseDir}${filepath}/${filename}.mp3 ${config.baseDir}${filepath}/${filename}.attach`)
                    }

                    const stat = await fs.stat(`${config.baseDir}${filepath}/${filename}.attach`)

                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text,
                        type,
                        filename: audioFileBox.name.replace(ext, '.mp3'),
                        filesize: stat.size,
                        attachment: `${attachment}.attach`,
                        isimage: false,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })

                    break
                }

                case global.wechat_bot.Message.Type.Video: {
                    const videoFileBox = await msg.toFileBox()
                    const videoData = await videoFileBox.toBuffer()

                    const path = `${config.forum_attachment}/${ctx.helper.today('YYYYMM/DD')}`

                    await fse.mkdirp(`${config.baseDir}${path}`)

                    const filename = `${ctx.helper.today('HHmmss')}${ctx.helper.randomstring(16)}.attach`
                    const attachment = `${ctx.helper.today('YYYYMM/DD')}/${filename}`

                    await fs.writeFile(`${config.baseDir}${path}/${filename}`, videoData)

                    const stat = await fs.stat(`${config.baseDir}${path}/${filename}`)

                    wgmm = await ctx.model.WechatGroupMonitorMessage.create({
                        chatroom_id,
                        tid: group.tid,
                        fid: group.fid,
                        wx_id,
                        topic,
                        wx_nickname: wx_name,
                        text,
                        type,
                        filename: videoFileBox.name,
                        filesize: stat.size,
                        attachment,
                        isimage: false,
                        status: group.tid > 0 && group.send_post ? 0 : -1,
                    })

                    break
                }

                case global.wechat_bot.Message.Type.MiniProgram: {
                    const miniProgram = await msg.toMiniProgram()


                    break
                }

                default:
                    break
            }

            await ctx.model.WechatGroupMonitorAccount.update({
                last_message_id: wgmm.id,
            }, {
                where: {
                    chatroom_id,
                    wx_id,
                }
            })

            await ctx.model.WechatGroupMonitor.update({
                last_message_id: wgmm.id,
            }, {
                where: {
                    chatroom_id,
                }
            })
        } catch (err) {
            ctx.logger.error(err.message)
        }
    }

    async on_room_topic(room, new_topic, old_topic, changer, date) {
        const { ctx } = this

        await ctx.model.WechatGroupMonitor.update({
            name: new_topic,
        }, {
            where: {
                chatroom_id: room.id,
            }
        })
    }

    async message() {
        const { ctx, app } = this

        let max_post_id = 0

        const messages = await ctx.model.WechatGroupMonitorMessage.findAll({
            where: {
                status: 0,
            },
            limit: 15,
            order: [
                ['id', 'ASC'],
            ],
            raw: true,
        })

        for (const message of messages) {
            const wg_user = await ctx.model.WechatGroupMonitorUser.findOne({
                where: {
                    wx_id: message.wx_id
                },
                raw: true,
            })

            let success = true
            let post = undefined

            try {
                if (!wg_user) throw new Error('微信账号不存在')

                if (wg_user.forum_uid <= 0) throw new Error('用户没有绑定CD UID')

                switch (message.type) {
                    case global.wechat_bot.Message.Type.Text: {
                        post = await this.handle_message_text(message, wg_user)
                        break
                    }

                    case global.wechat_bot.Message.Type.Emoticon: {
                        post = await this.handle_message_emoticon_and_image_and_attach(message, wg_user)
                        break
                    }

                    case global.wechat_bot.Message.Type.Url: {
                        post = await this.handle_message_url(message, wg_user)
                        break
                    }

                    case global.wechat_bot.Message.Type.Image: {
                        post = await this.handle_message_emoticon_and_image_and_attach(message, wg_user)
                        break
                    }

                    case global.wechat_bot.Message.Type.Attachment: {
                        post = await this.handle_message_emoticon_and_image_and_attach(message, wg_user)
                        break
                    }

                    case global.wechat_bot.Message.Type.Video: {
                        post = await this.handle_message_emoticon_and_image_and_attach(message, wg_user)
                        break
                    }

                    case global.wechat_bot.Message.Type.Audio: {
                        post = await this.handle_message_emoticon_and_image_and_attach(message, wg_user)
                        break
                    }

                    default:
                        break
                }
            } catch (err) {
                await ctx.model.WechatGroupMonitorMessage.update({
                    error_message: err.message
                }, {
                    where: {
                        id: message.id,
                    }
                })
                success = false
            } finally {
                await ctx.model.WechatGroupMonitorMessage.update({
                    pid: post && post.pid ? post.pid : 0,
                    status: success ? 1 : 2
                }, {
                    where: {
                        id: message.id,
                    }
                })

                if (post && post.pid && post.pid > max_post_id) {
                    max_post_id = post.pid
                }
            }
        }

        if (max_post_id > 0) {
            await ctx.forumModel.CommonSyscache.upsert({
                cname: 'max_post_id',
                ctype: 0,
                dateline: ctx.helper.now(),
                data: max_post_id,
            })

            if (config.env === 'prod') {
                await app.memcached.flushAsync()
            }
        }
    }

    async handle_message_text(message, wg_user) {
        const { ctx, app } = this

        await ctx.forumModel.ForumThreadpartake.create({
            tid: message.tid,
            uid: wg_user.forum_uid,
            dateline: ctx.helper.now(),
        })

        const post_table = await ctx.forumModel.ForumPostTableid.create({
            pid: '',
        })

        const position = await ctx.forumModel.ForumPost.findOne({
            attributes: ['position'],
            where: {
                tid: message.tid,
            },
            limit: 1,
            order: [
                ['position', 'DESC'],
            ],
            raw: true,
        })

        const post = await ctx.forumModel.ForumPost.create({
            pid: post_table.pid,
            fid: message.fid,
            tid: message.tid,
            first: 0,
            author: wg_user.forum_username,
            authorid: wg_user.forum_uid,
            dateline: ctx.helper.now(),
            message: message.text,
            useip: '127.0.0.1',
            port: '00000',
            usesig: 1,
            bbcodeoff: -1,
            smileyoff: -1,
            status: 1024,
            position: position.position + 1,
        })

        await ctx.forumModel.ForumThread.update({
            heats: app.Sequelize.literal('heats + 1'),
            replies: app.Sequelize.literal('replies + 1'),
            maxposition: post.position,
            lastposter: wg_user.forum_username,
            lastpost: ctx.helper.now(),
        }, {
            where: {
                tid: message.tid,
            }
        })

        return post
    }

    async handle_message_emoticon_and_image_and_attach(message, wg_user) {
        const { ctx, app } = this

        const attach = await ctx.forumModel.ForumAttachment.create({})

        message.text = `[attach]${attach.aid}[/attach]`

        const post = await this.handle_message_text(message, wg_user)

        const tableid = post.tid.toString().slice(-1)

        await ctx.forumModel.ForumAttachment.update({
            tid: post.tid,
            pid: post.pid,
            uid: post.authorid,
            tableid,
        }, {
            where: {
                aid: attach.aid,
            }
        })

        await ctx.forumModel[`ForumAttachment_${tableid}`].create({
            aid: attach.aid,
            tid: post.tid,
            pid: post.pid,
            uid: post.authorid,
            dateline: ctx.helper.now(),
            filename: message.filename,
            filesize: message.filesize,
            attachment: message.attachment,
            description: '',
            isimage: message.isimage,
            width: message.width,
            height: message.height,
            thumb: message.thumb,
            thumbwidth: message.thumbwidth,
            thumbheight: message.thumbheight,
        })

        const attach_image = await ctx.forumModel[`ForumAttachment_${tableid}`].count({
            where: {
                aid: attach.aid,
                isimage: 1,
            },
            raw: true,
        })

        const attach_all = await ctx.forumModel[`ForumAttachment_${tableid}`].count({
            where: {
                aid: attach.aid,
            },
            raw: true,
        })

        let attach_type = 0
        if (attach_all > 0 && attach_image > 0 && attach_all === attach_image) {
            attach_type = 2
        } else if (attach_all > 0 && attach_image > 0) {
            attach_type = 1
        }

        await ctx.forumModel.ForumThread.update({
            attachment: attach_type,
        }, {
            where: {
                tid: post.tid,
            }
        })

        await ctx.forumModel.ForumPost.update({
            attachment: 2,
        }, {
            where: {
                tid: post.tid,
            }
        })

        return post
    }

    async handle_message_url(message, wg_user) {
        const { ctx, app } = this

        const post = await this.handle_message_text(message, wg_user)

        return post
    }

    async on_room_join(room, invitees, inviter, date) {
        const { ctx } = this

        const group = await ctx.model.WechatGroupMonitor.findOne({
            where: {
                chatroom_id: room.id,
            },
            raw: true,
        })

        if (!group || group.status !== 0) return

        const wgm_user = await ctx.model.WechatGroupMonitorUser.findOne({
            where: {
                wx_id: inviter.id,
            },
            raw: true,
        })

        const opt_wx_id = wgm_user.id

        const arr = Array.isArray(invitees) ? invitees : [invitees]

        for (const invitee of arr) {
            await this.room_in_out_handler({
                chatroom_id: room.id,
                in_out: true,
                opt_wx_id,
                wx_info: invitee,
            })
        }
    }

    async on_room_leave(room, leavers, remover, date) {
        const { ctx } = this

        const group = await ctx.model.WechatGroupMonitor.findOne({
            where: {
                chatroom_id: room.id,
            },
            raw: true,
        })

        if (!group || group.status !== 0) return

        const wgm_user = await ctx.model.WechatGroupMonitorUser.findOne({
            where: {
                wx_id: remover.id,
            },
            raw: true,
        })

        const opt_wx_id = wgm_user.id

        const arr = Array.isArray(leavers) ? leavers : [leavers]

        for (const leaver of arr) {
            await this.room_in_out_handler({
                chatroom_id: room.id,
                in_out: false,
                opt_wx_id,
                wx_info: leaver,
            })
        }
    }

    async room_in_out_handler(obj) {
        const { ctx } = this

        try {
            if (obj.in_out) {
                const wgm_account = await ctx.model.WechatGroupMonitorAccount.findOne({
                    where: {
                        chatroom_id: obj.chatroom_id,
                        wx_id: obj.wx_info.payload.id,
                    },
                    raw: true,
                })

                if (!wgm_account) {
                    await ctx.model.WechatGroupMonitorAccount.create({
                        chatroom_id: obj.chatroom_id,
                        wx_id: obj.wx_info.payload.id,
                        wx: obj.wx_info.payload.weixin,
                        wx_nickname: obj.wx_info.payload.name,
                        join_time: ctx.helper.now(),
                        join_method: obj.opt_wx_id,
                    })
                }

                const wgm_user = await ctx.model.WechatGroupMonitorUser.findOne({
                    where: {
                        wx_id: obj.wx_info.payload.id,
                    },
                    raw: true,
                })

                if (!wgm_user) {
                    await ctx.model.WechatGroupMonitorUser.create({
                        wx_id: obj.wx_info.payload.id,
                        avatar: obj.wx_info.payload.avatar,
                        wx: obj.wx_info.payload.weixin,
                        wx_nickname: obj.wx_info.payload.name,
                        gender: obj.wx_info.payload.gender,
                        area: `${obj.wx_info.payload.province} ${obj.wx_info.payload.city}`,
                        sign: obj.wx_info.payload.sign,
                        created_at: ctx.helper.now(),
                    })
                }
            } else {
                await ctx.model.WechatGroupMonitorAccount.destroy({
                    where: {
                        chatroom_id: obj.chatroom_id,
                        wx_id: obj.wx_info.payload.id,
                    }
                })
            }

            await ctx.model.WechatGroupMonitorLog.create({
                chatroom_id: obj.chatroom_id,
                wx_id: obj.wx_info.payload.id,
                in_out: obj.in_out,
                method: obj.opt_wx_id,
                created_at: ctx.helper.now(),
            })
        } catch (err) {
            ctx.logger.error(err.message)
        }
    }

    async self_group_list() {
        const { ctx } = this

        const rooms = await global.wechat_bot.Room.findAll()
        return rooms
    }

    async get_room_by_id(chatroom_id) {
        const rooms = await this.self_group_list()

        const room = _.filter(rooms, { 'id': chatroom_id })
        return room.shift()
    }

    async wechat_group_monitor_create(payload) {
        const { ctx } = this

        const wgm = await ctx.model.WechatGroupMonitor.findOne({
            where: {
                chatroom_id: payload.id,
            },
            raw: true,
        })

        if (wgm || payload.topic === '') return

        const group = await ctx.model.WechatGroupMonitor.create({
            chatroom_id: payload.id,
            chatroom_name: payload.topic,
            member_count: payload.memberIdList.length,
            admin: payload.adminIdList.length > 0 ? payload.adminIdList.join(',') : '',
            created_at: ctx.helper.now(),
        })

        const room = (await global.wechat_bot.Room.find({ id: payload.id }))
        const members = await room.memberAll()

        for (const member of members) {
            try {
                const wgmu = await ctx.model.WechatGroupMonitorUser.findOne({
                    where: {
                        wx_id: member.payload.id,
                    },
                    raw: true,
                })

                if (!wgmu) {
                    await ctx.model.WechatGroupMonitorUser.create({
                        wx_id: member.payload.id,
                        avatar: member.payload.avatar,
                        wx: member.payload.weixin,
                        wx_nickname: member.payload.name,
                        gender: member.payload.gender,
                        area: `${member.payload.province} ${member.payload.city}`,
                        sign: member.payload.sign,
                        created_at: ctx.helper.now(),
                    })
                }

                const wgma = await ctx.model.WechatGroupMonitorAccount.findOne({
                    where: {
                        chatroom_id: payload.id,
                        wx_id: member.payload.id,
                    },
                    raw: true,
                })

                if (!wgma) {
                    await ctx.model.WechatGroupMonitorAccount.create({
                        chatroom_id: payload.id,
                        wx_id: member.payload.id,
                        wx: member.payload.weixin,
                        wx_nickname: member.payload.name,
                        join_time: ctx.helper.now(),
                        created_at: ctx.helper.now(),
                    })
                }
            } catch (err) {
                ctx.logger.error(err.message)
            }
        }

        return group
    }

    async wechat_group_monitor_update(payload) {
        const { ctx } = this

        const room = (await global.wechat_bot.Room.find({ id: payload.id }))

        await ctx.model.WechatGroupMonitor.update({
            member_count: payload.memberIdList.length,
        }, {
            where: {
                chatroom_id: payload.id,
            }
        })

        const members = await room.memberAll()

        for (const member of members) {
            try {
                const wgmu = await ctx.model.WechatGroupMonitorUser.findOne({
                    where: {
                        wx_id: member.payload.id,
                    },
                    raw: true,
                })

                if (!wgmu) {
                    await ctx.model.WechatGroupMonitorUser.create({
                        wx_id: member.payload.id,
                        avatar: member.payload.avatar,
                        wx: member.payload.weixin,
                        wx_nickname: member.payload.name,
                        gender: member.payload.gender,
                        area: `${member.payload.province} ${member.payload.city}`,
                        sign: member.payload.sign,
                        created_at: ctx.helper.now(),
                    })
                } else if (wgmu.wx !== member.payload.weixin || wgmu.wx_nickname !== member.payload.name) {
                    await ctx.model.WechatGroupMonitorUser.update({
                        wx: member.payload.weixin,
                        wx_nickname: member.payload.name,
                    }, {
                        where: {
                            wx_id: member.payload.id,
                        }
                    })
                }

                const wgma = await ctx.model.WechatGroupMonitorAccount.findOne({
                    where: {
                        chatroom_id: payload.id,
                        wx_id: member.payload.id,
                    },
                    raw: true,
                })

                if (!wgma) {
                    await ctx.model.WechatGroupMonitorAccount.create({
                        chatroom_id: payload.id,
                        wx_id: member.payload.id,
                        wx: member.payload.weixin,
                        wx_nickname: member.payload.name,
                        join_time: ctx.helper.now(),
                        created_at: ctx.helper.now(),
                    })
                } else if (wgma.wx !== member.payload.weixin || wgma.wx_nickname !== member.payload.name) {
                    await ctx.model.WechatGroupMonitorAccount.update({
                        wx: member.payload.weixin,
                        wx_nickname: member.payload.name,
                    }, {
                        where: {
                            wx_id: member.payload.id,
                        }
                    })
                }
            } catch (err) {
                ctx.logger.error(err.message)
            }
        }
    }

    async update_group_monitor() {
        const { ctx } = this

        const rooms = await this.self_group_list()
        for (const room of rooms) {
            let group = await ctx.model.WechatGroupMonitor.findOne({
                where: {
                    chatroom_id: room.id,
                },
                raw: true,
            })
            if (!group) {
                await this.wechat_group_monitor_create(room.payload)
            } else {
                await this.wechat_group_monitor_update(room.payload)
            }
        }
    }
}

module.exports = WechatGroupService
