const html_entities = require('html-entities')

module.exports = app => {
    class Www extends app.Service {
        async publish_to_forum() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            const user = await ctx.model.EventUser.findOne({
                where: {
                    id: body.uid,
                },
                raw: true,
            })

            body.username = user.username
            body.password = ctx.helper.decode_dz_string(user.password)

            const result = await ctx.service.thread.send(body)

            await ctx.model.Event.update({
                forum_url: result.Variables.tid,
                forum_url_fid: body.fid,
                forum_url_pid: result.Variables.pid,
                forum_url_typeid: body.typeid,
                forum_url_senduser: user.id,
            }, {
                where: {
                    id: body.event_id
                }
            })

            return result.Variables.tid
        }

        async publish_to_www() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            const user = await ctx.model.EventUser.findOne({
                where: {
                    id: body.uid,
                },
                raw: true,
            })

            body.classid = 11
            body.memberid = 4
            body.author = 'ChaseDream'
            body.source = 'ChaseDream论坛'
            body.sourceUrl = 'https://forum.chasedream.com'
            body.title = html_entities.encode(body.title)
            body.allowRemark = false

            const event = await ctx.wwwModel.IwmsNews.create(body)

            const article_num = await ctx.wwwModel.IwmsNews.count({
                where: {
                    classid: event.classid
                }
            })

            await ctx.wwwModel.IwmsClass.update({
                articleNum: article_num,
            }, {
                where: {
                    classID: event.classid
                }
            })

            await ctx.model.Event.update({
                www_url: event.articleid,
                www_url_senduser: user.id,
            }, {
                where: {
                    id: body.event_id
                }
            })

            await this.clear_cache()

            return event
        }

        async clear_cache() {
            try {
                const { ctx } = this

                const cookie = await this.login({
                    Username: 'Danny',
                    Password: '6RW3ME]yp@vi',
                })

                const url = 'https://www.chasedream.com/admin_maintain.aspx'
                let data = {
                    ClearCache: true
                }

                const res = await ctx.curl(url, {
                    method: 'GET',
                    headers: {
                        'Cookie': cookie,
                    },
                    timeout: 10000,
                })

                const body = res.data.toString()

                let regex = /<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)" \/>/g
                let matched = regex.exec(body)
                data['__VIEWSTATE'] = matched[1]

                regex = /<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="(.*?)" \/>/g
                matched = regex.exec(body)
                data['__EVENTVALIDATION'] = matched[1]

                await ctx.curl(url, {
                    method: 'POST',
                    contentType: 'application/x-www-form-urlencoded',
                    data: data,
                    headers: {
                        'Cookie': cookie,
                    },
                    timeout: 10000,
                })
            } catch (err) {
                ctx.logger.error(err.message)
            }
        }

        async login(user) {
            const { ctx } = this

            const login_url = 'https://www.chasedream.com/Sesame-In.aspx'

            const res = await ctx.curl(login_url, {
                method: 'GET',
                timeout: 10000,
            })

            const body = res.data.toString()

            let regex = /<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)" \/>/g
            let matched = regex.exec(body)

            let form = {
                '__VIEWSTATE': matched[1],
                Submit: true,
            }

            regex = /<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="(.*?)" \/>/g
            matched = regex.exec(body)

            form['__EVENTVALIDATION'] = matched[1]

            const data = Object.assign(form, user)

            const result = await ctx.curl(login_url, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: data,
                timeout: 10000,
            })

            const headers = result.headers
            let iwmsPass = headers['set-cookie'] && headers['set-cookie'][1] || ''

            iwmsPass = iwmsPass.substring(iwmsPass.indexOf('=') + 1, iwmsPass.indexOf(';'))

            return `iwmsUser=${user.Username};iwmsPass=${iwmsPass};iwmsAdmin=y;`
        }
    }

    return Www
}
