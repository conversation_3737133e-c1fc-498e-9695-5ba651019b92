require('moment-timezone')
const _ = require('lodash')
const querystring = require('querystring')
const md5 = require('md5')
const moment = require('moment')
const Core = require('@alicloud/pop-core')
const twilio = require('twilio')
const validator = require('validator')
const { code, areaCode } = require('../tools/area_code')
const await = require('await-stream-ready/lib/await')
const Service = require('egg').Service

class AuthService extends Service {
    async check_mobile(area_code, mobile) {
        const { ctx, config } = this

        const count = await ctx.model.Mobile.count({
            where: {
                area_code,
                mobile_encrypt: ctx.helper.AESEncrypt(mobile)
            }
        })

        return count > 0
    }

    async send_sms(area_code, mobile, type) {
        const { ctx, config } = this

        const today = moment().tz('Asia/Shanghai').format('YYYY-MM-DD').toString()
        const timestamp = ctx.helper.now()

        const sms = await ctx.model.SmsLog.findAll({
            where: {
                area_code,
                mobile: ctx.helper.AESEncrypt(mobile),
                created_at: today
            },
            raw: true
        }) || []

        const last = _.last(sms)

        ctx.error(sms.length > 0 && timestamp - last.timestamp < 60, "每分钟只能发送1次验证码")

        if (sms.length >= config.sms.limit.hour) {
            const filted = _.filter(sms, s => {
                return timestamp - s.timestamp < 60 * 60
            })
            ctx.error(filted.length >= config.sms.limit.hour, "每小时只能发送3次验证码")
        }

        if (sms.length >= config.sms.limit.day) {
            const filted = _.filter(sms, s => {
                return timestamp - s.timestamp < 60 * 60 * 24
            })
            ctx.error(filted.length >= config.sms.limit.day, "当前手机号码发送次数达到上限，请明天再试")
        }

        let captcha = ctx.helper.get_random_num(4)
        if (captcha.startsWith('0')) captcha = captcha.replace('0', '1')

        const sms_config = await ctx.model.Isp.findOne({})

        let flag = true
        let model = {
            area_code,
            mobile: ctx.helper.AESEncrypt(mobile),
            captcha,
            success: 1,
            ip: ctx.ip,
            token: ctx.get('user-agent') || '',
            timestamp,
            type
        }

        try {
            let res = null

            if (area_code === 86) {
                model.platform = sms_config.local

                switch (sms_config.local) {
                    case 'aliyun':
                        res = await this.send_to_aliyun(mobile, captcha, config.sms.aliyun.template_code[type]['local'], area_code)
                        break
                    case 'send_cloud':
                        res = await this.send_to_sendcloud(mobile, captcha, config.sms.send_cloud.template_code[type]['local'], 0)
                        break
                    case 'azure':
                        res = await this.send_to_azure(mobile, captcha, config.sms.azure.template_code[type]['local'])
                        break
                }
            } else {
                model.platform = sms_config.oversea

                switch (sms_config.oversea) {
                    case 'aliyun':
                        res = await this.send_to_aliyun(`${area_code}${mobile}`, captcha, config.sms.aliyun.template_code[type]['oversea'], area_code, config.sms.aliyun.template_code[type]['oversea_template'])
                        break
                    case 'send_cloud':
                        res = await this.send_to_sendcloud(`00${area_code}${mobile}`, captcha, config.sms.send_cloud.template_code[type]['oversea'], 2)
                        break
                    case 'twilio':
                        res = await this.send_to_twilio(`+${area_code}${mobile}`, captcha, config.sms.twilio.template_code[type]['oversea'])
                        break
                }
            }

            model.platform_response = JSON.stringify(res)
        } catch (e) {
            flag = false
            model.platform_response = JSON.stringify(e)
            model.success = 0
        }

        await ctx.model.SmsLog.create(model)

        return {
            success: flag,
            message: flag ? "验证码已发送" : "系统异常"
        }
    }

    send_to_aliyun(mobile, captcha, template_code, area_code, oversea_template = '') {
        return new Promise((resolve, reject) => {
            const { config } = this
            let params = {}
            const global_areacode = [1, 64, 62, 84, 886]
            const in_global = global_areacode.includes(area_code)

            const client = new Core({
                accessKeyId: config.sms.aliyun.access_key_id,
                accessKeySecret: config.sms.aliyun.access_key_secret,
                endpoint: 'https://dysmsapi.aliyuncs.com',
                apiVersion: '2017-05-25'
            })

            if (in_global) {
                const message = oversea_template.replace('${code}', captcha)
                params = {
                    "RegionId": "cn-hangzhou",
                    "To": mobile,
                    "From": area_code == 1 ? 18556941767 : "Alirich",
                    "Message": message,
                    "Type": "OTP"
                }
            } else {
                params = {
                    "RegionId": "cn-hangzhou",
                    "PhoneNumbers": mobile,
                    "SignName": "北京润志咨询",
                    "TemplateCode": template_code,
                    "TemplateParam": `{"code":"${captcha}"}`
                }
            }

            const requestOption = {
                method: 'POST'
            }

            client.request(in_global ? 'SendMessageToGlobe' : 'SendSms', params, requestOption).then((result) => {
                resolve(result)
            }, (ex) => {
                reject(ex)
            })
        })
    }

    async send_to_sendcloud(mobile, captcha, template_code, msg_type) {
        const { ctx, config } = this

        let param = {
            'msgType': msg_type,
            'smsUser': 'ChaseDream',
            'templateId': template_code,
            'phone': mobile,
            'vars': `{%code%:${captcha}}`
        }

        let sorted_param = ctx.helper.sort_dict(param)

        let param_str = ""
        for (let key in sorted_param)
            param_str += (key + '=' + sorted_param[key] + '&')

        param_str = config.sms.send_cloud.sms_key + '&' + param_str + config.sms.send_cloud.sms_key
        let sign = md5(param_str)
        param['signature'] = sign.toUpperCase()

        const data = querystring.stringify(param)

        const result = await ctx.curl(`http://www.sendcloud.net/smsapi/send?${data}`, {
            method: 'POST',
            dataType: 'json',
        })

        return result.data
    }

    async send_to_twilio(mobile, captcha, template_code) {
        const { config } = this

        const client = twilio(config.sms.twilio.sid, config.sms.twilio.token)

        const data = await client.messages.create({
            body: template_code.replace('@@captcha', captcha),
            from: config.sms.twilio.from,
            to: mobile
        })

        return data
    }

    async send_to_azure(mobile, captcha, template_code) {
        const { ctx, config } = this

        const content = `se=${ctx.helper.now() + 60 * 30}&skn=${config.sms.azure.key_name}`
        const authorization = ctx.helper.azure_sms_sign(content, config.sms.azure.key)

        const result = await ctx.curl('https://cef.chinacloudapi.cn/services/sms/messages?api-version=2018-10-01', {
            method: 'POST',
            dataType: 'json',
            headers: {
                'Content-Type': 'application/json',
                'Account': config.sms.azure.account,
                'Authorization': authorization,
            },
            data: {
                "phoneNumber": [
                    `+86${mobile}`
                ],
                "extend": "",
                "messageBody": {
                    "templateName": template_code,
                    "templateParam": {
                        "otpcode": captcha
                    }
                }
            }
        })

        return result.data
    }

    async recovery_password(password, userinfo, keep_cookie) {
        const { ctx, app, config } = this
        let ret = {
            success: false
        }

        ctx.error(!ctx.session.recovery_password, "非法请求")

        const owner = await ctx.model.Mobile.findOne({
            where: {
                uid: userinfo[2]
            }
        }) || {}

        ctx.error(owner.area_code != ctx.session.recovery_password.area_code || owner.mobile_encrypt != ctx.helper.AESEncrypt(ctx.session.recovery_password.mobile), "手机号码不符")

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                area_code: ctx.session.recovery_password.area_code,
                mobile_encrypt: ctx.helper.AESEncrypt(ctx.session.recovery_password.mobile),
            }
        }) || {}

        const member = await ctx.model.Member.findOne({
            where: {
                id: mobile.uid
            }
        })

        if (member) {
            const salt = ctx.helper.get_random_num(6)

            password = md5(md5(password) + salt)
            const common_pwd = md5(ctx.helper.get_random_num(10))

            await ctx.forumModel.UcenterMember.update({
                password,
                salt,
            }, {
                where: {
                    uid: member.forum_uid
                }
            })

            await ctx.forumModel.CommonMember.update({
                password: common_pwd,
            }, {
                where: {
                    uid: member.forum_uid
                }
            })

            await ctx.model.Member.update({
                password,
                salt,
            }, {
                where: {
                    id: member.id
                },
                individualHooks: true
            })

            if (!keep_cookie) ctx.helper.logout_forum()

            const key = `JjgsH6_common_member_${member.forum_uid}`
            await app.memcached.delAsync(key)

            ret.success = true
        }

        return ret
    }

    async verify_register(area_code, mobile, captcha, username, password, email = "", openid = "", unionid = "", apple_token = "", apple_user = "", apple_email = "", nickname = "", platform = "") {
        const { ctx, app, config } = this

        if (platform != '9') {
            const sms = await this.verify_sms(area_code, mobile, captcha)

            await ctx.model.SmsLog.update({
                used: 1
            }, {
                where: { id: sms.id }
            })
        }

        let is_exist = await this.check_mobile(area_code, mobile)
        ctx.error(is_exist, "手机号已存在")

        const salt = ctx.helper.get_random_num(6)

        password = md5(md5(password) + salt)
        const common_pwd = md5(ctx.helper.get_random_num(10))
        const fake_email = `${mobile}@chasedream.mail`
        email = email.length !== 0 ? email : fake_email
        const regdate = ctx.helper.now()

        let tran = await ctx.model.transaction()

        try {
            const uc_member = await ctx.forumModel.UcenterMember.create({
                username,
                password,
                email,
                regip: ctx.ip,
                regdate,
                salt,
            })

            if (email === fake_email) {
                await ctx.forumModel.UcenterMember.update({
                    email: `${uc_member.uid}@chasedream.mail`
                }, {
                    where: {
                        uid: uc_member.uid
                    }
                })
            }

            await ctx.forumModel.CommonMember.upsert({
                uid: uc_member.uid,
                username,
                password: common_pwd,
                email: email !== fake_email ? email : `${uc_member.uid}@chasedream.mail`,
                groupid: '10',
                regdate,
                videophotostatus: 1,
                emailstatus: 1,
                timeoffset: '9999',
            })

            await ctx.forumModel.CommonMemberStatus.upsert({
                uid: uc_member.uid,
                regip: ctx.ip,
                lastip: ctx.ip,
                lastvisit: regdate,
                lastactivity: regdate,
            })

            await ctx.forumModel.CommonMemberCount.upsert({
                uid: uc_member.uid,
            })

            await ctx.forumModel.CommonMemberProfile.upsert({
                uid: uc_member.uid,
                field1: ctx.get('user-agent') || ''
            })

            await ctx.forumModel.CommonMemberFieldForum.upsert({
                uid: uc_member.uid,
            })

            await ctx.forumModel.CommonMemberFieldHome.upsert({
                uid: uc_member.uid,
            })

            const member = await ctx.model.Member.create({
                forum_uid: uc_member.uid,
                username,
                password,
                salt,
                regip: ctx.ip,
                regdate,
                create_at: ctx.helper.now(),
            }, {
                transaction: tran
            })

            await ctx.model.Mobile.create({
                uid: member.id,
                area_code,
                mobile: 0,
                mobile_encrypt: ctx.helper.AESEncrypt(mobile.toString()),
                mobile_mask: ctx.helper.mask_mobile(mobile.toString())
            }, {
                transaction: tran
            })

            if (openid) {
                await ctx.model.Wechat.upsert({
                    uid: member.id,
                    openid,
                    nickname,
                    unionid,
                }, {
                    transaction: tran
                })
            }

            await tran.commit()

            if (apple_token) {
                try {
                    await ctx.service.apple.register(member.id, apple_token, apple_user, apple_email)
                } catch (err) {
                    ctx.logger.error(err.message)
                }
            }

            try {
                const useragent = ctx.get('user-agent') || ''

                await ctx.model.MobileLog.create({
                    uid: member.id,
                    area_code,
                    mobile: ctx.helper.AESEncrypt(mobile),
                    ip: ctx.ip,
                    type: 'register',
                    useragent,
                })

                await this.create_userlogin_log({
                    uid: uc_member.uid,
                    email,
                    username,
                    dateline: ctx.helper.now(),
                    ip: ctx.ip,
                    loginsuccess: 1,
                    browserinfo: ctx.get('user-agent') || '',
                    loginmethod: `${(platform || 0)}5`,
                    referer: ctx.get('referer') || '',
                })
            } catch (err) {
                ctx.logger.error(err.message)
            }

            if (config.env === 'prod') {
                await app.memcached.flushAsync()
            }

            return ctx.helper.encode_dz_authkey(`${common_pwd}\t${uc_member.uid}\t${member.id}`, member.salt)
        } catch (err) {
            await tran.rollback()

            ctx.logger.error(err)
        }
    }

    async verify_login(area_code, mobile_email, password, auto_login, openid, unionid, apple_token, apple_user, apple_email, nickname, platform) {
        const { ctx, config } = this

        if (validator.isEmail(mobile_email)) {
            const member = await ctx.forumModel.UcenterMember.findOne({
                where: {
                    email: mobile_email
                }
            })

            if (!member || member.password !== md5(`${md5(password)}${member.salt}`)) {
                await this.create_userlogin_log({
                    uid: member && member.uid || 0,
                    email: member && member.email || encodeURIComponent(mobile_email),
                    username: member && member.username || '',
                    dateline: ctx.helper.now(),
                    ip: ctx.ip,
                    loginsuccess: 0,
                    browserinfo: ctx.get('user-agent') || '',
                    loginmethod: `${(platform || 0)}3`,
                    referer: ctx.get('referer') || '',
                })

                ctx.error(true, "用户名或密码错误")
            }

            await this.move_to_master(member.uid)

            const common_member = await ctx.forumModel.CommonMember.findOne({
                where: {
                    uid: member.uid
                }
            })

            ctx.error(common_member.groupid == 5 || common_member.status < 0, "您的账号被禁用")

            const member_model = await ctx.model.Member.findOne({
                where: {
                    forum_uid: member.uid,
                }
            }) || {}

            if (openid && member_model.id) {
                await ctx.model.Wechat.upsert({
                    uid: member_model.id,
                    openid,
                    nickname,
                    unionid,
                })
            }

            if (apple_token && member_model.id) {
                await ctx.service.apple.register(member_model.id, apple_token, apple_user, apple_email)
            }

            const cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member_model.id ? member_model.id : 0}`, member.salt, true, auto_login)

            await this.create_userlogin_log({
                uid: common_member.uid,
                email: common_member.email,
                username: common_member.username,
                dateline: ctx.helper.now(),
                ip: ctx.ip,
                loginsuccess: 1,
                browserinfo: ctx.get('user-agent') || '',
                loginmethod: `${(platform || 0)}3`,
                referer: ctx.get('referer') || '',
            })

            return Object.assign({
                cookiepre: config.discuz.prefix,
                uid: common_member.uid,
                member_uid: common_member.uid,
                member_username: common_member.username,
                member_email: common_member.email,
                member_avatar: `${config.discuz.home_url}/uc_server/avatar.php?uid=${common_member.uid}&size=small`,
                groupid: common_member.groupid,
                uploadhash: md5(md5(config.discuz.authkey).substring(8) + common_member.uid),
            }, cookie)
        } else {
            area_code = parseInt(area_code, 10)
            ctx.error(!code.includes(area_code), "区号目前不支持")

            mobile_email = mobile_email.substring(0, 15)

            const mobile = await ctx.model.Mobile.findOne({
                where: {
                    area_code,
                    mobile_encrypt: ctx.helper.AESEncrypt(mobile_email),
                }
            })

            ctx.error(!mobile, "手机号或密码错误")

            const member = await ctx.model.Member.findOne({
                where: {
                    id: mobile.uid
                }
            })

            const uc_member = await ctx.forumModel.UcenterMember.findOne({
                where: {
                    uid: member.forum_uid
                }
            })

            if (!uc_member || uc_member.password !== md5(`${md5(password)}${uc_member.salt}`)) {
                await this.create_userlogin_log({
                    uid: uc_member.uid || 0,
                    email: uc_member.email || mobile_email,
                    username: uc_member.username || '',
                    dateline: ctx.helper.now(),
                    ip: ctx.ip,
                    loginsuccess: 0,
                    browserinfo: ctx.get('user-agent') || '',
                    loginmethod: `${(platform || 0)}2`,
                    referer: ctx.get('referer') || '',
                })

                ctx.error(true, "手机号或密码错误")
            }

            await this.move_to_master(member.forum_uid)

            const common_member = await ctx.forumModel.CommonMember.findOne({
                where: {
                    uid: member.forum_uid
                }
            })

            ctx.error(common_member.groupid == 5 || common_member.status < 0, "您的账号被禁用")

            if (openid) {
                await ctx.model.Wechat.upsert({
                    uid: member.id,
                    nickname,
                    openid,
                    unionid
                })
            }

            if (apple_token) {
                await ctx.service.apple.register(member.id, apple_token, apple_user, apple_email)
            }

            const cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt)

            await this.create_userlogin_log({
                uid: common_member.uid,
                email: common_member.email,
                username: common_member.username,
                dateline: ctx.helper.now(),
                ip: ctx.ip,
                loginsuccess: 1,
                browserinfo: ctx.get('user-agent') || '',
                loginmethod: `${(platform || 0)}2`,
                referer: ctx.get('referer') || '',
            })

            return Object.assign({
                cookiepre: config.discuz.prefix,
                uid: common_member.uid,
                member_uid: common_member.uid,
                member_username: common_member.username,
                member_email: common_member.email,
                member_avatar: `${config.discuz.home_url}/uc_server/avatar.php?uid=${common_member.uid}&size=small`,
                groupid: common_member.groupid,
                uploadhash: md5(md5(config.discuz.authkey).substring(8) + common_member.uid),
            }, cookie)
        }
    }

    async create_userlogin_log(model) {
        const { ctx, config } = this

        try {
            const issteal = await ctx.forumModel.CommonStealhandleLog.count({
                where: {
                    uid: model.uid
                },
                raw: true,
            })

            model.issteal = issteal > 0

            const um = await ctx.forumModel.UcenterMember.findOne({
                where: {
                    uid: model.uid,
                }
            })

            if (um) {
                model.regip = um.regip || ''
                model.regdateline = um.regdate || 0
            }

            const member = await ctx.model.Member.findOne({
                where: {
                    forum_uid: model.uid,
                }
            })

            if (member) {
                const mobile = await ctx.model.Mobile.findOne({
                    where: {
                        uid: member.id
                    }
                })

                if (mobile) {
                    const item = areaCode.find(el => el.area_code == mobile.area_code)

                    model.areacode = `+${mobile.area_code} ${item.name}`
                    model.mobile = ctx.helper.AESEncrypt(mobile.mobile || '')
                }
            }

            await ctx.forumModel.CommonUserloginLog.create(model)
        } catch (err) {
            ctx.logger.error(err.message)
        }
    }

    async move_to_master(uid) {
        const { ctx } = this

        if (!uid) return false

        let archive = await ctx.forumModel.CommonMemberArchive.findOne({
            where: {
                uid
            },
            raw: true,
        })

        if (!archive) return false
        const member = await ctx.forumModel.CommonMember.create(archive)

        archive = await ctx.forumModel.CommonMemberCountArchive.findOne({
            where: {
                uid
            },
            raw: true,
        })
        await ctx.forumModel.CommonMemberCount.create(archive)

        archive = await ctx.forumModel.CommonMemberStatusArchive.findOne({
            where: {
                uid
            },
            raw: true,
        })
        await ctx.forumModel.CommonMemberStatus.create(archive)

        archive = await ctx.forumModel.CommonMemberProfileArchive.findOne({
            where: {
                uid
            },
            raw: true,
        })
        await ctx.forumModel.CommonMemberProfile.create(archive)

        archive = await ctx.forumModel.CommonMemberFieldHomeArchive.findOne({
            where: {
                uid
            },
            raw: true,
        })
        await ctx.forumModel.CommonMemberFieldHome.create(archive)

        archive = await ctx.forumModel.CommonMemberFieldForumArchive.findOne({
            where: {
                uid
            },
            raw: true,
        })
        await ctx.forumModel.CommonMemberFieldForum.create(archive)

        await ctx.forumModel.CommonMemberArchive.destroy({
            where: {
                uid
            }
        })
        await ctx.forumModel.CommonMemberCountArchive.destroy({
            where: {
                uid
            }
        })
        await ctx.forumModel.CommonMemberStatusArchive.destroy({
            where: {
                uid
            }
        })
        await ctx.forumModel.CommonMemberProfileArchive.destroy({
            where: {
                uid
            }
        })
        await ctx.forumModel.CommonMemberFieldHomeArchive.destroy({
            where: {
                uid
            }
        })
        await ctx.forumModel.CommonMemberFieldForumArchive.destroy({
            where: {
                uid
            }
        })

        return member
    }

    async verify_sms(area_code, mobile, captcha) {
        const { ctx } = this

        const log = await await ctx.model.CaptchaCheckLog.findOne({
            where: {
                area_code,
                mobile: ctx.helper.AESEncrypt(mobile),
            }
        }) || {}

        await this.show_captcha_error(log)

        const now = ctx.helper.now()

        const sms = await ctx.model.SmsLog.findOne({
            where: {
                area_code,
                mobile: ctx.helper.AESEncrypt(mobile),
                captcha,
                used: 0
            },
            raw: true,
            order: [
                ['id', 'DESC']
            ]
        })

        if (!sms) {
            await this.captcha_check_log(area_code, mobile)
            await this.show_captcha_error(log, true)
        }

        if (now - sms.timestamp > 60 * 30) {
            await this.captcha_check_log(area_code, mobile)
            ctx.error(true, "验证码已失效")
        }

        return sms
    }

    async show_captcha_error(log, force = false) {
        const { ctx } = this

        if (log.count > 4 && ctx.helper.now() - log.dateline < 60 * 60 * 24) {
            ctx.error(true, "验证码错误，1天后再试")
        } else if (log.count === 4 && ctx.helper.now() - log.dateline < 60 * 20) {
            ctx.error(true, "验证码错误，20分钟后再试")
        } else if (log.count === 3 && ctx.helper.now() - log.dateline < 60 * 5) {
            ctx.error(true, "验证码错误，5分钟后再试")
        } else if (force) {
            ctx.error(true, "验证码错误")
        }
    }

    async captcha_check_log(area_code, mobile) {
        const { ctx } = this

        const log = await ctx.model.CaptchaCheckLog.findOne({
            where: {
                area_code,
                mobile: ctx.helper.AESEncrypt(mobile),
            }
        })

        if (log) {
            await ctx.model.CaptchaCheckLog.update({
                area_code,
                mobile: ctx.helper.AESEncrypt(mobile),
                count: ++log.count,
                dateline: ctx.helper.now()
            }, {
                where: {
                    area_code,
                    mobile,
                }
            })
        } else {
            await ctx.model.CaptchaCheckLog.create({
                area_code,
                mobile: ctx.helper.AESEncrypt(mobile),
                count: 1,
                dateline: ctx.helper.now()
            })
        }
    }

    async get_mobile_info(userinfo) {
        const { ctx } = this

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                uid: userinfo[2]
            }
        })

        mobile.mobile = mobile.mobile_mask

        return mobile
    }

    async change_mobile1(password, userinfo) {
        const { ctx } = this

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                uid: userinfo.uid
            }
        })

        ctx.error(!member, "用户不存在")
        ctx.error(member.password !== md5(md5(password) + member.salt), "密码错误")

        ctx.session.change_mobile = userinfo

        return {
            success: true
        }
    }

    async change_mobile2(area_code, mobile) {
        const { ctx } = this

        await ctx.model.Mobile.update({
            area_code,
            mobile_encrypt: ctx.helper.AESEncrypt(mobile.toString()),
            mobile_mask: ctx.helper.mask_mobile(mobile.toString())
        }, {
            where: {
                uid: ctx.session.change_mobile.id
            },
            individualHooks: true
        })

        return {
            success: true
        }
    }

    async binding_mobile_change(area_code, mobile, userinfo, keep_cookie) {
        const { ctx, app, config } = this

        const uc_member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                uid: userinfo[1]
            }
        })

        const common_member = await ctx.forumModel.CommonMember.findOne({
            where: {
                uid: userinfo[1]
            }
        })

        ctx.error(!uc_member, '论坛用户状态异常')

        let tran = await ctx.model.transaction()

        try {
            let cm_model = {
                videophotostatus: 1,
            }
            if (!common_member.emailstatus) {
                cm_model.emailstatus = 1
            }
            if (common_member.groupid == 8) {
                cm_model.groupid = 10
            }
            await ctx.forumModel.CommonMember.update(cm_model, {
                where: {
                    uid: uc_member.uid,
                }
            })

            let m_model = {
                forum_uid: uc_member.uid,
                username: uc_member.username,
                email: uc_member.email,
                password: uc_member.password,
                salt: uc_member.salt,
                regip: uc_member.regip,
                regdate: uc_member.regdate,
                create_at: ctx.helper.now(),
            }
            const member = await ctx.model.Member.create(m_model, {
                transaction: tran
            })

            await ctx.model.Mobile.create({
                uid: member.id,
                area_code,
                mobile: 0,
                mobile_encrypt: ctx.helper.AESEncrypt(mobile.toString()),
                mobile_mask: ctx.helper.mask_mobile(mobile.toString())
            }, {
                transaction: tran
            })

            try {
                const item = areaCode.find(el => el.area_code == area_code)

                await ctx.forumModel.CommonUserloginLog.update({
                    areacode: `+${area_code} ${item.name}`,
                    mobile: mobile,
                }, {
                    where: {
                        uid: common_member.uid
                    }
                })

                const useragent = ctx.get('user-agent') || ''

                await ctx.model.MobileLog.create({
                    uid: member.id,
                    area_code,
                    mobile: ctx.helper.AESEncrypt(mobile),
                    ip: ctx.ip,
                    type: 'binding_mobile_change',
                    useragent,
                })
            } catch (err) {
                ctx.logger.error(err.message)
            }

            await tran.commit()

            if (!keep_cookie) ctx.helper.logout_forum()

            try {
                await app.memcached.flushAsync()
            } catch (err) {
                ctx.logger.error(err.message)
            }

            const cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt)
            return cookie

        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async is_blocked(area_code, mobile) {
        const { ctx } = this

        const block = await ctx.model.Blacklist.findOne({
            area_code,
            mobile
        })

        return block != null
    }

    async username_exist(username) {
        const { ctx } = this

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username
            }
        })

        return member !== null
    }

    async email_exist(email) {
        const { ctx } = this

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                email
            }
        })

        return member !== null
    }
}

module.exports = AuthService
