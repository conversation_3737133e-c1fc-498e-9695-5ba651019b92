const _ = require('lodash')

module.exports = app => {
    class Thread extends app.Service {
        async schedule_send_thread() {
            const { ctx, app } = this
            const Op = app.Sequelize.Op

            const now = ctx.helper.today('YYYYMMDD HHmm')

            const threads = await ctx.model.Thread.findAll({
                where: {
                    status: 0,
                    schedule: {
                        [Op.lte]: now
                    }
                },
                raw: true,
            })

            for (let thread of threads) {
                let body = {
                    status: 1,
                    fid: thread.fid,
                    subject: thread.subject,
                    content: thread.content,
                    typeid: thread.typeid,
                    htmlon: thread.htmlon
                }

                const user = await ctx.model.ThreadSendUser.findOne({
                    where: {
                        id: thread.uid,
                    },
                    raw: true,
                })

                body.username = user.username
                body.password = ctx.helper.decode_dz_string(user.password)
                body.send_username = user.nickname

                const res = await this.send(body)
                body.tid = res.Variables.tid
                body.pid = res.Variables.pid

                await ctx.model.Thread.update(body, {
                    where: { id: thread.id }
                })
            }
        }

        async top_post() {
            const { ctx } = this
            const Op = app.Sequelize.Op

            const threads = await ctx.model.TopPostThread.findAll({
                where: {
                    next_tick: {
                        [Op.lt]: ctx.helper.now()
                    }
                }
            })

            if (threads.length === 0) return

            const now = ctx.helper.now()
            const users = await ctx.model.TopPostUser.findAll({
                where: {}
            })

            const messages = await ctx.model.TopPostMessage.findAll({
                where: {}
            })

            for (const thread of threads) {
                try {
                    let body = {}
                    const user = _.nth(users, _.random(users.length - 1))
                    const message = _.nth(messages, _.random(messages.length - 1))

                    const res = await this.login({
                        username: user.username,
                        password: ctx.helper.decode_dz_string(user.password)
                    })

                    ctx.error(res.Message.messageval !== 'login_succeed', res.Message)

                    const variables = res.Variables
                    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`

                    body.formhash = variables.formhash
                    body.tid = thread.tid
                    body.message = message.message

                    await this.send_post(cookies, body)
                    const min = now + thread.min * 60
                    const max = now + thread.max * 60

                    await ctx.model.TopPostThread.update({
                        next_tick: _.random(min, max),
                        total: ++thread.total,
                    }, {
                        where: {
                            id: thread.id
                        }
                    })
                } catch (err) {
                    ctx.logger.error(err)
                }
            }
        }

        async stick(body) {
            const { ctx } = this

            const res = await this.login(body)

            ctx.error(res.Message.messageval !== 'login_succeed', res.Message)

            const variables = res.Variables
            const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`
            body.formhash = variables.formhash

            const result = await this.stick_thread(cookies, body)
            return result
        }

        async highlight_digest(body) {
            const { ctx } = this

            const res = await this.login(body)

            ctx.error(res.Message.messageval !== 'login_succeed', res.Message)

            const variables = res.Variables
            const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`
            body.formhash = variables.formhash

            const result = await this.highlight_digest_thread(cookies, body)
            return result
        }

        async update(body) {
            const { ctx } = this

            const res = await this.login(body)

            ctx.error(res.Message.messageval !== 'login_succeed', res.Message)

            const variables = res.Variables
            const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`
            body.formhash = variables.formhash

            const result = await this.update_thread(cookies, body)
            return result
        }

        async delete(body) {
            const { ctx } = this

            const res = await this.login(body)

            ctx.error(res.Message.messageval !== 'login_succeed', res.Message)

            const variables = res.Variables
            const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`
            body.formhash = variables.formhash

            const result = await this.delete_thread(cookies, body)
            return result
        }

        async send(body) {
            const { ctx } = this

            const res = await this.login(body)

            ctx.error(res.Message.messageval !== 'login_succeed', res.Message.messagestr)

            const variables = res.Variables
            const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`
            body.formhash = variables.formhash

            const result = await this.send_thread(cookies, body)

            const chasedream = await ctx.model.EventUser.findOne({
                where: {
                    username: '<EMAIL>'
                },
                raw: true,
            })

            await this.update({
                username: chasedream.username,
                password: ctx.helper.decode_dz_string(chasedream.password),
                tid: result.Variables.tid,
                pid: result.Variables.pid,
                fid: body.fid,
                typeid: body.typeid,
                subject: body.subject,
                content: body.content,
                htmlon: 1,
            })

            return result
        }

        async send_without_update(body) {
            const { ctx } = this

            const res = await this.login(body)

            ctx.error(res.Message.messageval !== 'login_succeed', res.Message.messagestr)

            const variables = res.Variables
            const cookies = `${variables.cookiepre}auth=${encodeURIComponent(variables.auth)};${variables.cookiepre}saltkey=${variables.saltkey};`
            body.formhash = variables.formhash

            const result = await this.send_thread(cookies, body)

            return result
        }

        async send_post(cookies, body) {
            const { ctx, config } = this

            const result = await ctx.curl(`${config.discuz.send_post}&tid=${body.tid}`, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: {
                    formhash: body.formhash,
                    message: body.message,
                },
                headers: {
                    'Cookie': cookies,
                },
                timeout: 10000,
            })

            const regex = /'pid':'(\d+)'/g
            const matched = regex.exec(result.data.toString())
            let pid = 0

            if (matched) {
                pid = matched[1]
            } else {
                ctx.logger.error(result.data.toString())
            }

            return pid
        }

        async stick_thread(cookies, body) {
            const { ctx, config } = this

            const result = await ctx.curl(`${config.discuz.stick}`, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: {
                    formhash: body.formhash,
                    fid: body.fid,
                    listextra: 'page%3D1',
                    handlekey: 'mods',
                    'moderate[]': body.tid,
                    'operations[]': 'stick',
                    sticklevel: body.stick,
                },
                headers: {
                    'Cookie': cookies,
                },
                timeout: 10000,
            })

            return result.data
        }

        async highlight_digest_thread(cookies, body) {
            const { ctx, config } = this

            let data = {
                fid: body.fid,
                'moderate[]': body.tid,
                formhash: body.formhash,
                handlekey: 'mods',
                rdodigest: 'on',
            }

            if (body.opt === 'highlight') {
                data['operations[]'] = ['highlight']
                data.highlight_color = body.highlight_color
            }

            if (body.opt === 'digest') {
                data['operations[]'] = ['digest', 'stamp']

                if (parseInt(body.digest, 10)) {
                    data.digestlevel = 1
                    data.sticklevel = 0
                    data.stamp = 0
                } else {
                    data.digestlevel = 0
                    data.sticklevel = 0
                    data.stamp = ''
                }
            }

            const result = await ctx.curl(config.discuz.highlight_digest, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: data,
                headers: {
                    'Cookie': cookies,
                },
                timeout: 10000,
            })

            return result.data
        }

        async delete_thread(cookies, body) {
            const { ctx, config } = this

            const result = await ctx.curl(config.discuz.delete_thread, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: {
                    fid: body.fid,
                    formhash: body.formhash,
                    handlekey: 'mods',
                    'listextra': 'page%3D1',
                    'moderate[]': body.tid,
                    'operations[]': 'delete',
                },
                followRedirect: false,
                headers: {
                    'Cookie': cookies,
                },
                timeout: 10000,
            })

            return result.data
        }

        async update_thread(cookies, body) {
            const { ctx, config } = this
            let data = {
                fid: body.fid,
                tid: body.tid,
                pid: body.pid,
                formhash: body.formhash,
                subject: body.subject,
                message: body.content,
                typeid: body.typeid,
                usesig: 1,
                wysiwyg: 1,
                htmlon: body.htmlon,
            }

            if (body.hiddenreplies) {
                data.hiddenreplies = 1
            }

            if (body.ispid) {
                const post = await ctx.forumModel.ForumPost.findOne({
                    where: {
                        pid: body.pid
                    },
                    raw: true,
                })

                if (post.position > 1) delete data.subject
            }

            const result = await ctx.curl(`${config.discuz.api_base_url}&module=editpost&editsubmit=yes`, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: data,
                dataType: 'json',
                headers: {
                    'Cookie': cookies,
                },
                timeout: 10000,
            })

            return result.data
        }

        async send_thread(cookies, body) {
            const { ctx, config } = this

            let data = {
                allownoticeauthor: 1,
                formhash: body.formhash,
                subject: body.subject,
                message: body.content,
                typeid: body.typeid,
                usesig: 1,
                wysiwyg: 1,
                htmlon: body.htmlon,
                hiddenreplies: 0,
            }

            if (body.hiddenreplies) {
                data.hiddenreplies = 1
            }

            const result = await ctx.curl(`${config.discuz.api_base_url}&module=newthread&fid=${body.fid}&topicsubmit=yes`, {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data,
                dataType: 'json',
                headers: {
                    'Cookie': cookies,
                },
                timeout: 10000,
            })

            return result.data
        }

        async login(user) {
            const { ctx } = this

            const result = await ctx.curl('https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=login&loginsubmit=yes&loginfield=auto&submodule=checkpost', {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: user,
                dataType: 'json',
                timeout: 10000,
            })

            return result.data
        }
    }

    return Thread
}
