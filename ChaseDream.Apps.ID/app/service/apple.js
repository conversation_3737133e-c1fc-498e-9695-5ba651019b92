const _ = require('lodash')
const NodeRSA = require('node-rsa')
const base64 = require('base64url')
const jwt = require('jsonwebtoken')
const Service = require('egg').Service

class AppleService extends Service {
    async register(uid, apple_token, apple_user, apple_email) {
        const { ctx } = this

        const res = await this.verify_id_token(apple_token)

        ctx.error(res.sub !== apple_user, "验证失败")

        await ctx.model.Apple.upsert({
            uid,
            user: apple_user,
            email: apple_email
        })
    }

    async verify_id_token(id_token) {
        const { ctx } = this

        const jwt_parts = id_token.split('.')
        const header = JSON.parse(base64.decode(jwt_parts[0]))        

        const res = await ctx.curl('https://appleid.apple.com/auth/keys', {
            method: 'GET',
            dataType: 'json',
            timeout: 10000,
        })
        
        let key = _.find(res.data.keys, { 'kid': header.kid })
        const pub_key = new NodeRSA()
        pub_key.importKey({ n: Buffer.from(key.n, 'base64'), e: Buffer.from(key.e, 'base64') }, 'components-public')
        const pk = pub_key.exportKey(['public'])

        return jwt.verify(id_token, pk, { algorithms: key.alg })
    }
}

module.exports = AppleService
