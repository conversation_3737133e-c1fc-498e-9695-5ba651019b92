const WebSocket = require('ws')
const Service = require('egg').Service

class WechatService extends Service {
    async get_userinfo_by_openid(openid) {
        const { ctx, app } = this

        let token = await app.redis.get('wechat_access_token') || ''
        token = JSON.parse(token)

        const url = `https://api.weixin.qq.com/cgi-bin/user/info?access_token=${token.accessToken}&openid=${openid}`

        const res = await ctx.curl(url, {
            method: 'GET',
            dataType: 'json',
            timeout: 10000,
        })

        return res.data
    }

    async connet_zhiqungj() {
        const { ctx, config } = this

        if (config.env !== 'prod') return

        const heartbeat = {
            timeout: 30000,
            timeout_obj: null,
            reset: function () {
                console.log(`ws has been reset.`)
                clearInterval(this.timeout_obj)
            },
            start: function (ws) {
                this.timeout_obj = setInterval(function () {
                    console.log('ws heartbeat')

                    ws.send(JSON.stringify({
                        action: 'active'
                    }))
                }, this.timeout)
            }
        }

        let res = await ctx.curl(config.zhiqungj.url, {
            method: 'POST',
            dataType: 'json',
            data: {
                account: config.zhiqungj.username,
                password: config.zhiqungj.password,
                remember: 1,
            },
            timeout: 5000,
        })

        const cookie = ctx.helper.get_cookie(res.headers['set-cookie'], 'PHPSESSID')

        res = await ctx.curl(`https://wapi.zhiqungj.com/chat/ws`, {
            method: 'GET',
            dataType: 'json',
            headers: {
                cookie,
            },
            timeout: 5000,
        })

        const ws = new WebSocket(res.data.data.url, {
            headers: {
                Cookie: cookie
            }
        })

        global.ws = ws
        ws.heartbeat = heartbeat

        ws.on('open', this.ws_open.bind(this))
        ws.on('close', this.ws_close.bind(this))
        ws.on('error', this.ws_error.bind(this))
        ws.on('message', this.ws_message.bind(this))
    }

    async ws_open() {
        console.log('ws opened')

        setTimeout(function () {
            global.ws.send(JSON.stringify({
                action: 'accounts'
            }))
        }, 1000)

        setTimeout(function () {
            global.ws.send(JSON.stringify({ "action": "setCurrentTalkKey", "data": "wxid_x8lgskj26a4r22|***********@chatroom" }))
        }, 2000)

        global.ws.heartbeat.start(ws)
    }

    async ws_close() {
        console.log('ws closed')

        global.ws.heartbeat.reset()
        setTimeout(async () => {
            await this.connet_zhiqungj()
        }, 1000 * 5)
    }

    async ws_error(err) {
        console.log(`error:${err}`)

        global.ws.heartbeat.reset()
        setTimeout(async () => {
            await this.connet_zhiqungj()
        }, 1000 * 5)
    }

    async ws_message(data) {
        const { ctx } = this

        const _data = JSON.parse(data)

        switch (_data.action) {
            case 'receive': {
                // const test = _data.data.content.test
                // const from = _data.data.from_user.group_nickname || _data.data.from_user.nickname
                // const to = _data.data.to_user.group_nickname || _data.data.to_user.nickname

                const majia = await ctx.model.WechatGroupMajia.findOne({
                    where: {
                        wx_id: _data.data.wxid,
                        status: 0,
                    },
                    raw: true,
                })

                if (!majia) return

                const title = '【微信提示】'
                const message = `有新消息`

                await ctx.curl('http://push.chasedream.com/api/v1/notification/system', {
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        title,
                        message,
                        uid: majia.uid,
                        payload: {
                            type: 'system',
                            msgfromid: 0,
                            msgfrom: '微信机器人'
                        }
                    }
                })

                break
            }
            case 'talkSnapshot': {
                for (const [key, value] of Object.entries(_data.data)) {
                    // const test = value.msg.data.content.test
                    // const from = value.msg.data.from_user.group_nickname || value.msg.data.from_user.nickname
                    // const to = value.msg.data.to_user.group_nickname || value.msg.data.to_user.nickname

                    const [wx_id, _] = key.split('|')
                    const majia = await ctx.model.WechatGroupMajia.findOne({
                        where: {
                            wx_id,
                            status: 0,
                        },
                        raw: true,
                    })

                    if (!majia) return

                    const title = '【微信提示】'
                    const message = `有新消息`

                    await ctx.curl('http://push.chasedream.com/api/v1/notification/system', {
                        method: 'POST',
                        dataType: 'json',
                        data: {
                            title,
                            message,
                            uid: majia.uid,
                            payload: {
                                type: 'system',
                                msgfromid: 0,
                                msgfrom: '微信机器人'
                            }
                        }
                    })
                }

                break
            }
            case 'offline': {
                const majia = await ctx.model.WechatGroupMajia.findOne({
                    where: {
                        wx_id: _data.data.wxid,
                        status: 0,
                    },
                    raw: true,
                })

                if (!majia) return

                const title = '【微信提示】'
                const message = `微信: ${_data.data.wxid} 已离线`

                await ctx.curl('http://push.chasedream.com/api/v1/notification/system', {
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        title,
                        message,
                        uid: majia.uid,
                        payload: {
                            type: 'system',
                            msgfromid: 0,
                            msgfrom: '微信机器人'
                        }
                    }
                })
                break
            }
        }
    }
}

module.exports = WechatService
