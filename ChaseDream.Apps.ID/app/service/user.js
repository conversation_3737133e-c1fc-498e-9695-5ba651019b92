const Service = require('egg').Service

class UserService extends Service {
    async get_roles_by_id(id) {
        const { ctx,app } = this

        let roles = await app.zrole.getRolesForUser(`${id}_user`)
        roles = Array.from(roles).map(ele => {
            return ele.substring(0, ele.indexOf('_'))
        })

        roles = await ctx.model.Role.findAll({
            attributes: ['id', 'name'],
            where: {
                id: roles
            },
            raw: true
        }).map(row => { return row.name })

        return roles
    }
}

module.exports = UserService
