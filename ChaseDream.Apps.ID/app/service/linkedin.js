const _ = require('lodash')
const fs = require('fs')
const fse = require('fs-extra')
const pad = require('pad')
const moment = require("moment")
const cheerio = require('cheerio')
const puppeteer = require('puppeteer')
const random_string = require("randomstring")
// const scrollPageToBottom = require('puppeteer-autoscroll-down')
const Service = require('egg').Service
const await = require('await-stream-ready/lib/await')

class LinkedinService extends Service {
    async login(account) {
        const { ctx, config } = this

        const browser = await puppeteer.launch({
            defaultViewport: { width: config.linkedin.browser.width, height: config.linkedin.browser.height },
            ignoreHTTPSErrors: true,
            args: [`--window-size=${config.linkedin.browser.width},${config.linkedin.browser.height}`, '--no-sandbox', '--disable-setuid-sandbox'],
            headless: config.env === 'prod' ? true : false
        })

        const page = await browser.newPage()
        await page.setUserAgent(config.linkedin.useragent)
        await page.goto('https://www.linkedin.com/', { waitUntil: 'domcontentloaded' })

        await page.waitForSelector('a.nav__button-secondary')
        await page.click('a.nav__button-secondary')

        await page.waitForSelector('#username')
        await page.waitForSelector('#password')
        await page.waitForSelector('.login__form_action_container button')
        await page.waitForTimeout(1000)

        const username_input = await page.$("#username")
        await username_input.type(account.username, { delay: 100 })

        const password_input = await page.$("#password")
        await password_input.type(account.password, { delay: 100 })

        await page.click('.login__form_action_container button')
        await page.waitForTimeout(2000)

        let pin = ''
        const url = await page.url()

        if (url.includes('checkpoint/challenge')) {
            await this.crawl_log(account.username, '登录账号需要进一步处理', page)

            for (let i = 0; i < 150; i++) {
                await ctx.helper.timeout(2000)

                const model = await ctx.model.LinkedinUser.findOne({
                    where: {
                        id: account.id,
                    }
                })

                if (model.pin) {
                    pin = model.pin
                    break
                }
            }

            if (!model.pin) {
                await this.account_error(account.id, account.username, '没有输入手机验证码')
            } else {
                const pin_input = await page.$(".input_verification_pin")
                await pin_input.type(pin)

                await page.click('#email-pin-submit-button')
                await page.waitForTimeout(2000)
                // 等待页面写入cookie
                await page.waitForNavigation({ waitUntil: 'domcontentloaded' })
            }
        }

        let cookie = await page.cookies()

        await ctx.model.LinkedinUser.update({
            cookie: JSON.stringify(cookie),
            pin: '',
        }, {
            where: {
                id: account.id,
            }
        })

        await browser.close()
    }

    async account_error(id, username, message, status = -1) {
        const { ctx } = this

        await ctx.model.LinkedinUser.update({
            status,
            error_message: message,
        }, {
            where: {
                id,
            }
        })
        throw new Error(`${username} ${message}`)
    }

    async crawl(manual = '') {
        const { ctx, config } = this

        let url = ''
        let wait_list = ''

        if (manual) {
            url = manual
        } else {
            wait_list = await ctx.model.LinkedinWaitList.findOne({
                where: {
                    status: 0,
                },
                order: [
                    ['id', 'ASC'],
                ],
                raw: true,
            }) || {}

            url = wait_list.url
        }

        if (!url) return

        const users = await ctx.model.LinkedinUser.findAll({
            where: {
                status: 0,
            },
            raw: true,
        })

        if (users.length === 0) return

        const user = _.sample(users)

        const username = user.username
        const password = user.password

        const browser = await puppeteer.launch({
            defaultViewport: { width: config.linkedin.browser.width, height: config.linkedin.browser.height },
            ignoreHTTPSErrors: true,
            args: [`--window-size=${config.linkedin.browser.width},${config.linkedin.browser.height}`, '--no-sandbox', '--disable-setuid-sandbox'],
            headless: config.env === 'prod' ? true : false
        })

        try {
            let page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)

            if (!user.cookie) {
                await page.goto('https://www.linkedin.com/', { waitUntil: 'domcontentloaded' })
                await page.waitForSelector('a.nav__button-secondary')
                await page.click('a.nav__button-secondary')

                await page.waitForSelector('#username')
                await page.waitForSelector('#password')
                await page.waitForSelector('.login__form_action_container button')
                await page.waitForTimeout(1000)

                const username_input = await page.$("#username")
                await username_input.type(username, { delay: 100 })

                const password_input = await page.$("#password")
                await password_input.type(password, { delay: 100 })

                await page.click('.login__form_action_container button')
                await page.waitForTimeout(2000)

                const pin_input = await page.$(".input_verification_pin")
                if (pin_input) {
                    await this.crawl_log(username, '需要输入手机验证码', page)
                    let model = {}

                    for (let i = 0; i < 150; i++) {
                        await ctx.helper.timeout(2000)

                        model = await ctx.model.LinkedinUser.findOne({
                            where: {
                                id: user.id,
                            }
                        })

                        if (model.pin) {
                            await pin_input.type(model.pin)
                            await page.click('#email-pin-submit-button')

                            break
                        }
                    }

                    if (!model.pin) {
                        await this.account_error(user.id, username, '没有输入手机验证码')
                    } else {
                        // 等待页面写入cookie
                        await page.waitForNavigation({ waitUntil: 'domcontentloaded' })
                    }
                }

                let cookie = await page.cookies()

                await ctx.model.LinkedinUser.update({
                    cookie: JSON.stringify(cookie),
                }, {
                    where: {
                        id: user.id
                    }
                })

                await this.crawl_log(username, '正在登录账号', page)
            } else {
                let cookies = JSON.parse(user.cookie)

                await page.setCookie(...cookies)
                await this.crawl_log(username, '使用Cookie登录账号', page)
            }

            await page.goto(url, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(10000)

            await this.crawl_log(username, '跳转到目标URL', page)
            if ((await page.url()).includes('/in/unavailable/')) throw new Error('无效链接')

            let html = await page.content()
            let $ = cheerio.load(html)
            let avatar_big = ''

            if ($('.global-nav__me-photo').length === 0) {
                const message = '没获取到用户登录状态'

                await this.crawl_log(username, message, page)
                await ctx.curl(`https://sctapi.ftqq.com/SCT41422T4bP8ETS1xPsql4rVa4vuFP2r.send?title=${encodeURIComponent('爬虫提示')}&desp=${encodeURIComponent('Linkedin账号Cookie失效')}`, {
                    method: 'GET',
                    timeout: 3000,
                })
                throw new Error(message)
            }

            const reg_btn = $('.join-form .join-form__form-body-submit-button')
            if (reg_btn.length) {
                await ctx.model.LinkedinUser.update({
                    cookie: '',
                }, {
                    where: {
                        id: user.id
                    }
                })
                await this.crawl_log(username, '登录失败', page)
                await browser.close()
                return
            }

            const src = $('button.presence-entity__image img').prop('src')

            if (src && !src.includes('data:image')) {
                let avatar = $('.pv-top-card--photo')
                if (avatar.length) {
                    await page.click('.pv-top-card--photo')
                } else {
                    await page.click('.pv-top-card--photo__v2')
                }

                await page.waitForTimeout(3000)

                html = await page.content()
                $ = cheerio.load(html)

                if (!html.includes('Something went wrong')) {
                    avatar_big = $('.pv-member-photo-modal__profile-image').prop('src') || ''
                    avatar_big = await this.download_image(browser, avatar_big, config.upload_linkedin)

                    await page.click('.artdeco-modal__dismiss')
                } else {
                    await page.goto(url, { waitUntil: 'domcontentloaded' })
                }

                await page.waitForTimeout(3000)
            }

            // await scrollPageToBottom(page)
            await page.waitForTimeout(3000)

            html = await page.content()
            $ = cheerio.load(html)

            await this.crawl_log(username, '开始个人信息', page)
            // 个人信息
            const linkedin = await this.info($, browser, page, url, avatar_big)
            if (wait_list) {
                await ctx.model.LinkedinWaitList.update({
                    status: 1,
                    lid: linkedin.id,
                }, {
                    where: {
                        id: wait_list.id
                    }
                })
            }

            await this.crawl_log(username, '结束个人信息', page)
            // 工作经历
            await this.crawl_log(username, '开始工作经历', page)
            await this.work_experience($, browser, linkedin)
            await this.crawl_log(username, '结束工作经历', page)
            await page.waitForTimeout(5000)
            // 教育经历
            await this.crawl_log(username, '开始教育经历', page)
            await this.education($, browser, linkedin)
            await this.crawl_log(username, '结束教育经历', page)
            await page.waitForTimeout(5000)
            // 资格认证
            await this.crawl_log(username, '开始资格认证', page)
            await this.certification($, browser, linkedin)
            await this.crawl_log(username, '结束资格认证', page)
            await page.waitForTimeout(5000)
            // 志愿者经历
            await this.crawl_log(username, '开始志愿者经历', page)
            await this.volunteer($, browser, linkedin)
            await this.crawl_log(username, '结束志愿者经历', page)
            await page.waitForTimeout(5000)
            // 技能认可
            await this.crawl_log(username, '开始技能认可', page)
            await this.skill($, browser, linkedin)
            await this.crawl_log(username, '结束技能认可', page)
            await page.waitForTimeout(5000)
            // 个人成就
            await this.crawl_log(username, '开始个人成就', page)
            await this.accomplishment($, browser, page, linkedin)
            await this.crawl_log(username, '结束个人成就', page)

            await this.crawl_log(username, `-----${linkedin.name}-抓取完成-----`, page)

            ctx.json_body = {
                success: true,
            }
        } catch (err) {
            ctx.logger.error(err.message)

            if (wait_list && wait_list.id) {
                await ctx.model.LinkedinWaitList.update({
                    status: 2,
                    error_message: err.message,
                }, {
                    where: {
                        id: wait_list.id,
                    }
                })
            }

            ctx.json_body = err
        } finally {
            await browser.close()
        }
    }

    async download_image(browser, url, path) {
        const { config } = this

        const filename = `${random_string.generate(8)}.jpeg`
        const month = pad(2, moment().month() + 1, '0')
        path = [config.upload_linkedin_school, config.upload_linkedin_organization].indexOf(path) !== -1 ? path : `${path}/${moment().year()}/${month}`
        const full_path = `${config.baseDir}${path}`

        await fse.mkdirp(full_path)

        const page = await browser.newPage()
        await page.setUserAgent(config.linkedin.useragent)
        const source = await page.goto(url, { waitUntil: 'domcontentloaded' })

        const final = `${full_path}/${filename}`
        fs.writeFileSync(final, await source.buffer())

        await page.close()

        return final.replace(config.baseDir, '')
    }

    async info($, browser, page, url, avatar_big) {
        const { ctx, config } = this

        const element = await page.$('.pv-test-details__left-panel .pv-test-details__separator a.test-heading-small')
        await element.click()
        await page.waitForTimeout(2000)

        let html = await page.content()
        $ = cheerio.load(html)

        let mobile = '', address = '', email = '', social1 = '', social2 = '', social3 = '', birthday = '', gender = ''

        let section = $('.ci-phone')
        if (section.length) {
            mobile = ctx.helper.format_linkedin_string($('.ci-phone .pv-contact-info__ci-container .t-black').text())
        }

        section = $('.ci-address')
        if (section.length) {
            address = ctx.helper.format_linkedin_string($('.ci-address .pv-contact-info__ci-container a').text())
        }

        section = $('.ci-email')
        if (section.length) {
            email = $('.ci-email .pv-contact-info__ci-container a').prop('href')
            email = email.replace('mailto:', '')
        }

        section = $('.ci-ims')
        if (section.length) {
            const itmes = []
            $('.ci-ims ul li').each(function (i, elem) {
                const spans = $(this).find('span')
                let u = ctx.helper.format_linkedin_string(spans.eq(0).text())
                let v = ctx.helper.format_linkedin_string(spans.eq(1).text())
                itmes.push(`${u}${v}`)
            })

            let [first, second, third] = itmes
            social1 = first || ''
            social2 = second || ''
            social3 = third || ''
        }

        section = $('.ci-birthday')
        if (section.length) {
            birthday = ctx.helper.format_linkedin_string($('.ci-birthday .pv-contact-info__ci-container span').text())
        }

        try {
            await page.click('.artdeco-modal__dismiss')
        } catch (err) { }

        await page.waitForTimeout(5000)

        html = await page.content()
        $ = cheerio.load(html)

        const name = ctx.helper.format_linkedin_string($('.test-heading-xlarge').text())
        const geo = ctx.helper.format_linkedin_string($('.pb2 span.test-body-small').text())
        let [location, nationality] = geo.split(',')
        location = location || ''
        nationality = nationality || ''
        let avatar_small = $('button.presence-entity__image img').prop('src') || ''
        const friends = ctx.helper.format_linkedin_string($('ul.pv-top-card--list-bullet li span.t-bold').text())
        const followers = ctx.helper.format_linkedin_string($('.pv-deferred-area__content span.align-self-center').text())
        const resume = ctx.helper.format_linkedin_string($('[id$=ABOUT-en-US] span.visually-hidden').text())
        const summary = ctx.helper.format_linkedin_string($('.pv-test-details__left-panel .test-body-medium').text())

        if (!avatar_small.includes('data:image')) {
            avatar_small = await this.download_image(browser, avatar_small, config.upload_linkedin)
        } else {
            avatar_small = ''
            avatar_big = ''
        }

        if (avatar_small) {
            try {
                const res = await ctx.curl(config.faceplusplus.url, {
                    method: 'POST',
                    dataType: 'json',
                    contentType: 'application/x-www-form-urlencoded',
                    data: {
                        api_key: config.faceplusplus.api_key,
                        api_secret: config.faceplusplus.api_secret,
                        image_url: `https://static.chasedream.com${avatar_small.replace('/upload', '')}`,
                        return_attributes: 'gender',
                    },
                    timeout: 5000,
                })

                if (res.data.error_message === undefined && res.data.face_num) {
                    const face = res.data.faces[0]
                    gender = face.attributes.gender.value
                }
            } catch (err) { }
        }

        let uid = ''
        if (url.indexOf('?') !== -1) {
            uid = decodeURIComponent(url.substring(0, url.indexOf('?')).replace('https://www.linkedin.com/in/', '').replace('/', ''))
        } else {
            uid = decodeURIComponent(url.replace('https://www.linkedin.com/in/', '').replace('/', ''))
        }

        const linkedin = await ctx.model.LinkedinInfo.create({
            uid,
            name,
            gender,
            nationality: nationality.trim(),
            avatar_small,
            avatar_big,
            url,
            friends: friends.replace(' connections', '').replace(' connection', '').replace('+', '') || 0,
            followers: followers.replace(' followers', '').replace(' follower', '').replace(/,/ig, '').replace('+', '') || 0,
            summary: summary.trim(),
            location: location.trim(),
            resume: resume.trim().replace('see more', ''),
            mobile,
            address,
            email,
            social1,
            social2,
            social3,
            birthday,
            update_by: '爬虫抓取更新',
            created_at: ctx.helper.now(),
            updated_at: ctx.helper.now(),
        })

        return linkedin
    }

    async work_experience($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const wes = []
        let page = undefined
        let wrapper = '[id$=EXPERIENCE-en-US] ul.pvs-list li.artdeco-list__item'

        const has_more = $('[id$=EXPERIENCE-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            if ($(this).find('.pvs-entity__path-node').length <= 0) {
                let title = '', org_name = '', job_category = '', city = '', job_desc = '', dateline = '', url = ''

                title = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a div.align-items-center span span.visually-hidden').text()) || ''
                org_name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a span.t-14 span.visually-hidden').eq(0).text()) || ''
                let arr = org_name.split('·')
                org_name = arr[0] || ''
                job_category = arr[1] || ''
                city = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a span.t-14 span.visually-hidden').eq(2).text()) || ''
                job_desc = ctx.helper.format_linkedin_string($(this).find('.pvs-entity .pvs-list__outer-container span.visually-hidden').text()) || ''
                dateline = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a span.t-14 span.visually-hidden').eq(1).text()) || ''
                url = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a').prop('href')) || ''

                if (title && org_name) {
                    const { begin_year, begin_month, end_year, end_month } = ctx.helper.format_linkedin_year_month(dateline)

                    wes.push({
                        title: title.trim(),
                        org_name: org_name.trim(),
                        job_category: job_category.trim(),
                        city,
                        job_desc,
                        begin_year,
                        begin_month,
                        end_year,
                        end_month,
                        url,
                    })
                }
            } else {
                const org_name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a div.align-items-center span span.visually-hidden').text())
                const url = $(this).find('.pvs-entity div.justify-space-between a').prop('href')

                $(this).find('ul.pvs-list > li .pvs-entity').each(function (i, elem) {
                    const title = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.align-items-center span.visually-hidden').eq(0).text()) || ''
                    const job_category = ''
                    const city = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-14 span.visually-hidden').eq(1).text()) || ''
                    const job_desc = ctx.helper.format_linkedin_string($(this).find('.pvs-entity .pvs-list__outer-container span.visually-hidden').text()) || ''
                    const dateline = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-14 span.visually-hidden').eq(0).text()) || ''

                    if (title && org_name) {
                        const { begin_year, begin_month, end_year, end_month } = ctx.helper.format_linkedin_year_month(dateline)

                        wes.push({
                            title: title.trim(),
                            org_name: org_name.trim(),
                            job_category: job_category.trim(),
                            city,
                            job_desc,
                            begin_year,
                            begin_month,
                            end_year,
                            end_month,
                            url,
                        })
                    }
                })
            }
        })

        for (let we of wes) {
            const org_id = await this.organization(browser, we.url)
            if (org_id >= 0) {
                we.org_id = org_id
                we.lid = linkedin.id

                const created = await ctx.model.LinkedinWorkExperience.create(we)

                if (!linkedin.cur_work_id) {
                    await ctx.model.LinkedinInfo.update({
                        cur_work_id: created.id,
                    }, {
                        where: {
                            id: linkedin.id
                        }
                    })

                    linkedin.cur_work_id = created.id
                }
            }

            await ctx.helper.timeout(5000)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    async education($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const edus = []

        let page = undefined
        let wrapper = '[id$=EDUCATION-en-US] ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=EDUCATION-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const school_name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between a div span.visually-hidden').text()) || ''
            let degree = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-14 span.visually-hidden').eq(0).text()) || ''
            let arr = degree.split([','])
            degree = arr[0]
            const major = arr[1] || ''
            let enrollment_year = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-14 span.visually-hidden').eq(1).text()) || ''
            let arr2 = enrollment_year.split('-')
            enrollment_year = arr2[0] || ''
            const graduation_year = arr2[1] || ''
            const school_record = ctx.helper.format_linkedin_string($(this).find('.pvs-entity .pvs-list__outer-container ul li span.visually-hidden').eq(0).text()) || ''
            const club_activity = ctx.helper.format_linkedin_string($(this).find('.pvs-entity .pvs-list__outer-container ul li span.visually-hidden').eq(1).text()) || ''
            const other = ctx.helper.format_linkedin_string($(this).find('.pvs-entity .pvs-list__outer-container ul li').eq(2).text()) || ''
            const url = $(this).find('.pvs-entity div.justify-space-between a').prop('href')

            if (school_name) {
                edus.push({
                    school_name,
                    degree,
                    major,
                    enrollment_year: enrollment_year.trim(),
                    graduation_year,
                    school_record: school_record.replace('Grade:', '').trim(),
                    club_activity: club_activity.replace('Activities and societies:', '').trim(),
                    other,
                    url,
                })
            }
        })

        for (let edu of edus) {
            const res = await this.school(browser, edu.url)

            if (res) {
                edu.school_id = res.id
                edu.lid = linkedin.id

                const created = await ctx.model.LinkedinEducation.create(edu)

                if (!linkedin.cur_edu_id) {
                    await ctx.model.LinkedinInfo.update({
                        cur_edu_id: created.id,
                    }, {
                        where: {
                            id: linkedin.id
                        }
                    })

                    linkedin.cur_edu_id = created.id
                }
            } else {
                edu.lid = linkedin.id
                await ctx.model.LinkedinEducation.create(edu)
            }

            await ctx.helper.timeout(5000)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    async certification($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const cers = []

        let page = undefined
        let wrapper = '[id$=CERTIFICATIONS-en-US] ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=CERTIFICATIONS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            let begin_year = '', begin_month = '', end_year = '', end_month = ''

            let count = $(this).find('.pvs-entity a > span').length

            let name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity a div span span.visually-hidden').text())
            let issue_authority = ctx.helper.format_linkedin_string($(this).find('.pvs-entity a span.t-normal span.visually-hidden').eq(0).text())

            if (count > 2) {
                let dateline = ctx.helper.format_linkedin_string($(this).find('.pvs-entity a span.t-normal span.visually-hidden').eq(1).text())
                const raw_dateline = dateline
                dateline = dateline.substring(0, dateline.indexOf('·'))
                let arr = dateline.split('·')

                let begin = arr[0]
                begin = begin.replace('Issued ', '').replace('No Expiration Date', '').replace('\n', '').trim()
                begin = begin.replace('\n', '')

                const t = begin.split(' ')
                begin_year = t[1] || 0
                begin_month = t[0] || 0

                let end = arr[1] || ''
                if (raw_dateline.includes('No Expiration Date')) {
                    end_year = 9999
                    end_month = 99
                } else {
                    [end_month, end_year] = end.split(' ')
                }

                end_year = end_year || 0
            }

            let link = $(this).find('.pvs-entity a').prop('href')

            if (name) {
                cers.push({
                    name,
                    issue_authority,
                    begin_year: parseInt(begin_year, 10),
                    begin_month: ctx.helper.get_month(begin_month),
                    end_year: parseInt(end_year, 10),
                    end_month: ctx.helper.get_month(end_month),
                    link,
                })
            }
        })

        for (let cer of cers) {
            cer.lid = linkedin.id
            await ctx.model.LinkedinCertification.create(cer)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    async volunteer($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const vols = []

        let page = undefined
        let wrapper = '[id$=VOLUNTEERING-EXPERIENCE-en-US] ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=VOLUNTEERING-EXPERIENCE-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between div.align-items-center span span.visually-hidden').text())
            const org_name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-normal span.visually-hidden').eq(0).text())

            let dateline = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-normal span.visually-hidden').eq(1).text())
            dateline = dateline.substring(0, dateline.indexOf('·'))

            const { begin_year, begin_month, end_year, end_month } = ctx.helper.format_linkedin_year_month(dateline)

            const focus = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.justify-space-between span.t-normal span.visually-hidden').eq(2).text())
            const desc = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.pvs-list__outer-container span.visually-hidden').text())

            const url = $(this).find('.pvs-entity a').prop('href') || ''

            if (name) {
                vols.push({
                    name,
                    org_name,
                    focus,
                    begin_year,
                    begin_month,
                    end_year,
                    end_month,
                    desc,
                    url,
                })
            }
        })

        for (let vol of vols) {
            vol.lid = linkedin.id
            vol.org_id = await this.organization(browser, vol.url)

            await ctx.model.LinkedinVolunteer.create(vol)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    async skill($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const sks = []

        let page = undefined
        let wrapper = '[id$=SKILLS-en-US] ul.pvs-list li.artdeco-list__item'

        const has_more = $('[id$=SKILLS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('.pvs-entity div.align-items-center span.visually-hidden').eq(0).text())

            if (name) {
                sks.push({
                    name,
                    top: true,
                })
            }
        })

        for (let sk of sks) {
            sk.lid = linkedin.id

            await ctx.model.LinkedinSkill.create(sk)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就
    async accomplishment($, browser, page, linkedin) {
        const { ctx } = this

        await this.publication($, browser, linkedin)
        await this.patent($, browser, linkedin)
        await this.course($, browser, linkedin)
        await this.project($, browser, linkedin)
        await this.honor($, browser, linkedin)
        await this.test($, browser, linkedin)
        await this.language($, browser, linkedin)
        await this.org($, browser, linkedin)
    }

    // 个人成就 - 语言能力
    async language($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const ls = []

        let page = undefined
        let wrapper = '[id$=LANGUAGES-en-US] .pvs-list__outer-container ul.pvs-list li.artdeco-list__item'

        const has_more = $('[id$=LANGUAGES-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('div.align-items-center span span.visually-hidden').text()) || ''
            const level = ctx.helper.format_linkedin_string($(this).find('.t-14 span.visually-hidden').text()) || ''

            ls.push({
                name: name.replace('Language name', '').trim(),
                level,
            })
        })

        for (let l of ls) {
            l.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentLanguage.create(l)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 所学课程
    async course($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const cs = []

        let page = undefined
        let wrapper = '[id$=COURSES-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=COURSES-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('div.justify-space-between div.align-items-center span span.visually-hidden').text()) || ''
            const number = ctx.helper.format_linkedin_string($(this).find('.t-14 span.visually-hidden').eq(0).text()) || ''

            cs.push({
                name: name.replace('Course name', '').trim(),
                number: number.replace('Course number', '').trim(),
            })
        })

        for (let c of cs) {
            c.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentCourse.create(c)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 专利发明
    async patent($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const ps = []

        let page = undefined
        let wrapper = '[id$=PATENTS-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=PATENTS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('div.align-items-center span span.visually-hidden').text()) || ''
            let number = ctx.helper.format_linkedin_string($(this).find('span.t-14 span.visually-hidden').text()) || ''
            let arr = number.split('·')
            number = arr[0] || ''
            const dateline = arr[1] || ''
            const desc = ctx.helper.format_linkedin_string($(this).find('.inline-show-more-test--is-collapsed span.visually-hidden').text()) || ''
            const website = ctx.helper.format_linkedin_string($(this).find('.pvs-list__outer-container a').prop('href')) || ''

            ps.push({
                name: name.replace('Patent title', '').trim(),
                number: number.replace('Patent issuer and number', '').trim(),
                dateline: dateline.replace('Patent date', '').replace('Filed ', '').replace('Issued ', '').trim(),
                desc: desc.replace('Patent description', '').trim(),
                website: website,
            })
        })

        for (let p of ps) {
            p.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentPatent.create(p)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 荣誉奖项
    async honor($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const hs = []

        let page = undefined
        let wrapper = '[id$=HONORS-AND-AWARDS-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=HONORS-AND-AWARDS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('div.align-items-center span span.visually-hidden').text()) || ''
            let issue_authority = ctx.helper.format_linkedin_string($(this).find('span.t-14 span.visually-hidden').text()) || ''
            let arr = issue_authority.split('·')
            issue_authority = arr[0] || ''
            const dateline = arr[1] || ''
            const desc = ctx.helper.format_linkedin_string($(this).find('.inline-show-more-test--is-collapsed span.visually-hidden').text()) || ''

            hs.push({
                name: name.replace('honor title', '').trim(),
                issue_authority: issue_authority.replace('honor issuer', '').trim(),
                dateline: dateline.replace('honor date', '').trim(),
                desc: desc.replace('honor description', '').trim(),
            })
        })

        for (let h of hs) {
            h.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentHonor.create(h)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 参与组织
    async org($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const os = []

        let page = undefined
        let wrapper = '[id$=ORGANIZATIONS-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=ORGANIZATIONS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('div.align-items-center span span.visually-hidden').text()) || ''
            let dateline = ctx.helper.format_linkedin_string($(this).find('span.t-14 span.visually-hidden').text()) || ''
            let arr = dateline.split('·')
            dateline = arr[1] || ''

            const { begin_year, begin_month, end_year, end_month } = ctx.helper.format_linkedin_year_month(dateline)

            const title = arr[0] || ''
            const desc = ctx.helper.format_linkedin_string($(this).find('.inline-show-more-test--is-collapsed span.visually-hidden').text()) || ''

            os.push({
                name: name.replace('organization name', '').trim(),
                title: title.replace('organization position', '').trim(),
                begin_year,
                begin_month,
                end_year,
                end_month,
                desc: desc.replace('organization description', '').trim(),
            })
        })

        for (let o of os) {
            o.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentOrganization.create(o)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 所做项目
    async project($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const prs = []

        let page = undefined
        let wrapper = '[id$=PROJECTS-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=PROJECTS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('div.align-items-center span span.visually-hidden').text()) || ''
            const dateline = ctx.helper.format_linkedin_string($(this).find('span.t-14 span.visually-hidden').text()) || ''

            const { begin_year, begin_month, end_year, end_month } = ctx.helper.format_linkedin_year_month(dateline)

            const website = ctx.helper.format_linkedin_string($(this).find('.pvs-list__outer-container a').prop('href')) || ''
            const desc = ctx.helper.format_linkedin_string($(this).find('.inline-show-more-test span span.visually-hidden').text()) || ''

            prs.push({
                name: name.replace('Project name', '').trim(),
                begin_year,
                begin_month,
                end_year,
                end_month,
                website,
                desc,
            })
        })

        for (let p of prs) {
            p.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentProject.create(p)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 出版作品
    async publication($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const pus = []

        let page = undefined
        let wrapper = '[id$=PUBLICATIONS-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=PUBLICATIONS-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('.align-items-center span span.visually-hidden').text()) || ''
            let company = ctx.helper.format_linkedin_string($(this).find('span.t-14 span.visually-hidden').text()) || ''
            let arr = company.split('·')
            company = arr[0] || ''
            const dateline = arr[1] || ''
            const website = ctx.helper.format_linkedin_string($(this).find('.pvs-list__outer-container a.app-aware-link').prop('href')) || ''
            const desc = ctx.helper.format_linkedin_string($(this).find('.pvs-list__outer-container span.visually-hidden').text()) || ''

            if (name && company) {
                pus.push({
                    name: name.replace('publication title', '').trim(),
                    company: company.replace('publication description', '').trim(),
                    dateline: dateline.replace('publication date', '').trim(),
                    website,
                    desc: desc.replace('publication description', '').trim(),
                })
            }
        })

        for (let p of pus) {
            p.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentPublication.create(p)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    // 个人成就 - 测试成绩
    async test($, browser, linkedin) {
        const { ctx, config } = this

        const $$ = $
        const ts = []

        let page = undefined
        let wrapper = '[id$=TEST-SCORES-en-US] .pvs-list__outer-container ul.pvs-list > li.artdeco-list__item'

        const has_more = $('[id$=TEST-SCORES-en-US] .pvs-list__footer-wrapper a').prop('href')

        if (has_more) {
            page = await browser.newPage()
            await page.setUserAgent(config.linkedin.useragent)
            await page.goto(has_more, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(2000)

            let html = await page.content()
            $ = cheerio.load(html)

            wrapper = '.scaffold-finite-scroll__content ul.pvs-list li.artdeco-list__item'
        }

        $(wrapper).each(function (i, elem) {
            const name = ctx.helper.format_linkedin_string($(this).find('.align-items-center span span.visually-hidden').text()) || ''
            let dateline = ctx.helper.format_linkedin_string($(this).find('span.t-14 span.visually-hidden').text()) || ''
            let arr = dateline.split('·')
            dateline = arr[1] || ''
            const score = arr[0] || ''
            const desc = ctx.helper.format_linkedin_string($(this).find('.pvs-list__outer-container span.visually-hidden').text()) || ''

            ts.push({
                name: name.replace('Test name', '').trim(),
                dateline: dateline.replace('Taken on ', '').trim(),
                score: score.replace('Score:', '').trim(),
                desc: desc.replace('Description', '').trim(),
            })
        })

        for (let t of ts) {
            t.lid = linkedin.id

            await ctx.model.LinkedinAccomplishmentTest.create(t)
        }

        if (has_more) {
            await page.close()
        }

        $ = $$
    }

    async organization(browser, url) {
        const { ctx, config } = this

        if (!url || !url.includes('/company')) {
            return 0
        }

        const regex = /https:\/\/www.linkedin.com\/company\/(.*?)\//ig
        const matched = regex.exec(url)
        const linkedin_org_id = matched[1]

        let org = await ctx.model.LinkedinOrganization.findOne({
            where: {
                linkedin_org_id
            },
            raw: true,
        })

        if (org) return org.id

        const page = await browser.newPage()
        await page.setUserAgent(config.linkedin.useragent)
        await page.goto(url, { waitUntil: 'domcontentloaded' })
        await page.waitForTimeout(2000)

        let html = await page.content()
        let $ = cheerio.load(html)
        const about = ctx.helper.format_linkedin_string($('.org-page-navigation ul li a').eq(0).prop('href')) || ''

        if (!about.includes('about')) {
            await page.goto(`https://www.linkedin.com/${about}/about/`, { waitUntil: 'domcontentloaded' })
        }

        try {
            await page.waitForSelector('.org-top-card-primary-content__logo-container', { timeout: 3000 })
        } catch (err) {
            ctx.logger.error(err.message)
        }

        html = await page.content()
        $ = cheerio.load(html)

        const empty = $('.artdeco-empty-state__message')
        if (empty.length > 0) {
            await page.close()
            return -1
        }

        let name = $('.org-top-card__primary-content .block h1 span').text() || ''
        let logo = $('.org-top-card-primary-content__logo-container img').prop('src') || ''
        const summary = $('.org-top-card-summary__tagline').text()
        const desc = $('.artdeco-card p.break-words').text()
        const location = ctx.helper.format_linkedin_string($('.org-top-card-summary-info-list .inline-block .org-top-card-summary-info-list__info-item').eq(0).text())
        const linkedin_followers = ctx.helper.format_linkedin_string($('.org-top-card-summary-info-list__info-item').eq(2).text())
        const linkedin_staff_size = ctx.helper.format_linkedin_string($('.mt1 span.link-without-visited-state').text())

        if (!name) {
            name = $('.org-top-card-listing__summary .block h1 span').text()
        }
        if (!name) {
            name = $('.org-top-card-summary__title span').text()
        }

        if (!logo) {
            logo = $('.align-items-flex-start img').prop('src') || ''
        }
        if (logo) {
            logo = await this.download_image(browser, logo, config.upload_linkedin_organization)
        }

        let dt = [], dd = [], industry = '', website = '', staff_size = '', company_type = '', company_location = '', established_time = '', focus_areas = ''

        $('dl.overflow-hidden dt.test-heading-small').each(function (i, elem) {
            dt.push(ctx.helper.format_linkedin_string($(this).text()))
        })

        $('dl.overflow-hidden dd').each(function (i, elem) {
            if ($(this).text().indexOf('on LinkedIn') === -1) dd.push($(this))
        })

        const arr = _.zip(dt, dd)
        for (const item of arr) {
            const [text, val] = item
            switch (text) {
                case 'Website':
                    website = ctx.helper.format_linkedin_string(val.find('.link-without-visited-state').prop('href'))
                    break
                case 'Industry':
                    industry = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Company size':
                    staff_size = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Headquarters':
                    company_location = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Type':
                    company_type = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Founded':
                    established_time = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Specialties':
                    focus_areas = ctx.helper.format_linkedin_string(val.text())
                    break
            }
        }

        org = await ctx.model.LinkedinOrganization.create({
            linkedin_org_id,
            name,
            logo,
            summary,
            url: about,
            desc,
            location,
            industry,
            website,
            staff_size: staff_size.replace(/,/ig, ''),
            company_type,
            company_location,
            established_time,
            focus_areas,
            linkedin_staff_size: linkedin_staff_size.replace('See all ', '').replace(' employees on LinkedIn', '').replace(' employee on LinkedIn', '').replace(/,/ig, '') || 0,
            linkedin_followers: linkedin_followers.replace(' followers', '').replace(' follower', '').replace(/,/ig, '') || 0,
        })

        await page.close()

        return org.id
    }

    async school(browser, url) {
        const { ctx, config } = this

        if (!url || !url.includes('/company')) {
            return 0
        }

        const regex = /https:\/\/www.linkedin.com\/company\/(.*?)\//ig
        const matched = regex.exec(url)
        const linkedin_school_id = matched[1]

        let school = await ctx.model.LinkedinSchool.findOne({
            where: {
                linkedin_school_id
            }
        })

        if (school) return school

        const page = await browser.newPage()
        await page.setUserAgent(config.linkedin.useragent)
        await page.goto(url, { waitUntil: 'domcontentloaded' })
        await page.waitForTimeout(2000)

        let html = await page.content()
        let $ = cheerio.load(html)
        const about = ctx.helper.format_linkedin_string($('.org-page-navigation ul li a').eq(0).prop('href')) || ''

        if (!about.includes('about')) {
            await page.goto(`https://www.linkedin.com/${about}/about/`, { waitUntil: 'domcontentloaded' })
        }

        try {
            await page.waitForSelector('.org-top-card-primary-content__logo-container', { timeout: 3000 })
        } catch (err) {
            ctx.logger.error(err.message)
        }

        if (!about.includes('about')) {
            html = await page.content()
            $ = cheerio.load(html)
        }

        const name = $('.org-top-card__primary-content .block h1 span').text()
        let logo = $('.org-top-card-primary-content__logo-container img').prop('src') || $('.org-top-card-listing__logo').prop('src') || ''
        const summary = ctx.helper.format_linkedin_string($('.org-top-card-summary__tagline').text())
        const desc = $('.artdeco-card p.break-words').text()
        const linkedin_alumni_size = ctx.helper.format_linkedin_string($('.org-top-card-summary-info-list__info-item').eq(1).text())
        const linkedin_followers = ctx.helper.format_linkedin_string($('.org-top-card-summary-info-list__info-item').eq(2).text())
        const linkedin_staff_size = ctx.helper.format_linkedin_string($('.mt1 span.link-without-visited-state').text())

        logo = await this.download_image(browser, logo, config.upload_linkedin_school)

        let dt = [], dd = [], website = '', phone = '', industry = '', staff_size = '', school_location = '', established_time = '', focus_areas = ''

        $('dl.overflow-hidden dt.test-heading-small').each(function (i, elem) {
            dt.push(ctx.helper.format_linkedin_string($(this).text()))
        })

        $('dl.overflow-hidden dd').each(function (i, elem) {
            if ($(this).text().indexOf('on LinkedIn') === -1) dd.push($(this))
        })

        const arr = _.zip(dt, dd)
        for (const item of arr) {
            const [text, val] = item
            switch (text) {
                case 'Website':
                    website = ctx.helper.format_linkedin_string(val.find('.link-without-visited-state').prop('href'))
                    break
                case 'Phone':
                    phone = ctx.helper.format_linkedin_string(val.find('.link-without-visited-state span').eq(0).text())
                    break
                case 'Industry':
                    industry = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Company size':
                    staff_size = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Headquarters':
                    school_location = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Type':
                    established_time = ctx.helper.format_linkedin_string(val.text())
                    break
                case 'Founded':
                    focus_areas = ctx.helper.format_linkedin_string(val.text())
                    break
            }
        }

        school = await ctx.model.LinkedinSchool.create({
            linkedin_school_id,
            name,
            logo,
            summary,
            url: about,
            desc,
            website,
            phone,
            industry,
            staff_size: staff_size.replace(' employees', '').replace(' employee', '').replace(/,/ig, ''),
            school_location,
            established_time,
            focus_areas,
            linkedin_alumni_size: 0,
            linkedin_staff_size: linkedin_staff_size.replace('See all ', '').replace(' employees on LinkedIn', '').replace(' employee on LinkedIn', '').replace(/,/ig, '') || 0,
            linkedin_followers: linkedin_followers.replace(' followers', '').replace(' follower', '').replace(/,/ig, '') || 0,
        })

        await page.close()

        return school
    }

    async info_delete(id) {
        const { ctx } = this

        const linkedin = await ctx.model.LinkedinInfo.findOne({
            where: {
                id
            },
            raw: true,
        })

        if (!linkedin) return

        await ctx.model.LinkedinWorkExperience.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinEducation.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinCertification.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinVolunteer.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinSkill.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentLanguage.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentCourse.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentPatent.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentHonor.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentOrganization.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentProject.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentPublication.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinAccomplishmentTest.destroy({
            where: {
                lid: linkedin.id,
            }
        })

        await ctx.model.LinkedinInfo.destroy({
            where: {
                id
            }
        })
    }

    async crawl_log(username, message, page) {
        const { ctx, config } = this

        const month = pad(2, moment().month() + 1, '0')
        const path = `${config.baseDir}${config.upload_linkedin_screenshot}/${moment().year()}/${month}`

        await fse.mkdirp(path)

        const fullpath = `${path}/${random_string.generate(8)}.png`
        await page.screenshot({ path: fullpath })

        await ctx.model.LinkedinCrawlLog.create({
            username,
            message,
            screenshot: fullpath.replace(config.baseDir, ''),
            created_at: ctx.helper.now(),
        })
    }

    async batch_update_gender() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const infos = await ctx.model.LinkedinInfo.findAll({
            where: {
                gender: '',
                avatar_small: {
                    [Op.ne]: '',
                },
            }
        })

        for (let info of infos) {
            try {
                const res = await ctx.curl(config.faceplusplus.url, {
                    method: 'POST',
                    dataType: 'json',
                    contentType: 'application/x-www-form-urlencoded',
                    data: {
                        api_key: config.faceplusplus.api_key,
                        api_secret: config.faceplusplus.api_secret,
                        image_url: `https://static.chasedream.com${info.avatar_small.replace('/upload', '')}`,
                        return_attributes: 'gender',
                    },
                    timeout: 5000,
                })

                if (res.data.error_message === undefined && res.data.face_num) {
                    const face = res.data.faces[0]
                    const gender = face.attributes.gender.value

                    await ctx.model.LinkedinInfo.update({
                        gender
                    }, {
                        where: {
                            id: info.id,
                        }
                    })
                } else {
                    await ctx.model.LinkedinInfo.update({
                        gender: 'N/A'
                    }, {
                        where: {
                            id: info.id,
                        }
                    })
                }
            } catch (err) { }

            await ctx.helper.timeout(2000)
        }
    }

    async get_avatar() {
        const { ctx, app, config } = this

        const params = ctx.params
        let avatar_small = ''
        let avatar_big = ''

        const userinfo = await ctx.model.LinkedinInfo.findOne({
            where: {
                id: params.id,
            },
            raw: true,
        })

        const browser = await puppeteer.launch({
            defaultViewport: { width: config.linkedin.browser.width, height: config.linkedin.browser.height },
            ignoreHTTPSErrors: true,
            args: [`--window-size=${config.linkedin.browser.width},${config.linkedin.browser.height}`, '--no-sandbox', '--disable-setuid-sandbox'],
            headless: config.env === 'prod' ? true : false
        })

        const page = await browser.newPage()
        await page.setUserAgent(config.linkedin.useragent)

        try {
            const cookies = await app.redis.get(`linkedin_account_cookie:${config.linkedin.account.username}`)
            if (!cookies) {
                await page.goto('https://www.linkedin.com/login/', { waitUntil: 'domcontentloaded' })

                await page.waitForSelector('#username')
                await page.waitForSelector('#password')
                await page.waitForSelector('.login__form_action_container button')

                const username_input = await page.$("#username")
                await username_input.type(config.linkedin.account.username, { delay: 50 })

                const password_input = await page.$("#password")
                await password_input.type(config.linkedin.account.password, { delay: 50 })

                await page.click('.login__form_action_container button')
                await page.waitForTimeout(2000)

                let pin = ''
                const url = await page.url()

                if (url.includes('checkpoint/challenge')) {
                    await this.crawl_log(config.linkedin.account.username, '登录账号需要输入验证码', page)

                    for (let i = 0; i < 150; i++) {
                        await ctx.helper.timeout(2000)

                        pin = await app.redis.get(`linkedin_account_pin:${config.linkedin.account.username}`) || ''

                        if (pin) {
                            await app.redis.del(`linkedin_account_pin:${config.linkedin.account.username}`)
                            break
                        }
                    }

                    const pin_input = await page.$(".input_verification_pin")
                    await pin_input.type(pin)

                    await page.click('#email-pin-submit-button')
                    await page.waitForTimeout(3000)
                }

                let cookie = await page.cookies()
                await app.redis.setex(`linkedin_account_cookie:${config.linkedin.account.username}`, 60 * 60 * 24 * 3, JSON.stringify(cookie))
                await this.crawl_log(config.linkedin.account.username, '正在登录账号', page)
            } else {
                await page.setCookie(...JSON.parse(cookies))
                await this.crawl_log(config.linkedin.account.username, '使用Cookie登录账号', page)
            }

            await page.goto(`https://www.linkedin.com/in/${userinfo.uid}/`, { waitUntil: 'domcontentloaded' })
            await page.waitForTimeout(3000)

            await this.crawl_log(config.linkedin.account.username, '跳转到目标URL', page)
            if ((await page.url()).includes('/in/unavailable/')) throw new Error('无效链接')

            let html = await page.content()
            let $ = cheerio.load(html)

            if ($('.global-nav__me-photo').length === 0) {
                const message = '没获取到用户登录状态'

                await app.redis.del(`linkedin_account_cookie:${config.linkedin.account.username}`)
                await this.crawl_log(config.linkedin.account.username, message, page)
                throw new Error(message)
            }

            const reg_btn = $('.join-form .join-form__form-body-submit-button')
            if (reg_btn.length) {
                await app.redis.del(`linkedin_account_cookie:${config.linkedin.account.username}`)
                await this.crawl_log(config.linkedin.account.username, '登录失败', page)
                throw new Error('登录失败')
            }

            const src = $('button.presence-entity__image img').prop('src')

            if (src && !src.includes('data:image')) {
                let gender = undefined
                avatar_small = await this.download_image(browser, src, config.upload_linkedin)

                if (avatar_small) {
                    try {
                        const res = await ctx.curl(config.faceplusplus.url, {
                            method: 'POST',
                            dataType: 'json',
                            contentType: 'application/x-www-form-urlencoded',
                            data: {
                                api_key: config.faceplusplus.api_key,
                                api_secret: config.faceplusplus.api_secret,
                                image_url: `https://static.chasedream.com${avatar_small.replace('/upload', '')}`,
                                return_attributes: 'gender',
                            },
                            timeout: 5000,
                        })

                        if (res.data.error_message === undefined && res.data.face_num) {
                            const face = res.data.faces[0]
                            gender = face.attributes.gender.value
                        }
                    } catch (err) { }
                }

                let avatar = $('.pv-top-card--photo')
                if (avatar.length) {
                    await page.click('.pv-top-card--photo')
                } else {
                    await page.click('.pv-top-card--photo__v2')
                }

                await page.waitForTimeout(2000)

                html = await page.content()
                $ = cheerio.load(html)

                if (!html.includes('Something went wrong')) {
                    avatar_big = $('.pv-member-photo-modal__profile-image').prop('src') || ''
                    avatar_big = await this.download_image(browser, avatar_big, config.upload_linkedin)

                    await ctx.model.LinkedinInfo.update({
                        gender,
                        avatar_small,
                        avatar_big,
                        updated_at: ctx.helper.now(),
                    }, {
                        where: {
                            id: params.id,
                        },
                    })

                    await page.click('.artdeco-modal__dismiss')
                } else {
                    throw new Error('Something went wrong')
                }
            }
        } catch (err) {
            ctx.logger.error(err.message)
        } finally {
            await this.crawl_log(config.linkedin.account.username, `https://www.linkedin.com/in/${userinfo.uid}/ 更新完成`, page)
            await browser.close()
        }

        return {
            avatar_small,
            avatar_big,
        }
    }

    async fix_org_edu() {
        const { ctx, app, config } = this

        let infos = await ctx.model.LinkedinInfo.findAll({
            where: {
                cur_work_id: 0,
            },
            raw: true,
        })

        for (const info of infos) {
            const work = await ctx.model.LinkedinWorkExperience.findOne({
                where: {
                    lid: info.id,
                },
                order: [
                    ['id', 'ASC'],
                ],
                raw: true,
            })

            if (work) {
                await ctx.model.LinkedinInfo.update({
                    cur_work_id: work.id,
                }, {
                    where: {
                        id: info.id
                    }
                })
            }
        }

        infos = await ctx.model.LinkedinInfo.findAll({
            where: {
                cur_edu_id: 0,
            },
            raw: true,
        })

        for (const info of infos) {
            const edu = await ctx.model.LinkedinEducation.findOne({
                where: {
                    lid: info.id,
                },
                order: [
                    ['id', 'ASC'],
                ],
                raw: true,
            })

            if (edu) {
                await ctx.model.LinkedinInfo.update({
                    cur_edu_id: edu.id,
                }, {
                    where: {
                        id: info.id
                    }
                })
            }
        }
    }
}

module.exports = LinkedinService
