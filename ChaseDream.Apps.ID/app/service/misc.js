const _ = require('lodash')
const path = require('path')
const fse = require('fs-extra')
const uuidv1 = require('uuid/v1')
const fileregex = require('file-regex')
const pad = require('pad')
const await = require('await-stream-ready/lib/await')
const Service = require('egg').Service

class MiscService extends Service {
    async register_forum() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const users = await ctx.model.MockUser.findAll({
            where: {
                status: 0,
            },
            limit: 1,
        })

        if (users.length) {
            const min = 1 * 1000 * 60
            const max = 59 * 1000 * 60
            const timeout = ctx.helper.get_random_between(min, max)
            await ctx.helper.timeout(timeout)
        }

        for (const user of users) {
            try {
                const exist = await ctx.service.auth.username_exist(user.username)

                if (!exist) {
                    let mobile = await ctx.model.Mobile.findOne({
                        where: {
                            mobile: {
                                [Op.like]: `1373269%`,
                            }
                        },
                        order: [
                            ['mobile', 'DESC'],
                        ],
                    })

                    if (!mobile) {
                        mobile = `13732690001`
                    } else {
                        mobile = ++mobile.mobile
                    }

                    let email = await ctx.model.TopPostUser.findOne({
                        where: {
                            username: {
                                [Op.like]: `cd--%`,
                            }
                        },
                        order: [
                            ['id', 'DESC'],
                        ],
                    })

                    let max = email.username.replace('cd--', '').replace('@cd.com', '')
                    ++max
                    email = `cd--${pad(3, max, '0')}@cd.com`

                    const pwd = ctx.helper.randomstring(10)
                    await ctx.service.auth.verify_register(86, mobile, '', user.username, pwd, email, '', '', '', '', '', '', '9')

                    await ctx.model.TopPostUser.create({
                        username: email,
                        nickname: user.username,
                        password: ctx.helper.encode_dz_string(pwd),
                        status: 1,
                    })
                }
            } catch (err) {
                console.log(err.message)
            } finally {
                await ctx.model.MockUser.update({
                    status: -1,
                }, {
                    where: {
                        id: user.id
                    }
                })
            }
        }
    }

    async has_contract_stamp(uid, stamp_id) {
        const { ctx } = this

        const stamp = await ctx.model.ContractStampsRoles.findOne({
            where: {
                uid,
                stamp_id,
            },
            raw: true,
        })

        return !!(stamp && stamp.approve)
    }

    async contract_stamp_create() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const contract = await ctx.model.ContractStamp.findOne({
            include: [{
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'raw_att',
            }, {
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'final_att',
            }],
            where: {
                status: 0,
                stamp: {
                    [Op.gt]: 0
                }
            },
        })

        if (!contract) return

        await ctx.model.ContractStamp.update({
            status: 1,
        }, {
            where: {
                id: contract.id,
            }
        })

        let pdf_file = ''

        if (['.doc', '.docx'].includes(contract.raw_att.ext)) {
            const word = `${config.baseDir}/upload${contract.raw_att.fullpath}`
            const pdf = word.substring(0, word.lastIndexOf('/'))

            await ctx.helper.convert_word_to_pdf(word, pdf)

            pdf_file = `${word.substring(0, word.lastIndexOf('.'))}.pdf`
        } else {
            pdf_file = `${config.baseDir}/upload${contract.raw_att.fullpath}`
        }

        const output_file = pdf_file.replace('.pdf', '.final.pdf')
        const tmp_path = `${pdf_file.substring(0, pdf_file.lastIndexOf('/'))}/tmp`

        await fse.remove(output_file)
        await fse.mkdirp(tmp_path)

        await ctx.helper.pdf_to_images(pdf_file, tmp_path, 'idx', 'png')

        let images = await fileregex(tmp_path, /idx.*\.png$/g)
        for (const img of images) {
            const source = `${img.dir}/${img.file}`
            const target = `${img.dir}/${img.file.replace('.png', `.${ctx.helper.randomstring(12)}.png`)}`
            fse.moveSync(source, target)
        }

        await ctx.model.ContractStamp.update({
            status: 2,
        }, {
            where: {
                id: contract.id,
            }
        })
    }

    async send_notice(id, title, message) {
        const { ctx } = this

        try {
            const user = await ctx.model.User.findOne({
                where: {
                    id
                },
                raw: true,
            })

            const member = await ctx.model.Member.findOne({
                where: {
                    username: user.username
                },
                raw: true,
            })

            await ctx.curl('http://push.chasedream.com/api/v1/notification/system', {
                method: 'POST',
                dataType: 'json',
                data: {
                    title,
                    message,
                    uid: member.forum_uid,
                    payload: {
                        type: 'system',
                        msgfromid: 0,
                        msgfrom: ''
                    }
                }
            })
        } catch (err) {
            ctx.logger.error(err.message)
        }
    }

    async contract_stamp_approve() {
        const { ctx, config } = this

        const contract = await ctx.model.ContractStamp.findOne({
            include: [{
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'raw_att',
            }, {
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'final_att',
            }],
            where: {
                status: 4,
            },
        })

        if (!contract) return

        await ctx.model.ContractStamp.update({
            status: 5,
        }, {
            where: {
                id: contract.id,
            }
        })

        const stamp = config.contract_stamp.stamps.find((stamp) => {
            return stamp.id === contract.stamp
        })

        const stamp_file = `${config.baseDir}${config.upload_contract}/stamp/${stamp.file}`

        contract.stamp_positons = contract.stamp_positons && JSON.parse(contract.stamp_positons)

        const ext = path.extname(contract.raw_att.fullpath)

        let pdf_file = `${config.baseDir}/upload${contract.raw_att.fullpath}`
        if (ext === '.docx' || ext === '.doc') {
            pdf_file = pdf_file.replace(ext, '.pdf')
        }

        let output_file = `${config.baseDir}/upload${contract.raw_att.fullpath}`.replace(ext, '.final.pdf')

        const tmp_path = `${pdf_file.substring(0, pdf_file.lastIndexOf('/'))}/tmp`

        let images = await fileregex(tmp_path, /idx.*\.png$/g)
        images.sort((a, b) => {
            a = a.file.split('.')[1]
            b = b.file.split('.')[1]
            return a - b
        })

        await ctx.helper.crop_stamp(stamp_file, images.length, tmp_path)

        let croped = await fileregex(tmp_path, /stamp_.*\.png$/g)
        croped.sort((a, b) => {
            a = a.file.split('.')[1]
            b = b.file.split('.')[1]
            return a - b
        })

        images = images.map(row => { return `${row.dir}/${row.file}` })
        croped = croped.map(row => { return `${row.dir}/${row.file}` })

        await ctx.helper.make_pdf(images, croped, stamp_file, output_file, tmp_path, contract.stamp_positons, contract.cross_page_y)

        output_file = output_file.replace('.final.pdf', '')

        const attach = await ctx.model.ContractStampAttachment.create({
            uid: contract.approve_uid,
            original: output_file.substring(output_file.lastIndexOf('/') + 1),
            fullpath: output_file.replace(config.baseDir, '').replace('/upload/', '/'),
            ext,
            type: contract.raw_att.type,
            created_at: ctx.helper.now(),
        })

        await ctx.model.ContractStamp.update({
            final_aid: attach.id,
            status: 6,
        }, {
            where: {
                id: contract.id,
            }
        })

        const title = '【合同盖章提示】'
        const message = `你申请的【文件名】已完成盖章，请查看。`

        await this.send_notice(contract.apply_uid, title, message)
    }

    async tag_create(tid, name, property) {
        const { ctx } = this

        const uuid = uuidv1().replace(/-/gi, '')

        const tag = await ctx.model.Tag.Tag.create({
            name,
            synonym_id: uuid,
            main: 1,
            property,
        })

        await this.tag_bind(tid, tag.id)
    }

    async tag_bind(tid, tagid) {
        const { ctx } = this

        const exist = await ctx.model.Tag.TagThread.findOne({
            where: {
                tid,
                tagid,
            }
        })

        if (exist) return

        const thread = await ctx.forumModel.ForumThread.findOne({
            where: {
                tid
            }
        })

        if (!thread) return

        await ctx.model.Tag.TagThread.create({
            tid,
            fid: thread.fid,
            tagid,
        })

        const thread_tag = await ctx.model.Tag.ThreadTag.findOne({
            where: {
                tid,
            },
            raw: true,
        })

        const tag = await ctx.model.Tag.Tag.findOne({
            where: {
                id: tagid,
            },
            raw: true,
        })

        let tags = thread_tag ? thread_tag.tags.split(',') : []
        tags.push(tag.name)
        tags = tags.filter(e => e)

        if (tags.length === 1) {
            await ctx.model.Tag.ThreadTag.create({
                tid,
                tags: tags.join(',')
            })
        } else {
            await ctx.model.Tag.ThreadTag.update({
                tags: tags.join(',')
            }, {
                where: {
                    tid,
                }
            })
        }
    }

    async tag_delete(tid, tagid) {
        const { ctx } = this

        await ctx.model.Tag.TagThread.destroy({
            where: {
                tid,
                tagid,
            }
        })

        const thread_tag = await ctx.model.Tag.ThreadTag.findOne({
            where: {
                tid,
            },
            raw: true,
        })

        const tag = await ctx.model.Tag.Tag.findOne({
            where: {
                id: tagid,
            },
            raw: true,
        })

        const tags = thread_tag.tags.split(',')
        _.pull(tags, tag.name)

        await ctx.model.Tag.ThreadTag.update({
            tags: tags.join(',')
        }, {
            where: {
                tid,
            }
        })

        if (tags.join(',').length <= 0) {
            await ctx.model.Tag.ThreadTag.destroy({
                where: {
                    tid
                }
            })
        }
    }
}

module.exports = MiscService
