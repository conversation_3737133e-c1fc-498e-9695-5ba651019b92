const moment = require('moment-timezone')
const Service = require('egg').Service

class RecruitService extends Service {
    async thread_create(body) {
        const { ctx } = this

        const form = await ctx.model.RecruitForm.findOne({
            where: {
                id: body.form_id
            },
            raw: true
        })

        ctx.error(!form, "invalid form id")

        body.form_subject = form.subject

        const tags = []

        for (const item of ['wechat', 'qq', 'test_date', 'test_plan', 'city', 'status', 'bachelor', 'industry', 'appeal', 'ps']) {
            !!form[item] ? tags.push(`recruit_${item}`) : ""
        }

        const values = await ctx.model.SystemSettings.findAll({
            attributes: ['value'],
            where: {
                name: tags
            },
            raw: true
        }).map(row => { return row.value })

        const user = await ctx.model.RecruitUser.findOne({
            where: {
                id: body.sender_uid,
            },
            raw: true,
        })

        const wrapper = await ctx.model.SystemSettings.findOne({
            where: {
                name: 'recruit_wrapper',
            },
            raw: true,
        })

        body.username = user.username
        body.password = ctx.helper.decode_dz_string(user.password)

        let form_content = values.join(' ')

        body.content = '占位'
        body.htmlon = 1
        body.hiddenreplies = 1

        const res = await ctx.service.thread.send_without_update(body)

        body.tid = res.Variables.tid
        body.pid = res.Variables.pid

        const recruit_thread = await ctx.model.RecruitThread.create(body)

        form_content = form_content + `<input name="recruit_thread_id" value="${ctx.helper.encode_dz_string(recruit_thread.id)}" type="hidden">`
        form_content = wrapper.value.replace('{{content}}', form_content)
        body.content = body.thread_html.replace(`[Form${body.form_id}]`, form_content)

        const chasedream = await ctx.model.RecruitUser.findOne({
            where: {
                username: '<EMAIL>'
            },
            raw: true,
        })

        body.username = chasedream.username
        body.password = ctx.helper.decode_dz_string(chasedream.password)
        body.htmlon = 1

        await ctx.service.thread.update(body)

        return recruit_thread
    }

    async thread_update(body) {
        const { ctx } = this

        const form = await ctx.model.RecruitForm.findOne({
            where: {
                id: body.form_id
            },
            raw: true
        })

        ctx.error(!form, "invalid form id")

        body.form_subject = form.subject

        const tags = []

        for (const item of ['wechat', 'qq', 'test_date', 'test_plan', 'city', 'status', 'bachelor', 'industry', 'appeal', 'ps']) {
            !!form[item] ? tags.push(`recruit_${item}`) : ""
        }

        const values = await ctx.model.SystemSettings.findAll({
            attributes: ['value'],
            where: {
                name: tags
            },
            order: [
                ['id', 'ASC'],
            ],
            raw: true
        }).map(row => { return row.value })

        const user = await ctx.model.RecruitUser.findOne({
            where: {
                username: '<EMAIL>',
            },
            raw: true,
        })

        const wrapper = await ctx.model.SystemSettings.findOne({
            where: {
                name: 'recruit_wrapper',
            },
            raw: true,
        })

        body.username = user.username
        body.password = ctx.helper.decode_dz_string(user.password)

        const recruit_thread = await ctx.model.RecruitThread.findOne({
            where: {
                id: body.id
            },
            raw: true,
        })

        let form_content = values.join(' ')
        form_content = form_content + `<input name="recruit_thread_id" value="${ctx.helper.encode_dz_string(recruit_thread.id)}" type="hidden">`
        form_content = wrapper.value.replace('{{content}}', form_content)

        body.content = body.thread_html.replace(`[Form${body.form_id}]`, form_content)
        body.htmlon = 1
        body.hiddenreplies = 1
        body.tid = recruit_thread.tid
        body.pid = recruit_thread.pid
        body.fid = recruit_thread.fid

        const chasedream = await ctx.model.RecruitUser.findOne({
            where: {
                username: '<EMAIL>'
            },
            raw: true,
        })

        body.username = chasedream.username
        body.password = ctx.helper.decode_dz_string(chasedream.password)

        await ctx.service.thread.update(body)

        await ctx.model.RecruitThread.update({
            subject: body.subject,
            thread_html: body.thread_html,
            form_id: form.id,
            form_subject: form.subject,
            owner_uid: body.owner_uid,
            owner_name: body.owner_name,
            owner_html: body.owner_html,
        }, {
            where: { id: body.id }
        })
    }

    async recruit_detail_create(body) {
        const { ctx, app, config } = this

        const form = await ctx.model.RecruitForm.findOne({
            where: {
                id: body.form_id
            }
        })

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: body.uid,
            },
            raw: true,
        })

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                uid: member.id
            },
            raw: true,
        })

        const recruit_thread = await ctx.model.RecruitThread.findOne({
            where: {
                id: body.thread_id,
            },
            raw: true,
        })

        const auth = ctx.cookies.get(`${config.discuz.prefix}auth`, {
            httpOnly: true,
            signed: false,
        })
        const saltkey = ctx.cookies.get(`${config.discuz.prefix}saltkey`, {
            signed: false,
        })

        const cookie = `${config.discuz.prefix}auth=${auth};${config.discuz.prefix}saltkey=${saltkey};`

        let res = await ctx.curl(`${config.discuz.home_url}/api/mobile/index.php?mobile=no&version=1&module=profile&do=profile`, {
            method: 'GET',
            dataType: 'json',
            headers: {
                'cookie': cookie,
            },
            timeout: 5000,
        })

        body.formhash = res.data.Variables.formhash
        body.tid = recruit_thread.tid

        const owner = await ctx.model.RecruitOwner.findOne({
            where: {
                id: recruit_thread.owner_uid,
            },
            raw: true,
        })

        let template = ''
        if (owner.type == 0 || owner.type == 1) {
            template = await ctx.model.SystemSettings.findOne({
                where: {
                    name: 'owner_template_0_1',
                },
                raw: true,
            })

            template = template.value.replace('[owner_type]', owner.type == 0 ? '微信' : 'QQ').replace('[owner_number]', owner.account).replace('[owner_url]', owner.qr_code)
        } else if (owner.type == 2) {
            template = await ctx.model.SystemSettings.findOne({
                where: {
                    name: 'owner_template_2',
                },
                raw: true,
            })

            template = template.value.replace('[owner_url]', owner.qr_code)
        }

        recruit_thread.owner_html = recruit_thread.owner_html.replace(`[admin${recruit_thread.owner_uid}]`, template)

        for (const item of ['wechat', 'qq', 'test_date', 'test_plan', 'city', 'status', 'bachelor', 'industry', 'appeal', 'ps']) {
            const tag = `[${item}]`
            if (recruit_thread.owner_html.includes(tag)) {
                if (tag === '[ps]') {
                    recruit_thread.owner_html = recruit_thread.owner_html.replace(tag, ctx.helper.exclude_special_with_enter(body[item]))
                } else if (tag === '[test_date]') {
                    const txt = body[item] === '1970-01-01' ? '待定' : ctx.helper.exclude_special(body[item])
                    recruit_thread.owner_html = recruit_thread.owner_html.replace(tag, txt)
                } else {
                    recruit_thread.owner_html = recruit_thread.owner_html.replace(tag, ctx.helper.exclude_special(body[item]))
                }
            }
        }

        if (recruit_thread.owner_html.includes('[username]')) {
            recruit_thread.owner_html = recruit_thread.owner_html.replace('[username]', member.username)
        }

        body.message = recruit_thread.owner_html

        const pid = await ctx.service.thread.send_post(cookie, body)

        let ip_location = ''
        let forum_data = ''

        try {
            const ip_data = await ctx.curl(`${config.ip_location.aliyun.url}${ctx.ip}`, {
                method: 'GET',
                dataType: 'json',
                headers: {
                    'Authorization': `APPCODE ${config.ip_location.aliyun.APPCODE}`,
                },
                timeout: 2000,
            })

            const nation = ip_data.data.ENTITY.INPUT_IP_ADDRESS.NATION || ''
            const province = ip_data.data.ENTITY.INPUT_IP_ADDRESS.PROVINCE || ''
            const city = ip_data.data.ENTITY.INPUT_IP_ADDRESS.CITY || ''

            ip_location = `${nation}-${province}-${city}`

            const cm = await ctx.forumModel.CommonMember.findOne({
                where: {
                    uid: body.uid
                }
            })

            const cmc = await ctx.forumModel.CommonMemberCount.findOne({
                attributes: ['threads', 'posts', 'friends'],
                where: {
                    uid: body.uid
                },
                raw: true
            }) || {
                threads: 0,
                posts: 0,
                friends: 0,
            }

            forum_data = `${ctx.helper.format_date(moment.unix(cm.regdate))}|${cmc.threads}|${cmc.posts - cmc.threads}|${cmc.friends}|${cm.credits}`

        } catch (err) {
            ctx.logger.error(err.message)
        }

        const status2 = `${(ctx.helper.exclude_special(body.status2) || '')}`

        await ctx.model.RecruitDetail.create({
            tid: recruit_thread.tid,
            pid: pid,
            form_id: form.id,
            thread_id: body.thread_id,
            form_subject: form.subject,
            type: form.type,
            uid: member.forum_uid,
            username: member.username,
            mobile: `${mobile.area_code}-${mobile.mobile}`,
            wechat: ctx.helper.exclude_special(body.wechat) || '',
            qq: ctx.helper.exclude_special(body.qq) || '',
            test_date: ctx.helper.exclude_special(body.test_date) || '',
            test_plan: ctx.helper.exclude_special(body.test_plan) || '',
            city: ctx.helper.exclude_special(body.city) || '',
            status1: ctx.helper.exclude_special(body.status1) || '',
            status2,
            status3: '',
            status4: ctx.helper.exclude_special(body.status4) || '',
            country_id: parseInt(body.country_id || 0, 10),
            province_id: parseInt(body.province_id || 0, 10),
            city_id: parseInt(body.city_id || 0, 10),
            ps: ctx.helper.exclude_special_with_enter(body.ps) || '',
            bachelor: ctx.helper.exclude_special_with_enter(body.bachelor) || '',
            industry: ctx.helper.exclude_special_with_enter(body.industry) || '',
            appeal: ctx.helper.exclude_special_with_enter(body.appeal) || '',
            ip: ctx.ip,
            ip_location,
            forum_data,
        })

        await ctx.forumModel.ForumPost.update({
            useip: ctx.ip,
        }, {
            where: {
                pid,
            }
        })

        await ctx.forumModel.CommonMemberStatus.update({
            lastip: ctx.ip,
        }, {
            where: {
                uid: member.forum_uid,
            }
        })

        const form_total = await ctx.model.RecruitDetail.count({
            where: {
                form_id: form.id,
            },
            raw: true,
        })

        const form_new = await ctx.model.RecruitDetail.count({
            where: {
                form_id: form.id,
                opt: 0,
            },
            raw: true,
        })

        const thread_count = await ctx.model.RecruitDetail.count({
            where: {
                thread_id: body.thread_id,
            },
            raw: true,
        })

        await ctx.model.RecruitThread.update({
            count: thread_count
        }, {
            where: {
                id: body.thread_id,
            }
        })

        await ctx.model.RecruitForm.update({
            total: form_total,
            new: form_new,
        }, {
            where: {
                id: form.id,
            }
        })

        if (config.env === 'prod') {
            await app.memcached.flushAsync()
        }

        const url = `https://forum.chasedream.com/forum.php?mod=redirect&goto=findpost&ptid=${recruit_thread.tid}&pid=${pid}&fromuid=${body.uid}`

        return url
    }

    async detail_opt(body) {
        const { ctx } = this

        await ctx.model.RecruitDetail.update({
            opt: body.opt,
            reason_code: body.reason_code,
            reason: body.reason,
        }, {
            where: {
                id: body.id
            }
        })

        const detail = await ctx.model.RecruitDetail.findOne({
            where: {
                id: body.id,
            },
            raw: true,
        })

        const form_new = await ctx.model.RecruitDetail.count({
            where: {
                form_id: detail.form_id,
                opt: 0,
            },
            raw: true,
        })

        await ctx.model.RecruitForm.update({
            new: form_new,
        }, {
            where: {
                id: detail.form_id,
            }
        })

        if (body.opt == 3 || body.opt == 4 || body.opt == 6) {
            const uc_member = await ctx.forumModel.UcenterMember.findOne({
                where: {
                    username: detail.username,
                },
                raw: true,
            }) || {}

            const entity = {
                username: detail.username,
                mobile: detail.mobile,
                email: uc_member.email,
                desc: detail.reason,
                record_code: detail.reason_code,
                record_type: body.record_type,
                operator: ctx.locals.user.username,
            }

            const behavior = await ctx.model.Behavior.create(entity)

            await ctx.model.BehaviorOpt.create({
                behavior_id: behavior.id,
                opt_id: detail.opt == 3 ? 6 : 7,
                opt_text: detail.opt == 3 ? '拒绝入群' : '永久禁加群',
            })
        }
    }

    async detail_report(body) {
        const { ctx } = this

        let i = 0
        let report = ''

        const detail = await ctx.model.RecruitDetail.findOne({
            where: {
                id: body.id,
            }
        })

        const usernames = await ctx.model.RecruitDetail.findAll({
            where: {
                username: detail.username,
            },
            raw: true,
        }) || []

        report += `论坛名：${detail.username}\n`
        for (const username of usernames) {
            report += this.color_recruit_opt(username.opt, `${++i}、${ctx.helper.format_date(moment(username.created_at), 'YYYY-MM-DD HH:mm')} ${username.form_subject} ${username.username} ${username.wechat} ${this.format_recruit_opt(username.opt)}\n`)
        }
        i = 0

        const ips = await ctx.model.RecruitDetail.findAll({
            where: {
                ip: detail.ip,
            },
            raw: true,
        }) || []

        report += `${'-'.repeat(30)}\nIP：${detail.ip}\n`
        for (const ip of ips) {
            report += this.color_recruit_opt(ip.opt, `${++i}、${ctx.helper.format_date(moment(ip.created_at), 'YYYY-MM-DD HH:mm')} ${ip.form_subject} ${ip.username} ${ip.wechat} ${this.format_recruit_opt(ip.opt)}\n`)
        }
        i = 0

        const wechats = await ctx.model.RecruitDetail.findAll({
            where: {
                wechat: detail.wechat,
            },
            raw: true,
        }) || []

        report += `${'-'.repeat(30)}\n微信：${detail.wechat}\n`

        for (const wechat of wechats) {
            report += this.color_recruit_opt(wechat.opt, `${++i}、${ctx.helper.format_date(moment(wechat.created_at), 'YYYY-MM-DD HH:mm')} ${wechat.form_subject} ${wechat.username} ${wechat.wechat} ${this.format_recruit_opt(wechat.opt)}\n`)
        }
        i = 0

        const crimes = await ctx.model.Behavior.findAll({
            where: {
                username: detail.username,
            },
            raw: true,
        }) || []

        report += `\n论坛用户违规记录：\n\n`
        for (const crime of crimes) {
            report += `${ctx.helper.format_date(moment(crime.created_at), 'YYYY-MM-DD')}\n`
            report += `${crime.desc}\n\n`
        }
        i = 0

        await ctx.model.RecruitDetail.update({
            report
        }, {
            where: {
                id: body.id,
            }
        })

        return report
    }

    format_recruit_opt(opt) {
        let result = '未知'
        switch (opt) {
            case 0:
                result = '未'
                break
            case 1:
                result = '完'
                break
            case 2:
                result = '略'
                break
            case 3:
                result = '拒'
                break
            case 4:
                result = '封'
                break
            case 5:
                result = '待'
                break
            case 6:
                result = '踢'
                break
        }

        return result
    }

    color_recruit_opt(opt, str) {
        let result = ''
        switch (opt) {
            case 0:
                result = `<p style="color:#333">${str}</p>`
                break
            case 1:
                result = `<p style="color:#76be86">${str}</p>`
                break
            case 2:
                result = `<p style="color:#999">${str}</p>`
                break
            case 3:
                result = `<p style="color:#e35454">${str}</p>`
                break
            case 4:
                result = `<p style="color:#e35454">${str}</p>`
                break
            case 5:
                result = `<p style="color:#333">${str}</p>`
                break
            case 6:
                result = `<p style="color:#e35454">${str}</p>`
                break
        }

        return result
    }
}

module.exports = RecruitService
