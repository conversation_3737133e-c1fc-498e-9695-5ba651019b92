module.exports = (options, app) => {
    return async (ctx, next) => {
        const { path, method } = ctx
        const config = app.config

        const userinfo = config.env === 'prod' ? ctx.helper.get_userinfo_from_cookie() : [0, config.dev.uid, 0]

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: userinfo[1],
            }
        })
        ctx.error(!member, "Forbidden")

        let user = await ctx.model.User.findOne({
            where: {
                username: member.username
            },
            raw: true
        })
        ctx.error(!user, "Forbidden")

        let role = await app.zrole.getRolesForUser(`${user.id}_user`)
        role = Array.from(role).map(ele => {
            return ele.substring(0, ele.indexOf('_'))
        })

        role = await ctx.model.Role.findAll({
            attributes: ['id', 'name'],
            where: {
                id: role
            },
            raw: true
        })

        let role_name = ''
        if (role.length) {
            role_name = role.map(row => row.name).join(',')
            user.role = role.map(row => row.id)
        }

        if (role_name.indexOf(config.super_admin) > -1) {
            user.role_name = role_name
            user.permissions = user.permissions && JSON.parse(user.permissions) || ''

            ctx.locals.user = user
            await next()
        } else {
            const auth = await app.zrole.enforce(`${user.id}_user`, path.replace(config.api_prefix, ''), method.toUpperCase())
            if (auth) {
                user.role_name = role_name
                user.permissions = user.permissions && JSON.parse(user.permissions) || ''

                ctx.locals.user = user
                await next()
            } else {
                ctx.status = 403
                ctx.body = 'Forbidden'
            }
        }
    }
}
