const {VError} = require("verror")

module.exports = (options, app) => {
    return async (ctx, next) => {
        try {
            await next()
        } catch (e) {
            if (e instanceof app.jwt.UnauthorizedError || e.name === 'JsonWebTokenError' || e.name === 'TokenExpiredError') {
                ctx.status = 401
                ctx.body = 'UnauthorizedError'
            } else if (e instanceof VError) {
                ctx.status = e.status;
                ctx.body = {
                    code: e.code,
                    msg: e.message,
                    errors: e
                }
            } else if (e.status && e.statusCode) {
                ctx.body = {
                    code: e.status,
                    msg: e.message,
                    errors: e
                }
            } else {
                ctx.body = {
                    code: 500,
                    msg: e.message || e,
                }
            }
        }
    }
}