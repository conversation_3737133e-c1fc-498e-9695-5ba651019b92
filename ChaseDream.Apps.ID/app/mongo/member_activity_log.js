module.exports = app => {
    const mongoose = app.mongoose
    const Schema = mongoose.Schema

    const MemberActivityLogSchema = new Schema({
        uid: Number,
        tid: Number,
        fid: Number,
        action_id: Number,
        created_at: Number,
    })

    MemberActivityLogSchema.index({ uid: 1, type: -1 })
    MemberActivityLogSchema.index({ tid: 1, type: -1 })
    MemberActivityLogSchema.index({ created_at: 1, type: -1 })

    return mongoose.model('member_activity_log', MemberActivityLogSchema)
}