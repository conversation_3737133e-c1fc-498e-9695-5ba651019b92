module.exports = app => {
    const {INTEGER, BO<PERSON>EAN, STRING, DATE, TEXT} = app.Sequelize

    return app.wwwModel.define('IwmsNews', {
        articleid: {
            type: INTEGER,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        content: {
            type: TEXT,
            allowNull: true
        },
        title: {
            type: STRING(200),
            allowNull: true
        },
        classid: {
            type: INTEGER,
            allowNull: true
        },
        Nkey: {
            type: STRING(255),
            allowNull: true
        },
        hits: {
            type: INTEGER,
            allowNull: true
        },
        dateandtime: {
            type: DATE,
            allowNull: true
        },
        dayHits: {
            type: INTEGER,
            allowNull: true
        },
        weekHits: {
            type: INTEGER,
            allowNull: true
        },
        remarkNum: {
            type: INTEGER,
            allowNull: true
        },
        imgNews: {
            type: BOOLEAN,
            allowNull: true
        },
        titleImg: {
            type: STRING(200),
            allowNull: true
        },
        topicid: {
            type: INTEGER,
            allowNull: true
        },
        summary: {
            type: TEXT,
            allowNull: true
        },
        highlight: {
            type: BOOLEAN,
            allowNull: true
        },
        permitGroups: {
            type: STRING(255),
            allowNull: true
        },
        memberid: {
            type: INTEGER,
            allowNull: true
        },
        checkUserid: {
            type: INTEGER,
            allowNull: true
        },
        aUrl: {
            type: STRING(255),
            allowNull: true
        },
        author: {
            type: STRING(50),
            allowNull: true
        },
        source: {
            type: STRING(50),
            allowNull: true
        },
        authorEmail: {
            type: STRING(100),
            allowNull: true
        },
        sourceUrl: {
            type: STRING(255),
            allowNull: true
        },
        remarkLink: {
            type: BOOLEAN,
            allowNull: true
        },
        allowRemark: {
            type: BOOLEAN,
            allowNull: true
        },
        price: {
            type: INTEGER,
            allowNull: true
        },
        markLimit: {
            type: INTEGER,
            allowNull: true
        },
        pageTitle: {
            type: TEXT,
            allowNull: true
        },
        pollIds: {
            type: STRING(255),
            allowNull: true
        },
        digg: {
            type: INTEGER,
            allowNull: true
        },
        diggWeek: {
            type: INTEGER,
            allowNull: true
        },
        editor: {
            type: STRING(100),
            allowNull: true
        }
    }, {
        tableName: 'iwms_news',
        timestamps: false,
    })
}
