module.exports = app => {
    const {INTEGER, BOOLEAN, STRING, DATE, TEXT} = app.Sequelize

    return app.wwwModel.define('IwmsClass', {
        classID: {
            type: INTEGER,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        class: {
            type: STRING(50),
            allowNull: true
        },
        orderID: {
            type: INTEGER,
            allowNull: true
        },
        articleNum: {
            type: INTEGER,
            allowNull: true
        },
        imgNewsNum: {
            type: INTEGER,
            allowNull: true
        },
        listStyle: {
            type: INTEGER,
            allowNull: true
        },
        newsAd: {
            type: TEXT,
            allowNull: true
        },
        headAd: {
            type: TEXT,
            allowNull: true
        },
        logo: {
            type: TEXT,
            allowNull: true
        },
        styleid: {
            type: INTEGER,
            allowNull: true
        },
        lastupdate: {
            type: DATE,
            allowNull: true
        },
        parentID: {
            type: INTEGER,
            allowNull: true
        },
        pPath: {
            type: STRING(255),
            allowNull: true
        },
        depth: {
            type: INTEGER,
            allowNull: true
        },
        child: {
            type: INTEGER,
            allowNull: true
        },
        canAdd: {
            type: BOOLEAN,
            allowNull: true
        },
        cTempid: {
            type: INTEGER,
            allowNull: true
        },
        newsTempID: {
            type: INTEGER,
            allowNull: true
        },
        cUrl: {
            type: STRING(255),
            allowNull: true
        },
        LastNode: {
            type: BOOLEAN,
            allowNull: true
        },
        cInBar: {
            type: BOOLEAN,
            allowNull: true
        },
        cInNav: {
            type: BOOLEAN,
            allowNull: true
        },
        cBindNum: {
            type: INTEGER,
            allowNull: true
        },
        color: {
            type: STRING(10),
            allowNull: true
        },
        newWin: {
            type: BOOLEAN,
            allowNull: true
        },
        allowGroups: {
            type: STRING(255),
            allowNull: true
        },
        addMark: {
            type: INTEGER,
            allowNull: true
        },
        inLeft: {
            type: BOOLEAN,
            allowNull: true
        },
        pageSize: {
            type: INTEGER,
            allowNull: true
        },
        metaDescription: {
            type: STRING(255),
            allowNull: true
        },
        sDir: {
            type: STRING(50),
            allowNull: true
        },
        rssFeed: {
            type: STRING(255),
            allowNull: true
        },
        staticPages: {
            type: INTEGER,
            allowNull: true
        },
        sideAd: {
            type: TEXT,
            allowNull: true
        },
        topAd: {
            type: TEXT,
            allowNull: true
        },
        footAd: {
            type: TEXT,
            allowNull: true
        },
        newsSideAd: {
            type: TEXT,
            allowNull: true
        },
        newsTopAd: {
            type: TEXT,
            allowNull: true
        },
        newsFootAd: {
            type: TEXT,
            allowNull: true
        },
        addVMoney: {
            type: INTEGER,
            allowNull: true
        },
        ipLimited: {
            type: TEXT,
            allowNull: true
        },
        ipLimit: {
            type: BOOLEAN,
            allowNull: true
        },
        cType: {
            type: INTEGER,
            allowNull: true
        },
        cssInBar: {
            type: STRING(20),
            allowNull: true
        },
        picFrame: {
            type: STRING(100),
            allowNull: true
        },
        cssFrame: {
            type: STRING(20),
            allowNull: true
        },
        metaKeyword: {
            type: STRING(255),
            allowNull: true
        },
        headlineSummary: {
            type: TEXT,
            allowNull: true
        },
        cBindSorts: {
            type: STRING(255),
            allowNull: true
        }
    }, {
        tableName: 'iwms_class',
        timestamps: false,
    })
}
