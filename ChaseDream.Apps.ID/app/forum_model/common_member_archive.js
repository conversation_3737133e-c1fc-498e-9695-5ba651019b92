module.exports = app => {
    const {STRING, INTEGER, CHAR} = app.Sequelize

    const CommonMemberArchive = app.forumModel.define("CommonMemberArchive", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        email: {
            type: CHAR(40),
            allowNull: false,
            defaultValue: ''
        },
        username: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: '',
            unique: true
        },
        password: {
            type: CHAR(32),
            allowNull: false,
            defaultValue: ''
        },
        status: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        emailstatus: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        avatarstatus: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        videophotostatus: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        adminid: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        groupid: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        groupexpiry: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        extgroupids: {
            type: CHAR(20),
            allowNull: false,
            defaultValue: ''
        },
        regdate: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        credits: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
        },
        notifysound: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        timeoffset: {
            type: CHAR(4),
            allowNull: false,
            defaultValue: ''
        },
        newpm: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        newprompt: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        accessmasks: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        allowadmincp: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        onlyacceptfriendpm: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        conisbind: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        avatar: {
            type: STRING(255),
            allowNull: true
        }
    }, {
        tableName: 'cc_common_member_archive',
        timestamps: false,
    })

    return CommonMemberArchive
}
