module.exports = app => {
    const { INTEGER } = app.Sequelize

    return app.forumModel.define('ForumAttachment', {
        aid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        tid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        pid: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        tableid: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        downloads: {
            type: INTEGER(8),
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_forum_attachment',
        timestamps: false,
    })
}
