module.exports = app => {
    const { INTEGER, STRING } = app.Sequelize

    return app.forumModel.define('ForumAttachment_7', {
        aid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
        },
        tid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        pid: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        filename: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        filesize: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        attachment: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        remote: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        description: {
            type: STRING(255),
            allowNull: false
        },
        readperm: {
            type: INTEGER(3).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        price: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        isimage: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        width: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        thumb: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        picid: {
            type: INTEGER(8),
            allowNull: false,
            defaultValue: '0'
        },
        height: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        thumbwidth: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        thumbheight: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_forum_attachment_7',
        timestamps: false,
    })
}
