module.exports = app => {
    const { STRING, INTEGER } = app.Sequelize

    const CommonSyscache = app.forumModel.define("CommonSyscache", {
        cname: {
            type: STRING(32),
            allowNull: false,
            primaryKey: true
        },
        ctype: {
            type: INTEGER(3).UNSIGNED,
            allowNull: false
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false
        },
        data: {
            type: "MEDIUMBLOB",
            allowNull: false
        }
    }, {
        tableName: 'cc_common_syscache',
        timestamps: false,
    })

    return CommonSyscache
}
