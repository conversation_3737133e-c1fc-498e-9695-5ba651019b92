module.exports = app => {
    const {INTEGER, CHAR} = app.Sequelize

    const CommonMemberStatusArchive = app.forumModel.define("CommonMemberStatusArchive", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
          },
          regip: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
          },
          lastip: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
          },
          lastvisit: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          lastactivity: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          lastpost: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          lastsendmail: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          invisible: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
          },
          buyercredit: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
          },
          sellercredit: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
          },
          favtimes: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          sharetimes: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          profileprogress: {
            type: INTEGER(2).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          }
    }, {
        tableName: 'cc_common_member_status_archive',
        timestamps: false,
    })

    return CommonMemberStatusArchive
}
