module.exports = app => {
    const {STRING, INTEGER} = app.Sequelize

    return app.forumModel.define('HomeFriend', {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0',
            primaryKey: true
        },
        fuid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0',
            primaryKey: true
        },
        fusername: {
            type: STRING(15),
            allowNull: false,
            defaultValue: ''
        },
        gid: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        num: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        note: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        }
    }, {
        tableName: 'cc_home_friend',
        timestamps: false,
    })
}
