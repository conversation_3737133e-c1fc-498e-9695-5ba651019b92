module.exports = app => {
    const {STRING, INTEGER, TEXT} = app.Sequelize

    const CommonMemberProfileArchive = app.forumModel.define("CommonMemberProfileArchive", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
          },
          realname: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          gender: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
          },
          birthyear: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          birthmonth: {
            type: INTEGER(3).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          birthday: {
            type: INTEGER(3).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          constellation: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          zodiac: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          telephone: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          mobile: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          idcardtype: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          idcard: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          address: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          zipcode: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          nationality: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          birthprovince: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          birthcity: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          birthdist: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
          },
          birthcommunity: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          resideprovince: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          residecity: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          residedist: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
          },
          residecommunity: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          residesuite: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          graduateschool: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          company: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          education: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          occupation: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          position: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          revenue: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          affectivestatus: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          lookingfor: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          bloodtype: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          height: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          weight: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          alipay: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          icq: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          qq: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          yahoo: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          msn: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          taobao: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          site: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          bio: {
            type: TEXT,
            allowNull: false
          },
          interest: {
            type: TEXT,
            allowNull: false
          },
          field1: {
            type: TEXT,
            allowNull: false
          },
          field2: {
            type: TEXT,
            allowNull: false
          },
          field3: {
            type: TEXT,
            allowNull: false
          },
          field4: {
            type: TEXT,
            allowNull: false
          },
          field5: {
            type: TEXT,
            allowNull: false
          },
          field6: {
            type: TEXT,
            allowNull: false
          },
          field7: {
            type: TEXT,
            allowNull: false
          },
          field8: {
            type: TEXT,
            allowNull: false
          }
    }, {
        tableName: 'cc_common_member_profile_archive',
        timestamps: false,
    })

    return CommonMemberProfileArchive
}
