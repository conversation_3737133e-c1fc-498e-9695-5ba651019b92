module.exports = app => {
    const {STRING, INTEGER, TEXT} = app.Sequelize

    const CommonMemberFieldForumArchive = app.forumModel.define("CommonMemberFieldForumArchive", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
          },
          publishfeed: {
            type: INTEGER(3),
            allowNull: false,
            defaultValue: '0'
          },
          customshow: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '26'
          },
          customstatus: {
            type: STRING(30),
            allowNull: false,
            defaultValue: ''
          },
          medals: {
            type: TEXT,
            allowNull: false
          },
          sightml: {
            type: TEXT,
            allowNull: false
          },
          groupterms: {
            type: TEXT,
            allowNull: false
          },
          authstr: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
          },
          groups: {
            type: TEXT,
            allowNull: false
          },
          attentiongroup: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          }
    }, {
        tableName: 'cc_common_member_field_forum_archive',
        timestamps: false,
    })

    return CommonMemberFieldForumArchive
}
