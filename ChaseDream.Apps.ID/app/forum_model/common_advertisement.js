module.exports = app => {
    const {STRING, INTEGER, TEXT} = app.Sequelize

    const CommonAdvertisement = app.forumModel.define("CommonAdvertisement", {
        advid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
          },
          available: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
          },
          type: {
            type: STRING(50),
            allowNull: false,
            defaultValue: '0'
          },
          displayorder: {
            type: INTEGER(3),
            allowNull: false,
            defaultValue: '0'
          },
          title: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          targets: {
            type: TEXT,
            allowNull: false
          },
          parameters: {
            type: TEXT,
            allowNull: false
          },
          code: {
            type: TEXT,
            allowNull: false
          },
          starttime: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          endtime: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          }
    }, {
        tableName: 'cc_common_advertisement',
        timestamps: false,
    })

    return CommonAdvertisement
}
