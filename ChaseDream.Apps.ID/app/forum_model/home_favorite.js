module.exports = app => {
    const {STRING, INTEGER, TEXT} = app.Sequelize

    return app.forumModel.define('HomeFavorite', {
        favid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        id: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        idtype: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        spaceuid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        title: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        description: {
            type: TEXT,
            allowNull: false
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_home_favorite',
        timestamps: false,
    })
}
