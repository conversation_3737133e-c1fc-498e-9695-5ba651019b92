module.exports = app => {
    const {INTEGER} = app.Sequelize

    const CommonMemberCountArchive = app.forumModel.define("CommonMemberCountArchive", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
          },
          extcredits1: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits2: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits3: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits4: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits5: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits6: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits7: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          extcredits8: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
          },
          friends: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          posts: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          threads: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          digestposts: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          doings: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          blogs: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          albums: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          sharings: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          attachsize: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          views: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          oltime: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          todayattachs: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          todayattachsize: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          feeds: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          follower: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          following: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          newfollower: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          }
    }, {
        tableName: 'cc_common_member_count_archive',
        timestamps: false,
    })

    return CommonMemberCountArchive
}
