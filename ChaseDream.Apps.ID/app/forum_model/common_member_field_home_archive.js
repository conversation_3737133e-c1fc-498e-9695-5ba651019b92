module.exports = app => {
    const {STRING, INTEGER, CHAR, TEXT} = app.Sequelize

    const CommonMemberFieldHomeArchive = app.forumModel.define("CommonMemberFieldHomeArchive", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
          },
          videophoto: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          spacename: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          spacedescription: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
          },
          domain: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
          },
          addsize: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          addfriend: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          menunum: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
          },
          theme: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
          },
          spacecss: {
            type: TEXT,
            allowNull: false
          },
          blockposition: {
            type: TEXT,
            allowNull: false
          },
          recentnote: {
            type: TEXT,
            allowNull: false
          },
          spacenote: {
            type: TEXT,
            allowNull: false
          },
          privacy: {
            type: TEXT,
            allowNull: false
          },
          feedfriend: {
            type: TEXT,
            allowNull: false
          },
          acceptemail: {
            type: TEXT,
            allowNull: false
          },
          magicgift: {
            type: TEXT,
            allowNull: false
          },
          stickblogs: {
            type: TEXT,
            allowNull: false
          }
    }, {
        tableName: 'cc_common_member_field_home_archive',
        timestamps: false,
    })

    return CommonMemberFieldHomeArchive
}
