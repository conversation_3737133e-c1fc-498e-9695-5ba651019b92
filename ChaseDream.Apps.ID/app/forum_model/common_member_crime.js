module.exports = app => {
    const { STRING, INTEGER, TEXT } = app.Sequelize

    return app.forumModel.define("CommonMemberCrime", {
        cid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        operatorid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        operator: {
            type: STRING(15),
            allowNull: false
        },
        action: {
            type: INTEGER(5),
            allowNull: false
        },
        reason: {
            type: TEXT,
            allowNull: false
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_common_member_crime',
        timestamps: false,
    })
}
