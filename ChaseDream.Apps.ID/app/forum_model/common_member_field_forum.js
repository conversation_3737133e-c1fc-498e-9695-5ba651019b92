module.exports = app => {
    const { STRING, INTEGER, TEXT } = app.Sequelize

    const CommonMemberFieldForum = app.forumModel.define("CommonMemberFieldForum", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
        },
        publishfeed: {
            type: INTEGER(3),
            allowNull: false,
            defaultValue: '0'
        },
        customshow: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '26'
        },
        customstatus: {
            type: STRING(30),
            allowNull: false,
            defaultValue: ''
        },
        medals: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        sightml: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        groupterms: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        authstr: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
        },
        groups: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        attentiongroup: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        }
    }, {
        tableName: 'cc_common_member_field_forum',
        timestamps: false,
    })

    return CommonMemberFieldForum
}
