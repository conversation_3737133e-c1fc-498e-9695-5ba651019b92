module.exports = app => {
    const {STRING, INTEGER, TEXT} = app.Sequelize

    return app.forumModel.define('HomeDoing', {
        doid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        username: {
            type: STRING(15),
            allowNull: false,
            defaultValue: ''
        },
        from: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        message: {
            type: TEXT,
            allowNull: false
        },
        ip: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
        },
        port: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        replynum: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        status: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_home_doing',
        timestamps: false,
    })
}
