module.exports = app => {
    const {INTEGER} = app.Sequelize

    return app.forumModel.define('UcenterPmMembers', {
        plid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0',
            primaryKey: true
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0',
            primaryKey: true
        },
        isnew: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        pmnum: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        lastupdate: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        lastdateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_ucenter_pm_members',
        timestamps: false,
    })
}
