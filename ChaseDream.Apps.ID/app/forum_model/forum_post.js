module.exports = app => {
    const {STRING, INTEGER, TEXT} = app.Sequelize

    return app.forumModel.define('ForumPost', {
        pid: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            unique: true
        },
        fid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        tid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0',
            primaryKey: true
        },
        first: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        author: {
            type: STRING(15),
            allowNull: false,
            defaultValue: ''
        },
        authorid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        subject: {
            type: STRING(80),
            allowNull: false,
            defaultValue: ''
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        message: {
            type: TEXT,
            allowNull: false
        },
        useip: {
            type: STRING(15),
            allowNull: false,
            defaultValue: ''
        },
        port: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        invisible: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        anonymous: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        usesig: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        htmlon: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        bbcodeoff: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        smileyoff: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        parseurloff: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        attachment: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        rate: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        ratetimes: {
            type: INTEGER(3).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        status: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
        },
        tags: {
            type: STRING(255),
            allowNull: false,
            defaultValue: '0'
        },
        comment: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        replycredit: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
        },
        position: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            // autoIncrement: true
        }
    }, {
        tableName: 'cc_forum_post',
        timestamps: false,
    })
}
