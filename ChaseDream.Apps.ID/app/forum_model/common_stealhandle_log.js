module.exports = app => {
  const DataTypes = app.Sequelize

  const Model = app.forumModel.define('CommonStealhandleLog', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    uid: {
      type: DataTypes.INTEGER(8).UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    username: {
      type: DataTypes.CHAR(15),
      allowNull: false
    },
    password: {
      type: DataTypes.CHAR(32),
      allowNull: false,
      defaultValue: ''
    },
    lastvisit: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    lastip: {
      type: DataTypes.CHAR(15),
      allowNull: false
    },
    threadtitle: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    createtime: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    }
  }, {
    tableName: 'cc_common_stealhandle_log',
    timestamps: false,
  })

  return Model
}
