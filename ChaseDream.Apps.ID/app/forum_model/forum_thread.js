module.exports = app => {
    const {INTEGER, CHAR} = app.Sequelize

    return app.forumModel.define('ForumThread', {
        tid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        fid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        posttableid: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        typeid: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        sortid: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        readperm: {
            type: INTEGER(3).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        price: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        author: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
        },
        authorid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        subject: {
            type: CHAR(80),
            allowNull: false,
            defaultValue: ''
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        lastpost: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        lastposter: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
        },
        views: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        replies: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        displayorder: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        highlight: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        digest: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        rate: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        special: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        attachment: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        moderated: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        closed: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        stickreply: {
            type: INTEGER(1).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        recommends: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        recommend_add: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        recommend_sub: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        heats: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        status: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        isgroup: {
            type: INTEGER(1),
            allowNull: false,
            defaultValue: '0'
        },
        favtimes: {
            type: INTEGER(8),
            allowNull: false,
            defaultValue: '0'
        },
        sharetimes: {
            type: INTEGER(8),
            allowNull: false,
            defaultValue: '0'
        },
        stamp: {
            type: INTEGER(3),
            allowNull: false,
            defaultValue: '-1'
        },
        icon: {
            type: INTEGER(3),
            allowNull: false,
            defaultValue: '-1'
        },
        pushedaid: {
            type: INTEGER(8),
            allowNull: false,
            defaultValue: '0'
        },
        cover: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        replycredit: {
            type: INTEGER(6),
            allowNull: false,
            defaultValue: '0'
        },
        relatebytag: {
            type: CHAR(255),
            allowNull: false,
            defaultValue: '0'
        },
        maxposition: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        bgcolor: {
            type: CHAR(8),
            allowNull: false,
            defaultValue: ''
        },
        comments: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        hidden: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        oldtypeid: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false
        }
    }, {
        tableName: 'cc_forum_thread',
        timestamps: false,
    })
}
