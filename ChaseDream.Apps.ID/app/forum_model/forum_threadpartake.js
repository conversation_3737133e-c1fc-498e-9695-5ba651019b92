module.exports = app => {
    const { INTEGER } = app.Sequelize

    return app.forumModel.define('ForumThreadpartake', {
        tid: {
            type: INTEGER(8).UNSIGNED,
            primaryKey: true,
            allowNull: false,
            defaultValue: 0
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            primaryKey: true,
            allowNull: false,
            defaultValue: 0
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: 0
        }
    }, {
        tableName: 'cc_forum_threadpartake',
        timestamps: false,
    })
}
