module.exports = app => {
  const DataTypes = app.Sequelize

  const Model = app.forumModel.define('CommonUserloginLog', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    uid: {
      type: DataTypes.INTEGER(8).UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    email: {
      type: DataTypes.CHAR(40),
      allowNull: false
    },
    username: {
      type: DataTypes.CHAR(15),
      allowNull: false
    },
    areacode: {
      type: DataTypes.STRING(20),
      defaultValue: '',
      allowNull: false
    },
    mobile: {
      type: DataTypes.BIGINT.UNSIGNED,
      defaultValue: '0',
      allowNull: false
    },
    dateline: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      allowNull: false
    },
    regdateline: {
      type: DataTypes.INTEGER(10).UNSIGNED,
      defaultValue: '0',
      allowNull: false
    },
    ip: {
      type: DataTypes.CHAR(15),
      allowNull: false
    },
    regip: {
      type: DataTypes.CHAR(15),
      defaultValue: '',
      allowNull: false
    },
    loginsuccess: {
      type: DataTypes.INTEGER(1),
      allowNull: false
    },
    browserinfo: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    issteal: {
      type: DataTypes.INTEGER(1),
      allowNull: false,
      defaultValue: '0'
    },
    phonestamp: {
      type: DataTypes.STRING(11),
      defaultValue: '',
    },
    // 1 web 桌面版 
    // 2 web 移动版
    // 3 iOS
    // 4 安卓
    //---------------
    // 1 手机一键登录
    // 2 手机+密码
    // 3 email+密码
    // 4 微信登录
    // 5 注册后自动登录
    loginmethod: {
      type: DataTypes.INTEGER(2),
      defaultValue: 0,
      allowNull: false
    },
    referer: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    tableName: 'cc_common_userlogin_log',
    timestamps: false,
  })

  return Model
}
