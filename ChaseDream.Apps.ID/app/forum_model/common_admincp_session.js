module.exports = app => {
  const { STRING, INTEGER, TEXT } = app.Sequelize

  const CommonAdmincpSession = app.forumModel.define('CommonAdmincpSession', {
    uid: {
      type: INTEGER(8).UNSIGNED,
      allowNull: false,
      defaultValue: '0',
      primaryKey: true
    },
    adminid: {
      type: INTEGER(6).UNSIGNED,
      allowNull: false,
      defaultValue: '0'
    },
    panel: {
      type: INTEGER(1),
      allowNull: false,
      defaultValue: '0',
      primaryKey: true
    },
    ip: {
      type: STRING(15),
      allowNull: false,
      defaultValue: ''
    },
    dateline: {
      type: INTEGER(10).UNSIGNED,
      allowNull: false,
      defaultValue: '0'
    },
    errorcount: {
      type: INTEGER(1),
      allowNull: false,
      defaultValue: '0'
    },
    storage: {
      type: TEXT,
      allowNull: false
    }
  }, {
    tableName: 'cc_common_admincp_session',
    timestamps: false,
  })

  return CommonAdmincpSession
}
