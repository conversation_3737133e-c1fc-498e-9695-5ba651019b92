module.exports = app => {
    const { STRING, INTEGER, CHAR, TEXT } = app.Sequelize

    const CommonMemberFieldHome = app.forumModel.define("CommonMemberFieldHome", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true
        },
        videophoto: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        spacename: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        spacedescription: {
            type: STRING(255),
            allowNull: false,
            defaultValue: ''
        },
        domain: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
        },
        addsize: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        addfriend: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        menunum: {
            type: INTEGER(6).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        theme: {
            type: STRING(20),
            allowNull: false,
            defaultValue: ''
        },
        spacecss: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        blockposition: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        recentnote: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        spacenote: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        privacy: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        feedfriend: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        acceptemail: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        magicgift: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        },
        stickblogs: {
            type: TEXT,
            allowNull: false,
            defaultValue: ''
        }
    }, {
        tableName: 'cc_common_member_field_home',
        timestamps: false,
    })

    return CommonMemberFieldHome
}
