module.exports = app => {
    const { INTEGER } = app.Sequelize

    return app.forumModel.define("CommonMemberBlockUser", {
        id: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        blocked_uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        dateline: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        }
    }, {
        tableName: 'cc_common_member_block_user',
        indexes: [
            {
                fields: ['uid', 'blocked_uid']
            }
        ],
        timestamps: false,
    })
}
