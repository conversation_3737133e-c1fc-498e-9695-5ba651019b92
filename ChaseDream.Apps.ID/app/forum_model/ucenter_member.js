module.exports = app => {
    const {INTEGER, CHAR} = app.Sequelize

    const UcenterMember = app.forumModel.define("UcenterMember", {
        uid: {
            type: INTEGER(8).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true
        },
        username: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: '',
            unique: true
        },
        password: {
            type: CHAR(32),
            allowNull: false,
            defaultValue: ''
        },
        email: {
            type: CHAR(32),
            allowNull: false,
            defaultValue: ''
        },
        myid: {
            type: CHAR(30),
            allowNull: false,
            defaultValue: ''
        },
        myidkey: {
            type: CHAR(16),
            allowNull: false,
            defaultValue: ''
        },
        regip: {
            type: CHAR(15),
            allowNull: false,
            defaultValue: ''
        },
        regdate: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        lastloginip: {
            type: INTEGER(10),
            allowNull: false,
            defaultValue: '0'
        },
        lastlogintime: {
            type: INTEGER(10).UNSIGNED,
            allowNull: false,
            defaultValue: '0'
        },
        salt: {
            type: CHAR(6),
            allowNull: false
        },
        secques: {
            type: CHAR(8),
            allowNull: false,
            defaultValue: ''
        }
    }, {
        tableName: 'cc_ucenter_members',
        timestamps: false,
    })

    return UcenterMember
}
