const Controller = require('egg').Controller

class PermissionController extends Controller {
    async my_permission() {
        const { ctx } = this

        let user = ctx.locals.user
        delete user.password
        delete user.created_at
        delete user.updated_at
        delete user.deleted_at

        ctx.json_body = user
    }

    async my_menu() {
        const { ctx, config } = this

        const user = ctx.locals.user

        const role_menu = await ctx.model.RoleMenu.findAll({
            attributes: ['menu_id'],
            where: {
                role_id: user.role
            },
            raw: true
        }).map(s => {
            return s.menu_id
        })

        let where = {
            id: role_menu,
            pid: 0
        }

        if (user.role_name.indexOf(config.super_admin) !== -1 || user.role_name.indexOf(config.system_maintenance) !== -1) delete where.id

        let menus = await ctx.model.Menu.findAll({
            attributes: ['id', 'name', 'icon', 'text', 'router', 'pid', 'is_show', 'component', 'order'],
            where: where,
            order: [
                ['order', 'ASC'],
            ],
            raw: true
        })

        if (user.role_name.indexOf(config.super_admin) === -1 && user.role_name.indexOf(config.system_maintenance) === -1) {
            where.id = role_menu
        }

        for (const menu of menus) {
            where.pid = menu.id

            const sub_menu = await ctx.model.Menu.findAll({
                attributes: ['id', 'name', 'icon', 'text', 'router', 'pid', 'is_show', 'component', 'order'],
                where: where,
                order: [
                    ['order', 'ASC'],
                ],
                raw: true
            })

            if (sub_menu.length > 0) menu.sub_menu = sub_menu

            delete menu.pid
        }

        ctx.json_body = menus
    }

    async get_menu() {
        const { ctx } = this

        let menus = await ctx.model.Menu.findAll({
            where: {
                pid: 0
            },
            order: [
                ['order', 'ASC'],
            ],
            raw: true
        })

        for (const menu of menus) {
            const sub_menu = await ctx.model.Menu.findAll({
                where: {
                    pid: menu.id,
                },
                order: [
                    ['order', 'ASC'],
                ],
                raw: true
            })

            if (sub_menu.length > 0) menu.sub_menu = sub_menu
        }

        ctx.json_body = menus
    }

    async create_menu() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = await ctx.model.Menu.create(body)
    }

    async update_menu() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Menu.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = { success: true }
    }

    async delete_menu() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Menu.destroy({
            where: { id: body.id }
        })

        ctx.json_body = { success: true }
    }

    async get_role() {
        const { ctx } = this

        ctx.json_body = await ctx.model.Role.findAll({
            where: {}
        })
    }

    async add_role() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Role.create(body)
        ctx.json_body = { success: true }
    }

    async update_role() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Role.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = { success: true }
    }

    async delete_role() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Role.destroy({
            where: { id: body.id },
        })

        ctx.json_body = { success: true }
    }

    async get_role_menu() {
        const { ctx } = this

        ctx.json_body = await ctx.model.RoleMenu.findAll({
            where: {},
        })
    }

    async get_role_menu_role_id() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        ctx.json_body = await ctx.model.RoleMenu.findAll({
            where: {
                role_id: params.role_id
            },
        })
    }

    async update_role_menu() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        let tran = await ctx.model.transaction()
        try {
            await ctx.model.RoleMenu.destroy({
                where: {
                    role_id: body.role_id
                },
                force: true,
                transaction: tran
            })

            for (const menu_id of body.menu_id) {
                await ctx.model.RoleMenu.create({
                    role_id: body.role_id,
                    menu_id
                }, {
                    transaction: tran
                })
            }
            await tran.commit()

            ctx.json_body = { success: true }
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async delete_role_menu() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RoleMenu.destroy({
            where: {
                role_id: body.role_id,
                menu_id: body.menu_id,
            },
        })

        ctx.json_body = { success: true }
    }

    async get_permission_for_role() {
        const { ctx, app } = this
        let ret = []

        const params = Object.assign({}, ctx.params)

        const permissions = await app.zrole.getPermissionsForUser(`${params.role}_role`)

        for (let permission of permissions) {
            const [_, router, method] = permission

            ret.push({
                router,
                method
            })
        }

        ctx.json_body = ret
    }

    async update_permission_for_role() {
        const { ctx, app } = this

        const body = Object.assign({}, ctx.request.body)

        await app.zrole.deletePermissionsForUser(`${body.role}_role`)

        for (const permission of body.permissions) {
            await app.zrole.addPermissionForUser(`${body.role}_role`, permission.router, permission.method)
        }

        await app.zrole.savePolicy()

        ctx.json_body = { success: true }
    }

    async all_permissions() {
        const { ctx, app, config } = this
        let permissions = []

        const routers = app.router.stack

        for (let router of routers) {
            if (router.path.toString().startsWith(`${config.apiPrefix}/admin`)) {
                let methods = router.methods
                let index = methods.indexOf('HEAD')

                if (index > -1) {
                    methods.splice(index, 1);
                }

                permissions.push({
                    path: router.path,
                    methods: methods.join(',')
                })
            }
        }

        ctx.json_body = permissions
    }
}

module.exports = PermissionController
