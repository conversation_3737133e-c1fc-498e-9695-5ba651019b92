const await = require('await-stream-ready/lib/await')
const _ = require('lodash')
const BaseController = require('./base')

class LinkedinController extends BaseController {
    async info_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let where = {}
        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        for (const item of ['id', 'uid', 'gender']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['name', 'nationality', 'location']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        if (query.edus) {
            const lids = await ctx.model.LinkedinEducation.findAll({
                attributes: ['lid'],
                where: {
                    school_name: {
                        [Op.like]: `%${query.edus}%`,
                    }
                }
            }).map(row => { return row.lid })

            where.id = lids
        }

        if (query.works) {
            let lids = await ctx.model.LinkedinWorkExperience.findAll({
                attributes: ['lid'],
                where: {
                    org_name: {
                        [Op.like]: `%${query.works}%`,
                    }
                }
            }).map(row => { return row.lid })

            if (where.id && where.id.length > 0) {
                lids = where.id.filter(function (v) { return lids.indexOf(v) > -1 })
            }

            where.id = lids
        }

        if (query.edu) {
            where.cur_edu_id = await ctx.model.LinkedinEducation.findAll({
                attributes: ['id'],
                where: {
                    school_name: {
                        [Op.like]: `%${query.edu}%`,
                    }
                }
            }).map(row => { return row.id })
        }

        if (query.work) {
            where.cur_work_id = await ctx.model.LinkedinWorkExperience.findAll({
                attributes: ['id'],
                where: {
                    org_name: {
                        [Op.like]: `%${query.work}%`,
                    }
                }
            }).map(row => { return row.id })
        }

        ctx.json_body = await ctx.model.LinkedinInfo.findAndCountAll({
            include: [{
                model: ctx.model.LinkedinEducation,
                required: false,
                as: 'education',
            }, {
                model: ctx.model.LinkedinWorkExperience,
                required: false,
                as: 'work_experience',
            }],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            distinct: true,
        })
    }

    async info_get_one() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const linkedin = await ctx.model.LinkedinInfo.findOne({
            where: {
                uid: body.uid
            },
            raw: true,
        })

        linkedin.work_experience = await ctx.model.LinkedinWorkExperience.findAll({
            include: [{
                model: ctx.model.LinkedinOrganization,
                required: false,
                as: 'org',
            }],
            where: {
                lid: linkedin.id,
            },
        })

        linkedin.education = await ctx.model.LinkedinEducation.findAll({
            include: [{
                model: ctx.model.LinkedinSchool,
                required: false,
                as: 'school',
            }],
            where: {
                lid: linkedin.id,
            },
        })

        linkedin.certification = await ctx.model.LinkedinCertification.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.volunteer = await ctx.model.LinkedinVolunteer.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        const items = await ctx.model.LinkedinSkill.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        }) || []

        const skills = {
            top: _.filter(items, { 'top': 1 }).map(row => { return row.name }),
            others: [],
        }

        _.pullAllBy(items, [{ 'top': 1 }], 'top')

        let arr = items.map(row => { return row.type })
        arr = Array.from(new Set(arr))

        for (const type of arr) {
            let objs = _.filter(items, { 'type': type })
            _.pullAllBy(items, [{ 'type': type }], 'type')

            let content = []

            for (const obj of objs) {
                content.push(obj.name)
            }
            skills.others.push({
                name: type,
                content,
            })
        }

        linkedin.skill = skills

        linkedin.accomplishment_language = await ctx.model.LinkedinAccomplishmentLanguage.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_course = await ctx.model.LinkedinAccomplishmentCourse.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_patent = await ctx.model.LinkedinAccomplishmentPatent.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_honor = await ctx.model.LinkedinAccomplishmentHonor.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_organization = await ctx.model.LinkedinAccomplishmentOrganization.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_project = await ctx.model.LinkedinAccomplishmentProject.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_publication = await ctx.model.LinkedinAccomplishmentPublication.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        linkedin.accomplishment_test = await ctx.model.LinkedinAccomplishmentTest.findAll({
            where: {
                lid: linkedin.id,
            },
            raw: true,
        })

        ctx.json_body = linkedin
    }

    async info_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.service.linkedin.info_delete(body.id)

        ctx.json_body = {
            success: true,
        }
    }

    async crawl() {
        const { ctx } = this
        const body = Object.assign({}, ctx.request.body)

        const url = body.url

        await ctx.service.linkedin.crawl(url)
    }

    async wait_list_info() {
        const { ctx } = this

        const waiting = await ctx.model.LinkedinWaitList.count({
            where: {
                status: 0
            }
        })

        const total = await ctx.model.LinkedinWaitList.count({
            where: {}
        })

        ctx.json_body = {
            waiting,
            total,
        }
    }

    async wait_list() {
        const { ctx, config } = this

        let where = {}
        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        for (const item of ['url', 'status', 'error_message']) {
            if (query[item]) where[item] = query[item]
        }

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.LinkedinWaitList.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })
    }

    async wait_list_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const urls = body.urls.split('\n')

        for (let url of urls) {
            try {
                if (url.indexOf('?') !== -1) {
                    url = decodeURIComponent(url.substring(0, url.indexOf('?')))
                } else {
                    url = decodeURIComponent(url)
                }

                const wl = await ctx.model.LinkedinWaitList.findOne({
                    where: {
                        url
                    }
                })

                if (!wl) {
                    await ctx.model.LinkedinWaitList.create({
                        url
                    })
                }
            } catch (err) {
                ctx.logger.error(err.message)
            }
        }

        ctx.json_body = {
            success: true,
        }
    }

    async wait_list_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (body.wait_list) {
            for (const wait_list of body.wait_list) {
                await ctx.model.LinkedinWaitList.destroy({
                    where: {
                        id: wait_list.id,
                    }
                })

                if (wait_list.lid) {
                    await ctx.service.linkedin.info_delete(wait_list.lid)
                }

                if (wait_list.recrawl && wait_list.url) {
                    await ctx.model.LinkedinWaitList.create({
                        url: wait_list.url,
                    })
                }
            }
        } else {
            await ctx.model.LinkedinWaitList.destroy({
                where: {
                    id: body.id,
                }
            })

            if (body.lid) {
                await ctx.service.linkedin.info_delete(body.lid)
            }

            if (body.recrawl && body.url) {
                await ctx.model.LinkedinWaitList.create({
                    url: body.url,
                })
            }
        }

        ctx.json_body = {
            success: true,
        }
    }

    async account_list() {
        const { ctx, config } = this

        let where = {}
        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.LinkedinUser.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })
    }

    async account_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const account = await ctx.model.LinkedinUser.create(body)

        await ctx.service.linkedin.login(account)

        ctx.json_body = {
            success: true,
            account,
        }
    }

    async account_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (body.clear) {
            body.cookie = ''
            body.pin = ''
            body.error_message = ''
        }

        await ctx.model.LinkedinUser.update(body, {
            where: {
                id: body.id,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async account_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.LinkedinUser.destroy({
            where: {
                id: body.id,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async account_verify() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.LinkedinUser.update({
            pin: body.pin,
        }, {
            where: {
                username: body.username,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async crawl_log_list() {
        const { ctx, config } = this

        let where = {}
        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.LinkedinCrawlLog.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })
    }

    async organization_list() {
        const { ctx, config } = this

        let where = {}
        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.LinkedinOrganization.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })
    }

    async organization_get_one() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        ctx.json_body = await ctx.model.LinkedinOrganization.findOne({
            where: {
                id: params.id
            },
            raw: true,
        })
    }

    async school_list() {
        const { ctx, config } = this

        let where = {}
        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.LinkedinSchool.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })
    }

    async school_get_one() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        ctx.json_body = await ctx.model.LinkedinSchool.findOne({
            where: {
                id: params.id
            },
            raw: true,
        })
    }

    async fix_org_edu() {
        const { ctx } = this

        await ctx.service.linkedin.fix_org_edu()

        ctx.json_body = {
            success: true,
        }
    }

    async get_avatar() {
        const { ctx } = this

        const { avatar_small, avatar_big } = await ctx.service.linkedin.get_avatar()

        ctx.json_body = {
            success: true,
            avatar_small,
            avatar_big,
        }
    }

    async set_pin_for_avatar() {
        const { ctx, app, config } = this

        const pin = ctx.params.pin && ctx.params.pin.trim()

        await app.redis.set(`linkedin_account_pin:${config.linkedin.account.username}`, pin)

        ctx.json_body = {
            success: true,
        }
    }
}

module.exports = LinkedinController
