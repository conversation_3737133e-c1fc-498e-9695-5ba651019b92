const fs = require('fs')
const fse = require('fs-extra')
const path = require('path')
const pad = require('pad')
const moment = require("moment")
const random_string = require("randomstring")
const BaseController = require('./base')

class BehaviorController extends BaseController {
    async behavior_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        let where = {}

        for (const item of ['id', 'username', 'email', 'username', 'record_type', 'record_code', 'operator']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['mobile', 'desc']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        let include = {
            model: ctx.model.BehaviorOpt,
            required: false,
            as: 'opt',
        }

        if (query.opt_id) {
            include.where = { opt_id: query.opt_id }
            include.required = true
        }

        ctx.json_body = await ctx.model.Behavior.findAndCountAll({
            include: [include],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            distinct: true,
        })
    }

    async behavior_get_one() {
        const { ctx } = this

        ctx.json_body = await ctx.model.Behavior.findOne({
            include: [{
                model: ctx.model.BehaviorOpt,
                required: false,
                as: 'opt',
            }],
            where: {
                id: ctx.params.id
            },
        })
    }

    async behavior_search() {
        const { ctx } = this

        ctx.json_body = await ctx.model.Behavior.findAll({
            include: [{
                model: ctx.model.BehaviorOpt,
                required: false,
                as: 'opt',
            }],
            where: {
                username: ctx.params.username
            },
        })
    }

    async behavior_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const uc_member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: body.username,
            },
            raw: true,
        })

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: uc_member.uid,
            },
            raw: true,
        })

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                uid: member.id
            },
            raw: true,
        })

        ctx.error(!uc_member, '未找到该用户')

        body.mobile = `${mobile.area_code}-${mobile.mobile}`
        body.email = uc_member.email
        body.operator = ctx.locals.user.username

        const behavior = await ctx.model.Behavior.create(body)

        for (const opt of body.opt_behavior) {
            await ctx.model.BehaviorOpt.create({
                behavior_id: behavior.id,
                opt_id: opt.opt_id,
                opt_text: opt.opt_text,
            })
        }

        ctx.json_body = behavior
    }

    async behavior_upload() {
        const { ctx, config } = this

        let filename = ''
        const month = pad(2, moment().month() + 1, '0')
        const full_path = `${config.baseDir}${config.upload_user_path}/${moment().year()}/${month}`

        await fse.mkdirp(full_path)

        try {
            const parts = ctx.multipart()
            let part

            while ((part = await parts()) != null) {
                if (!part.length) {
                    filename = random_string.generate(8) + path.extname(part.filename)
                    part.pipe(fs.createWriteStream(`${full_path}/${filename}`))
                }
            }

            ctx.json_body = {
                fullpath: `${config.upload_public_url}${config.upload_user_path.replace('/upload', '')}/${moment().year()}/${month}/${filename}`
            }
        } catch (err) {
            ctx.json_body = err
        }
    }

    async behavior_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const uc_member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: body.username,
            },
            raw: true,
        })

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: uc_member.uid,
            },
            raw: true,
        })

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                uid: member.id
            },
            raw: true,
        })

        ctx.error(!uc_member, '未找到该用户')

        body.mobile = `${mobile.area_code}-${mobile.mobile}`
        body.email = uc_member.email
        body.operator = ctx.locals.user.username

        await ctx.model.Behavior.update(body, {
            where: { id: body.id }
        })

        await ctx.model.BehaviorOpt.destroy({
            where: { behavior_id: body.id },
            force: true
        })

        for (const opt of body.opt_behavior) {
            await ctx.model.BehaviorOpt.create({
                behavior_id: body.id,
                opt_id: opt.opt_id,
                opt_text: opt.opt_text,
            })
        }

        ctx.json_body = {
            success: true
        }
    }

    async behavior_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Behavior.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async record_type() {
        const { ctx } = this

        const forum = ['论坛发广告贴', '论坛私信广告', '发帖内容有嫌疑持续追踪', '论坛违法信息发布', '论坛恶意攻击他人', '论坛头像违规', '论坛签名等违规']
        const community = ['群内广告', '加群过多', '群内僵尸', '违规前科']
        const resume = ['管理员错误操作，恢复清白', '用户知错就改，再给机会']

        const opt_behavior = ['持续追踪', '论坛禁言', '论坛锁定', '禁止访问', '从群里踢出', '拒绝入群', '永久禁加群', '无操作，仅记录', '恢复正常'].map((val, key) => { return { key: val !== '恢复正常' ? (key + 1) : 0, val } })

        ctx.json_body = {
            record_type: {
                forum: forum.map((val, key) => { return { key: `1.${key}`, val } }),
                community: community.map((val, key) => { return { key: `2.${key}`, val } }),
                resume: resume.map((val, key) => { return { key: `3.${key}`, val } }),
            },
            opt_behavior
        }
    }
}

module.exports = BehaviorController
