const phpUnserialize = require('phpunserialize')
require('moment-timezone')
const moment = require('moment')
const BaseController = require('./base')

module.exports = app => {
    class CDGController extends BaseController {
        async index() {
            const { ctx, app } = this
            const QueryTypes = app.Sequelize.QueryTypes

            // const model = ctx.request.body
            const now = moment().unix()

            let sql = `select advid, type, displayorder, title, targets, parameters, code, starttime, endtime from cc_common_advertisement where available=1 and (starttime < ${now} or starttime=0) and (endtime > ${now} or endtime=0) and type <> 'custom'`

            let rows = await ctx.forumModel.query(sql, {
                type: QueryTypes.SELECT
            })

            let ret = []

            for (let row of rows) {
                row.parameters = phpUnserialize(row.parameters)
                // if (!row.parameters.extra.fids || (row.parameters.extra.fids && row.parameters.extra.fids.includes(model.fid)))
                ret.push(row)
            }

            ctx.body = ret
        }
    }

    return CDGController
}
