const BaseController = require('./base')

class RenameController extends BaseController {
    async search() {
        const { ctx } = this

        const query = Object.assign({}, ctx.request.query)

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: query.username
            }
        })

        ctx.error(!member, `用户 ${query.username} 不存在`)

        let member_count = await ctx.forumModel.CommonMemberCount.findOne({
            attributes: ['threads', 'posts', 'friends', 'doings'],
            where: {
                uid: member.uid
            },
            raw: true
        }) || {
            threads: 0,
            posts: 0,
            friends: 0,
            doings: 0,
        }

        member_count.posts -= member_count.threads

        member_count.pm = await ctx.forumModel.UcenterPmMembers.count({
            where: {
                uid: member.uid
            },
            raw: true
        })

        member_count.favorite = await ctx.forumModel.HomeFavorite.count({
            where: {
                uid: member.uid
            },
            raw: true
        })

        const uc = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: query.new_username
            }
        })

        const cm = await ctx.forumModel.CommonMember.findOne({
            where: {
                username: query.new_username
            }
        })

        const cma = await ctx.forumModel.CommonMemberArchive.findOne({
            where: {
                username: query.new_username
            }
        })

        ctx.json_body = {
            member_count,
            member_exist: {
                uc: !!uc,
                cm: !!cm,
                cma: !!cma,
            }
        }
    }

    async rename() {
        const { ctx, app } = this

        const body = Object.assign({}, ctx.request.body)

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: body.username
            }
        })

        const um = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: body.new_username
            }
        })

        ctx.error(um, `用户 ${body.new_username} 已存在`)

        await ctx.forumModel.UcenterMember.update({
            username: body.new_username
        }, {
            where: {
                uid: member.uid
            }
        })

        await ctx.forumModel.CommonMember.update({
            username: body.new_username
        }, {
            where: {
                uid: member.uid
            }
        })

        await ctx.forumModel.CommonMemberArchive.update({
            username: body.new_username
        }, {
            where: {
                uid: member.uid
            }
        })

        await ctx.forumModel.ForumThread.update({
            author: body.new_username
        }, {
            where: {
                authorid: member.uid
            }
        })

        await ctx.forumModel.ForumThread.update({
            lastposter: body.new_username
        }, {
            where: {
                authorid: member.uid,
                lastposter: body.username,
            }
        })

        await ctx.forumModel.ForumPost.update({
            author: body.new_username
        }, {
            where: {
                authorid: member.uid
            }
        })

        await ctx.forumModel.CommonMemberCount.update({
            friends: 0,
            doings: 0,
        }, {
            where: {
                uid: member.uid
            }
        })

        await ctx.forumModel.UcenterPmMembers.destroy({
            where: {
                uid: member.uid
            },
            force: true
        })

        await ctx.forumModel.HomeFriend.destroy({
            where: {
                uid: member.uid
            },
            force: true
        })

        await ctx.forumModel.HomeFavorite.destroy({
            where: {
                uid: member.uid
            },
            force: true
        })

        await ctx.forumModel.HomeDoing.destroy({
            where: {
                uid: member.uid
            },
            force: true
        })

        await ctx.model.Member.update({
            username: body.new_username
        }, {
            where: {
                forum_uid: member.uid
            }
        })

        try {
            await app.memcached.flushAsync()
        } catch (err) {
            ctx.logger.error(err.message)
        }

        ctx.json_body = {
            success: true
        }
    }
}

module.exports = RenameController
