const md5 = require('md5')
const BaseController = require('./base')

class SmsController extends BaseController {
    async user() {
        const { ctx, config, app } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        let where = {}
        let include = [{
            attributes: ['area_code', 'mobile', 'mobile_encrypt', 'mobile_mask'],
            model: ctx.model.Mobile,
            required: false,
            as: 'mobile',
        }, {
            model: ctx.model.Wechat,
            required: false,
            as: 'wechat',
        }, {
            attributes: ['useragent'],
            model: ctx.model.MobileLog,
            required: false,
            as: 'mobile_log',
        }]

        if (query.regip) {
            where.regip = query.regip.trim()
        }
        if (query.username) {
            where.username = query.username.trim()
        }

        if (query.area_code) {
            include[0].where = {
                area_code: query.area_code.trim()
            }
            include[0].required = true
        }
        if (query.mobile) {
            include[0].where = {
                mobile_encrypt: ctx.helper.AESEncrypt(query.mobile.trim())
            }
            include[0].required = true
        }
        if (query.useragent) {
            include[2].where = {
                useragent: {
                    [Op.like]: `%${query.useragent.trim()}%`
                }
            }
            include[2].required = true
        }

        const setting = await ctx.model.SystemSettings.findOne({
            where: {
                name: 'show_mobile'
            },
            raw: true
        })

        const res = await ctx.model.Member.findAndCountAll({
            attributes: ['id', 'forum_uid', 'username', 'status', 'regip', 'regdate'],
            include: include,
            where: where,
            distinct: true,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })

        for (const row of res.rows) {
            if (setting.value == 1 && row.mobile.mobile_encrypt.slice(-2) === "==") {
                row.mobile.mobile = ctx.helper.AESDecrypt(row.mobile.mobile_encrypt)
            } else {
                row.mobile.mobile = row.mobile.mobile_mask
            }
        }

        ctx.json_body = res
    }

    async log() {
        const { ctx, config } = this

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        let where = {}
        if (query.success) {
            where.success = query.success
        }
        if (query.area_code) {
            where.area_code = query.area_code
        }
        if (query.mobile) {
            where.mobile = ctx.helper.AESEncrypt(query.mobile)
        }
        if (query.ip) {
            where.ip = query.ip
        }

        const res = await ctx.model.SmsLog.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })

        for (const row of res.rows) {
            if (row.mobile.slice(-2) === "==") {
                row.mobile = ctx.helper.AESDecrypt(row.mobile)
            }
        }

        ctx.json_body = res
    }

    async blacklist() {
        const { ctx, config } = this

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        let where = {}
        if (query.s) {
            where.mobile = query.s
        }

        ctx.json_body = await ctx.model.Blacklist.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async blacklist_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const block = await ctx.model.Blacklist.findOne({
            where: {
                area_code: body.area_code,
                mobile: body.mobile
            }
        })

        ctx.error(block, "手机号已存在")

        ctx.json_body = {
            data: await ctx.model.Blacklist.create(body)
        }
    }

    async blacklist_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = {
            data: await ctx.model.Blacklist.destroy({
                where: { id: body.id },
                force: true
            })
        }
    }

    async binding_mobile_manual() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const body = Object.assign({}, ctx.request.body)

        const usernames = body.usernames.trim('\n').split('\n')
        const type = body.type

        let mobile = await ctx.model.Mobile.findOne({
            where: {
                mobile: {
                    [Op.like]: `137326${type}%`,
                }
            },
            order: [
                ['mobile', 'DESC'],
            ],
        })

        if (!mobile) {
            mobile = `137326${type}001`
        } else {
            mobile = ++mobile.mobile
        }

        let tran = await ctx.model.transaction()

        try {
            for (const username of usernames) {
                const uc_member = await ctx.forumModel.UcenterMember.findOne({
                    where: {
                        username
                    }
                })

                await ctx.forumModel.CommonMember.update({
                    videophotostatus: 1,
                }, {
                    where: {
                        uid: uc_member.uid,
                    }
                })

                let member = await ctx.model.Member.findOne({
                    where: {
                        username
                    }
                })

                if (member) {
                    await ctx.model.Mobile.update({
                        area_code: 86,
                        mobile,
                        mobile_encrypt: md5(mobile.toString()),
                        mobile_mask: ctx.helper.mask_mobile(mobile.toString())
                    }, {
                        where: {
                            uid: member.id,
                        },
                        transaction: tran
                    })
                } else {
                    member = await ctx.model.Member.create({
                        forum_uid: uc_member.uid,
                        username: uc_member.username,
                        password: uc_member.password,
                        salt: uc_member.salt,
                        regip: uc_member.regip,
                        regdate: uc_member.regdate,
                        create_at: ctx.helper.now(),
                    }, {
                        transaction: tran
                    })

                    await ctx.model.Mobile.create({
                        uid: member.id,
                        area_code: 86,
                        mobile,
                        mobile_encrypt: md5(mobile.toString()),
                        mobile_mask: ctx.helper.mask_mobile(mobile.toString())
                    }, {
                        transaction: tran
                    })
                }

                mobile++
            }

            await tran.commit()

            try {
                await app.memcached.flushAsync()
            } catch (err) {
                ctx.logger.error(err.message)
            }

        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }

        ctx.json_body = {
            success: true
        }
    }

    async binding_mobile_manual_list() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.request.query)
        const type = query.type

        query.page = query.page || 1
        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        let mobiles = await ctx.model.Mobile.findAndCountAll({
            attributes: ['uid', 'mobile'],
            where: {
                area_code: 86,
                mobile: {
                    [Op.like]: `137326${type}%`,
                }
            },
            order: [
                ['mobile', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })

        for (let mobile of mobiles.rows) {
            const member = await ctx.model.Member.findOne({
                attributes: ['username'],
                where: {
                    id: mobile.uid,
                }
            })

            mobile.member = member
        }

        ctx.json_body = mobiles
    }
}

module.exports = SmsController
