const _ = require('lodash')
const fs = require('fs')
const fse = require('fs-extra')
const pad = require('pad')
const moment = require("moment")
const randomstring = require("randomstring")
const BaseController = require('./base')

class ThreadController extends BaseController {
    async pid_list() {
        const { ctx, config } = this

        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1
        let where = {
            status: 2
        }

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.Thread.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async pid_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const post = await ctx.forumModel.ForumPost.findOne({
            where: {
                pid: body.pid
            }
        })

        const thread = await ctx.forumModel.ForumThread.findOne({
            where: {
                tid: post.tid
            }
        })

        const chasedream = await ctx.model.ThreadSendUser.findOne({
            where: {
                username: '<EMAIL>'
            },
            raw: true,
        })

        const res = await ctx.curl(`${config.discuz.api_base_url}&module=forumdisplay&tpp=1&fid=${post.fid}&page=1`, {
            method: 'GET',
            dataType: 'json',
            timeout: 5000,
        })

        await ctx.model.Thread.create({
            subject: thread.subject,
            content: post.message,
            opt_id: chasedream.id,
            send_username: chasedream.nickname,
            typeid: thread.typeid,
            tid: post.tid,
            pid: post.pid,
            fid: post.fid,
            forum: res.data.Variables.forum.name,
            uid: chasedream.id,
            htmlon: 1,
            status: 2,
        })

        ctx.json_body = {
            success: true
        }
    }

    async stick() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        body.opt_id = ctx.locals.user.id

        const user = await ctx.model.ThreadSendUser.findOne({
            where: {
                id: body.uid,
            },
            raw: true,
        })

        const admin = await ctx.model.ThreadSendUser.findOne({
            where: {
                username: '<EMAIL>',
            },
            raw: true,
        })

        body.username = admin.username
        body.password = ctx.helper.decode_dz_string(admin.password)
        body.send_username = user.nickname

        const result = await ctx.service.thread.stick(body)

        await ctx.model.Thread.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async highlight_digest() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        body.opt_id = ctx.locals.user.id

        const user = await ctx.model.ThreadSendUser.findOne({
            where: {
                id: body.uid,
            },
            raw: true,
        })

        const admin = await ctx.model.ThreadSendUser.findOne({
            where: {
                username: '<EMAIL>',
            },
            raw: true,
        })

        body.username = admin.username
        body.password = ctx.helper.decode_dz_string(admin.password)
        body.send_username = user.nickname

        const result = await ctx.service.thread.highlight_digest(body)

        if (body.opt === 'highlight') {
            body.highlight = body.highlight_color
        }

        await ctx.model.Thread.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async thread_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1
        let where = {
            status: [0, 1]
        }

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        if (model.stick > 0) {
            where.stick = {
                [Op.gt]: 0
            }
        } else {
            where.stick = 0
        }

        ctx.json_body = await ctx.model.Thread.findAndCountAll({
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async thread_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        body.opt_id = ctx.locals.user.id

        const user = await ctx.model.ThreadSendUser.findOne({
            where: {
                id: body.uid,
            },
            raw: true,
        })

        body.username = user.username
        body.password = ctx.helper.decode_dz_string(user.password)
        body.send_username = user.nickname

        if (!body.schedule) {
            const thread = await ctx.service.thread.send(body)
            body.tid = thread.Variables.tid
            body.pid = thread.Variables.pid
            body.status = 1
        } else {
            body.tid = 0
            body.pid = 0
            body.status = 0
        }

        const result = await ctx.model.Thread.create(body)

        ctx.json_body = result
    }

    async thread_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const chasedream = await ctx.model.EventUser.findOne({
            where: {
                username: '<EMAIL>'
            },
            raw: true,
        })

        body.username = chasedream.username
        body.password = ctx.helper.decode_dz_string(chasedream.password)

        if (body.status) {
            const result = await ctx.service.thread.update(body)
        }

        await ctx.model.Thread.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async thread_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const thread = await ctx.model.Thread.findOne({
            where: {
                id: body.id
            },
            raw: true,
        })

        const user = await ctx.model.ThreadSendUser.findOne({
            where: {
                id: thread.uid,
            },
            raw: true,
        })

        body.username = user.username
        body.password = ctx.helper.decode_dz_string(user.password)

        body.fid = thread.fid
        body.tid = thread.tid

        const result = await ctx.service.thread.delete(body)

        await ctx.model.Thread.destroy({
            where: { id: body.id },
            force: true,
        })

        ctx.json_body = {
            success: true
        }
    }

    async user_list() {
        const { ctx, config } = this

        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.ThreadSendUser.findAndCountAll({
            attributes: ['id', 'username', 'nickname', 'created_at'],
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async user_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        body.password = ctx.helper.encode_dz_string(body.password)

        ctx.json_body = await ctx.model.ThreadSendUser.create(body)
    }

    async user_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (body.password) {
            body.password = ctx.helper.encode_dz_string(body.password)
        }

        await ctx.model.ThreadSendUser.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async user_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.ThreadSendUser.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async forum_nav() {
        const { ctx, app, config } = this

        const forum_group = await app.redis.get(`${config.redis_key.forum_group}`) || ''
        if (forum_group) {
            ctx.json_body = JSON.parse(forum_group)
            return
        }

        const groupList = [38, 3, 1, 97, 2, 4, 45, 65, 5]

        const res = await ctx.curl(`${config.discuz.api_base_url}&module=forumnav`, {
            method: 'GET',
            dataType: 'json',
            timeout: 5000,
        })

        const forums = res.data.Variables.forums

        let groups = _.filter(forums, (forum) => {
            return forum.type === 'group' && groupList.includes(parseInt(forum.fid, 10))
        })

        for (let group of groups) {
            group.forums = _.filter(forums, (forum) => {
                return forum.type === 'forum' && forum.fup === group.fid
            })

            delete group.type
            delete group.fup
            delete group.status

            for (let forum of group.forums) {
                delete forum.type
                delete forum.fup
                delete forum.status
                delete forum.viewperm
                delete forum.postperm
                delete forum.threadtypes
            }
        }

        await app.redis.setex(`${config.redis_key.forum_group}`, 60 * 60 * 24, JSON.stringify(groups))

        ctx.json_body = groups
    }

    async forum_nav_sub() {
        const { ctx, config } = this

        const fid = ctx.query.fid

        const res = await ctx.curl(`${config.discuz.api_base_url}&module=forumdisplay&tpp=1&fid=${fid}&page=1`, {
            method: 'GET',
            dataType: 'json',
            timeout: 5000,
        })

        let threadtypes = []

        if (res.data.Variables.threadtypes) {
            for (let [key, value] of Object.entries(res.data.Variables.threadtypes.types)) {
                threadtypes.push({
                    typeid: key,
                    name: value.replace(/(<([^>]+)>)/ig, ""),
                })
            }
        }

        ctx.json_body = threadtypes
    }

    async thread_image() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        let where = {
            category: 'thread',
        }

        if (query.s) {
            where.original = {
                [Op.like]: `%${query.s}%`,
            }
        }

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const res = await ctx.model.Upload.findAndCountAll({
            attributes: ['id', 'fullpath', 'original', 'type', 'created_at'],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true,
        })

        res.rows.map(row => {
            if (config.env !== 'prod') {
                row.fullpath = `http://localhost:${config.cluster.listen.port}/misc/thread/${row.fullpath.replace(config.baseDir, '').replace(config.upload_threadpost_img_path, '').replace(/\//g, '-').substring(1)}`
            } else {
                row.fullpath = `https://static.chasedream.com${row.fullpath}`
            }
        })

        ctx.json_body = res
    }

    async image_upload() {
        const { ctx, config } = this
        let attach = {}

        try {
            const parts = ctx.multipart()
            let part
            while ((part = await parts()) != null) {
                if (!part.length) {
                    let path = ''
                    let random = ''
                    let filename = ''
                    let month = pad(2, moment().month() + 1, '0')

                    random = randomstring.generate(10)
                    path = `${config.upload_threadpost_img_path}/${moment().year()}/${month}`
                    filename = `${random}`

                    await fse.mkdirp(`${config.baseDir}${path}`)
                    let fullpath = `${path}/${filename}${part.filename.substring(part.filename.lastIndexOf('.'))}`
                    part.pipe(fs.createWriteStream(`${config.baseDir}${fullpath}`))

                    attach = await ctx.model.Upload.create({
                        category: 'thread',
                        original: part.filename,
                        fullpath: fullpath.replace('/upload/', '/'),
                        type: part.mime
                    })
                }
            }

            if (config.env !== 'prod') {
                attach.fullpath = `http://localhost:${config.cluster.listen.port}/misc/thread/${attach.fullpath.replace(config.baseDir, '').replace(config.upload_threadpost_img_path, '').replace(/\//g, '-').substring(1)}`
            } else {
                attach.fullpath = `https://static.chasedream.com${attach.fullpath}`
            }

            ctx.json_body = {
                aid: attach.id,
                fullpath: attach.fullpath,
                type: attach.type,
                original: attach.original,
            }
        } catch (err) {
            ctx.json_body = err
        }
    }

    async image_digest() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Upload.update({
            digest: body.digest,
        }, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async thread_material_digest() {
        const { ctx, config } = this

        const res = await ctx.model.Upload.findAll({
            attributes: ['id', 'digest', 'original', 'fullpath', 'order', 'created_at'],
            where: {
                category: 'thread',
                digest: 1,
            },
            order: [
                ['order', 'DESC'],
            ],
            raw: true,
        })

        res.map(row => {
            if (config.env !== 'prod') {
                row.fullpath = `http://localhost:${config.cluster.listen.port}/misc/thread/${row.fullpath.replace(config.baseDir, '').replace(config.upload_threadpost_img_path, '').replace(/\//g, '-').substring(1)}`
            } else {
                row.fullpath = `https://static.chasedream.com${row.fullpath}`
            }
        })

        ctx.json_body = res
    }

    async put_upload() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Upload.update({
            original: body.original,
        }, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async image_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Upload.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }
}

module.exports = ThreadController
