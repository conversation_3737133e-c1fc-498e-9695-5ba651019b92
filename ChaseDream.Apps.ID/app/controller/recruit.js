const fs = require('fs')
const fse = require('fs-extra')
const pad = require('pad')
const moment = require("moment")
const random_string = require("randomstring")
const download = require('download')
const BaseController = require('./base')
const await = require('await-stream-ready/lib/await')

class RecruitController extends BaseController {
    async thread_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const where = {}

        for (const item of ['id', 'tid', 'owner_name', 'count']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['subject', 'form_subject']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        ctx.json_body = await ctx.model.RecruitThread.findAndCountAll({
            where: where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
            raw: true,
        })
    }

    async thread_get_one() {
        const { ctx } = this

        ctx.json_body = await ctx.model.RecruitThread.findOne({
            where: {
                id: ctx.params.id
            },
            raw: true,
        })
    }

    async thread_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        let regex = /\[Form(\d+)\]/g
        let matched = regex.exec(body.thread_html)

        ctx.error(!matched, "表单标签不存在")
        body.form_id = matched[1]

        regex = /\[admin(\d+)\]/g
        matched = regex.exec(body.owner_html)

        ctx.error(!matched, "主理人标签不存在")
        body.owner_uid = matched[1]

        ctx.json_body = await ctx.service.recruit.thread_create(body)
    }

    async thread_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        let regex = /\[Form(\d+)\]/g
        let matched = regex.exec(body.thread_html)

        ctx.error(!matched, "表单标签不存在")
        body.form_id = matched[1]

        regex = /\[admin(\d+)\]/g
        matched = regex.exec(body.owner_html)

        ctx.error(!matched, "主理人标签不存在")
        body.owner_uid = matched[1]

        await ctx.service.recruit.thread_update(body)

        ctx.json_body = {
            success: true
        }
    }

    async thread_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitThread.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async detail_opt() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.service.recruit.detail_opt(body)

        ctx.json_body = {
            success: true
        }
    }

    async detail_report() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const report = await ctx.service.recruit.detail_report(body)

        ctx.json_body = {
            success: true,
            report
        }
    }

    async form_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const where = {}

        if (query.id) {
            where.id = query.id
        }

        if (query.subject) {
            where.subject = {
                [Op.like]: `%${query.subject}%`,
            }
        }

        if (query.type) {
            where.type = query.type
        }

        ctx.json_body = await ctx.model.RecruitForm.findAndCountAll({
            where: where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
            raw: true,
        })
    }

    async form_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = await ctx.model.RecruitForm.create(body)
    }

    async form_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitForm.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async form_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitForm.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async form_get_one() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        ctx.json_body = await ctx.model.RecruitForm.findOne({
            where: {
                id: params.id
            },
            raw: true,
        })
    }

    async owner_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const where = {}

        if (query.id) {
            where.id = query.id
        }

        if (query.name) {
            where.name = {
                [Op.like]: `%${query.name}%`,
            }
        }

        if (query.type) {
            where.type = query.type
        }

        if (query.account) {
            where.account = {
                [Op.like]: `%${query.account}%`,
            }
        }

        if (query.qr_code) {
            where.qr_code = query.qr_code
        }

        const owner = await ctx.model.RecruitOwner.findAndCountAll({
            where: where,
            offset: page,
            limit: page_size,
            raw: true,
        })

        ctx.json_body = owner
    }

    async owner_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = await ctx.model.RecruitOwner.create(body)
    }

    async owner_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitOwner.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async owner_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitOwner.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async owner_upload() {
        const { ctx, config } = this

        const filename = random_string.generate(8) + '.png'
        const month = pad(2, moment().month() + 1, '0')
        const full_path = `${config.baseDir}${config.upload_recruit_img_path}/owner/${moment().year()}/${month}`

        await fse.mkdirp(full_path)

        try {
            const parts = ctx.multipart()
            let part
            while ((part = await parts()) != null) {
                if (!part.length) {
                    part.pipe(fs.createWriteStream(`${full_path}/${filename}`))
                }
            }

            ctx.json_body = {
                fullpath: `${config.upload_public_url}${config.upload_recruit_img_path.replace('/upload', '')}/owner/${moment().year()}/${month}/${filename}`
            }
        } catch (err) {
            ctx.json_body = err
        }
    }

    async user_list() {
        const { ctx, config } = this

        let where = {}
        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.RecruitUser.findAndCountAll({
            attributes: ['id', 'username', 'nickname', 'status', 'created_at'],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async user_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (parseInt(body.type) === 1) {
            const res = await ctx.service.thread.login(body)

            body.password = ctx.helper.encode_dz_string(body.password)
            body.status = res.Message.messageval === 'login_succeed' ? 1 : 0
        } else {
            const res = await ctx.service.www.login({
                Username: body.username,
                Password: body.password,
            })

            body.password = ctx.helper.encode_dz_string(body.password)
            body.status = res ? 1 : 0
        }

        ctx.json_body = await ctx.model.RecruitUser.create(body)
    }

    async user_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (body.password) {
            body.password = ctx.helper.encode_dz_string(body.password)
        }

        await ctx.model.RecruitUser.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async user_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitUser.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async get_password() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const user = await ctx.model.RecruitUser.findOne({
            where: {
                id: body.id
            },
            raw: true,
        })

        const password = ctx.helper.decode_dz_string(user.password)

        ctx.json_body = {
            password
        }
    }

    async recruit_detail_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const thread_id = ctx.helper.decode_dz_string(body.recruit_thread_id)

        ctx.error(!thread_id, "invalid params")

        const thread = await ctx.model.RecruitThread.findOne({
            where: {
                id: thread_id
            },
            raw: true,
        })

        body.thread_id = thread_id
        body.form_id = thread.form_id
        body.thread = thread

        let userinfo = ctx.helper.decode_dz_authkey()
        ctx.error(!userinfo, "请重新登录后，再次尝试")

        userinfo = userinfo.split("\t")
        ctx.error(userinfo.length < 3, "请重新登录后，再次尝试")

        body.uid = userinfo[1]

        const url = await ctx.service.recruit.recruit_detail_create(body)

        ctx.json_body = {
            success: true,
            url
        }
    }

    async recruit_detail_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const where = {}

        for (const item of ['id', 'tid', 'form_id', 'type', 'username', 'wechat', 'qq', 'ip', 'opt']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['form_subject', 'mobile']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        ctx.json_body = await ctx.model.RecruitDetail.findAndCountAll({
            where: where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
            raw: true,
        })
    }

    async detail_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const where = {}

        for (const item of ['id', 'tid', 'form_id', 'type', 'ip', 'opt']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['form_subject', 'mobile', 'username', 'wechat', 'qq', 'city', 'status1', 'status2', 'status3', 'status4', 'ps', 'note']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        const output = await ctx.model.RecruitDetail.findAndCountAll({
            where: where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
            raw: true,
        })

        for (let row of output.rows) {
            row.behavior = await ctx.model.Behavior.findAll({
                include: [{
                    model: ctx.model.BehaviorOpt,
                    required: false,
                    as: 'opt',
                }],
                where: {
                    username: row.username,
                },
                raw: true,
            })
        }

        ctx.json_body = output
    }

    async detail_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.RecruitDetail.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async loginlog_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op
        const QueryTypes = app.Sequelize.QueryTypes

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const where = {}
        let whereSql = ''

        for (const item of ['id', 'uid', 'mobile', 'loginsuccess']) {
            if (query[item]) {
                where[item] = query[item]
                whereSql += ` and a.${item}=${query[item]}`
            }
        }
        for (const item of ['username', 'email', 'ip', 'regip']) {
            if (query[item]) {
                where[item] = query[item]
                whereSql += ` and a.${item}='${query[item]}'`
            }
        }
        for (const item of ['browserinfo']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
                whereSql += ` and a.${item} like %${query[item]}%`
            }
        }

        if (query.dateline === 'false') {
            where.dateline = {
                [Op.gte]: ctx.helper.now() - 60 * 60 * 24 * 30
            }
        }
        if (query.sameIp && query.sameIpNum && query.atLeastNum) {
            const time = ctx.helper.now() - 60 * 60 * 24 * parseInt(query.sameIpNum, 10)
            const sql = `SELECT ip FROM (SELECT email,ip FROM cc_common_userlogin_log a WHERE dateline >= ${time} ${whereSql} GROUP BY email,ip) as b GROUP BY ip HAVING COUNT(ip) >= ${query.atLeastNum}`
            let ips = await ctx.forumModel.query(sql, {
                type: QueryTypes.SELECT
            })

            ips = Object.values(ips)
            if (ips.length) {
                where.ip = ips.map(el => el.ip)
            }
        }

        ctx.json_body = await ctx.forumModel.CommonUserloginLog.findAndCountAll({
            where: where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
            raw: true,
        })
    }
}

module.exports = RecruitController
