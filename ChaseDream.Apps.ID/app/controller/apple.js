const fs = require('fs')
const BaseController = require('./base')

module.exports = app => {
    class AppleController extends BaseController {
        async login() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            try {
                const res = await ctx.service.apple.verify_id_token(body.id_token)

                ctx.error(res.sub !== body.user, "验证失败")

                const apple = await ctx.model.Apple.findOne({
                    where: {
                        user: body.user
                    }
                })

                if (!apple) {
                    ctx.json_body = {
                        exists: false
                    }
                } else {
                    const member = await ctx.model.Member.findOne({
                        where: {
                            id: apple.uid
                        }
                    })

                    const common_member = await ctx.forumModel.CommonMember.findOne({
                        where: {
                            uid: member.forum_uid
                        }
                    })

                    ctx.json_body = {
                        cookie: ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt),
                        auth_time: res.auth_time,
                    }
                }
            } catch (err) {
                ctx.error(true, err.message)
            }
        }

        async apple_app_site_association() {
            const { ctx, config } = this

            // ctx.body = config.apple.oia            

            ctx.attachment('apple-app-site-association')
            ctx.set('Content-Type', 'application/octet-stream')
            ctx.body = fs.createReadStream(`${config.baseDir}/apple-app-site-association`)
        }
    }

    return AppleController
}
