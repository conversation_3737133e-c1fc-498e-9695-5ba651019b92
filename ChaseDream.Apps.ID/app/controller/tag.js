const await = require('await-stream-ready/lib/await')
const _ = require('lodash')
const uuidv1 = require('uuid/v1')
const BaseController = require('./base')

module.exports = app => {
    class TagController extends BaseController {
        async tag_list() {
            const { ctx, app, config } = this
            const Op = app.Sequelize.Op

            let where = {}

            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            if (query.property) where.property = query.property

            if (query.s) {
                if (query.s.endsWith(' ')) {
                    where.name = query.s.trim()
                } else {
                    where.name = {
                        [Op.like]: `%${ctx.helper.insert_str(query.s)}%`,
                    }
                }

                let tags = await ctx.model.Tag.Tag.findAll({
                    attributes: ['id', 'name', 'synonym_id', 'main', 'property'],
                    where,
                    raw: true,
                })

                const main = _.filter(tags, { 'main': 1 }) || []
                const sub = _.filter(tags, { 'main': 0 }) || []

                let synonym_id = _.map(sub, ele => { return ele.synonym_id })

                const filtered = await ctx.model.Tag.Tag.findAll({
                    attributes: ['id', 'name', 'synonym_id', 'main', 'property'],
                    where: {
                        synonym_id,
                        main: 1,
                    },
                    raw: true,
                })

                const all = main.concat(filtered)

                let final = new Map()

                for (let ele of all) {
                    if (!final.has(ele.id)) {
                        final.set(ele.id, ele)
                    }
                }

                tags = final.values()
                tags = Array.from(tags)

                synonym_id = []
                for (const tag of tags) {
                    synonym_id.push(tag.synonym_id)
                }

                if (synonym_id.length > 0) {
                    where = {
                        synonym_id,
                        main: 1,
                    }
                }
            } else {
                where.main = 1
            }

            const tags = await ctx.model.Tag.Tag.findAndCountAll({
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
                raw: true,
            })

            for (let row of tags.rows) {
                const tag_ids = [row.id]

                row.synonym = await ctx.model.Tag.Tag.findAll({
                    where: {
                        synonym_id: row.synonym_id,
                        main: 0,
                    },
                    raw: true,
                })

                for (const synonym of row.synonym) {
                    tag_ids.push(synonym.id)
                }

                row.count = await ctx.model.Tag.TagThread.count({
                    where: {
                        tagid: tag_ids
                    },
                    raw: true,
                })
            }

            ctx.json_body = tags
        }

        async tag_check() {
            const { ctx } = this

            const count = await ctx.model.Tag.TagThread.count({
                where: {
                    tid: ctx.params.tid,
                },
                raw: true,
            })

            ctx.json_body = {
                success: true,
                count,
            }
        }

        async tag_get_one() {
            const { ctx } = this

            let tag = await ctx.model.Tag.Tag.findOne({
                attributes: ['id', 'name', 'synonym_id', 'property'],
                where: {
                    id: ctx.params.id,
                    main: 1,
                },
                raw: true,
            }) || {}

            if (tag.id) {
                tag.synonym = await ctx.model.Tag.Tag.findAll({
                    attributes: ['id', 'name'],
                    where: {
                        synonym_id: tag.synonym_id,
                        main: 0,
                    }
                })
            }

            ctx.json_body = tag
        }

        async tag_create() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            for (let tags in body) {
                const uuid = uuidv1().replace(/-/gi, '')

                for (let tag of body[tags]) {
                    tag.synonym_id = uuid
                    await ctx.model.Tag.Tag.create(tag)
                }
            }

            ctx.json_body = {
                success: true,
            }
        }

        async tag_create_batch() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            const tags = body.tags.split('\n')

            for (let tag of tags) {
                const property = tag.substring(tag.indexOf('['), tag.indexOf(']') + 1).replace('[', '').replace(']', '')

                let names = tag.substring(tag.indexOf(']') + 1)
                names = names.split('||').filter(row => row.length > 0)

                const uuid = uuidv1().replace(/-/gi, '')

                for (let name of names) {
                    await ctx.model.Tag.Tag.create({
                        name,
                        synonym_id: uuid,
                        main: names[0] === name ? 1 : 0,
                        property
                    })
                }
            }

            ctx.json_body = {
                success: true,
            }
        }

        async tag_update() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            for (let tag of body.tags) {
                switch (tag.action) {
                    case 'create':
                        tag.synonym_id = body.synonym_id
                        await ctx.model.Tag.Tag.create(tag)
                        break
                    case 'update':
                        await ctx.model.Tag.Tag.update(tag, {
                            where: {
                                id: tag.id,
                            }
                        })
                        break
                    case 'delete':
                        await ctx.model.Tag.Tag.destroy({
                            where: {
                                id: tag.id,
                            }
                        })
                        break
                }
            }

            ctx.json_body = {
                success: true,
            }
        }

        async tag_delete() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.Tag.Tag.destroy({
                where: {
                    id: body.id,
                }
            })

            ctx.json_body = {
                success: true,
            }
        }

        async thread_tag() {
            const { ctx, config } = this

            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            ctx.json_body = await ctx.model.Tag.ThreadTag.findAndCountAll({
                where: {},
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
                raw: true,
            })
        }

        async tag_thread() {
            const { ctx, config } = this

            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            ctx.json_body = await ctx.model.Tag.TagThread.findAndCountAll({
                where: {
                    tagid: query.tagid || 0,
                },
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
                raw: true,
            })
        }

        async tag_merge() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            const left = await ctx.model.Tag.Tag.findOne({
                where: {
                    id: body.left,
                },
                raw: true,
            })

            const right = await ctx.model.Tag.Tag.findOne({
                where: {
                    id: body.right,
                },
                raw: true,
            })

            ctx.error(!left || !right, '不存在的TagID')

            const tagids = await ctx.model.Tag.Tag.findAll({
                where: {
                    synonym_id: right.synonym_id,
                    main: 0,
                },
                raw: true,
            }).map(row => { return row.id })

            tagids.push(right.id)

            await ctx.model.Tag.TagThread.update({
                tagid: left.id,
            }, {
                where: {
                    tagid: tagids
                }
            })

            await ctx.model.Tag.Tag.destroy({
                where: {
                    id: tagids,
                },
                force: true,
            })

            ctx.json_body = {
                success: true,
            }
        }
    }

    return TagController
}
