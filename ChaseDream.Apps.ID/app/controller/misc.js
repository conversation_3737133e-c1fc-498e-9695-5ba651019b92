const _ = require('lodash')
const fs = require('fs')
const fse = require('fs-extra')
const path = require('path')
const pad = require('pad')
const fileregex = require('file-regex')
const moment = require('moment-timezone')
const randomstring = require("randomstring")
const BaseController = require('./base')
const await = require('await-stream-ready/lib/await')

class MiscController extends BaseController {
    async info() {
        const { ctx, app } = this

        const user = ctx.locals.user

        let role = await app.zrole.getRolesForUser(`${user.id}_user`)
        role = Array.from(role).map(ele => {
            return ele.substring(0, ele.indexOf('_'))
        })

        delete user.password
        delete user.permissions

        const roles = await ctx.model.Role.findAll({
            attributes: ['id', 'name'],
            where: {
                id: role
            },
            raw: true
        })

        ctx.json_body = {
            user,
            roles
        }
    }

    async contract_stamp_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let where = {}
        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1
        const user = ctx.locals.user

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        if (query.begin && query.end) {
            where = {
                [Op.and]: [{
                    created_at: {
                        [Op.gte]: query.begin
                    }
                }, {
                    created_at: {
                        [Op.lte]: query.end
                    }
                }]
            }
        } else if (query.begin) {
            where.created_at = {
                [Op.gte]: query.begin
            }
        } else if (query.end) {
            where.created_at = {
                [Op.lte]: query.end
            }
        }

        for (const item of ['id', 'apply_uid', 'apply_username', 'approve_uid', 'approve_username', 'department', 'stamp', 'status']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['contract_name', 'remark']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        if (!ctx.helper.isAdmin(user)) {
            where.department = user.department
        }

        let cs = await ctx.model.ContractStamp.findAndCountAll({
            include: [{
                attributes: ['id', 'uid', 'original', 'ext', 'type'],
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'raw_att',
            }, {
                attributes: ['id', 'uid', 'ext', 'type'],
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'final_att',
            }],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })

        cs = JSON.parse(JSON.stringify(cs))

        for (let row of cs.rows) {
            const stamp = config.contract_stamp.stamps.find(el => row.stamp === el.id)
            row.stamp_name = stamp?.name

            const apply_user = await ctx.model.User.findOne({
                where: {
                    id: row.apply_uid
                }
            })
            const approve_user = await ctx.model.User.findOne({
                where: {
                    id: row.approve_uid
                }
            })

            row.apply_username = apply_user?.realname || ''
            row.approve_username = approve_user?.realname || ''
        }

        ctx.json_body = cs
    }

    async contract_stamp_get_one() {
        const { ctx } = this

        const user = ctx.locals.user

        const where = {
            id: ctx.params.id
        }

        const contract = await ctx.model.ContractStamp.findOne({
            include: [{
                attributes: ['id', 'uid', 'original', 'ext', 'type', 'created_at'],
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'raw_att',
            }, {
                attributes: ['id', 'uid', 'ext', 'type', 'created_at'],
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'final_att',
            }],
            where,
        }) || {}

        if (!ctx.helper.isAdmin(user)) {
            const has_stamp = await ctx.service.misc.has_contract_stamp(user.id, contract.stamp)
            ctx.error(!contract || user.department !== contract.department || (user.id !== contract.apply_uid && !has_stamp), 'invalid request')
        }

        contract.stamp_positons = contract.stamp_positons && JSON.parse(contract.stamp_positons)

        ctx.json_body = contract
    }

    async contract_stamp_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)
        const user = ctx.locals.user

        const model = {
            apply_uid: user.id,
            apply_username: user.username,
            department: user.department,
            raw_aid: body.raw_aid,
            remark: body.remark,
            stamp: body.stamp && parseInt(body.stamp, 10) || 0,
            contract_name: body.contract_name || '',
            created_at: ctx.helper.now()
        }

        if (body.original) {
            model.contract_name = body.original
        }
        const created = await ctx.model.ContractStamp.create(model)

        ctx.json_body = {
            success: true,
            id: created.id,
        }
    }

    async contract_stamp_stamps() {
        const { ctx, config } = this

        const output = []
        const user = ctx.locals.user
        let stamps = []

        for (const stamp of config.contract_stamp.stamps) {
            stamps.push({
                name: stamp.name,
                id: stamp.id,
                url: `${config.upload_public_url}/contract/stamp/0.png`,
            })
        }

        const roles = await ctx.model.ContractStampsRoles.findAll({
            where: {
                uid: user.id,
                apply: true,
            },
            raw: true,
        }).map(row => row.stamp_id)

        for (const role of roles) {
            const stamp = _.filter(stamps, { 'id': role }).map(row => {
                output.push(row)
            })
        }

        ctx.json_body = output
    }

    async contract_stamp_stamps_roles() {
        const { ctx, app, config } = this

        const role = await ctx.model.Role.findOne({
            where: {
                name: '盖章模块',
            }
        })

        let users = await app.zrole.getUsersForRole(`${role.id}_role`)

        const uids = []
        for (let [key, val] of users.entries()) {
            uids.push(val.split('_')[0])
        }

        users = await ctx.model.User.findAll({
            attributes: ['id', 'username', 'realname'],
            where: {
                id: uids,
            },
            raw: true,
        })

        const stamps = await ctx.model.ContractStampsRoles.findAll({
            where: {},
            raw: true,
        })

        for (const user of users) {
            user.stamps = stamps.filter(stamp => stamp.uid === user.id)
        }

        ctx.json_body = {
            users,
            stamps: config.contract_stamp.stamps,
        }
    }

    async contract_stamp_stamps_perms() {
        const { ctx } = this

        const user = ctx.locals.user

        const approves = await ctx.model.ContractStampsRoles.findAll({
            attributes: ['stamp_id'],
            where: {
                uid: user.id,
                approve: true,
            },
            raw: true,
        }).map(row => row.stamp_id)

        const applies = await ctx.model.ContractStampsRoles.findAll({
            attributes: ['stamp_id'],
            where: {
                uid: user.id,
                apply: true,
            },
            raw: true,
        }).map(row => row.stamp_id)

        ctx.json_body = {
            applies,
            approves
        }
    }

    async contract_stamp_preview() {
        const { ctx, config } = this

        const contract = await ctx.model.ContractStamp.findOne({
            include: [{
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'raw_att',
            }, {
                model: ctx.model.ContractStampAttachment,
                required: false,
                as: 'final_att',
            }],
            where: {
                id: ctx.params.id,
            },
        })

        const pdf_file = `${config.baseDir}/upload${contract.raw_att.fullpath}`
        const tmp_path = `${pdf_file.substring(0, pdf_file.lastIndexOf('/'))}/tmp`

        let images = await fileregex(tmp_path, /idx.*\.png$/g)
        images.sort((a, b) => {
            a = a.file.split('.')[1]
            b = b.file.split('.')[1]
            return a - b
        })
        images = images.map(row => {
            let url = `${row.dir}/${row.file}`
            return config.env === 'prod' ? url.replace(`${config.baseDir}/upload`, config.upload_public_url) : `http://localhost:${config.cluster.listen.port}/misc/contract/${url.replace(config.baseDir, '').replace(config.upload_contract, '').replace(/\//g, '-').substring(1)}`
        })

        ctx.json_body = {
            success: true,
            images,
        }
    }

    async for_dev_show_img() {
        const { ctx, config } = this

        ctx.error(config.env === 'prod')

        const params = ctx.params

        let url = params.url.replaceAll('-', '/')

        ctx.attachment(url.substring(url.lastIndexOf('/') + 1))
        ctx.set('Content-Type', ' image/png')
        const full_path = `${config.baseDir}${config.upload_contract}/${url}`

        ctx.body = fs.createReadStream(full_path)
    }

    async for_dev_thread_show_img() {
        const { ctx, config } = this

        ctx.error(config.env === 'prod')

        const params = ctx.params

        let url = params.url.replaceAll('-', '/')

        ctx.attachment(url.substring(url.lastIndexOf('/') + 1))
        ctx.set('Content-Type', ' image/png')
        const full_path = `${config.baseDir}/upload/${url}`

        ctx.body = fs.createReadStream(full_path)
    }

    async contract_stamp_apply() {
        const { ctx, config } = this

        const user = ctx.locals.user

        const body = Object.assign({}, ctx.request.body)
        const cross_page_y = parseInt(body.cross_page_y, 10)

        const applies = await ctx.model.ContractStampsRoles.findAll({
            attributes: ['stamp_id'],
            where: {
                uid: user.id,
                apply: true,
            },
            raw: true,
        }).map(row => row.stamp_id)

        const contract = await ctx.model.ContractStamp.findOne({
            where: {
                id: body.id,
            }
        })

        ctx.error(!applies.includes(contract.stamp), '没有操作权限')

        await ctx.model.ContractStamp.update({
            status: 3,
            cross_page_y,
            stamp_positons: JSON.stringify(body.stamp_positons),
        }, {
            where: {
                id: body.id,
            }
        })

        const dep = config.department.find(el => el.id === contract.department)
        const stamp = config.contract_stamp.stamps.find(el => el.id === contract.stamp)

        const title = '【合同盖章提示】'
        const message = `【${dep.name}】部门【${contract.apply_username}】发起了一个【${stamp.name}】盖章申请，请处理。`

        const uids = await ctx.model.ContractStampsRoles.findAll({
            attributes: ['uid'],
            where: {
                stamp_id: contract.stamp,
                approve: true,
            },
            raw: true,
        }).map(row => row.uid)

        for (const uid of uids) {
            await ctx.service.misc.send_notice(uid, title, message)
        }

        ctx.json_body = {
            success: true,
        }
    }

    async contract_stamp_approve() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        const cross_page_y = parseInt(body.cross_page_y, 10)
        const stamp = body.stamp && parseInt(body.stamp, 10) || 0
        const user = ctx.locals.user

        const approves = await ctx.model.ContractStampsRoles.findAll({
            attributes: ['stamp_id'],
            where: {
                uid: user.id,
                approve: true,
            },
            raw: true,
        }).map(row => row.stamp_id)

        const cs = await ctx.model.ContractStamp.findOne({
            where: {
                id: body.id,
            }
        })

        ctx.error(!approves.includes(cs.stamp), '没有操作权限')

        let um = {
            approve_uid: user.id,
            approve_username: user.username,
            status: 4,
            cross_page_y,
            stamp_positons: JSON.stringify(body.stamp_positons),
            approve_at: ctx.helper.now()
        }

        if (stamp !== 0) um.stamp = stamp

        await ctx.model.ContractStamp.update(um, {
            where: {
                id: body.id,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async contract_stamp_reject() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const status = parseInt(body.status, 10)

        await ctx.model.ContractStamp.update({
            status,
            message: body.message,
            approve_at: ctx.helper.now()
        }, {
            where: {
                id: body.id,
            }
        })

        if (status === -1 || status === -2) {
            const contract = await ctx.model.ContractStamp.findOne({
                where: { id: body.id }
            })

            let reason = ''
            if (contract.status === -1) {
                reason = '被拒绝盖章'
            } else if (contract.status === -2) {
                reason = '被驳回修改'
            }

            const title = '【合同盖章提示】'
            const message = `你申请的【文件名】${reason}，请查看。`

            await ctx.service.misc.send_notice(contract.apply_uid, title, message)
        }

        ctx.json_body = {
            success: true,
        }
    }

    async contract_upload() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)
        const user = ctx.locals.user
        let attach = {}

        try {
            const parts = ctx.multipart()
            let part
            while ((part = await parts()) != null) {
                if (!part.length) {
                    let ext = path.extname(part.filename)?.toLocaleLowerCase()
                    let month = pad(2, moment().month() + 1, '0')
                    let date = pad(2, moment().date(), '0')
                    let filename = ctx.helper.today('YYYYMMDDHHmmss')
                    let filepath = `${config.upload_contract}/${moment().year()}/${month}/${date}/${filename}`

                    await fse.mkdirp(`${config.baseDir}${filepath}`)
                    let fullpath = `${filepath}/${filename}${ext}`
                    part.pipe(fs.createWriteStream(`${config.baseDir}${fullpath}`))

                    attach = await ctx.model.ContractStampAttachment.create({
                        uid: user.id,
                        original: part.filename.substring(0, part.filename.lastIndexOf('.')),
                        fullpath: fullpath.replace('/upload/', '/'),
                        ext,
                        type: part.mime,
                        created_at: ctx.helper.now(),
                    })
                } else if (part.length) {
                    await ctx.model.ContractStamp.update({
                        raw_aid: attach.id,
                        stamp_positons: '',
                        cross_page_y: 0,
                        status: 0,
                    }, {
                        where: {
                            id: part[1],
                        }
                    })
                }
            }

            ctx.json_body = {
                aid: attach.id
            }
        } catch (err) {
            ctx.json_body = err
        }
    }

    async contract_download() {
        const { ctx, app, config } = this

        const body = Object.assign({}, ctx.request.body)
        const user = ctx.locals.user

        const id = parseInt(body.id, 10) || 0
        const type = parseInt(body.type, 10) || 0
        const aid = parseInt(body.aid, 10) || 0
        const file = body.file || ''

        const where = {
            id,
        }

        if (type === 0) {
            where.raw_aid = aid
        } else {
            where.final_aid = aid
        }

        const contract = await ctx.model.ContractStamp.findOne({
            where
        })

        if (!ctx.helper.isAdmin(user)) {
            const has_stamp = await ctx.service.misc.has_contract_stamp(user.id, contract.stamp)
            ctx.error(!contract || user.department !== contract.department || (user.id !== contract.apply_uid && !has_stamp), 'invalid request')
        }

        const ca = await ctx.model.ContractStampAttachment.findOne({
            where: {
                id: aid,
            }
        })

        ctx.attachment(`[已盖章] ${ca.original}`)
        ctx.set('Content-Type', 'application/octet-stream')

        let full_path = `${config.baseDir}/upload${ca.fullpath}`
        const ext = path.extname(full_path)
        if (file === 'pdf') full_path = full_path.replace(ext, '.pdf')

        ctx.body = fs.createReadStream(full_path)
    }

    async contract_stamp_stamps_roles_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const uid = parseInt(body.uid, 10) || 0
        const apply = parseInt(body.apply, 10) || 0
        const approve = parseInt(body.approve, 10) || 0

        const role = await ctx.model.ContractStampsRoles.findOne({
            where: {
                uid,
                stamp_id: body.stamp_id,
            }
        })

        if (role) {
            await ctx.model.ContractStampsRoles.update({
                apply,
                approve,
            }, {
                where: {
                    id: role.id,
                }
            })
        } else {
            await ctx.model.ContractStampsRoles.create({
                uid,
                stamp_id: body.stamp_id,
                apply,
                approve,
                created_at: ctx.helper.now()
            })
        }

        ctx.json_body = {
            success: true,
        }
    }

    async contract_stamp_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.ContractStamp.update(body, {
            where: { id: body.id }
        })

        if (body.contract_name && body.raw_aid) {
            await ctx.model.ContractStampAttachment.update({
                original: body.contract_name,
            }, {
                where: {
                    id: body.raw_aid,
                }
            })
        }

        ctx.json_body = {
            success: true
        }
    }

    async contract_stamp_delete() {
        const { ctx, app } = this

        const body = Object.assign({}, ctx.request.body)
        const user = ctx.locals.user

        const contract = await ctx.model.ContractStamp.findOne({
            where: {
                id: body.id
            }
        })

        if (!ctx.helper.isAdmin(user)) {
            ctx.error(!contract || ![0, 1, 2].includes(contract.status) || user.id !== contract.apply_uid, 'invalid request')
        }

        await ctx.model.ContractStamp.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async member_activity_log() {
        const { ctx, app } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.error(!body.uid || !body.tid || !body.fid, 'invalid params')

        await app.mongo.MemberActivityLog.create({
            uid: body.uid,
            tid: body.tid,
            fid: body.fid,
            action_id: body.action_id,
            created_at: ctx.helper.now(),
        })

        ctx.json_body = {
            success: true,
        }
    }

    async member_activity_log_list() {
        const { ctx, app, config } = this
        const where = {}

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        for (const item of ['uid', 'fid', 'tid', 'action_id']) {
            if (query[item]) where[item] = query[item]
        }

        const count = await app.mongo.MemberActivityLog.count(where)
        const rows = await app.mongo.MemberActivityLog.find(where, null, {
            skip: page,
            limit: page_size,
            sort: { created_at: -1 },
        })

        ctx.json_body = {
            count,
            rows
        }
    }

    async university_list() {
        const { ctx, config } = this
        const where = {}

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        if (query.s) {
            const all = await ctx.model.UniversityDic.findAll({
                where: {},
                order: [
                    ['id', 'DESC'],
                ],
            }).filter(row => row.keyword.toLowerCase().indexOf(query.s.toLowerCase()) > -1)

            ctx.json_body = {
                count: all.length,
                rows: all
            }
        } else {
            ctx.json_body = await ctx.model.UniversityDic.findAndCountAll({
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
            })
        }
    }

    async university_get_one() {
        const { ctx } = this

        const params = ctx.params

        let university = await ctx.model.UniversityDic.findOne({
            where: {
                id: params.university_id
            },
            raw: true,
        }) || {}

        university.school_area = await ctx.model.SchoolAreaDic.findAll({
            include: [{
                model: ctx.model.SchoolAreaPhoto,
                required: false,
                as: 'school_area_photo',
            }],
            where: {
                university_id: params.university_id
            },
        })

        ctx.json_body = JSON.parse(JSON.stringify(university))
    }

    async university_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const university = await ctx.model.UniversityDic.create(body)

        for (let school_area of body.school_area) {
            school_area.university_id = university.id
            const sa = await ctx.model.SchoolAreaDic.create(school_area)

            if (school_area.photoid && school_area.photoid.length > 0) {
                await ctx.model.SchoolAreaPhoto.update({
                    school_area_id: sa.id,
                }, {
                    where: {
                        id: school_area.photoid
                    }
                })
            }
        }

        ctx.json_body = university
    }

    async university_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.UniversityDic.update(body, {
            where: { id: body.id }
        })

        for (let school_area of body.school_area) {
            switch (school_area.action) {
                case 'create':
                    school_area.university_id = body.id
                    const sa = await ctx.model.SchoolAreaDic.create(school_area)
                    school_area.id = sa.id
                    break
                case 'update':
                    await ctx.model.SchoolAreaDic.update(school_area, {
                        where: { id: school_area.id }
                    })
                    break
                case 'delete':
                    await ctx.model.SchoolAreaDic.destroy({
                        where: { id: school_area.id },
                    })
                    break
            }

            if (school_area.photo && school_area.photo.length > 0) {
                for (const photo of school_area.photo) {
                    switch (photo.action) {
                        case 'create':
                            await ctx.model.SchoolAreaPhoto.update({
                                school_area_id: school_area.id,
                            }, {
                                where: {
                                    id: photo.id
                                }
                            })
                            break
                        case 'delete':
                            await ctx.model.SchoolAreaPhoto.destroy({
                                where: { id: photo.id }
                            })
                            break
                    }
                }
            }
        }

        ctx.json_body = {
            success: true,
        }
    }

    async university_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.UniversityDic.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async program_list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.ProgramDic.findAll({
            where: {},
            order: [
                ['id', 'DESC'],
            ],
        })
    }

    async program_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        if (body.program) {
            for (const program of body.program) {
                program.school_id = body.school_id
                await ctx.model.ProgramDic.create(program)
            }

            ctx.json_body = {
                success: true,
            }
        } else {
            ctx.json_body = await ctx.model.ProgramDic.create(body)
        }
    }

    async program_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.ProgramDic.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async program_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.ProgramDic.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async program_direction_list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.ProgramDirectionDic.findAll({
            include: [{
                attributes: ['parent_id', 'name'],
                model: ctx.model.ProgramDirectionOrgTypeDic,
                required: false,
                as: 'organization_type',
            }, {
                attributes: ['parent_id', 'name'],
                model: ctx.model.ProgramDirectionOrgTypeDic,
                required: false,
                as: 'program_direction_type',
            }],
            where: {},
            order: [
                ['id', 'DESC'],
            ],
        })
    }

    async program_degree_category_list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.ProgramDegreeCategoryDic.findAll({
            where: {},
            order: [
                ['id', 'DESC'],
            ],
        })
    }

    async major_category_list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.MajorCategoryDic.findAll({
            where: {},
            order: [
                ['id', 'DESC'],
            ],
        })
    }

    async major_category_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = await ctx.model.MajorCategoryDic.create(body)
    }

    async major_category_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.MajorCategoryDic.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async major_category_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.MajorCategoryDic.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async school_area_photo_upload() {
        const { ctx, config } = this

        const query = Object.assign({}, ctx.query)
        let attach = {}

        if (!query.school_area_id) {
            ctx.error(true, "缺少参数")
        }

        try {
            const output = []
            const parts = ctx.multipart()
            let part
            while ((part = await parts()) != null) {
                if (!part.length) {
                    let month = pad(2, moment().month() + 1, '0')
                    let path = `${config.upload_school_area}/${moment().year()}/${month}`
                    let filename = randomstring.generate(8)

                    await fse.mkdirp(`${config.baseDir}${path}`)
                    let fullpath = `${path}/${filename}${part.filename.substring(part.filename.lastIndexOf('.'))}`
                    part.pipe(fs.createWriteStream(`${config.baseDir}${fullpath}`))

                    attach = await ctx.model.SchoolAreaPhoto.create({
                        school_area_id: query.school_area_id || 0,
                        original: part.filename,
                        fullpath: fullpath.replace('/upload/', '/'),
                        type: part.mime
                    })
                    output.push({
                        aid: attach.id,
                        fullpath: attach.fullpath,
                        type: attach.type,
                    })
                }
            }

            ctx.json_body = output
        } catch (err) {
            ctx.json_body = err
        }
    }

    async school_get_one() {
        const { ctx } = this

        const params = ctx.params

        let school = await ctx.model.SchoolDic.findOne({
            include: [{
                model: ctx.model.ProgramDic,
                required: false,
                as: 'program',
            }],
            where: {
                id: params.school_id
            },
        })

        school = JSON.parse(JSON.stringify(school))

        // for (let school_area of school.school_area) {
        //     const country = await ctx.model.Geo.findOne({
        //         attributes: ['name'],
        //         where: {
        //             id: school_area.country_id || 0
        //         },
        //         raw: true,
        //     }) || {}
        //     school_area.country = country.name || ''

        //     const province = await ctx.model.Geo.findOne({
        //         attributes: ['name'],
        //         where: {
        //             id: school_area.province_id || 0
        //         },
        //         raw: true,
        //     }) || {}
        //     school_area.province = province.name || ''

        //     const city = await ctx.model.Geo.findOne({
        //         attributes: ['name'],
        //         where: {
        //             id: school_area.city_id || 0
        //         },
        //         raw: true,
        //     }) || {}
        //     school_area.city = city.name || ''
        // }

        ctx.json_body = school
    }

    async school_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const path = `${config.upload_event_img_path}/${body.directory}`

        if (!fs.existsSync(`${config.baseDir}${path}`)) {
            ctx.error(true, "目录不存在，请联系管理员创建目录")
        }

        const university = await ctx.model.UniversityDic.create({
            university_name_en: body.university_name,
            university_name_cn: body.university_name_cn,
            display_name: body.display_name,
        })
        body.university_id = university.id

        const school = await ctx.model.SchoolDic.create(body)
        for (let school_area of body.school_area) {
            school_area.university_id = university.id

            const sa = await ctx.model.SchoolAreaDic.create(school_area)

            await ctx.model.SchoolAreaSchool.create({
                school_area_id: sa.id,
                school_id: school.id,
            })

            if (school_area.photoid && school_area.photoid.length > 0) {
                await ctx.model.SchoolAreaPhoto.update({
                    school_area_id: sa.id,
                }, {
                    where: {
                        id: school_area.photoid
                    }
                })
            }
        }

        ctx.json_body = school
    }

    async school_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const path = `${config.upload_event_img_path}/${body.directory}`

        if (!fs.existsSync(`${config.baseDir}${path}`)) {
            ctx.error(true, "目录不存在，请联系管理员创建目录")
        }

        await ctx.model.UniversityDic.update({
            university_name_en: body.university_name,
            university_name_cn: body.university_name_cn,
            display_name: body.display_name,
        }, { where: { id: body.university_id } })

        await ctx.model.SchoolDic.update(body, {
            where: { id: body.id }
        })

        for (let school_area of body.school_area) {
            switch (school_area.action) {
                case 'create':
                    school_area.school_id = body.id
                    school_area.university_id = body.university_id || 0

                    const sa = await ctx.model.SchoolAreaDic.create(school_area)

                    await ctx.model.SchoolAreaSchool.create({
                        school_area_id: sa.id,
                        school_id: body.id,
                    })
                    break
                case 'update':
                    school_area.school_id = body.id
                    school_area.university_id = body.university_id || 0

                    await ctx.model.SchoolAreaDic.update(school_area, {
                        where: { id: school_area.id }
                    })
                    break
                case 'delete':
                    await ctx.model.SchoolAreaSchool.destroy({
                        where: {
                            school_area_id: school_area.id,
                            school_id: body.id,
                        },
                    })
                    await ctx.model.SchoolAreaDic.destroy({
                        where: { id: school_area.id },
                    })
                    break
            }
        }

        ctx.json_body = {
            success: true,
        }
    }

    async geo() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)
        let where = {}

        if (query.parent_id) {
            where.parent_id = query.parent_id
        } else {
            where.parent_id = 0
        }

        if (query.s) {
            where.keyword = {
                [Op.like]: `%${query.s}%`
            }

            delete where.parent_id
        }

        ctx.json_body = await ctx.model.Geo.findAll({
            attributes: ['id', 'name', 'parent_id'],
            where: where,
            order: [
                ['order', 'DESC'],
            ],
            raw: true
        })
    }

    async geo_get_one() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)
        let where = {
            id: params.id,
        }

        ctx.json_body = await ctx.model.Geo.findOne({
            attributes: ['id', 'name', 'parent_id'],
            where: where,
            raw: true
        })
    }

    async ip_location() {
        const { ctx, config } = this

        const query = Object.assign({}, ctx.query)

        const ip = ctx.ip || query.ip

        const res = await ctx.curl(`${config.ip_location.aliyun.url}${ip}`, {
            method: 'GET',
            dataType: 'json',
            headers: {
                'Authorization': `APPCODE ${config.ip_location.aliyun.APPCODE}`,
            },
            timeout: 5000,
        })

        const output = res.data.ENTITY.INPUT_IP_ADDRESS

        const city = res.data.ENTITY.INPUT_IP_ADDRESS.CITY
        const nation = res.data.ENTITY.INPUT_IP_ADDRESS.NATION

        if (city) {
            const geo = await ctx.model.Geo.findOne({
                where: {
                    name: city,
                }
            }) || {}

            output.city_id = geo.id
        } else if (nation) {
            const geo = await ctx.model.Geo.findOne({
                where: {
                    name: nation,
                }
            }) || {}

            output.country_id = geo.id
        }

        ctx.json_body = output
    }

    async timezone() {
        const { ctx } = this

        const names = moment.tz.names()
        const output = new Map()

        for (const name of names) {
            let [key, value1, value2] = name.split('/')

            if (value2) {
                value1 = `${value1}/${value2}`
            }

            if (output.has(key)) {
                let v = output.get(key)
                v.push(value1)
                output.set(key, v)
            } else {
                output.set(key, value1 ? [value1] : '')
            }
        }

        ctx.json_body = Object.fromEntries(output)
    }

    async tag_get_one() {
        const { ctx } = this

        const tagids = await ctx.model.Tag.TagThread.findAll({
            attributes: ['tagid'],
            where: {
                tid: ctx.params.tid,
            },
            raw: true,
        }).map(row => row.tagid)

        const all = await ctx.model.Tag.Tag.findAll({
            attributes: ['id', 'name', 'synonym_id', 'main', 'property'],
            where: {
                id: tagids,
            },
            raw: true,
        })

        const one = _.filter(all, { 'property': 1 }) || []
        const two = _.filter(all, { 'property': 2 }) || []
        const three = _.filter(all, { 'property': 3 }) || []
        const four = _.filter(all, { 'property': 4 }) || []

        ctx.json_body = {
            one,
            two,
            three,
            four
        }
    }

    async tag_get_property_list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.Tag.Tag.findAll({
            attributes: ['id', 'name', 'synonym_id', 'order'],
            where: {
                property: ctx.params.property,
                main: 1,
            },
            order: [
                ['order', 'ASC'],
            ],
            raw: true,
        })
    }

    async tag_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        for (let tag of body.tags) {
            switch (tag.action) {
                case 'create':
                    await ctx.service.misc.tag_create(body.tid, tag.name, tag.property)
                    break
                case 'bind':
                    await ctx.service.misc.tag_bind(body.tid, tag.tagid)
                    break
            }
        }

        ctx.json_body = {
            success: true,
        }
    }

    async tag_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.service.misc.tag_delete(body.tid, body.tagid)

        ctx.json_body = {
            success: true,
        }
    }

    async tag_search() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const body = Object.assign({}, ctx.request.body)

        let where = {}

        if (body.property) where.property = body.property

        if (body.s.endsWith(' ')) {
            where.name = body.s.trim()
        } else {
            where.name = {
                [Op.like]: `%${ctx.helper.insert_str(body.s)}%`,
            }
        }

        let tags = await ctx.model.Tag.Tag.findAll({
            attributes: ['id', 'name', 'synonym_id', 'main', 'property'],
            where,
            raw: true,
        })

        const main = _.filter(tags, { 'main': 1 }) || []
        const sub = _.filter(tags, { 'main': 0 }) || []

        const synonym_id = _.map(sub, ele => { return ele.synonym_id })

        const filtered = await ctx.model.Tag.Tag.findAll({
            attributes: ['id', 'name', 'synonym_id', 'main', 'property'],
            where: {
                synonym_id,
                main: 1,
            },
            raw: true,
        })

        const all = main.concat(filtered)

        let final = new Map()

        for (let ele of all) {
            if (!final.has(ele.id)) {
                const synonyms = await ctx.model.Tag.Tag.findAll({
                    attributes: ['name'],
                    where: {
                        synonym_id: ele.synonym_id,
                        main: 0,
                    },
                    raw: true,
                }).map(row => { return row.name })

                ele.synonyms = synonyms
                final.set(ele.id, ele)
            }
        }

        tags = final.values()
        tags = Array.from(tags)

        ctx.json_body = tags
    }

    async get_added_tids() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        let res = await ctx.model.Tag.ThreadTag.findAll({
            attributes: ['id', 'tid'],
            where: {
                tid: body.tids,
            },
            raw: true,
        })

        if (res.length > 0) {
            let tids = res.map(el => el.tid)
            const tidsS = new Set(tids)
            tids = Array.from(tidsS)

            const map = new Map()

            for (const tid of tids) {
                const count = await ctx.model.Tag.TagThread.count({
                    where: {
                        tid,
                    }
                })

                map.set(tid, count)
            }

            for (const o of res) {
                if (map.has(o.tid)) o.count = map.get(o.tid)
            }
        }

        ctx.json_body = res
    }

    async fish_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op
        const where = {}

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        for (const item of ['id', 'lastip']) {
            if (query[item]) where[item] = query[item]
        }

        for (const item of ['username']) {
            if (query[item]) {
                where[item] = {
                    [Op.like]: `%${query[item]}%`,
                }
            }
        }

        ctx.json_body = await ctx.model.Fish.findAndCountAll({
            where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
        })
    }

    async fish_create() {
        const { ctx } = this

        let username = ''
        const querystring = ctx.querystring || ''
        const url = `https://forum.chasedream.com/forum.php?${querystring}`

        let userinfo = ctx.helper.decode_dz_authkey()
        if (userinfo) {
            userinfo = userinfo.split("\t")

            const common_member = await ctx.forumModel.CommonMember.findOne({
                where: {
                    uid: userinfo[1],
                }
            })
            username = common_member.username
        }

        await ctx.model.Fish.create({
            username: username || '',
            ip: ctx.ip,
            created_at: ctx.helper.now(),
        })

        ctx.redirect(url)
    }

    async monitor_sameip_whitelist_list() {
        const { ctx, config } = this
        const where = {}

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.MonitorSameipWhitelist.findAndCountAll({
            where,
            offset: page,
            limit: page_size,
            order: [
                ['id', 'DESC'],
            ],
        })
    }

    async monitor_sameip_whitelist_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        body.created_at = ctx.helper.now()

        await ctx.model.MonitorSameipWhitelist.create(body)

        ctx.json_body = {
            success: true,
        }
    }

    async monitor_sameip_whitelist_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.MonitorSameipWhitelist.update({
            ip: body.ip,
            remark: body.remark,
        }, {
            where: {
                id: body.id,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async monitor_sameip_whitelist_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.MonitorSameipWhitelist.destroy({
            where: {
                id: body.id,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async taglist() {
        const { ctx } = this

        let where = {
            tagid: ctx.params.tagid || 0,
        }

        const query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || 50
        const page = (query.page - 1) * page_size

        if (query.fid) {
            where.fid = query.fid
        }

        ctx.json_body = await ctx.model.Tag.TagThread.findAndCountAll({
            attributes: ['tid'],
            where,
            offset: page,
            limit: page_size,
            order: [
                ['tid', 'ASC'],
            ],
            raw: true,
        })
    }

    async validateUser() {
        const { ctx } = this
        const res = {
            success: false,
        }

        let userinfo = ctx.helper.decode_dz_authkey()
        if (userinfo) {
            userinfo = userinfo.split("\t")

            const member = await ctx.forumModel.UcenterMember.findOne({
                where: {
                    uid: userinfo[1]
                }
            })

            res.success = !!member
            res.member = member
        }

        ctx.json_body = res
    }
}

module.exports = MiscController
