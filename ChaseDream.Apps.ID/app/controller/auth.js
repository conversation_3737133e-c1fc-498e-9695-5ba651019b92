const fs = require('fs')
const md5 = require('md5')
const path = require('path')
const crypto = require('crypto')
const Geetest = require('gt3-sdk')
const NodeRSA = require('node-rsa')
const tencentcloud = require("tencentcloud-sdk-nodejs")
const password_validator = require('password-validator')
const QRCode = require('qrcode')
const randomstring = require("randomstring")
const validator = require('validator')
const { addSlashes, stripSlashes } = require('slashes')
const { code, areaCode } = require('../tools/area_code')
const BaseController = require('./base')
const await = require('await-stream-ready/lib/await')
const { toUpper } = require('lodash')
const _ = require('lodash')

class AuthController extends BaseController {
    async index() {
        const { ctx } = this

        await ctx.render('index')
    }

    async geetest_register() {
        const { ctx } = this

        ctx.json_body = await this.geetest_slide_register()
    }

    geetest_slide_register() {
        return new Promise((resolve, reject) => {
            const { ctx, config } = this

            const captcha = new Geetest({
                geetest_id: config.geetest.geetest_id,
                geetest_key: config.geetest.geetest_key
            })
            captcha.register(null, function (err, data) {
                if (err) {
                    reject(err)
                }

                ctx.session.fallback = !data.success
                resolve(data)
            })
        })
    }

    geetest_slide_validate() {
        return new Promise((resolve, reject) => {
            const { ctx, config } = this

            const captcha = new Geetest({
                geetest_id: config.geetest.geetest_id,
                geetest_key: config.geetest.geetest_key
            })

            const { geetest_challenge, geetest_validate, geetest_seccode } = ctx.request.body

            captcha.validate(ctx.session.fallback, {
                geetest_challenge: geetest_challenge,
                geetest_validate: geetest_validate,
                geetest_seccode: geetest_seccode
            }, function (err, success) {
                if (err) {
                    reject(err)
                }

                resolve(success)
            })
        })
    }

    async tencent_verify() {
        const { ctx } = this

        const res = await this.tencent_slide_validate()

        ctx.json_body = { res }
    }

    tencent_slide_validate() {
        return new Promise((resolve, reject) => {
            const { ctx, config } = this

            const body = Object.assign({}, ctx.request.body)

            const CaptchaClient = tencentcloud.captcha.v20190722.Client

            const clientConfig = {
                credential: {
                    secretId: config.tencent.secretId,
                    secretKey: config.tencent.secretKey,
                },
                region: "",
                profile: {
                    httpProfile: {
                        endpoint: "captcha.tencentcloudapi.com",
                    },
                },
            }

            const client = new CaptchaClient(clientConfig)
            const params = {
                "Ticket": body.ticket,
                "Randstr": body.randstr,
                "CaptchaType": 9,
                "UserIp": ctx.ip,
                "CaptchaAppId": config.tencent.CaptchaAppId,
                "AppSecretKey": config.tencent.AppSecretKey,
            }
            client.DescribeCaptchaResult(params).then(
                (data) => {
                    if (data.CaptchaCode === 1) {
                        resolve(true)
                    } else {
                        reject(data.CaptchaMsg)
                    }
                },
                (err) => {
                    reject(err)
                }
            )
        })
    }

    async slide_validate() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        let success = undefined

        try {
            // if (body.ticket && body.randstr) {
            success = await this.tencent_slide_validate()
            // }
            // else {
            //     success = await this.geetest_slide_validate()
            // }

            ctx.error(!success, "验证码异常")
        } catch (err) {
            ctx.error(true, "验证码异常")
        }
    }

    async identity() {
        const { ctx, config } = this

        const body = ctx.request.body

        ctx.error(!body.token, 'token不能为空')

        const auth = Buffer.from(`${config.jiguang.identity.key}:${config.jiguang.identity.secret}`).toString('base64')

        const res = await ctx.curl('https://api.verification.jpush.cn/v1/web/loginTokenVerify', {
            method: 'POST',
            dataType: 'json',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Basic ${auth}`,
            },
            data: {
                'loginToken': body.token,
            }
        })

        let data = res.data

        ctx.error(data.code !== 8000, data.content)

        const priv = fs.readFileSync(path.join(__dirname, '../../jiguang_identity.pem')).toString()

        const buffer = Buffer.from(data.phone, 'base64')
        data.phone = crypto.privateDecrypt(
            {
                key: priv.toString(),
                passphrase: '',
                padding: crypto.constants.RSA_PKCS1_PADDING
            },
            buffer,
        ).toString('utf8')

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                area_code: 86,
                mobile_encrypt: ctx.helper.AESEncrypt(data.phone),
            }
        })

        ctx.error(!mobile, "手机号不存在，请先注册")

        const member = await ctx.model.Member.findOne({
            where: {
                id: mobile.uid
            }
        })

        await ctx.service.auth.move_to_master(member.forum_uid)

        const common_member = await ctx.forumModel.CommonMember.findOne({
            where: {
                uid: member.forum_uid
            }
        })

        ctx.error(common_member.groupid == 5 || common_member.status < 0, "您的账号被禁用")

        const cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt)

        await ctx.service.auth.create_userlogin_log({
            uid: common_member.uid,
            email: common_member.email,
            username: common_member.username,
            dateline: ctx.helper.now(),
            ip: ctx.ip,
            loginsuccess: 1,
            browserinfo: ctx.get('user-agent') || '',
            phonestamp: body.phonestamp || '',
            loginmethod: `${(body.platform || 0)}1`,
            referer: ctx.get('referer') || '',
        })

        if (member.phonestamp === '') {
            await ctx.model.Member.update({
                phonestamp: body.phonestamp || ''
            }, {
                where: {
                    id: member.id
                }
            })
        } else if (member.phonestamp !== body.phonestamp) {
            await ctx.model.Member.update({
                phonestamp2: body.phonestamp || ''
            }, {
                where: {
                    id: member.id
                }
            })
        }

        data = Object.assign({
            cookiepre: config.discuz.prefix,
            uid: common_member.uid,
            member_uid: common_member.uid,
            member_username: common_member.username,
            member_email: common_member.email,
            member_avatar: `${config.discuz.home_url}/uc_server/avatar.php?uid=${common_member.uid}&size=small`,
            groupid: common_member.groupid,
            uploadhash: md5(md5(config.discuz.authkey).substring(8) + common_member.uid),
        }, cookie, data)

        ctx.json_body = {
            success: true,
            data,
        }
    }

    async check_login() {
        const { ctx, config } = this

        const output = {
            uid: 0,
            username: '',
        }

        let userinfo = config.env === 'prod' ? ctx.helper.decode_dz_authkey() : `0\t${config.dev.uid}\t0`

        if (userinfo) {
            userinfo = userinfo.split("\t")
            output.uid = userinfo[1]

            const member = await ctx.model.Member.findOne({
                where: {
                    forum_uid: userinfo[1],
                }
            })

            output.username = member?.username
        }

        ctx.json_body = output
    }

    async admin_login() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const { username, password } = ctx.request.body

        await ctx.verify('user.login', 'body')

        await this.slide_validate()

        let user = await ctx.model.User.findOne({
            where: {
                username: username,
                role: {
                    [Op.gt]: 0
                }
            }
        })

        ctx.error(!user || user.password !== md5(password), "用户名或密码错误")

        const token = app.jwt.sign({
            uid: user.id,
            role: user.role
        }, config.jwt.secret, { expiresIn: '7d' })

        ctx.json_body = {
            access_token: token,
            uid: user.id,
            username: user.username
        }
    }

    async admin_logout() {
        const { ctx } = this

        ctx.status = 204
    }

    async logout_forum() {
        const { ctx } = this

        ctx.helper.logout_forum()

        ctx.json_body = {
            success: true,
        }
    }

    async qrcode() {
        const { ctx, app, config } = this

        const random = randomstring.generate(45)
        const token = encodeURIComponent(ctx.helper.encode_dz_string(random))
        const lasttime = ctx.helper.now() + config.app_scan_login_timeout

        const url = `https://id.chasedream.com/api/v1/auth/verify/qrcode?token=${token}&lasttime=${lasttime}`

        await app.redis.setex(`app.scan_login:${token}`, config.app_scan_login_timeout, 0)

        ctx.json_body = {
            image: await QRCode.toDataURL(url, { errorCorrectionLevel: 'H' }),
            token,
            lasttime
        }
    }

    async verify_qrcode_check() {
        const { ctx, app } = this

        const token = ctx.request.query.token

        const ret = {
            uid: await app.redis.get(`app.scan_login:${encodeURIComponent(token)}`) || 0
        }

        ctx.json_body = ret
    }

    async verify_qrcode_handle() {
        const { ctx, app, config } = this

        const userinfo = ctx.helper.get_userinfo_from_cookie()

        let token = ctx.request.query.token || ''

        ctx.error(!token, '验证失败')
        ctx.error(!ctx.helper.decode_dz_string(token), '验证失败')

        token = encodeURIComponent(token)

        const exist = await app.redis.exists(`app.scan_login:${token}`)
        ctx.error(!exist, '二维码过期')

        await app.redis.setex(`app.scan_login:${token}`, config.app_scan_login_timeout, userinfo[1])

        await ctx.forumModel.CommonAdmincpSession.upsert({
            uid: userinfo[1],
            adminid: 1,
            panel: 1,
            ip: ctx.ip,
            dateline: ctx.helper.now(),
            errorcount: -1,
            storage: '',
        })

        ctx.json_body = {
            success: true
        }
    }

    async check_mobile() {
        const { ctx } = this

        await ctx.verify('auth.checkmobile', 'body')

        const body = Object.assign({}, ctx.request.body)

        body.area_code = parseInt(body.area_code, 10)

        ctx.error(!code.includes(body.area_code), "区号目前不支持")

        ctx.json_body = {
            exist: await ctx.service.auth.check_mobile(body.area_code, body.mobile)
        }
    }

    async sms_send_register() {
        const { ctx, config } = this

        ctx.json_body = await this.sms_send_core(config.sms.type.register)
    }

    async sms_send_change_mobile() {
        const { ctx, config } = this

        ctx.json_body = await this.sms_send_core(config.sms.type.change_mobile)
    }

    async sms_send_binding_mobile() {
        const { ctx, config } = this

        ctx.json_body = await this.sms_send_core(config.sms.type.binding_mobile)
    }

    async sms_send_recovery_password() {
        const { ctx, config } = this

        await ctx.verify('auth.sendsms', 'body')

        const body = Object.assign({}, ctx.request.body)
        const area_code = body.area_code
        const mobile = body.mobile.trim()

        ctx.error(!code.includes(body.area_code), "区号目前不支持")
        ctx.error(await ctx.service.auth.is_blocked(area_code, mobile), "该号码已进入黑名单")

        const mobile_info = await ctx.model.Mobile.findOne({
            where: {
                area_code,
                mobile_encrypt: ctx.helper.AESEncrypt(mobile)
            },
            raw: true,
        })

        await this.slide_validate()

        ctx.error(!mobile_info, "号码不存在")

        const member = await ctx.model.Member.findOne({
            where: {
                id: mobile_info.uid
            },
            raw: true,
        })

        const uc_member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                uid: member.forum_uid
            },
            raw: true,
        })

        ctx.error(uc_member.password.length < 10, 'error')

        const behavior = await ctx.model.Behavior.findOne({
            where: {
                username: uc_member.username,
            },
            order: [
                ['id', 'DESC'],
            ],
            raw: true,
        })

        ctx.error(behavior && !['3.0', '3.1'].includes(behavior.record_code), 'error')

        ctx.json_body = await ctx.service.auth.send_sms(area_code, mobile, config.sms.type.recovery_password)
    }

    async sms_send_login() {
        const { ctx, config } = this

        ctx.json_body = await this.sms_send_core(config.sms.type.login)
    }

    async sms_send_core(type) {
        const { ctx } = this

        await ctx.verify('auth.sendsms', 'body')

        const body = Object.assign({}, ctx.request.body)
        const area_code = body.area_code
        const mobile = body.mobile.trim()

        ctx.error(!code.includes(body.area_code), "区号目前不支持")
        ctx.error(await ctx.service.auth.is_blocked(area_code, mobile), "该号码已进入黑名单")

        await this.slide_validate()

        return await ctx.service.auth.send_sms(area_code, mobile, type)
    }

    async change_mobile() {
        const { ctx, config } = this

        await ctx.verify('auth.sendsms', 'body')

        const body = Object.assign({}, ctx.request.body)

        body.area_code = parseInt(body.area_code, 10)

        ctx.error(!code.includes(body.area_code), "区号目前不支持")
        ctx.error(await ctx.service.auth.is_blocked(body.area_code, body.mobile), "该号码已进入黑名单")

        ctx.json_body = await ctx.service.auth.send_sms(body.area_code, body.mobile, config.sms.type.change_mobile)
    }

    async binding_mobile_change() {
        const { ctx, config } = this

        await ctx.verify('auth.verifysms', 'body')

        const body = Object.assign({}, ctx.request.body)

        const userinfo = ctx.helper.get_userinfo_from_cookie(false)

        let area_code = parseInt(body.area_code, 10)
        let mobile = body.mobile
        let captcha = body.captcha

        ctx.error(!code.includes(area_code), "区号目前不支持")
        ctx.error(await ctx.service.auth.is_blocked(area_code, mobile), "该号码已进入黑名单")

        const sms = await ctx.service.auth.verify_sms(area_code, mobile, captcha)
        ctx.error(!sms, '验证码错误')

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: userinfo[1]
            }
        })
        ctx.error(member, "已绑定过手机号")

        const m = await ctx.model.Mobile.findOne({
            where: {
                area_code,
                mobile_encrypt: ctx.helper.AESEncrypt(mobile)
            }
        })
        ctx.error(m, "已绑定过手机号")

        const keep_cookie = body.keep_cookie

        const cookie = await ctx.service.auth.binding_mobile_change(area_code, mobile, userinfo, keep_cookie)

        await ctx.model.SmsLog.update({
            used: 1
        }, {
            where: { id: sms.id }
        })

        ctx.json_body = {
            success: true,
            cookie
        }
    }

    async account_close() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const type = body.type || ''

        const userinfo = ctx.helper.get_userinfo_from_cookie(false)

        const common_member = await ctx.forumModel.CommonMember.findOne({
            where: {
                uid: userinfo[1]
            }
        })

        const message = `UID：${common_member.uid}<br />昵称：<a href='https://forum.chasedream.com/space-username-${common_member.username}.html' target='_blank'>${common_member.username}</a><br />申请端：${type}<br /><br />申请注销账号，请尽快处理，<a href='https://id.chasedream.com/console/operation/delete-account?username=${encodeURIComponent(common_member.username)}' target='_blank'>前往注销</a>`

        const data = {
            toemail: '<EMAIL>',
            subject: `${common_member.username}用户注销申请`,
            message: message,
            token: config.mail.token,
            mailfrom: config.mail.mailfrom,
        }

        const result = await ctx.curl(config.mail.url, {
            method: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: data,
            timeout: 5000,
        })

        ctx.json_body = {
            success: true,
        }
    }

    async verify_login() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await this.slide_validate()

        const nickname = decodeURIComponent(body.nickname)

        ctx.json_body = await ctx.service.auth.verify_login(body.area_code, body.mobile_email, body.password, body.auto_login, body.openid, body.unionid, body.apple_token, body.apple_user, body.apple_email, nickname, body.platform)
    }

    async verify_register() {
        const { ctx } = this

        await ctx.verify('auth.verifyregister', 'body')

        const body = Object.assign({}, ctx.request.body)

        await this.slide_validate()

        let area_code = parseInt(body.area_code, 10)
        let username = addSlashes((stripSlashes(body.username)).trim()) || ''
        let password = body.password.trim() || ''
        let email = body.email && body.email.trim() || ''
        let mobile = body.mobile || ''
        let captcha = body.captcha.trim() || ''
        let nickname = body.nickname || ''
        nickname = decodeURIComponent(nickname)

        ctx.error(!code.includes(area_code), "区号目前不支持")
        ctx.error(!username, "请填写昵称")

        const schema = new password_validator()
        schema.is().min(8)

        ctx.error(!schema.validate(password), "密码最少8位")

        let len = ctx.helper.string_utf8_len(username)

        ctx.error(len < 3 || len > 15, "昵称由3-15个字符组成")

        ctx.error(ctx.helper.contain_special(username), "昵称包含敏感字符")

        let is_exist = await ctx.service.auth.username_exist(username)
        ctx.error(is_exist, "昵称已存在")

        if (email) {
            len = email.length
            ctx.error(len > 40 || !validator.isEmail(email), "无效Email")

            is_exist = await ctx.service.auth.email_exist(email)
            ctx.error(is_exist, "Email已存在")
        }

        if (ctx.session.wechat) {
            body.openid = ctx.session.wechat.openid
            body.unionid = ctx.session.wechat.unionid
            body.nickname = ctx.session.wechat.nickname
        }

        ctx.json_body = await ctx.service.auth.verify_register(area_code, mobile, captcha, username, password, email, body.openid, body.unionid, body.apple_token, body.apple_user, body.apple_email, nickname, body.platform)
    }

    async recovery_password() {
        const { ctx } = this

        await ctx.verify('auth.verifyrecoverypassword', 'body')

        const body = Object.assign({}, ctx.request.body)

        const schema = new password_validator()
        schema.is().min(8)

        ctx.error(!schema.validate(body.password), "密码必须大于8位")

        const userinfo = ctx.helper.get_userinfo_from_cookie()
        const password = body.password
        const keep_cookie = body.keep_cookie

        const ret = await ctx.service.auth.recovery_password(password, userinfo, keep_cookie)

        ctx.json_body = ret
    }

    async recovery_password_get_code() {
        const { ctx, app, config } = this

        const body = Object.assign({}, ctx.request.body)

        await this.slide_validate()

        ctx.error(!validator.isEmail(body.email), "无效Email")

        const uc_member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                email: body.email.trim(),
            },
            raw: true,
        })

        ctx.error(!uc_member, "此邮箱未注册ChaseDream帐号")

        const random = ctx.helper.get_random_num(6)
        const code = `${random}\t${ctx.helper.now()}\t${uc_member.uid}\t${uc_member.email}`

        await app.redis.setex(`recovery_password:${uc_member.email}`, config.recovery_password_code_timeout, ctx.helper.encode_dz_string(code))

        const settings = await ctx.model.SystemSettings.findAll({
            attributes: ['value'],
            where: {
                name: ['recovery_password_subject', 'recovery_password_html']
            },
            order: [
                ['id', 'ASC'],
            ],
            raw: true
        }).map(row => { return row.value })

        const data = {
            toemail: uc_member.email,
            subject: settings[0],
            message: settings[1].replace('[code]', random),
            token: config.mail.token,
            mailfrom: config.mail.mailfrom,
        }

        await ctx.curl(config.mail.url, {
            method: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            data: data,
            timeout: 5000,
        })

        ctx.json_body = {
            success: true
        }
    }

    async recovery_password_verify_code() {
        const { ctx, app, config } = this

        const body = Object.assign({}, ctx.request.body)

        const code = await app.redis.get(`recovery_password:${body.email}`) || ''
        ctx.error(!code, '验证码不存在')

        const [random, timestamp, uid, email] = (ctx.helper.decode_dz_string(code)).split("\t")

        ctx.error((ctx.helper.now() - timestamp) > config.recovery_password_code_timeout, '验证码已过期')
        ctx.error(body.code !== random, '验证码错误')

        ctx.json_body = {
            token: code,
        }
    }

    async recovery_password_by_email() {
        const { ctx, app, config } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.error(!body.token, "缺少token")

        const token = ctx.helper.decode_dz_string(body.token) || ''
        ctx.error(!token, "非法请求")

        const [random, timestamp, uid, email] = token.split("\t")

        ctx.error((ctx.helper.now() - timestamp) > config.recovery_password_code_timeout, '验证码已过期')

        const salt = ctx.helper.get_random_num(6)

        const password = md5(md5(body.password) + salt)
        const common_pwd = md5(ctx.helper.get_random_num(10))

        await ctx.forumModel.UcenterMember.update({
            password,
            salt,
        }, {
            where: {
                uid
            }
        })

        await ctx.forumModel.CommonMember.update({
            password: common_pwd,
        }, {
            where: {
                uid
            }
        })

        await ctx.model.Member.update({
            password,
            salt,
        }, {
            where: {
                forum_uid: uid
            },
            individualHooks: true
        })

        if (config.env === 'prod') {
            const key = `JjgsH6_common_member_${uid}`
            await app.memcached.delAsync(key)
        }

        if (!body.keep_cookie) ctx.helper.logout_forum()

        ctx.json_body = {
            success: true
        }
    }

    async verify_sms() {
        const { ctx, config } = this

        await ctx.verify('auth.verifysms', 'body')

        const body = Object.assign({}, ctx.request.body)

        body.area_code = parseInt(body.area_code, 10)

        ctx.error(!code.includes(body.area_code), "区号目前不支持")

        const sms = await ctx.service.auth.verify_sms(body.area_code, body.mobile, body.captcha)

        let ret = {
            success: sms !== null
        }

        if (sms !== null) {
            ret.exist = await ctx.service.auth.check_mobile(body.area_code, body.mobile)

            if (body.type === 'recovery_password') {
                ctx.session.recovery_password = {
                    area_code: body.area_code,
                    mobile: body.mobile
                }

                await ctx.model.SmsLog.update({
                    used: 1
                }, {
                    where: { id: sms.id }
                })
            }

            if (ret.success && ret.exist) {
                const mobile = await ctx.model.Mobile.findOne({
                    where: {
                        area_code: body.area_code,
                        mobile_encrypt: ctx.helper.AESEncrypt(body.mobile),
                    }
                })

                const member = await ctx.model.Member.findOne({
                    where: {
                        id: mobile.uid
                    }
                })

                const common_member = await ctx.forumModel.CommonMember.findOne({
                    where: {
                        uid: member.forum_uid
                    }
                })

                ret.cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt)
            }
        }

        ctx.json_body = ret
    }

    async username_exist() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.error(!body.username, "用户名不能为空")

        ctx.json_body = {
            exist: await ctx.service.auth.username_exist(body.username),
            invalid: ctx.helper.contain_special(body.username)
        }
    }

    async email_exist() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.error(!body.email, "Email不能为空")

        ctx.json_body = {
            exist: await ctx.service.auth.email_exist(body.email),
            invalid: !validator.isEmail(body.email)
        }
    }

    async check_mobile_binding() {
        const { ctx } = this

        const userinfo = ctx.helper.get_userinfo_from_cookie()

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: userinfo[1]
            }
        })

        const mobile = !!member && await ctx.model.Mobile.findOne({
            where: {
                uid: member.id
            }
        })

        ctx.json_body = {
            bind: !!mobile
        }
    }

    async check_wechat_binding() {
        const { ctx } = this

        const userinfo = ctx.helper.get_userinfo_from_cookie()

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: userinfo[1]
            }
        })

        const wechat = !!member && await ctx.model.Wechat.findOne({
            where: {
                uid: member.id
            }
        })

        let output = {
            bind: !!wechat
        }

        if (output.bind) {
            output.nickname = wechat.nickname
        }

        ctx.json_body = output
    }

    async user_mobile() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: params.uid
            },
            raw: true,
        })

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                uid: member.id
            },
            raw: true,
        })

        const setting = await ctx.model.SystemSettings.findOne({
            where: {
                name: 'show_mobile'
            },
            raw: true
        })

        mobile.mobile = setting.value == 1 ? ctx.helper.AESDecrypt(mobile.mobile_encrypt) : mobile.mobile_mask

        ctx.json_body = mobile
    }

    async encStr(str) {
        const { ctx } = this

        ctx.json_body = { str: ctx.helper.AESEncrypt(str) }
    }

    async ttt() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = {
            success: true,
        }
    }

    async aliyun_sms_count(area_code) {
        let all = fs.readFileSync(path.join(__dirname, '../../all.txt')).toString()
        all = all.split('\n')
        let arr = []
        for (let item of all) {
            arr.push(item.split('	')[0])
        }

        arr = arr.filter(el => el.startsWith(area_code))
        arr = [...new Set(arr)]

        return {
            count: arr.length,
            arr
        }
    }

    async get_mobile_info() {
        const { ctx } = this

        let userinfo = ctx.helper.decode_dz_authkey()
        ctx.error(!userinfo, "请重新登录后，再次尝试")

        userinfo = userinfo.split("\t")
        ctx.error(userinfo.length < 3, "请重新登录后，再次尝试")

        ctx.json_body = await ctx.service.auth.get_mobile_info(userinfo)
    }

    async change_mobile1() {
        const { ctx } = this

        await ctx.verify('auth.verifychangemobilepassword', 'body')

        const body = Object.assign({}, ctx.request.body)

        let userinfo = ctx.helper.decode_dz_authkey()
        ctx.error(!userinfo, "请重新登录后，再次尝试")

        userinfo = userinfo.split("\t")
        ctx.error(userinfo.length < 3, "请重新登录后，再次尝试")

        ctx.json_body = await ctx.service.auth.change_mobile1(body.password, {
            uid: userinfo[1],
            id: userinfo[2]
        })
    }

    async change_mobile2() {
        const { ctx } = this

        await ctx.verify('auth.verifychangemobile', 'body')

        const body = Object.assign({}, ctx.request.body)

        body.area_code = parseInt(body.area_code, 10)
        ctx.error(!code.includes(body.area_code), "区号目前不支持")

        const sms = await ctx.service.auth.verify_sms(body.area_code, body.mobile, body.captcha)
        ctx.error(sms === null, "验证码错误")

        const exist = await ctx.service.auth.check_mobile(body.area_code, body.mobile)
        ctx.error(exist, "手机号已存在")

        ctx.error(!ctx.session.change_mobile, "非法请求")

        await ctx.model.SmsLog.update({
            used: 1
        }, {
            where: { id: sms.id }
        })

        ctx.json_body = await ctx.service.auth.change_mobile2(body.area_code, body.mobile)
    }

    async userinfo() {
        const { ctx, config } = this

        let userinfo = ctx.helper.decode_dz_authkey()

        if (userinfo) {
            const [_, uid] = userinfo.split("\t")

            const auth = ctx.cookies.get(`${config.discuz.prefix}auth`, {
                httpOnly: true,
                signed: false,
            })
            const saltkey = ctx.cookies.get(`${config.discuz.prefix}saltkey`, {
                signed: false,
            })
            const cookie = `${config.discuz.prefix}auth=${auth};${config.discuz.prefix}saltkey=${saltkey};`

            const res = await ctx.curl(`${config.discuz.home_url}/api/mobile/index.php?mobile=no&version=1&module=profile&do=profile`, {
                method: 'GET',
                dataType: 'json',
                headers: {
                    'cookie': cookie,
                },
                timeout: 30000,
            })

            ctx.json_body = {
                uid: uid,
                username: res.data.Variables.member_username
            }
        } else {
            ctx.json_body = {
                uid: 0,
                username: ''
            }
        }
    }

    async block_user_list() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const userinfo = ctx.helper.get_userinfo_from_cookie()

        ctx.json_body = await ctx.forumModel.CommonMemberBlockUser.findAll({
            where: {
                uid: userinfo[1]
            },
            raw: true,
        })
    }

    async block_user_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.error(!body.blocked_uid, 'error params')

        const userinfo = ctx.helper.get_userinfo_from_cookie()

        const blocked = await ctx.forumModel.CommonMemberBlockUser.findOne({
            where: {
                uid: userinfo[1],
                blocked_uid: body.blocked_uid,
            },
            raw: true,
        })

        if (!blocked) {
            await ctx.forumModel.CommonMemberBlockUser.create({
                uid: userinfo[1],
                blocked_uid: body.blocked_uid,
                dateline: ctx.helper.now(),
            })
        }

        ctx.json_body = {
            success: true,
        }
    }

    async block_user_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.error(!body.blocked_uid, 'error params')

        const userinfo = ctx.helper.get_userinfo_from_cookie()

        await ctx.forumModel.CommonMemberBlockUser.destroy({
            where: {
                uid: userinfo[1],
                blocked_uid: body.blocked_uid,
            }
        })

        ctx.json_body = {
            success: true,
        }
    }
}

module.exports = AuthController