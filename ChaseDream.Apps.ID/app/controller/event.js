const _ = require('lodash')
const fs = require('fs')
const fse = require('fs-extra')
const md5 = require('md5')
const pad = require('pad')
const moment = require("moment")
const awaitWriteStream = require('await-stream-ready').write
const sendToWormhole = require('stream-wormhole')
const randomstring = require("randomstring")
const BaseController = require('./base')
const await = require('await-stream-ready/lib/await')

class EventController extends BaseController {
    async highlight_digest() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const event = await ctx.model.Event.findOne({
            where: {
                id: body.id,
            },
            raw: true,
        })

        const user = await ctx.model.EventUser.findOne({
            where: {
                id: 2,
            },
            raw: true,
        })

        body.username = user.username
        body.password = ctx.helper.decode_dz_string(user.password)

        body.fid = event.forum_url_fid
        body.tid = event.forum_url

        const result = await ctx.service.thread.highlight_digest(body)

        let event_model = {}

        if (body.opt === 'highlight') {
            event_model.highlight = body.highlight_color
        } else if (body.opt === 'digest') {
            event_model.digest = body.digest
        }

        await ctx.model.Event.update(event_model, {
            where: {
                id: body.id
            }
        })

        ctx.json_body = {
            success: true
        }
    }

    async stick() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const event = await ctx.model.Event.findOne({
            where: {
                id: body.id,
            },
            raw: true,
        })

        const user = await ctx.model.EventUser.findOne({
            where: {
                id: 2,
            },
            raw: true,
        })

        body.username = user.username
        body.password = ctx.helper.decode_dz_string(user.password)

        body.fid = event.forum_url_fid
        body.tid = event.forum_url

        const result = await ctx.service.thread.stick(body)

        await ctx.model.Event.update({
            stick: body.stick,
        }, {
            where: {
                id: body.id
            }
        })

        ctx.json_body = {
            success: true
        }
    }

    async calendar_get_one() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const params = Object.assign({}, ctx.params)

        let where = {
            status: 0,
            event_id: params.event_id,
            event_end_date: {
                [Op.gte]: ctx.helper.now()
            },
        }

        const locations = await ctx.model.EventLocation.findAll({
            where: {
                event_id: params.event_id,
                end_date: {
                    [Op.gte]: ctx.helper.now()
                },
            },
            raw: true,
        })

        for (let location of locations) {
            location.event_school_major = await ctx.model.EventSchoolMajor.findAll({
                attributes: ['school_major_id'],
                where: {
                    event_id: params.event_id,
                    lid: location.id,
                },
                raw: true,
            }).map(row => {
                return row.school_major_id
            })

            location.event_major = await ctx.model.EventMajor.findAll({
                attributes: ['major_id'],
                where: {
                    event_id: params.event_id,
                    lid: location.id,
                },
                raw: true,
            }).map(row => {
                return row.major_id
            })

            location.event_type = await ctx.model.EventType.findAll({
                attributes: ['event_type_id'],
                where: {
                    event_id: params.event_id,
                    lid: location.id,
                },
                raw: true,
            }).map(row => {
                return row.event_type_id
            })
        }

        const events = await ctx.model.EventCalendar.findAll({
            include: [{
                model: ctx.model.SchoolDic,
                required: false,
                as: 'event_school',
            }, {
                model: ctx.model.EventSchoolMajor,
                required: false,
                as: 'event_school_major',
                attributes: ['id', 'school_major_id']
            }, {
                model: ctx.model.EventMajor,
                required: false,
                as: 'event_major',
                attributes: ['id', 'major_id']
            }, {
                model: ctx.model.EventType,
                required: false,
                as: 'event_type',
                attributes: ['id', 'event_type_id']
            }, {
                model: ctx.model.Upload,
                required: false,
                as: 'event_image',
                attributes: ['id', 'fullpath']
            }, {
                model: ctx.model.EventLocation,
                required: false,
                as: 'event_location',
            }, {
                model: ctx.model.Geo,
                required: false,
                as: 'event_geo',
                attributes: ['id', 'name']
            }],
            where: where,
        })

        ctx.json_body = {
            events,
            locations,
        }
    }

    async release_1_3() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const params = Object.assign({}, ctx.params)

        let where = {
            status: 0,
            event_id: params.event_id,
            type: params.event_type,
        }

        if (params.event_type == 1) {
            const locations = await ctx.model.EventLocation.findAll({
                where: {
                    event_id: params.event_id,
                    end_date: {
                        [Op.gte]: ctx.helper.now()
                    },
                },
            })

            const events = await ctx.model.EventRelease.findAll({
                include: [{
                    model: ctx.model.Geo,
                    required: false,
                    as: 'event_geo',
                    attributes: ['id', 'name']
                }],
                where: where,
            })

            ctx.json_body = {
                events,
                locations,
            }
        } else {
            ctx.json_body = await ctx.model.EventRelease.findAll({
                include: [{
                    model: ctx.model.Geo,
                    required: false,
                    as: 'event_geo',
                    attributes: ['id', 'name']
                }],
                where: where,
            })
        }
    }

    async push1_setting_get() {
        const { ctx, config } = this

        ctx.json_body = await ctx.model.SystemSettings.findOne({
            attributes: [['value', 'push1_count']],
            where: {
                name: config.system_settings.push1_count
            },
            raw: true
        })
    }

    async push1_setting_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.SystemSettings.update({
            value: body.push1_count
        }, {
            where: {
                name: config.system_settings.push1_count
            }
        })

        ctx.json_body = {
            success: true
        }
    }

    async push1_www_position_get() {
        const { ctx, config } = this

        ctx.json_body = await ctx.model.SystemSettings.findOne({
            attributes: [['value', 'push1_www_position']],
            where: {
                name: config.system_settings.push1_www_position
            },
            raw: true
        }) || {}
    }

    async www_hot_get() {
        const { ctx, config } = this

        ctx.json_body = await ctx.model.SystemSettings.findOne({
            attributes: [['value', 'www_hot']],
            where: {
                name: config.system_settings.www_hot
            },
            raw: true
        }) || {}
    }

    async push1_www_position_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.SystemSettings.update({
            value: body.push1_www_position
        }, {
            where: {
                name: config.system_settings.push1_www_position
            }
        })

        ctx.json_body = {
            success: true
        }
    }

    async www_hot_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.SystemSettings.update({
            value: body.www_hot
        }, {
            where: {
                name: config.system_settings.www_hot
            }
        })

        ctx.json_body = {
            success: true
        }
    }

    async push2_coming_soon() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const push2 = await ctx.model.EventRelease.findAll({
            where: {
                status: {
                    [Op.gte]: 0,
                },
                type: 2,
            },
            raw: true,
        })

        const level1 = _.filter(push2, { 'level': 1 })
        const level2 = _.filter(push2, { 'level': 2 })
        const level3 = _.filter(push2, { 'level': 3 })

        const index_image = {
            level1: _.orderBy(level1, ['order'], ['desc']),
            level2: _.orderBy(level2, ['order'], ['desc']),
            level3: _.orderBy(level3, ['order'], ['desc']),
        }

        ctx.json_body = index_image
    }

    async event_type_list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.EventTypeDict.findAll({
            where: {},
            raw: true,
        })
    }

    async publish_to_forum() {
        const { ctx } = this

        const tid = await ctx.service.www.publish_to_forum()

        ctx.json_body = {
            success: true,
            tid
        }
    }

    async publish_to_www() {
        const { ctx } = this

        const event = await ctx.service.www.publish_to_www()

        ctx.json_body = {
            success: true,
            articleid: event.articleid,
        }
    }

    async show_event_release() {
        const { ctx, app, config } = this

        let event_list = await app.redis.get(`${config.redis_key.event_list}`) || ''

        if (!event_list) {
            event_list = await ctx.service.event.load_event_release_to_cache()
        } else {
            event_list = JSON.parse(event_list)
        }

        const events_count = await ctx.model.SystemSettings.findOne({
            attributes: ['value'],
            where: {
                name: config.system_settings.push1_count
            },
            raw: true
        })

        event_list.events_count = events_count.value

        ctx.json_body = event_list
    }

    async show_event_calendar() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || 50
        const page = (query.page - 1) * page_size

        let one_day = 60 * 60 * 24
        const typeValue = query.type ? parseInt(query.type, 10) : 1

        let where = {
            status: {
                [Op.gte]: 0,
            },
            [Op.and]: [{
                push_begin_date: {
                    [Op.lte]: ctx.helper.now()
                }
            }, {
                push_end_date: {
                    [Op.gte]: ctx.helper.now()
                }
            }],
            type: typeValue === 1 ? 1 : {
                [Op.gte]: 2
            }
        }

        if (query.push_end_date) {
            where.push_end_date = {
                [Op.lte]: ctx.helper.now() + one_day * parseInt(query.push_end_date, 10)
            }
        }

        if (query.locations) {
            where.location_id = query.locations.split(',')
        }

        if (query.iese) {
            where.school_id = 116
        }

        let include = [{
            model: ctx.model.SchoolDic,
            required: false,
            as: 'event_school',
            attributes: ['id', 'display_name', 'website', 'logo_url', 'country']
        }, {
            model: ctx.model.Upload,
            required: false,
            as: 'event_image',
            attributes: ['id', 'fullpath']
        }, {
            model: ctx.model.EventLocation,
            required: false,
            as: 'event_location',
            attributes: ['id', 'app_join_event']
        }, {
            model: ctx.model.EventType,
            required: false,
            as: 'event_type',
            attributes: ['id', 'event_type_id', 'event_type_name']
        }, {
            model: ctx.model.Geo,
            required: false,
            as: 'event_geo',
            attributes: ['id', 'name']
        }]

        if (query.majors) {
            include.push({
                model: ctx.model.EventMajor,
                where: { major_id: query.majors.split(',') },
                required: true,
                as: 'event_major',
                attributes: ['id', 'major_id', 'major_name']
            })
        } else {
            include.push({
                model: ctx.model.EventMajor,
                required: false,
                as: 'event_major',
                attributes: ['id', 'major_id', 'major_name']
            })
        }

        const events = await ctx.model.EventCalendar.findAndCountAll({
            include: include,
            distinct: true,
            where: where,
            order: [
                ['event_begin_date', 'ASC'],
            ],
            offset: page,
            limit: page_size
        })

        ctx.json_body = events
    }

    async show_event_calendar_expire() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 30) || config.page_size
        const page = (query.page - 1) * page_size

        let one_day = 60 * 60 * 24
        const typeValue = query.type ? parseInt(query.type, 10) : 1

        let where = {
            status: -1,
            event_end_date: {
                [Op.lte]: ctx.helper.now()
            },
            type: typeValue === 1 ? 1 : {
                [Op.gte]: 2
            }
        }

        if (query.event_end_date) {
            where.event_end_date = {
                [Op.lte]: ctx.helper.now() + one_day * parseInt(query.event_end_date, 10)
            }
        }

        if (query.locations) {
            where.location_id = query.locations.split(',')
        }

        let include = [{
            model: ctx.model.SchoolDic,
            required: false,
            as: 'event_school',
            attributes: ['id', 'display_name', 'website', 'logo_url', 'country']
        }, {
            model: ctx.model.Upload,
            required: false,
            as: 'event_image',
            attributes: ['id', 'fullpath']
        }, {
            model: ctx.model.EventLocation,
            required: false,
            as: 'event_location',
            attributes: ['id', 'app_join_event']
        }, {
            model: ctx.model.EventType,
            required: false,
            as: 'event_type',
            attributes: ['id', 'event_type_id', 'event_type_name']
        }, {
            model: ctx.model.Geo,
            required: false,
            as: 'event_geo',
            attributes: ['id', 'name']
        }]

        if (query.majors) {
            include.push({
                model: ctx.model.EventMajor,
                where: { major_id: query.majors.split(',') },
                required: true,
                as: 'event_major',
                attributes: ['id', 'major_id', 'major_name']
            })
        } else {
            include.push({
                model: ctx.model.EventMajor,
                required: false,
                as: 'event_major',
                attributes: ['id', 'major_id', 'major_name']
            })
        }

        const events = await ctx.model.EventCalendar.findAndCountAll({
            include: include,
            distinct: true,
            where: where,
            order: [
                ['event_end_date', 'ASC'],
            ],
            offset: page,
            limit: page_size
        })

        ctx.json_body = events
    }

    async show_event_calendar_all() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const typeValue = query.type ? parseInt(query.type, 10) : 1

        let where = {
            status: {
                [Op.gte]: 0,
            },
            event_end_date: {
                [Op.gte]: ctx.helper.now()
            },
            type: typeValue === 1 ? 1 : {
                [Op.gte]: 2
            }
        }

        const event_ids = await ctx.model.EventCalendar.findAll({
            attributes: ['event_id'],
            where: where,
            distinct: true,
            raw: true,
        }).map(row => {
            return row.event_id
        })

        ctx.json_body = await ctx.model.Event.findAll({
            attributes: ['forum_url'],
            where: {
                id: event_ids,
                forum_url: {
                    [Op.gt]: 0,
                }
            },
            raw: true,
        }).map(row => {
            return row.forum_url
        })
    }

    async event_list() {
        const { ctx, config, app } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        let where = { status: 0 }

        let include = [{
            model: ctx.model.SchoolDic,
            required: false,
            as: 'event_school',
        }, {
            model: ctx.model.EventLocation,
            required: true,
            where: {
                end_date: {
                    [Op.gt]: ctx.helper.now()
                }
            },
            as: 'event_location',
        }]

        if (query.status >= 0) {
            where.status = query.status
            where.digest = 1
        } else if (query.status < 0) {
            include[1].where.end_date = {
                [Op.lt]: ctx.helper.now()
            }
            where.digest = 0
        }

        if (query.subject) {
            where.subject = {
                [Op.like]: `%${query.subject.trim()}%`,
            }
        }

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.Event.findAndCountAll({
            include: include,
            distinct: true,
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
        })
    }

    async event_get_one() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        let event = await ctx.model.Event.findOne({
            include: [{
                model: ctx.model.SchoolDic,
                required: false,
                as: 'event_school',
            }, {
                model: ctx.model.EventLocation,
                required: false,
                as: 'event_location',
            }],
            distinct: true,
            where: {
                id: params.event_id,
            }
        })

        event = JSON.parse(JSON.stringify(event))

        for (let location of event.event_location) {
            location.event_school_major = await ctx.model.EventSchoolMajor.findAll({
                attributes: ['school_major_id'],
                where: {
                    event_id: event.id,
                    lid: location.id,
                },
                raw: true,
            }).map(row => {
                return row.school_major_id
            })

            location.event_major = await ctx.model.EventMajor.findAll({
                attributes: ['major_id'],
                where: {
                    event_id: event.id,
                    lid: location.id,
                },
                raw: true,
            }).map(row => {
                return row.major_id
            })

            location.event_type = await ctx.model.EventType.findAll({
                attributes: ['event_type_id'],
                where: {
                    event_id: event.id,
                    lid: location.id,
                },
                raw: true,
            }).map(row => {
                return row.event_type_id
            })
        }

        ctx.json_body = event
    }

    async event_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        let tran = await ctx.model.transaction()

        try {
            const event = await ctx.model.Event.create(body, {
                transaction: tran,
            })

            for (const loc of body.location) {
                let event_location = {
                    event_id: event.id,
                    country_id: loc.country_id,
                    province_id: loc.province_id,
                    city_id: loc.city_id,
                    begin_date: loc.begin_date,
                    end_date: loc.end_date,
                }

                const el = await ctx.model.EventLocation.create(event_location, {
                    transaction: tran,
                })

                for (const school_major_id of loc.school_major) {
                    await ctx.model.EventSchoolMajor.create({
                        event_id: event.id,
                        lid: el.id,
                        school_major_id: school_major_id,
                    }, {
                        transaction: tran,
                    })
                }

                for (const major_id of loc.major) {
                    const em = await ctx.model.MajorCategoryDic.findOne({
                        where: {
                            id: major_id
                        }
                    })
                    await ctx.model.EventMajor.create({
                        event_id: event.id,
                        lid: el.id,
                        major_id: major_id,
                        major_name: em.display_name,
                    }, {
                        transaction: tran,
                    })
                }

                for (const event_type_id of loc.event_type) {
                    const et = await ctx.model.EventTypeDict.findOne({
                        where: {
                            id: event_type_id
                        }
                    })
                    await ctx.model.EventType.create({
                        event_id: event.id,
                        lid: el.id,
                        event_type_id: event_type_id,
                        event_type_name: et.name,
                    }, {
                        transaction: tran,
                    })
                }
            }

            await tran.commit()

            ctx.json_body = event
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async event_update() {
        const { ctx } = this

        await ctx.service.event.event_update()

        ctx.json_body = {
            success: true
        }
    }

    async event_delete() {
        const { ctx } = this

        await ctx.service.event.event_delete()

        ctx.json_body = {
            success: true
        }
    }

    async event_location_create() {
        const { ctx } = this
        const body = Object.assign({}, ctx.request.body)

        let tran = await ctx.model.transaction()

        try {
            const el = await ctx.model.EventLocation.create(body, {
                transaction: tran,
            })

            for (let event_type_id of body.event_type) {
                const et = await ctx.model.EventTypeDict.findOne({
                    where: {
                        id: event_type_id
                    }
                })
                await ctx.model.EventType.create({
                    event_id: body.event_id,
                    lid: el.id,
                    event_type_id,
                    event_type_name: et.name,
                }, {
                    transaction: tran,
                })
            }

            for (let school_major_id of body.school_major) {
                await ctx.model.EventSchoolMajor.create({
                    event_id: body.event_id,
                    lid: el.id,
                    school_major_id,
                }, {
                    transaction: tran,
                })
            }

            for (let major_id of body.major) {
                const em = await ctx.model.MajorCategoryDic.findOne({
                    where: {
                        id: major_id
                    }
                })
                await ctx.model.EventMajor.create({
                    event_id: body.event_id,
                    lid: el.id,
                    major_id,
                    major_name: em.display_name,
                }, {
                    transaction: tran,
                })
            }

            await ctx.model.Event.update({
                status: 0
            }, {
                where: {
                    id: body.event_id
                },
                transaction: tran,
            })

            await tran.commit()

            ctx.json_body = {
                success: true
            }
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async event_location_update() {
        const { ctx } = this

        await ctx.service.event.event_location_update()

        ctx.json_body = {
            success: true
        }
    }

    async event_location_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventLocation.destroy({
            where: { id: body.id }
        })

        await ctx.model.EventType.destroy({
            where: { lid: body.id }
        })

        await ctx.model.EventMajor.destroy({
            where: { lid: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async event_release_list() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        let where = {
            status: {
                [Op.gte]: 0
            }
        }

        if (query.status) where.status = query.status
        if (query.type) where.type = query.type

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        let order = query.type == 1 ? ['event_begin_date', 'ASC'] : ['order', 'DESC']

        if (query.status == -1) {
            order = ['event_end_date', 'DESC']
        }

        const res = await ctx.model.EventRelease.findAndCountAll({
            include: [{
                model: ctx.model.Geo,
                required: false,
                as: 'event_geo',
                attributes: ['id', 'name']
            }],
            distinct: true,
            where: where,
            order: [
                order
            ],
            offset: page,
            limit: page_size
        })

        for (let row of res.rows) {
            if (row.event_geo && row.event_geo.name === '申请截止') {
                row.event_geo.name = 'Deadline'
            }
        }

        ctx.json_body = res
    }

    async event_release_create() {
        const { ctx } = this

        let body = Object.assign({}, ctx.request.body)
        let where = { type: body.type }

        if (parseInt(body.type, 10) === 2) {
            where.level = body.level || 1
        }

        const max = await ctx.model.EventRelease.findOne({
            where: where,
            order: [
                ['order', 'DESC'],
            ],
        }) || {}

        let order = max.order || 0
        body.order = ++order

        let tran = await ctx.model.transaction()

        try {
            if (parseInt(body.type, 10) === 1) {
                if (body.html) {
                    await ctx.model.EventRelease.create(body, {
                        transaction: tran,
                    })
                } else {
                    for (const loc of body.location) {
                        body.event_begin_date = loc.event_begin_date
                        body.event_end_date = loc.event_end_date
                        body.push_begin_date = loc.push_begin_date
                        body.push_end_date = loc.push_end_date

                        body.country_id = loc.country_id
                        body.province_id = loc.province_id
                        body.location_id = loc.location_id
                        body.lid = loc.lid

                        await ctx.model.EventRelease.create(body, {
                            transaction: tran,
                        })

                        body.order = ++order
                    }
                }
            } else {
                await ctx.model.EventRelease.create(body, {
                    transaction: tran,
                })
            }

            if (!body.html) {
                await ctx.model.Event.update({
                    [`push_${body.type}`]: true,
                }, {
                    where: {
                        id: body.event_id
                    },
                    transaction: tran,
                })
            }

            await ctx.service.event.clear_cache()

            await tran.commit()

            ctx.json_body = {
                success: true
            }
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async event_release_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventRelease.update(body, {
            where: { id: body.id }
        })

        await ctx.service.event.clear_cache()

        ctx.json_body = {
            success: true
        }
    }

    async event_release_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventRelease.destroy({
            where: { id: body.id }
        })

        await ctx.service.event.clear_cache()

        ctx.json_body = {
            success: true
        }
    }

    async event_calendar_list() {
        const { ctx, config, app } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        let where = {
            status: 0,
            event_end_date: {
                [Op.gte]: ctx.helper.now()
            }
        }

        let order = ['event_begin_date', 'ASC']

        if (query.status) where.status = query.status

        if (where.status > 0) {
            order = ['event_begin_date', 'DESC']
            delete where.event_end_date
        } else if (query.status == -1) {
            order = ['event_end_date', 'DESC']
            where.status = -1
            delete where.event_end_date
        }

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.EventCalendar.findAndCountAll({
            include: [{
                model: ctx.model.SchoolDic,
                required: false,
                as: 'event_school',
            }, {
                model: ctx.model.Upload,
                required: false,
                as: 'event_image',
                attributes: ['id', 'fullpath']
            }, {
                model: ctx.model.EventLocation,
                required: false,
                as: 'event_location',
                attributes: ['id', 'app_join_event']
            }, {
                model: ctx.model.Geo,
                required: false,
                as: 'event_geo',
                attributes: ['id', 'name']
            }],
            distinct: true,
            where: where,
            order: [
                order
            ],
            offset: page,
            limit: page_size
        })
    }

    async event_calendar_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        let tran = await ctx.model.transaction()

        try {
            for (const loc of body.location) {
                body.event_begin_date = loc.event_begin_date
                body.event_end_date = loc.event_end_date
                body.push_begin_date = loc.push_begin_date
                body.push_end_date = loc.push_end_date

                body.location_id = loc.location_id
                body.image = loc.image
                body.lid = loc.lid
                body.position = loc.position

                await ctx.model.EventCalendar.create(body, {
                    transaction: tran,
                })
            }

            await ctx.model.Event.update({
                push_0: true,
            }, {
                where: {
                    id: body.event_id
                },
                transaction: tran,
            })

            await tran.commit()

            ctx.json_body = {
                success: true
            }
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async event_calendar_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventCalendar.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async event_calendar_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventCalendar.update({
            status: -2,
        }, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async change_order() {
        const { ctx, app, config } = this

        const body = Object.assign({}, ctx.request.body)
        let tran = await ctx.model.transaction()

        try {
            if (body.type === 1 || body.type === 3) {
                const ids = body.ids
                let count = ids.length

                for (const id of ids) {
                    await ctx.model.EventRelease.update({
                        order: count--
                    }, {
                        where: { id: id },
                        transaction: tran
                    })
                }
            } else {
                for (const row of body.data) {
                    const ids = row.ids
                    let count = ids.length

                    for (const id of ids) {
                        await ctx.model.EventRelease.update({
                            level: row.level,
                            order: count--
                        }, {
                            where: { id: id },
                            transaction: tran
                        })
                    }
                }
            }

            await ctx.service.event.clear_cache()

            await tran.commit()

            ctx.json_body = { success: true }
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async event_image() {
        const { ctx, config } = this

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        let where = {
            school_id: query.school_id
        }

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.Upload.findAndCountAll({
            attributes: ['id', 'school_id', 'fullpath', 'type', 'created_at'],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async image_upload() {
        const { ctx, config } = this

        const query = Object.assign({}, ctx.query)
        let attach = {}

        if (!query.school_id) {
            ctx.error(true, "缺少参数")
        }

        const school = await ctx.model.SchoolDic.findOne({
            where: {
                id: query.school_id
            },
            raw: true,
        })

        try {
            const parts = ctx.multipart()
            let part
            while ((part = await parts()) != null) {
                if (!part.length) {
                    let path = ''
                    let random = ''
                    let filename = ''
                    let month = pad(2, moment().month() + 1, '0')

                    if (query.type === 'core') {
                        random = randomstring.generate(8)
                        path = `${config.upload_event_img_path}/${school.directory}/${moment().year()}/${month}`
                        filename = `C-${random}`
                    } else {
                        random = randomstring.generate(6)
                        path = `${config.upload_event_img_path}/${school.directory}/${moment().year()}/${month}`
                        filename = `${query.type}-${random}`
                    }

                    await fse.mkdirp(`${config.baseDir}${path}`)
                    let fullpath = `${path}/${filename}${part.filename.substring(part.filename.lastIndexOf('.'))}`
                    part.pipe(fs.createWriteStream(`${config.baseDir}${fullpath}`))

                    attach = await ctx.model.Upload.create({
                        school_id: school.id,
                        category: 'event',
                        original: part.filename,
                        fullpath: fullpath.replace('/upload/', '/'),
                        type: part.mime
                    })
                }
            }

            ctx.json_body = {
                aid: attach.id,
                fullpath: attach.fullpath,
                type: attach.type,
            }
        } catch (err) {
            ctx.json_body = err
        }
    }

    async image_upload_push2() {
        const { ctx, config } = this

        let attach = {}

        try {
            const parts = ctx.multipart()
            let part
            while ((part = await parts()) != null) {
                if (!part.length) {
                    let filename = randomstring.generate(6)
                    let month = pad(2, moment().month() + 1, '0')

                    let path = `${config.upload_event_img_path}/Push2/${moment().year()}/${month}`

                    await fse.mkdirp(`${config.baseDir}${path}`)
                    let fullpath = `${path}/${filename}${part.filename.substring(part.filename.lastIndexOf('.'))}`
                    part.pipe(fs.createWriteStream(`${config.baseDir}${fullpath}`))

                    attach = await ctx.model.Upload.create({
                        category: 'event',
                        original: part.filename,
                        fullpath: fullpath.replace('/upload/', '/'),
                        type: part.mime
                    })
                }
            }

            ctx.json_body = {
                aid: attach.id,
                fullpath: attach.fullpath,
                type: attach.type,
            }
        } catch (err) {
            ctx.json_body = err
        }
    }

    async image_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Upload.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async event_material_digest() {
        const { ctx } = this

        const query = Object.assign({}, ctx.request.query)

        ctx.json_body = await ctx.model.Upload.findAll({
            attributes: ['id', 'school_id', 'digest', 'fullpath', 'order', 'created_at'],
            where: {
                school_id: query.school_id,
                digest: 1,
            },
            order: [
                ['order', 'DESC'],
            ],
            raw: true,
        })
    }

    async material_change_order() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        let tran = await ctx.model.transaction()

        try {
            const ids = body.ids
            let count = ids.length

            for (const id of ids) {
                await ctx.model.Upload.update({
                    order: count--
                }, {
                    where: { id: id },
                    transaction: tran
                })
            }

            await tran.commit()

            ctx.json_body = { success: true }
        } catch (err) {
            await tran.rollback()
            ctx.throw(err)
        }
    }

    async image_digest() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.Upload.update({
            school_id: body.school_id,
            digest: body.digest,
        }, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async image_download() {
        const { ctx, config } = this

        const params = Object.assign({}, ctx.params)

        const file = await ctx.model.Upload.findOne({
            where: {
                id: params.id
            }
        })

        const filename = file.fullpath.substring(file.fullpath.lastIndexOf('/'))

        ctx.attachment(filename)
        ctx.set('Content-Type', 'application/octet-stream')
        ctx.body = fs.createReadStream(`${config.baseDir}${file.fullpath}`)
    }

    async geo() {
        const { ctx } = this
        const query = Object.assign({}, ctx.query)
        let where = {}

        if (query.parent_id) {
            where.parent_id = query.parent_id
        } else {
            where.parent_id = 0
        }

        ctx.json_body = await ctx.model.Geo.findAll({
            attributes: ['id', 'name'],
            where: where,
            order: [
                ['order', 'DESC'],
            ],
            raw: true
        })
    }

    async geo_search() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const query = Object.assign({}, ctx.query)

        let where = {
            parent_id: {
                [Op.gt]: 0,
            },
            name: {
                [Op.like]: `%${query.s}%`,
            }
        }

        let result = []

        let geos = await ctx.model.Geo.findAll({
            attributes: ['id', 'name', 'parent_id'],
            where: where,
            raw: true
        })

        for (let geo of geos) {
            let arr = []

            let parent = geo
            arr.push(geo)

            do {
                parent = await ctx.model.Geo.findOne({
                    attributes: ['id', 'name', 'parent_id'],
                    where: {
                        id: parent.parent_id
                    },
                    raw: true,
                })
                if (parent) arr.unshift(parent)
            } while (parent && parent.parent_id !== 0)

            result.push(arr)
        }

        ctx.json_body = result
    }

    async get_geo() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        ctx.json_body = await ctx.model.Geo.findOne({
            attributes: ['id', 'name'],
            where: {
                id: params.id
            },
            raw: true,
        })
    }

    async major() {
        const { ctx } = this
        let items = {}

        const majors = await ctx.model.MajorCategoryDic.findAll({
            where: {},
            raw: true
        })

        for (const major of majors) {
            if (items[major.type]) {
                items[major.type].push(major)
            } else {
                let arr = []
                arr.push(major)
                items[major.type] = arr
            }
        }

        let output = []

        for (const [key, value] of Object.entries(items)) {
            output.push({
                g: key,
                names: value
            })
        }

        ctx.json_body = output
    }

    async user_list() {
        const { ctx, config } = this

        let where = {}
        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        if (model.type) {
            where.type = model.type
        }

        ctx.json_body = await ctx.model.EventUser.findAndCountAll({
            attributes: ['id', 'username', 'nickname', 'type', 'status', 'created_at'],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async user_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (parseInt(body.type) === 1) {
            const res = await ctx.service.thread.login(body)

            body.password = ctx.helper.encode_dz_string(body.password)
            body.status = res.Message.messageval === 'login_succeed' ? 1 : 0
        } else {
            const res = await ctx.service.www.login({
                Username: body.username,
                Password: body.password,
            })

            body.password = ctx.helper.encode_dz_string(body.password)
            body.status = res ? 1 : 0
        }

        ctx.json_body = await ctx.model.EventUser.create(body)
    }

    async user_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        if (parseInt(body.type) === 1) {
            const res = await ctx.service.thread.login(body)

            body.password = ctx.helper.encode_dz_string(body.password)
            body.status = res.Message.messageval === 'login_succeed' ? 1 : 0
        } else {
            const res = await ctx.service.www.login({
                Username: body.username,
                Password: body.password,
            })

            body.password = ctx.helper.encode_dz_string(body.password)
            body.status = res ? 1 : 0
        }

        await ctx.model.EventUser.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async user_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.EventUser.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async event_school_list() {
        const { ctx, config } = this

        let where = {}

        const find = {
            where: where,
            order: [
                ['id', 'DESC'],
            ],
        }

        let query = Object.assign({}, ctx.query)

        if (query.page || query.page_size) {
            query.page = query.page || 1
            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            find.offset = page
            find.limit = page_size

            if (query.s) {
                const all = await ctx.model.SchoolDic.findAll({
                    where: {},
                    order: [
                        ['id', 'DESC'],
                    ],
                }).filter(row => row.keyword.toLowerCase().indexOf(query.s.toLowerCase()) > -1)

                ctx.json_body = {
                    count: all.length,
                    rows: all
                }
            } else {
                ctx.json_body = await ctx.model.SchoolDic.findAndCountAll(find)
            }
        } else {
            ctx.json_body = await ctx.model.SchoolDic.findAll(find)
        }
    }

    async event_school_get_one() {
        const { ctx } = this

        const params = ctx.params

        let school = await ctx.model.SchoolDic.findOne({
            where: {
                id: params.school_id
            },
            raw: true,
        })

        const said = await ctx.model.SchoolAreaSchool.findAll({
            attributes: ['school_area_id'],
            where: {
                school_id: params.school_id,
            },
            raw: true,
        }).map(row => row.school_area_id)

        school.school_area = await ctx.model.SchoolAreaDic.findAll({
            where: {
                id: said,
            },
            raw: true,
        })

        for (let school_area of school.school_area) {
            const country = await ctx.model.Geo.findOne({
                attributes: ['name'],
                where: {
                    id: school_area.country_id || 0
                },
                raw: true,
            }) || {}
            school_area.country = country.name || ''

            const province = await ctx.model.Geo.findOne({
                attributes: ['name'],
                where: {
                    id: school_area.province_id || 0
                },
                raw: true,
            }) || {}
            school_area.province = province.name || ''

            const city = await ctx.model.Geo.findOne({
                attributes: ['name'],
                where: {
                    id: school_area.city_id || 0
                },
                raw: true,
            }) || {}
            school_area.city = city.name || ''
        }

        ctx.json_body = school
    }

    async event_school_create() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const path = `${config.upload_event_img_path}/${body.directory}`

        if (!fs.existsSync(`${config.baseDir}${path}`)) {
            ctx.error(true, "目录不存在，请联系管理员创建目录")
        }

        const school = await ctx.model.SchoolDic.create(body)
        for (let area of body.school_area) {
            area.school_id = school.id
            await ctx.model.SchoolAreaDic.create(area)
        }

        ctx.json_body = school
    }

    async event_school_update() {
        const { ctx, config } = this

        const body = Object.assign({}, ctx.request.body)

        const path = `${config.upload_event_img_path}/${body.directory}`

        if (!fs.existsSync(`${config.baseDir}${path}`)) {
            ctx.error(true, "目录不存在，请联系管理员创建目录")
        }

        await ctx.model.SchoolDic.update(body, {
            where: { id: body.id }
        })

        for (let school_area of body.school_area) {
            switch (school_area.action) {
                case 'create':
                    school_area.school_id = body.id
                    await ctx.model.SchoolAreaDic.create(school_area)
                    break
                case 'update':
                    await ctx.model.SchoolAreaDic.update(school_area, {
                        where: { id: school_area.id }
                    })
                    break
                case 'delete':
                    await ctx.model.SchoolAreaDic.destroy({
                        where: { id: school_area.id },
                    })
                    break
            }
        }

        ctx.json_body = {
            success: true,
        }
    }

    async event_school_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.SchoolDic.destroy({
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async event_get_school_logo() {
        const { ctx, config } = this

        const params = Object.assign({}, ctx.params)

        let school = await ctx.model.SchoolDic.findOne({
            attributes: ['id', 'logo_url'],
            where: {
                id: params.school_id
            },
            raw: true,
        })

        school.logo_url = school.logo_url || `${config.upload_public_url}/events/event-IMG/1x1.png`

        ctx.json_body = school
    }

    async event_school_logo() {
        const { ctx, config } = this

        const path = `${config.upload_event_img_path}/${ctx.query.directory}`

        if (!fs.existsSync(`${config.baseDir}${path}`)) {
            ctx.error(true, "目录不存在，请联系管理员创建目录")
        }

        await fse.mkdirp(`${config.baseDir}${path}/logo`)

        let logo_path = `${config.baseDir}${path}/logo`
        let fullpath = `${logo_path}/1024.png`

        const stream = await this.ctx.getFileStream()
        const writeStream = fs.createWriteStream(fullpath)

        try {
            await awaitWriteStream(stream.pipe(writeStream))

            await ctx.helper.resize_spec(fullpath, logo_path, [{
                w: 512,
                h: null
            }, {
                w: 256,
                h: null
            }, {
                w: 128,
                h: null
            }, {
                w: 60,
                h: null
            }, {
                w: 21,
                h: null
            }], 'png')

            ctx.json_body = {
                fullpath: `${config.upload_public_url}/${path.replace('/upload/', '')}/logo/1024.png`
            }
        } catch (err) {
            await sendToWormhole(stream)

            ctx.logger.error(err.message)
            ctx.error(true, err.message)
        }
    }

    async event_school_major_find_all() {
        const { ctx } = this

        const params = Object.assign({}, ctx.params)

        ctx.json_body = await ctx.model.SchoolMajorDic.findAll({
            include: [{
                model: ctx.model.MajorCategoryDic,
                required: false,
                as: 'event_major',
            }],
            where: {
                school_id: params.school_id,
            }
        })
    }

    async event_school_major_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.SchoolMajorDic.create(body)

        ctx.json_body = {
            success: true,
        }
    }

    async event_school_major_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.SchoolMajorDic.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true,
        }
    }

    async search_items() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)

        let where = {
            status: {
                [Op.gte]: 0,
            },
            [Op.and]: [{
                push_begin_date: {
                    [Op.lte]: ctx.helper.now()
                }
            }, {
                push_end_date: {
                    [Op.gte]: ctx.helper.now()
                }
            }]
        }

        if (query.iese) {
            where.school_id = 116
        }

        const location_ids = await ctx.model.EventCalendar.findAll({
            attributes: ['location_id'],
            where,
            group: ['location_id'],
            raw: true,
        }).map(row => {
            return row.location_id
        }) || []

        const locations = await ctx.model.Geo.findAll({
            attributes: ['id', 'name'],
            where: {
                id: location_ids
            },
            raw: true
        })

        const lids = await ctx.model.EventCalendar.findAll({
            attributes: ['lid'],
            where: {
                status: {
                    [Op.gte]: 0,
                },
                [Op.and]: [{
                    push_begin_date: {
                        [Op.lte]: ctx.helper.now()
                    }
                }, {
                    push_end_date: {
                        [Op.gte]: ctx.helper.now()
                    }
                }]
            },
            group: ['lid'],
            raw: true,
        }).map(row => {
            return row.lid
        }) || []

        let major_ids = await ctx.model.EventMajor.findAll({
            attributes: ['major_id'],
            where: {
                lid: lids,
            },
            group: ['major_id'],
            raw: true,
        }).map(row => {
            return row.major_id
        }) || []

        const majors = await ctx.model.MajorCategoryDic.findAll({
            attributes: ['id', 'display_name'],
            where: {
                id: major_ids
            },
            raw: true
        })

        ctx.json_body = {
            locations,
            majors
        }
    }

    async event_statistics() {
        const { ctx, config } = this

        const events = new Map()
        let output = []

        await ctx.model.Event.findAll({
            where: {},
            raw: true,
        }).map(el => {
            let check = moment(el.created_at, 'YYYY-MM-DD')

            el.year = check.format('YYYY')
            el.month = check.format('M')
            el.day = check.format('D')
            el.group = `${el.year}${pad(2, el.month, '0')}`

            if (events.has(el.group)) {
                events.set(el.group, [...Object.values(events.get(el.group)), el])
            } else {
                events.set(el.group, [el])
            }
        })

        for (const [key, val] of events) {
            let views = 0
            let diffs = []

            const tids = val.filter(el => el.forum_url > 0)?.map(el => el.forum_url)

            if (tids.length) {
                views = await ctx.forumModel.ForumThread.findAll({
                    where: {
                        tid: tids
                    },
                    raw: true,
                })

                views = views?.map(el => el.views)?.reduce((a, b) => a + b, 0)
            }

            for (const event of val) {
                const cal = await ctx.model.EventCalendar.findOne({
                    where: {
                        event_id: event.id,
                    },
                    raw: true,
                })

                if (cal) {
                    const begin = moment(event.created_at, 'YYYY-MM-DD')
                    const end = moment.unix(cal.event_end_date, 'YYYY-MM-DD')

                    const diff = end.diff(begin, 'days')
                    diffs.push(diff)
                }
            }

            output.push(`
${key}
核心活动总数：${val.length}
推0总数:${val.filter(el => el.push_0 == 1)?.length}
论坛帖子总点击：${views}
宣传总时长：${diffs.reduce((a, b) => a + b, 0)}天
${'-'.repeat(30)}`)
        }

        ctx.body = output.join('')
    }

    async event_statistics2() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const body = Object.assign({}, ctx.request.body)
        const diffs = []

        const events = await ctx.model.Event.findAll({
            where: {
                [Op.and]: [{
                    created_at: {
                        [Op.lte]: body.end
                    }
                }, {
                    created_at: {
                        [Op.gte]: body.begin
                    }
                }]
            },
            raw: true,
        })

        const total = events.length

        const push1 = await ctx.model.EventRelease.findAll({
            where: {
                event_id: events.map(el => el.id),
                type: 1,
            },
            raw: true,
        })

        for (const event of events) {
            const items = _.filter(push1, { 'event_id': event.id })
            const maxItem = _.maxBy(items, function (o) { return o.event_end_date; })

            if (maxItem) {
                const begin = moment(event.created_at, 'YYYY-MM-DD')
                const end = moment.unix(maxItem?.event_end_date, 'YYYY-MM-DD')

                const diff = end.diff(begin, 'days')
                if (diff > 0) diffs.push(diff)
            }
        }

        const result = `
        核心活动总数：${total}
        推1总数:${push1.length}
        宣传总时长：${diffs.reduce((a, b) => a + b, 0)}天
        ${'-'.repeat(30)}`

        ctx.body = result
    }
}

module.exports = EventController
