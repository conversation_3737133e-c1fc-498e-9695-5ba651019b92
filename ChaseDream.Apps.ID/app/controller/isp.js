const BaseController = require('./base')

class IspController extends BaseController {
    async list() {
        const { ctx } = this

        ctx.json_body = await ctx.model.Isp.findOne({})
    }

    async update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = await ctx.model.Isp.update(body, {
            where: { id: body.id }
        })
    }
}

module.exports = IspController
