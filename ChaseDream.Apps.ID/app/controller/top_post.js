const _ = require('lodash')
const md5 = require('md5')
const pad = require('pad')
const randomstring = require("randomstring")
const Controller = require('egg').Controller

class TopPostController extends Controller {
    async change_forum_user_info() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const email_preffix = 'cd--'
        const email_suffix = '@cd.com'

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                email: {
                    [Op.like]: `${email_preffix}%${email_suffix}`,
                }
            },
            order: [
                ['email', 'DESC'],
            ],
            raw: true
        })

        let max = member && member.email.replace(email_preffix, '').replace(email_suffix, '') || 0

        let result = []

        const body = Object.assign({}, ctx.request.body)

        for (const username of body.username) {
            const forum_user = await ctx.forumModel.CommonMember.findOne({
                where: {
                    username
                },
                raw: true
            })

            const uid = forum_user.uid

            const email = `${email_preffix}${pad(3, ++max, '0')}${email_suffix}`

            // 改密码、改邮箱、改用户组
            const raw_password = randomstring.generate(10)
            const salt = ctx.helper.get_random_num(6)

            const password = md5(md5(raw_password) + salt)

            await ctx.forumModel.UcenterMember.update({
                email,
                password,
                salt,
            }, {
                where: {
                    uid
                }
            })

            await ctx.forumModel.CommonMember.update({
                email,
                password,
                emailstatus: 1,
                groupid: 10,
            }, {
                where: {
                    uid
                }
            })

            await ctx.model.TopPostUser.create({
                nickname: username,
                username: email,
                password: ctx.helper.encode_dz_string(raw_password),
                status: 1,
            })

            // try {
            //     await app.memcached.flushAsync()
            // } catch (err) {
            //     ctx.logger.error(err.message)
            // }

            result.push({
                username,
                password: raw_password,
                email
            })
        }

        ctx.json_body = result
    }

    async setting() {
        const { ctx } = this

        ctx.json_body = await ctx.model.TopPostSetting.findOne({
            where: {}
        })
    }

    async setting_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.TopPostSetting.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async thread_list() {
        const { ctx, config } = this

        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.TopPostThread.findAndCountAll({
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async thread_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        const now = ctx.helper.now()

        body.next_tick = now + 5 * 60

        const thread = await ctx.forumModel.ForumThread.findOne({
            where: {
                tid: body.tid,
            }
        })

        body.subject = thread.subject

        ctx.json_body = await ctx.model.TopPostThread.create(body)
    }

    async thread_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        const now = ctx.helper.now()

        const min = now + parseInt(body.min, 10) * 60
        const max = now + parseInt(body.max, 10) * 60

        body.next_tick = _.random(min, max)

        const thread = await ctx.forumModel.ForumThread.findOne({
            where: {
                tid: body.tid,
            }
        })

        body.subject = thread.subject

        await ctx.model.TopPostThread.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async thread_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.TopPostThread.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async message_list() {
        const { ctx, config } = this

        let model = Object.assign({}, ctx.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        ctx.json_body = await ctx.model.TopPostMessage.findAndCountAll({
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async message_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        ctx.json_body = await ctx.model.TopPostMessage.create(body)
    }

    async message_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.TopPostMessage.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = {
            success: true
        }
    }

    async message_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.TopPostMessage.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async user_list() {
        const { ctx, config, app } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.query)
        query.page = query.page || 1

        let where = {}

        if (query.s) {
            where.nickname = {
                [Op.like]: `%${query.s.trim()}%`,
            }
        }

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        ctx.json_body = await ctx.model.TopPostUser.findAndCountAll({
            attributes: ['id', 'username', 'nickname', 'status', 'created_at'],
            where: where,
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size
        })
    }

    async user_create() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const res = await ctx.service.thread.login(body)

        body.password = ctx.helper.encode_dz_string(body.password)
        body.status = res.Message.messageval.indexOf('login_succeed') !== -1 ? 1 : 0

        const result = await ctx.model.TopPostUser.create(body)

        ctx.json_body = body.status ? result : res.Message
    }

    async user_update() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)
        let res = {}

        if (body.password) {
            res = await ctx.service.thread.login(body)

            body.status = res.Message.messageval.indexOf('login_succeed') !== -1 ? 1 : 0
            body.password = ctx.helper.encode_dz_string(body.password)
        }

        await ctx.model.TopPostUser.update(body, {
            where: { id: body.id }
        })

        ctx.json_body = body.status ? { success: true } : res.Message
    }

    async user_delete() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        await ctx.model.TopPostUser.destroy({
            where: { id: body.id },
            force: true
        })

        ctx.json_body = {
            success: true
        }
    }

    async get_password() {
        const { ctx } = this

        const body = Object.assign({}, ctx.request.body)

        const user = await ctx.model.TopPostUser.findOne({
            where: {
                id: body.id
            },
            raw: true,
        })

        const password = ctx.helper.decode_dz_string(user.password)

        ctx.json_body = {
            password
        }
    }
}

module.exports = TopPostController
