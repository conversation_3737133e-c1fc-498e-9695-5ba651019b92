const fs = require('fs')
const md5 = require('md5')
const randomstring = require("randomstring")
const BaseController = require('./base')

class UserController extends BaseController {
    async search() {
        const { ctx } = this

        const query = Object.assign({}, ctx.request.query)

        const member = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username: query.username
            }
        })

        ctx.error(!member, `用户 ${query.username} 不存在`)

        let member_count = await ctx.forumModel.CommonMemberCount.findOne({
            attributes: ['uid', 'threads', 'posts', 'friends', 'doings'],
            where: {
                uid: member.uid
            },
            raw: true
        }) || {
            threads: 0,
            posts: 0,
            friends: 0,
            doings: 0,
        }

        member_count.posts -= member_count.threads

        member_count.pm = await ctx.forumModel.UcenterPmMembers.count({
            where: {
                uid: member.uid
            },
            raw: true
        })

        member_count.favorite = await ctx.forumModel.HomeFavorite.count({
            where: {
                uid: member.uid
            },
            raw: true
        })

        const m = await ctx.model.Member.findOne({
            where: {
                forum_uid: member.uid
            }
        }) || {}

        const wechat = await ctx.model.Wechat.findOne({
            where: {
                uid: m.id || 0
            }
        })

        const avatar = ctx.helper.get_dz_avatar(member.uid)

        member_count.avatar = fs.existsSync(avatar[0])
        member_count.wechat = !!wechat
        member_count.username = query.username
        member_count.avatar_url = `https://forum.chasedream.com/uc_server/avatar.php?uid=${member.uid}&size=big`

        ctx.json_body = member_count
    }

    async account_close() {
        const { ctx, app } = this
        const Op = app.Sequelize.Op

        const body = Object.assign({}, ctx.request.body)
        const username = decodeURIComponent(body.username)

        let clear_count = {}

        const um = await ctx.forumModel.UcenterMember.findOne({
            where: {
                username
            }
        })

        ctx.error(!um, `用户不存在`)

        const new_username = `CD用户${um.uid}`

        const position = um.email.lastIndexOf('.')
        const new_email = `${um.email.substring(0, position)}.DEL${um.email.substring(position)}`

        await ctx.forumModel.UcenterMember.update({
            username: new_username,
            email: new_email,
        }, {
            where: {
                uid: um.uid
            }
        })

        await ctx.forumModel.CommonMember.update({
            username: new_username,
            password: '444',
            email: new_email,
            avatarstatus: 0,
        }, {
            where: {
                uid: um.uid
            }
        })

        await ctx.forumModel.CommonMemberArchive.update({
            username: new_username,
            email: new_email,
            avatarstatus: 0,
        }, {
            where: {
                uid: um.uid
            }
        })

        await ctx.forumModel.CommonMemberProfile.update({
            field4: username,
        }, {
            where: {
                uid: um.uid
            }
        })

        await ctx.forumModel.CommonMemberFieldHome.update({
            recentnote: '',
            spacenote: '',
        }, {
            where: {
                uid: um.uid
            }
        })

        await ctx.forumModel.CommonMemberFieldForum.update({
            sightml: '',
        }, {
            where: {
                uid: um.uid
            }
        })

        const member = await ctx.model.Member.findOne({
            where: {
                forum_uid: um.uid
            }
        }) || {}

        const mobile = await ctx.model.Mobile.findOne({
            where: {
                uid: member.id || 0
            }
        })

        if (mobile) {
            let area_code = 44300
            let exist = null

            do {
                area_code += 100
                exist = await ctx.model.Mobile.findOne({
                    where: {
                        area_code: mobile.area_code + area_code,
                        mobile_encrypt: mobile.mobile_encrypt,
                    }
                })
            } while (exist)

            await ctx.model.Mobile.update({
                area_code: mobile.area_code + area_code,
            }, {
                where: {
                    uid: member.id
                }
            })
        }

        await ctx.forumModel.ForumThread.update({
            author: new_username
        }, {
            where: {
                authorid: um.uid
            }
        })
        if (body.threads) {
            await ctx.forumModel.ForumThread.update({
                fid: 44,
                author: new_username
            }, {
                where: {
                    authorid: um.uid
                }
            })
        }

        await ctx.forumModel.ForumPost.update({
            author: new_username,
        }, {
            where: {
                authorid: um.uid,
            }
        })
        if (body.posts) {
            await ctx.forumModel.ForumPost.update({
                author: new_username,
                invisible: -1,
                status: 1032,
            }, {
                where: {
                    authorid: um.uid,
                    position: {
                        [Op.gt]: 1
                    }
                }
            })
        }

        await ctx.forumModel.HomeFriend.update({
            fusername: new_username,
        }, {
            where: {
                fuid: um.uid
            }
        })
        if (body.friends) {
            await ctx.forumModel.HomeFriend.destroy({
                where: {
                    uid: um.uid
                },
                force: true
            })

            clear_count.friends = 0
        }

        if (body.doings) {
            await ctx.forumModel.HomeDoing.destroy({
                where: {
                    uid: um.uid
                },
                force: true
            })

            clear_count.doings = 0
        } else {
            await ctx.forumModel.HomeDoing.update({
                username: new_username,
            }, {
                where: {
                    uid: um.uid
                }
            })
        }

        if (body.pm) {
            await ctx.forumModel.UcenterPmMembers.destroy({
                where: {
                    uid: um.uid
                },
                force: true
            })
        }

        if (body.favorite) {
            await ctx.forumModel.HomeFavorite.destroy({
                where: {
                    uid: um.uid
                },
                force: true
            })
        }

        if (body.wechat) {
            await ctx.model.Wechat.destroy({
                where: {
                    uid: member.id || 0
                },
                force: true
            })
        }

        if (body.avatar) {
            ctx.helper.rename_dz_avatar(um.uid)
        }

        await ctx.forumModel.CommonMemberCount.update(clear_count, {
            where: {
                uid: um.uid
            }
        })

        await ctx.model.Member.update({
            username: new_username
        }, {
            where: {
                forum_uid: um.uid
            }
        })

        try {
            await app.memcached.flushAsync()
        } catch (err) {
            ctx.logger.error(err.message)
        }

        ctx.json_body = {
            success: true,
            space: `https://forum.chasedream.com/space-uid-${um.uid}.html`,
            new_username,
            new_email
        }
    }

    async thread_down_search() {
        const { ctx, app, config } = this
        const Op = app.Sequelize.Op

        let query = Object.assign({}, ctx.request.query)
        query.page = query.page || 1

        const page_size = parseInt(query.page_size, 10) || config.page_size
        const page = (query.page - 1) * page_size

        const thread = await ctx.forumModel.ForumThread.findAndCountAll({
            attributes: ['tid', 'subject', 'author', 'authorid'],
            where: {
                subject: {
                    [Op.like]: `%${query.s}%`,
                },
            },
            offset: page,
            limit: page_size,
            raw: true
        })

        const tid = await ctx.forumModel.ForumThread.findAll({
            attributes: ['tid'],
            where: {
                subject: {
                    [Op.like]: `%${query.s}%`,
                },
            },
            raw: true
        }).map(row => {
            return row.tid
        })

        ctx.json_body = {
            thread,
            tid,
        }
    }

    async thread_down() {
        const { ctx } = this

        let body = Object.assign({}, ctx.request.body)

        if (!body.tid || body.tid.length === 0) ctx.error(true, 'tid参数错误')

        await ctx.forumModel.ForumThread.update({
            lastpost: ctx.helper.now() - 86400 * 730,
            moderated: 1,
        }, {
            where: {
                tid: body.tid,
            }
        })

        try {
            await app.memcached.flushAsync()
        } catch (err) {
            ctx.logger.error(err.message)
        }

        ctx.json_body = {
            success: true,
        }
    }

    async list() {
        const { ctx, config, app } = this

        let model = Object.assign({}, ctx.request.query)
        model.page = model.page || 1

        const page_size = parseInt(model.page_size, 10) || config.page_size
        const page = (model.page - 1) * page_size

        let users = await ctx.model.User.findAndCountAll({
            attributes: ['id', 'username', 'realname', 'department', 'created_at'],
            order: [
                ['id', 'DESC'],
            ],
            offset: page,
            limit: page_size,
            raw: true
        })

        let rows = []
        for (let row of users.rows) {
            let role = await app.zrole.getRolesForUser(`${row.id}_user`)

            role = Array.from(role).map(ele => {
                return ele.substring(0, ele.indexOf('_'))
            })

            role = await ctx.model.Role.findAll({
                attributes: ['id', 'name'],
                where: {
                    id: role
                },
                raw: true
            })

            row.role = role
            rows.push(row)
        }

        users.rows = rows

        ctx.json_body = users
    }

    async create() {
        const { ctx, app } = this

        await ctx.verify('user.create', 'body')

        let body = Object.assign({}, ctx.request.body)
        body.password = md5(randomstring.generate(8))

        let user = await ctx.model.User.findOne({
            where: { username: body.username }
        })

        ctx.error(user, "用户已存在")

        user = await ctx.model.User.create({
            username: body.username,
            password: body.password,
            realname: body.realname,
            department: body.department,
        })

        for (const role of body.role) {
            await app.zrole.addRoleForUser(`${user.id}_user`, `${role}_role`)
        }
        await app.zrole.savePolicy()

        ctx.json_body = { uid: user.id }
    }

    async update() {
        const { ctx, app } = this

        let body = Object.assign({}, ctx.request.body)

        if (body.password) {
            body.password = md5(body.password)
        }

        await ctx.model.User.update({
            username: body.username,
            realname: body.realname,
            department: body.department,
        }, {
            where: { id: body.id }
        })

        await app.zrole.deleteRolesForUser(`${body.id}_user`)
        for (const role of body.role) {
            await app.zrole.addRoleForUser(`${body.id}_user`, `${role}_role`)
        }
        await app.zrole.savePolicy()

        ctx.json_body = {
            success: true,
        }
    }

    async delete() {
        const { ctx, app } = this

        let body = Object.assign({}, ctx.request.body)

        let user = await ctx.model.User.destroy({
            where: { id: body.id }
        })

        await app.zrole.deleteRolesForUser(`${body.id}_user`)
        await app.zrole.savePolicy()

        ctx.json_body = { user }
    }

    async departments() {
        const { ctx, config } = this

        const user = ctx.locals.user

        let department = config.department

        if (!ctx.helper.isAdmin(user)) {
            department = [config.department.find(e => e.id === user.department)]
        }

        ctx.json_body = department
    }
}

module.exports = UserController
