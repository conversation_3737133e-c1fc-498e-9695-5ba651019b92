const wechat = require('co-wechat')
const oAuth = require('co-wechat-oauth')
const QRCode = require('qrcode')
const BaseController = require('./base')

module.exports = app => {
    class WechatController extends BaseController {
        async js_config() {
            const { ctx, app } = this

            const param = {
                debug: false,
                jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
                url: ctx.query.url
            }

            ctx.json_body = await app.wechatApi.getJsConfig(param)
        }

        async qrcode() {
            const { ctx, app } = this

            const res = await app.wechatApi.createTmpQRCode(10000, 60 * 5)

            ctx.json_body = {
                image: await QRCode.toDataURL(res.url, { errorCorrectionLevel: 'H' }),
                ticket: res.ticket,
            }
        }

        async verify_ticket() {
            const { ctx, app } = this

            const ticket = ctx.request.query.t
            const type = ctx.request.query.type

            let ret = {
                openid: await app.redis.get(`wx.login:${ticket}`) || "",
                uid: 0,
                username: ""
            }

            if (ret.openid === "") {
                ctx.json_body = ret
                return
            }

            switch (type) {
                case "login":
                    const wx_info = await ctx.service.wechat.get_userinfo_by_openid(ret.openid)
                    ret.unionid = wx_info.unionid
                    ret.nickname = wx_info.nickname

                    const wechat = await ctx.model.Wechat.findOne({
                        where: {
                            unionid: wx_info.unionid
                        }
                    }) || ""

                    if (wechat) {
                        const member = await ctx.model.Member.findOne({
                            where: {
                                id: wechat.uid
                            }
                        })

                        if (member) {
                            ret.uid = member.forum_uid
                            ret.username = member.username

                            const common_member = await ctx.forumModel.CommonMember.findOne({
                                where: {
                                    uid: member.forum_uid
                                }
                            })

                            await ctx.service.auth.create_userlogin_log({
                                uid: common_member.uid,
                                email: common_member.email,
                                username: common_member.username,
                                dateline: ctx.helper.now(),
                                ip: ctx.ip,
                                loginsuccess: 1,
                                browserinfo: ctx.get('user-agent') || '',                    
                                loginmethod: `${(ctx.request.query.platform || 0)}4`,
                                referer: ctx.get('referer') || '',
                            })

                            const cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt)

                            ret = Object.assign(ret, cookie)
                        }
                    }
                    break

                case "binding":
                    let userinfo = ctx.helper.decode_dz_authkey()

                    if (userinfo) {
                        userinfo = userinfo.split("\t")

                        const wx_info = await ctx.service.wechat.get_userinfo_by_openid(ret.openid)
                        ret.unionid = wx_info.unionid
                        ret.nickname = wx_info.nickname

                        const wechat = await ctx.model.Wechat.findOne({
                            where: {
                                unionid: wx_info.unionid,
                            }
                        }) || {}

                        if (!wechat.uid && userinfo.length > 2 && userinfo[2] > 0) {
                            await ctx.model.Wechat.upsert({
                                uid: userinfo[2],
                                nickname: ret.nickname,
                                openid: ret.openid,
                                unionid: ret.unionid,
                            })

                            ret.binding = true
                        } else {
                            const member = await ctx.model.Member.findOne({
                                where: {
                                    id: wechat.uid
                                }
                            })

                            const common_member = await ctx.forumModel.CommonMember.findOne({
                                where: {
                                    uid: member.forum_uid
                                }
                            })

                            ret.binding = false
                            ret.uid = member.forum_uid
                            ret.username = member.username

                            const cookie = ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt, false)

                            ret.auth = cookie.auth
                            ret.saltkey = cookie.saltkey
                        }
                    }
                    break
            }

            ctx.json_body = ret
        }

        async check_login() {
            const { ctx } = this
            let ret = {
                login: false
            }

            let userinfo = ctx.helper.decode_dz_authkey()

            if (userinfo) {
                userinfo = userinfo.split("\t")

                if (userinfo.length > 2) {
                    ret.login = true
                }
            }

            ctx.json_body = ret
        }

        async binding() {
            const { ctx } = this

            let ret = { binding: false }
            const body = Object.assign({}, ctx.request.body)

            const userinfo = ctx.helper.get_userinfo_from_cookie()

            await ctx.model.Wechat.upsert({
                uid: userinfo[2],
                nickname: body.nickname,
                openid: body.openid,
                unionid: body.unionid,
            })

            ret.binding = true

            ctx.json_body = ret
        }

        async change_binding() {
            const { ctx } = this

            let ret = { change_binding: false }
            const body = Object.assign({}, ctx.request.body)

            const userinfo = ctx.helper.get_userinfo_from_cookie()

            await ctx.model.Wechat.upsert({
                uid: userinfo[2],
                nickname: body.nickname,
                openid: body.openid,
                unionid: body.unionid,
            })

            ret.change_binding = true

            ctx.json_body = ret
        }

        async unbinding() {
            const { ctx } = this

            let ret = { unbinding: false }

            const userinfo = ctx.helper.get_userinfo_from_cookie()

            await ctx.model.Wechat.destroy({
                where: {
                    uid: userinfo[2]
                },
                force: true,
                individualHooks: true
            })

            ret.unbinding = true

            ctx.json_body = ret
        }

        async url() {
            const { ctx, config } = this
            const type = ctx.request.query.type

            const client = new oAuth(config.wechatApi.appId, config.wechatApi.appSecret)

            const url = client.getAuthorizeURL(config.wechatApi.callback[type], 'state', 'snsapi_userinfo')

            ctx.json_body = { url }
        }

        async id_web_code() {
            const { ctx, config } = this

            const client = new oAuth(config.wechatApi.appId, config.wechatApi.appSecret)

            const token = await client.getAccessToken(ctx.request.query.code)

            const ret = await client.getUser(token.data.openid)

            ctx.session.wechat = ret

            const wechat = ret.unionid ? await ctx.model.Wechat.findOne({
                where: {
                    unionid: ret.unionid
                }
            }) : ""

            if (wechat) {
                const member = await ctx.model.Member.findOne({
                    where: {
                        id: wechat.uid
                    }
                })

                if (member) {
                    ret.uid = member.forum_uid
                    ret.username = member.username

                    const common_member = await ctx.forumModel.CommonMember.findOne({
                        where: {
                            uid: member.forum_uid
                        }
                    })

                    ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt)

                    ctx.redirect(config.discuz.home_url)
                    return
                }
            }

            ctx.redirect(`/register/tip-unbind?openid=${ret.openid}&unionid=${ret.unionid}&nickname=${ret.nickname}`)
        }

        async app_login() {
            const { ctx } = this

            const res = await this.get_userinfo(ctx.query.code)

            ctx.error(!res.unionid, "微信验证异常")

            const wechat = await ctx.model.Wechat.findOne({
                where: {
                    unionid: res.unionid
                }
            })

            if (wechat) {
                const member = await ctx.model.Member.findOne({
                    where: {
                        id: wechat.uid
                    }
                })

                const common_member = await ctx.forumModel.CommonMember.findOne({
                    where: {
                        uid: member.forum_uid
                    }
                })

                await ctx.service.auth.create_userlogin_log({
                    uid: common_member.uid,
                    email: common_member.email,
                    username: common_member.username,
                    dateline: ctx.helper.now(),
                    ip: ctx.ip,
                    loginsuccess: 1,
                    browserinfo: ctx.get('user-agent') || '',                    
                    loginmethod: `${(ctx.query.platform || 0)}4`,
                    referer: ctx.get('referer') || '',
                })

                ctx.json_body = {
                    success: true,
                    cookie: ctx.helper.encode_dz_authkey(`${common_member.password}\t${common_member.uid}\t${member.id}`, member.salt),
                    openid: res.openid,
                    unionid: res.unionid,
                    nickname: res.nickname,
                }
            } else {
                ctx.json_body = {
                    success: false,
                    exist: false,
                    openid: res.openid,
                    unionid: res.unionid,
                    nickname: res.nickname,
                }
            }
        }

        async login_with_other() {
            const { ctx, config } = this

            const body = Object.assign({}, ctx.request.body)

            const domain = '.chasedream.com'

            ctx.cookies.set(`${config.discuz.prefix}auth`, body.auth, {
                httpOnly: false,
                domain
            })
            ctx.cookies.set(`${config.discuz.prefix}saltkey`, body.saltkey, {
                httpOnly: false,
                domain
            })

            ctx.json_body = {
                success: true
            }
        }

        async userinfo() {
            const { ctx } = this

            const userinfo = ctx.helper.get_userinfo_from_cookie()

            const wechat = await ctx.model.Wechat.findOne({
                where: {
                    uid: userinfo[2]
                }
            }) || {}

            ctx.json_body = wechat
        }

        async update_userinfo() {
            const { ctx, app } = this

            const wechats = await ctx.model.Wechat.findAll({
                where: {
                    nickname: ''
                }
            })

            for (let wechat of wechats) {
                try {
                    const info = await app.wechatApi.getUser(wechat.openid)
                    if (info.subscribe) {
                        await ctx.model.Wechat.update({
                            nickname: info.nickname
                        }, {
                            where: {
                                uid: wechat.uid
                            }
                        })
                    }
                } catch (e) { }
            }

            ctx.json_body = { success: true }
        }

        async get_userinfo(code) {
            const { ctx, config } = this

            let res = await ctx.curl(`https://api.weixin.qq.com/sns/oauth2/access_token?appid=${config.wechat_open.appid}&secret=${config.wechat_open.secret}&code=${code}&grant_type=authorization_code`, {
                method: 'GET',
                dataType: 'json',
                timeout: 10000,
            })

            res = await ctx.curl(`https://api.weixin.qq.com/sns/userinfo?access_token=${res.data.access_token}&openid=${res.data.openid}`, {
                method: 'GET',
                dataType: 'json',
                timeout: 10000,
            })

            return res.data
        }
    }

    WechatController.prototype.wechat = wechat({
        appid: app.config.wechatApi.appId,
        token: app.config.wechatApi.token,
        encodingAESKey: app.config.wechatApi.encodingAESKey
    }).middleware(async (message, ctx) => {
        console.log(message)
        /// 微信用户扫码二维码
        if (message.EventKey === "10000" || message.EventKey === "qrscene_10000") {
            await ctx.app.redis.setex(`wx.login:${message.Ticket
                }`, 60 * 5, message.FromUserName)
        }

        return ''
    })

    return WechatController
}
