const fs = require('fs')
const BaseController = require('./base')

module.exports = app => {
    class WechatGroupController extends BaseController {
        async wechat_logout() {
            await global.wechat_bot.logout()

            ctx.json_body = {
                success: true,
            }
        }

        async wechat_group_monitor_list() {
            const { ctx, app, config } = this
            const Op = app.Sequelize.Op

            let where = {
                status: {
                    [Op.gte]: -1,
                }
            }
            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            for (const item of ['id']) {
                if (query[item]) where[item] = query[item]
            }

            for (const item of ['chatroom_name', 'admin', 'announcement', 'note']) {
                if (query[item]) {
                    where[item] = {
                        [Op.like]: `%${query[item]}%`,
                    }
                }
            }

            ctx.json_body = await ctx.model.WechatGroupMonitor.findAndCountAll({
                include: [{
                    model: ctx.model.WechatGroupMonitorMessage,
                    required: false,
                    as: 'message',
                }],
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
            })
        }

        async wechat_group_monitor_switch() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.WechatGroupMonitor.update({
                status: body.status,
            }, {
                where: {
                    id: body.id,
                }
            })

            ctx.json_body = {
                success: true,
            }
        }

        async wechat_group_monitor_send_post() {
            const { ctx, app } = this
            const Op = app.Sequelize.Op

            const body = Object.assign({}, ctx.request.body)

            if (body.send_post) {
                const thread = await ctx.forumModel.ForumThread.findOne({
                    where: {
                        tid: body.tid,
                    },
                    raw: true,
                })

                ctx.error(!thread, 'tid不存在')

                await ctx.model.WechatGroupMonitor.update({
                    tid: thread.tid,
                    fid: thread.fid,
                    send_post: true,
                }, {
                    where: {
                        id: body.id,
                    }
                })

                await ctx.model.WechatGroupMonitorMessage.update({
                    tid: thread.tid,
                    fid: thread.fid,
                    status: 0,
                }, {
                    where: {
                        chatroom_id: body.chatroom_id,
                        status: {
                            [Op.lt]: 0,
                        },
                    }
                })
            } else if (!body.send_post) {
                await ctx.model.WechatGroupMonitor.update({
                    send_post: false,
                }, {
                    where: {
                        id: body.id,
                    }
                })
            }

            ctx.json_body = {
                success: true,
            }
        }

        async wechat_group_monitor_update() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.WechatGroupMonitor.update(body, {
                where: {
                    id: body.id,
                }
            })

            ctx.json_body = {
                success: true,
            }
        }

        async wechat_group_monitor_delete() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.WechatGroupMonitor.update({
                status: -2,
            }, {
                where: {
                    id: body.id,
                }
            })

            ctx.json_body = {
                success: true,
            }
        }

        async wechat_group_monitor_user_list() {
            const { ctx, app, config } = this
            const Op = app.Sequelize.Op

            let where = {}

            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            for (const item of ['id', 'wx_id', 'gender', 'forum_gender', 'forum_uid', 'forum_mobile']) {
                if (query[item]) where[item] = query[item]
            }

            for (const item of ['area', 'note', 'wx', 'wx_nickname', 'forum_username', 'label', 'desc', 'sign']) {
                if (query[item]) {
                    where[item] = {
                        [Op.like]: `%${query[item]}%`,
                    }
                }
            }

            ctx.json_body = await ctx.model.WechatGroupMonitorUser.findAndCountAll({
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
                raw: true,
            })
        }

        async wechat_group_monitor_user_update() {
            const { ctx, config } = this

            const body = Object.assign({}, ctx.request.body)
            let model = {}
            let member = undefined

            if (body.forum_uid) {
                member = await ctx.model.Member.findOne({
                    where: {
                        forum_uid: body.forum_uid
                    }
                })
            } else if (body.forum_username) {
                member = await ctx.model.Member.findOne({
                    where: {
                        username: body.forum_username
                    },
                    raw: true,
                })
            }

            if (member) {
                const mobile = await ctx.model.Mobile.findOne({
                    where: {
                        uid: member.id
                    },
                    raw: true,
                })

                if (mobile) {
                    model.forum_mobile = ctx.helper.AESDecrypt(mobile.mobile_encrypt)
                }

                const member_profile = await ctx.forumModel.CommonMemberProfile.findOne({
                    where: {
                        uid: member.id
                    },
                    raw: true,
                }) || {}

                model.wx = body.wx
                model.forum_uid = member.forum_uid
                model.forum_username = member.username
                model.forum_regip = member.regip
                model.forum_gender = member_profile.gender
            }

            await ctx.model.WechatGroupMonitorUser.update(model, {
                where: {
                    id: body.id,
                }
            })

            const wgmu = await ctx.model.WechatGroupMonitorUser.findOne({
                where: {
                    id: body.id,
                }
            })

            await ctx.model.WechatGroupMonitorAccount.update({
                wx: wgmu.wx,
            }, {
                where: {
                    wx_id: wgmu.wx_id,
                }
            })

            ctx.json_body = {
                success: true,
                forum_uid: model.forum_uid,
            }
        }

        async wechat_group_monitor_account_list() {
            const { ctx, app, config } = this
            const Op = app.Sequelize.Op

            let where = {}
            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            for (const item of ['id', 'wx_group_nickname', 'join_method', 'cd_worker']) {
                if (query[item]) where[item] = query[item]
            }

            for (const item of ['last_message_content', 'wx_id', 'chatroom_id', 'wx', 'wx_nickname']) {
                if (query[item]) {
                    where[item] = {
                        [Op.like]: `%${query[item]}%`,
                    }
                }
            }

            if (query.chatroom_name) {
                let chatroom_id = await ctx.model.WechatGroupMonitor.findAll({
                    where: {
                        chatroom_name: {
                            [Op.like]: `%${query.chatroom_name}%`,
                        }
                    },
                    raw: true,
                }).map(row => { return row.chatroom_id })

                if (query.chatroom_id) {
                    where.chatroom_id = chatroom_id.push(query.chatroom_id)
                } else {
                    where.chatroom_id = chatroom_id
                }
            }

            ctx.json_body = await ctx.model.WechatGroupMonitorAccount.findAndCountAll({
                include: [{
                    model: ctx.model.WechatGroupMonitorMessage,
                    required: false,
                    as: 'message',
                }, {
                    model: ctx.model.WechatGroupMonitor,
                    required: false,
                    as: 'wechat_group_monitor',
                }],
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
            })
        }

        async wechat_group_monitor_account_other_group() {
            const { ctx } = this

            const chatroom_id = await ctx.model.WechatGroupMonitorAccount.findAll({
                where: {
                    wx_id: ctx.params.wx_id
                },
                raw: true,
            }).map(row => { return row.chatroom_id }) || []

            ctx.json_body = await ctx.model.WechatGroupMonitor.findAll({
                attributes: ['id', 'chatroom_id', 'chatroom_name'],
                where: {
                    chatroom_id
                },
                raw: true,
            })
        }

        async wechat_group_monitor_account_update() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.WechatGroupMonitorAccount.update(body, {
                where: {
                    id: body.id,
                }
            })

            ctx.json_body = {
                success: true,
            }
        }

        async wechat_group_monitor_message_list() {
            const { ctx, app, config } = this
            const Op = app.Sequelize.Op

            let where = {}
            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            for (const item of ['id', 'chatroom_id', 'wx_id', 'wx_nickname', 'type', 'status']) {
                if (query[item]) where[item] = query[item]
            }

            for (const item of ['text']) {
                if (query[item]) {
                    where[item] = {
                        [Op.like]: `%${query[item]}%`,
                    }
                }
            }

            if (query.chatroom_name) {
                let chatroom_id = await ctx.model.WechatGroupMonitor.findAll({
                    where: {
                        chatroom_name: {
                            [Op.like]: `%${query.chatroom_name}%`,
                        }
                    },
                    raw: true,
                }).map(row => { return row.chatroom_id })

                if (query.chatroom_id) {
                    where.chatroom_id = chatroom_id.push(query.chatroom_id)
                } else {
                    where.chatroom_id = chatroom_id
                }
            }

            const messages = await ctx.model.WechatGroupMonitorMessage.findAndCountAll({
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
                raw: true,
            })

            for (let message of messages.rows) {
                message.wechat_group_monitor = await ctx.model.WechatGroupMonitor.findOne({
                    where: {
                        chatroom_id: message.chatroom_id,
                    },
                    raw: true,
                })
            }

            ctx.json_body = messages
        }

        async wechat_group_monitor_message_download() {
            const { ctx, config } = this

            const message = await ctx.model.WechatGroupMonitorMessage.findOne({
                where: {
                    id: ctx.params.id,
                }
            })

            ctx.attachment(message.filename)
            ctx.set('Content-Type', 'application/octet-stream')
            const full_path = `${config.baseDir}${config.forum_attachment}/${message.attachment}`

            ctx.body = fs.createReadStream(full_path)
        }

        async wechat_group_monitor_log_list() {
            const { ctx, app, config } = this
            const Op = app.Sequelize.Op

            let where = {}
            const query = Object.assign({}, ctx.query)
            query.page = query.page || 1

            const page_size = parseInt(query.page_size, 10) || config.page_size
            const page = (query.page - 1) * page_size

            for (const item of ['id', 'chatroom_id', 'wx_id', 'in_out']) {
                if (query[item]) where[item] = query[item]
            }

            if (query.chatroom_name) {
                let chatroom_id = await ctx.model.WechatGroupMonitor.findAll({
                    where: {
                        chatroom_name: {
                            [Op.like]: `%${query.chatroom_name}%`,
                        }
                    },
                    raw: true,
                }).map(row => { return row.chatroom_id })

                if (query.chatroom_id) {
                    where.chatroom_id = chatroom_id.push(query.chatroom_id)
                } else {
                    where.chatroom_id = chatroom_id
                }
            }

            if (query.wx_nickname) {
                let wx_id = await ctx.model.WechatGroupMonitorUser.findAll({
                    where: {
                        wx_nickname: {
                            [Op.like]: `%${query.wx_nickname}%`,
                        }
                    },
                    raw: true,
                }).map(row => { return row.wx_id })

                if (query.wx_id) {
                    where.wx_id = wx_id.push(query.wx_id)
                } else {
                    where.wx_id = wx_id
                }
            }

            if (query.method) {
                let ids = await ctx.model.WechatGroupMonitorUser.findAll({
                    where: {
                        wx_nickname: {
                            [Op.like]: `%${query.method}%`,
                        }
                    },
                    raw: true,
                }).map(row => { return row.id })

                where.method = ids
            }

            ctx.json_body = await ctx.model.WechatGroupMonitorLog.findAndCountAll({
                include: [{
                    model: ctx.model.WechatGroupMonitorUser,
                    required: false,
                    as: 'wx_group_method',
                }, {
                    model: ctx.model.WechatGroupMonitorUser,
                    required: false,
                    as: 'wx_group_user',
                }, {
                    model: ctx.model.WechatGroupMonitor,
                    required: false,
                    as: 'wechat_group_monitor',
                }],
                where,
                offset: page,
                limit: page_size,
                order: [
                    ['id', 'DESC'],
                ],
            })
        }

        async majia_list() {
            const { ctx, config, app } = this

            let model = Object.assign({}, ctx.request.query)
            model.page = model.page || 1

            const page_size = parseInt(model.page_size, 10) || config.page_size
            const page = (model.page - 1) * page_size

            ctx.json_body = await ctx.model.WechatGroupMajia.findAndCountAll({
                order: [
                    ['id', 'DESC'],
                ],
                offset: page,
                limit: page_size,
                raw: true
            })
        }

        async majia_create() {
            const { ctx, app } = this

            const body = Object.assign({}, ctx.request.body)

            body.created_at = ctx.helper.now()

            await ctx.model.WechatGroupMajia.create(body)

            ctx.json_body = {
                success: true,
            }
        }

        async majia_update() {
            const { ctx } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.WechatGroupMajia.update(body, {
                where: { id: body.id }
            })

            ctx.json_body = {
                success: true,
            }
        }

        async majia_delete() {
            const { ctx, app } = this

            const body = Object.assign({}, ctx.request.body)

            await ctx.model.WechatGroupMajia.destroy({
                where: { id: body.id }
            })

            ctx.json_body = {
                success: true,
            }
        }
    }

    return WechatGroupController
}
