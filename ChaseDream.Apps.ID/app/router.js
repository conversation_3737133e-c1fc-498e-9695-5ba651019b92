/**
 * @param {Egg.Application} app - egg application
 */
module.exports = app => {
    const { router, controller, config } = app
    const jsonp = app.jsonp()

    router.get(`/MP_verify_2eu1HjAX5ID3NHuD.txt`, controller.auth.ttt)
    router.get(`/MP_verify_2eu1HjAX5ID3NH`, controller.auth.ttt)

    router.get(`/console`, controller.console.index)
    router.get(/^\/console\/*/, controller.console.index)

    router.get('/', controller.auth.index)
    router.get('/login', controller.auth.index)
    router.get('/login-pc', controller.auth.index)
    router.get('/login-www-pc', controller.auth.index)
    router.get('/register', controller.auth.index)
    router.get('/captcha', controller.auth.index)
    router.get(/^\/register\/*/, controller.auth.index)
    router.get(/^\/forum-tag\/*/, controller.auth.index)
    //微信
    router.get(`${config.api_prefix}/wechat/js_config`, jsonp, controller.wechat.js_config)
    router.get(`${config.api_prefix}/wechat/qrcode`, controller.wechat.qrcode)
    router.get(`${config.api_prefix}/wechat/verify_ticket`, controller.wechat.verify_ticket)
    router.get(`${config.api_prefix}/wechat/check_login`, controller.wechat.check_login)
    router.get(`${config.api_prefix}/wechat/url`, jsonp, controller.wechat.url)
    router.get(`${config.api_prefix}/wechat/id_web_code`, controller.wechat.id_web_code)
    router.get(`${config.api_prefix}/wechat/app_login`, controller.wechat.app_login)
    router.get(`${config.api_prefix}/wechat/unbinding`, controller.wechat.unbinding)
    router.post(`${config.api_prefix}/wechat/binding`, controller.wechat.binding)
    router.post(`${config.api_prefix}/wechat/change_binding`, controller.wechat.change_binding)
    router.post(`${config.api_prefix}/wechat/login_with_other`, controller.wechat.login_with_other)
    router.get(`${config.api_prefix}/wechat/userinfo`, controller.wechat.userinfo)
    router.get(`${config.api_prefix}/wechat/update_userinfo`, controller.wechat.update_userinfo)
    router.get(`${config.api_prefix}/wechat`, controller.wechat.wechat)
    router.post(`${config.api_prefix}/wechat`, controller.wechat.wechat)
    //苹果登录
    router.post(`${config.api_prefix}/apple/login`, controller.apple.login)
    //极验证
    router.get(`${config.api_prefix}/auth/geetest/register`, controller.auth.geetest_register)
    //一键登录
    router.post(`${config.api_prefix}/auth/identity`, controller.auth.identity)
    //后台登录操作
    router.get(`${config.api_prefix}/auth/check_login`, controller.auth.check_login)
    router.get(`${config.api_prefix}/auth/logout`, controller.auth.admin_logout)
    router.post(`${config.api_prefix}/auth/login`, controller.auth.admin_login)

    router.get(`${config.api_prefix}/auth/logout_forum`, controller.auth.logout_forum)
    router.get(`${config.api_prefix}/auth/qrcode`, controller.auth.qrcode)
    router.get(`${config.api_prefix}/auth/userinfo`, jsonp, controller.auth.userinfo)
    router.get(`${config.api_prefix}/auth/check_mobile_binding`, jsonp, controller.auth.check_mobile_binding)
    router.get(`${config.api_prefix}/auth/check_wechat_binding`, jsonp, controller.auth.check_wechat_binding)
    router.get(`${config.api_prefix}/auth/user_mobile/:uid`, jsonp, controller.auth.user_mobile)
    router.post(`${config.api_prefix}/auth/encStr`, controller.auth.encStr)
    router.post(`${config.api_prefix}/auth/ttt`, controller.auth.ttt)

    router.post(`${config.api_prefix}/auth/sms/send`, controller.auth.sms_send_register)
    router.post(`${config.api_prefix}/auth/sms/send/register`, controller.auth.sms_send_register)
    router.post(`${config.api_prefix}/auth/sms/send/change_mobile`, controller.auth.sms_send_change_mobile)
    router.post(`${config.api_prefix}/auth/sms/send/binding_mobile`, controller.auth.sms_send_binding_mobile)
    router.post(`${config.api_prefix}/auth/sms/send/recovery_password`, controller.auth.sms_send_recovery_password)
    router.post(`${config.api_prefix}/auth/sms/send/login`, controller.auth.sms_send_login)

    router.post(`${config.api_prefix}/auth/verify/sms`, controller.auth.verify_sms)
    router.post(`${config.api_prefix}/auth/verify/login`, jsonp, controller.auth.verify_login)
    router.post(`${config.api_prefix}/auth/verify/register`, controller.auth.verify_register)
    router.post(`${config.api_prefix}/auth/verify/recovery_password`, controller.auth.recovery_password)
    router.post(`${config.api_prefix}/auth/verify/username_exist`, controller.auth.username_exist)
    router.post(`${config.api_prefix}/auth/verify/email_exist`, controller.auth.email_exist)
    router.get(`${config.api_prefix}/auth/verify/qrcode`, controller.auth.verify_qrcode_handle)
    router.get(`${config.api_prefix}/auth/verify/qrcode/check`, controller.auth.verify_qrcode_check)

    router.get(`${config.api_prefix}/auth/verify/change_mobile1`, controller.auth.get_mobile_info)
    router.post(`${config.api_prefix}/auth/verify/change_mobile1`, controller.auth.change_mobile1)
    router.post(`${config.api_prefix}/auth/verify/change_mobile2`, controller.auth.change_mobile2)

    //屏蔽用户
    router.get(`${config.api_prefix}/auth/block_user`, controller.auth.block_user_list)
    router.post(`${config.api_prefix}/auth/block_user`, controller.auth.block_user_create)
    router.delete(`${config.api_prefix}/auth/block_user`, controller.auth.block_user_delete)

    router.post(`${config.api_prefix}/auth/recovery_password/code`, jsonp, controller.auth.recovery_password_get_code)
    router.post(`${config.api_prefix}/auth/recovery_password/verify_code`, jsonp, controller.auth.recovery_password_verify_code)
    router.post(`${config.api_prefix}/auth/recovery_password/by_email`, jsonp, controller.auth.recovery_password_by_email)

    router.post(`${config.api_prefix}/auth/binding_mobile/change`, jsonp, controller.auth.binding_mobile_change)
    router.post(`${config.api_prefix}/auth/account_close`, jsonp, controller.auth.account_close)

    router.post(`${config.api_prefix}/auth/tag/forum/search`, jsonp, controller.misc.tag_search)

    //短信相关
    router.get(`${config.api_prefix}/admin/sms/user`, controller.sms.user)
    router.get(`${config.api_prefix}/admin/sms/log`, controller.sms.log)
    router.get(`${config.api_prefix}/admin/sms/blacklist`, controller.sms.blacklist)
    router.post(`${config.api_prefix}/admin/sms/blacklist`, controller.sms.blacklist_create)
    router.delete(`${config.api_prefix}/admin/sms/blacklist`, controller.sms.blacklist_delete)
    router.post(`${config.api_prefix}/admin/sms/binding_mobile/manual`, controller.sms.binding_mobile_manual)
    router.get(`${config.api_prefix}/admin/sms/binding_mobile/manual`, controller.sms.binding_mobile_manual_list)
    router.get(`${config.api_prefix}/admin/isp`, controller.isp.list)
    router.put(`${config.api_prefix}/admin/isp`, controller.isp.update)
    //LinkedIn
    router.post(`${config.api_prefix}/admin/linkedin/crawl`, controller.linkedin.crawl)
    router.get(`${config.api_prefix}/admin/linkedin/info`, controller.linkedin.info_list)
    router.post(`${config.api_prefix}/admin/linkedin/info`, controller.linkedin.info_get_one)
    router.delete(`${config.api_prefix}/admin/linkedin/info`, controller.linkedin.info_delete)
    router.get(`${config.api_prefix}/admin/linkedin/wait_list/info`, controller.linkedin.wait_list_info)
    router.get(`${config.api_prefix}/admin/linkedin/wait_list`, controller.linkedin.wait_list)
    router.post(`${config.api_prefix}/admin/linkedin/wait_list`, controller.linkedin.wait_list_create)
    router.delete(`${config.api_prefix}/admin/linkedin/wait_list`, controller.linkedin.wait_list_delete)
    router.get(`${config.api_prefix}/admin/linkedin/account`, controller.linkedin.account_list)
    router.post(`${config.api_prefix}/admin/linkedin/account`, controller.linkedin.account_create)
    router.put(`${config.api_prefix}/admin/linkedin/account`, controller.linkedin.account_update)
    router.delete(`${config.api_prefix}/admin/linkedin/account`, controller.linkedin.account_delete)
    router.post(`${config.api_prefix}/admin/linkedin/account/verify`, controller.linkedin.account_verify)
    router.get(`${config.api_prefix}/admin/linkedin/crawl_log`, controller.linkedin.crawl_log_list)
    router.get(`${config.api_prefix}/admin/linkedin/organization`, controller.linkedin.organization_list)
    router.get(`${config.api_prefix}/admin/linkedin/organization/fix_org_edu`, controller.linkedin.fix_org_edu)
    router.get(`${config.api_prefix}/admin/linkedin/organization/:id`, controller.linkedin.organization_get_one)
    router.get(`${config.api_prefix}/admin/linkedin/school`, controller.linkedin.school_list)
    router.get(`${config.api_prefix}/admin/linkedin/school/:id`, controller.linkedin.school_get_one)
    router.get(`${config.api_prefix}/admin/linkedin/avatar/:id`, controller.linkedin.get_avatar)
    router.post(`${config.api_prefix}/admin/linkedin/avatar/:pin`, controller.linkedin.set_pin_for_avatar)
    //账号注销
    router.get(`${config.api_prefix}/admin/user/account_close`, controller.user.search)
    router.post(`${config.api_prefix}/admin/user/account_close`, controller.user.account_close)
    //批量沉贴
    router.get(`${config.api_prefix}/admin/user/thread_down`, controller.user.thread_down_search)
    router.post(`${config.api_prefix}/admin/user/thread_down`, controller.user.thread_down)
    //用户信息
    router.get(`${config.api_prefix}/admin/info`, controller.misc.info)
    //用户重命名
    router.get(`${config.api_prefix}/admin/rename`, controller.rename.search)
    router.post(`${config.api_prefix}/admin/rename`, controller.rename.rename)
    //替我发帖
    router.get(`${config.api_prefix}/admin/thread/pid`, controller.thread.pid_list)
    router.post(`${config.api_prefix}/admin/thread/pid`, controller.thread.pid_create)
    router.get(`${config.api_prefix}/admin/thread`, controller.thread.thread_list)
    router.post(`${config.api_prefix}/admin/thread`, controller.thread.thread_create)
    router.put(`${config.api_prefix}/admin/thread`, controller.thread.thread_update)
    router.delete(`${config.api_prefix}/admin/thread`, controller.thread.thread_delete)
    router.post(`${config.api_prefix}/admin/thread/highlight_digest`, controller.thread.highlight_digest)
    router.post(`${config.api_prefix}/admin/thread/stick`, controller.thread.stick)
    router.get(`${config.api_prefix}/admin/thread/user`, controller.thread.user_list)
    router.post(`${config.api_prefix}/admin/thread/user`, controller.thread.user_create)
    router.put(`${config.api_prefix}/admin/thread/user`, controller.thread.user_update)
    router.delete(`${config.api_prefix}/admin/thread/user`, controller.thread.user_delete)
    router.get(`${config.api_prefix}/admin/thread/forum_nav`, controller.thread.forum_nav)
    router.get(`${config.api_prefix}/admin/thread/forum_nav_sub`, controller.thread.forum_nav_sub)
    router.get(`${config.api_prefix}/admin/thread/thread_image`, controller.thread.thread_image)
    router.post(`${config.api_prefix}/admin/thread/upload`, controller.thread.image_upload)
    router.post(`${config.api_prefix}/admin/thread/image_digest`, controller.thread.image_digest)
    router.get(`${config.api_prefix}/admin/thread/material/digest`, controller.thread.thread_material_digest)
    router.put(`${config.api_prefix}/admin/thread/upload`, controller.thread.put_upload)
    router.delete(`${config.api_prefix}/admin/thread/upload`, controller.thread.image_delete)

    //自动顶贴
    router.get(`${config.api_prefix}/admin/top_post/setting`, controller.topPost.setting)
    router.put(`${config.api_prefix}/admin/top_post/setting`, controller.topPost.setting_update)
    router.get(`${config.api_prefix}/admin/top_post/thread`, controller.topPost.thread_list)
    router.post(`${config.api_prefix}/admin/top_post/thread`, controller.topPost.thread_create)
    router.put(`${config.api_prefix}/admin/top_post/thread`, controller.topPost.thread_update)
    router.delete(`${config.api_prefix}/admin/top_post/thread`, controller.topPost.thread_delete)
    router.get(`${config.api_prefix}/admin/top_post/message`, controller.topPost.message_list)
    router.post(`${config.api_prefix}/admin/top_post/message`, controller.topPost.message_create)
    router.put(`${config.api_prefix}/admin/top_post/message`, controller.topPost.message_update)
    router.delete(`${config.api_prefix}/admin/top_post/message`, controller.topPost.message_delete)
    router.get(`${config.api_prefix}/admin/top_post/user`, controller.topPost.user_list)
    router.post(`${config.api_prefix}/admin/top_post/user`, controller.topPost.user_create)
    router.put(`${config.api_prefix}/admin/top_post/user`, controller.topPost.user_update)
    router.delete(`${config.api_prefix}/admin/top_post/user`, controller.topPost.user_delete)
    router.post(`${config.api_prefix}/admin/top_post/change_forum_user_info`, controller.topPost.change_forum_user_info)
    router.post(`${config.api_prefix}/admin/top_post/password`, controller.topPost.get_password)
    //活动核心发布
    router.get(`${config.api_prefix}/admin/event`, controller.event.event_list)
    router.post(`${config.api_prefix}/admin/event`, controller.event.event_create)
    router.put(`${config.api_prefix}/admin/event`, controller.event.event_update)
    router.delete(`${config.api_prefix}/admin/event`, controller.event.event_delete)
    router.get(`${config.api_prefix}/admin/event/core/:event_id`, controller.event.event_get_one)
    router.post(`${config.api_prefix}/admin/event/highlight_digest`, controller.event.highlight_digest)
    router.post(`${config.api_prefix}/admin/event/stick`, controller.event.stick)
    //活动类型
    router.get(`${config.api_prefix}/admin/event/type`, controller.event.event_type_list)
    //活动位置添加删除
    router.post(`${config.api_prefix}/admin/event/location`, controller.event.event_location_create)
    router.put(`${config.api_prefix}/admin/event/location`, controller.event.event_location_update)
    router.delete(`${config.api_prefix}/admin/event/location`, controller.event.event_location_delete)
    //推www和forum
    router.post(`${config.api_prefix}/admin/event/publish_to_www`, controller.event.publish_to_www)
    router.post(`${config.api_prefix}/admin/event/publish_to_forum`, controller.event.publish_to_forum)
    //活动列表
    router.get(`${config.api_prefix}/admin/event/release`, controller.event.event_release_list)
    router.post(`${config.api_prefix}/admin/event/release`, controller.event.event_release_create)
    router.put(`${config.api_prefix}/admin/event/release`, controller.event.event_release_update)
    router.delete(`${config.api_prefix}/admin/event/release`, controller.event.event_release_delete)
    router.get(`${config.api_prefix}/admin/event/release/:event_id/:event_type`, controller.event.release_1_3)
    router.get(`${config.api_prefix}/admin/event/release/push1_setting`, controller.event.push1_setting_get)
    router.post(`${config.api_prefix}/admin/event/release/push1_setting`, controller.event.push1_setting_update)
    router.post(`${config.api_prefix}/admin/event/release/push1_www_position`, controller.event.push1_www_position_update)
    router.post(`${config.api_prefix}/admin/event/release/www_hot`, controller.event.www_hot_update)
    router.get(`${config.api_prefix}/admin/event/release/push2_coming_soon`, controller.event.push2_coming_soon)
    //活动日历
    router.get(`${config.api_prefix}/admin/event/calendar`, controller.event.event_calendar_list)
    router.post(`${config.api_prefix}/admin/event/calendar`, controller.event.event_calendar_create)
    router.put(`${config.api_prefix}/admin/event/calendar`, controller.event.event_calendar_update)
    router.delete(`${config.api_prefix}/admin/event/calendar`, controller.event.event_calendar_delete)
    router.get(`${config.api_prefix}/admin/event/calendar/:event_id`, controller.event.calendar_get_one)
    //发布活动用户
    router.get(`${config.api_prefix}/admin/event/user`, controller.event.user_list)
    router.post(`${config.api_prefix}/admin/event/user`, controller.event.user_create)
    router.put(`${config.api_prefix}/admin/event/user`, controller.event.user_update)
    router.delete(`${config.api_prefix}/admin/event/user`, controller.event.user_delete)
    //地理位置
    router.get(`${config.api_prefix}/admin/event/geo`, controller.event.geo)
    router.get(`${config.api_prefix}/admin/event/geo/search`, controller.event.geo_search)
    router.get(`${config.api_prefix}/admin/event/geo/:id`, controller.event.get_geo)
    //专业
    router.get(`${config.api_prefix}/admin/event/major`, controller.event.major)
    //改变排序
    router.post(`${config.api_prefix}/admin/event/change_order`, controller.event.change_order)
    //上传下载图片
    router.get(`${config.api_prefix}/admin/event/material/digest`, controller.event.event_material_digest)
    router.post(`${config.api_prefix}/admin/event/material/change_order`, controller.event.material_change_order)
    router.post(`${config.api_prefix}/admin/event/image_digest`, controller.event.image_digest)
    router.post(`${config.api_prefix}/admin/event/upload`, controller.event.image_upload)
    router.post(`${config.api_prefix}/admin/event/upload/push2`, controller.event.image_upload_push2)
    router.delete(`${config.api_prefix}/admin/event/upload`, controller.event.image_delete)
    router.get(`${config.api_prefix}/admin/event/event_image`, controller.event.event_image)
    //活动数据统计
    router.get(`${config.api_prefix}/admin/event/statistics`, controller.event.event_statistics)
    router.post(`${config.api_prefix}/admin/event/statistics2`, controller.event.event_statistics2)
    //大学
    router.get(`${config.api_prefix}/admin/university`, controller.misc.university_list)
    router.get(`${config.api_prefix}/admin/university/:university_id`, controller.misc.university_get_one)
    router.post(`${config.api_prefix}/admin/university`, controller.misc.university_create)
    router.put(`${config.api_prefix}/admin/university`, controller.misc.university_update)
    router.delete(`${config.api_prefix}/admin/university`, controller.misc.university_delete)
    //校区
    router.post(`${config.api_prefix}/admin/school_area/photo_upload`, controller.misc.school_area_photo_upload)
    //专业分类
    router.get(`${config.api_prefix}/admin/major_category`, controller.misc.major_category_list)
    router.post(`${config.api_prefix}/admin/major_category`, controller.misc.major_category_create)
    router.put(`${config.api_prefix}/admin/major_category`, controller.misc.major_category_update)
    router.delete(`${config.api_prefix}/admin/major_category`, controller.misc.major_category_delete)
    //学院
    router.get(`${config.api_prefix}/admin/misc/school/:school_id`, controller.misc.school_get_one)
    router.post(`${config.api_prefix}/admin/misc/school`, controller.misc.school_create)
    router.put(`${config.api_prefix}/admin/misc/school`, controller.misc.school_update)

    router.get(`${config.api_prefix}/admin/event/school`, controller.event.event_school_list)
    router.get(`${config.api_prefix}/admin/event/school/:school_id`, controller.event.event_school_get_one)
    router.post(`${config.api_prefix}/admin/event/school`, controller.event.event_school_create)
    router.put(`${config.api_prefix}/admin/event/school`, controller.event.event_school_update)
    router.delete(`${config.api_prefix}/admin/event/school`, controller.event.event_school_delete)
    router.get(`${config.api_prefix}/admin/event/school/logo/:school_id`, controller.event.event_get_school_logo)
    router.post(`${config.api_prefix}/admin/event/school/logo`, controller.event.event_school_logo)
    router.get(`${config.api_prefix}/admin/event/school/major/:school_id`, controller.event.event_school_major_find_all)
    router.post(`${config.api_prefix}/admin/event/school/major`, controller.event.event_school_major_create)
    router.put(`${config.api_prefix}/admin/event/school/major`, controller.event.event_school_major_update)
    //项目
    router.get(`${config.api_prefix}/admin/program`, controller.misc.program_list)
    router.post(`${config.api_prefix}/admin/program`, controller.misc.program_create)
    router.put(`${config.api_prefix}/admin/program`, controller.misc.program_update)
    router.delete(`${config.api_prefix}/admin/program`, controller.misc.program_delete)
    //项目方向
    router.get(`${config.api_prefix}/admin/program_direction`, controller.misc.program_direction_list)
    router.get(`${config.api_prefix}/admin/program_degree_category`, controller.misc.program_degree_category_list)
    //帖子打标签--后台
    router.get(`${config.api_prefix}/admin/tag`, jsonp, controller.tag.tag_list)
    router.get(`${config.api_prefix}/admin/tag/check/:tid`, jsonp, controller.tag.tag_check)
    router.get(`${config.api_prefix}/admin/tag/thread_tag`, jsonp, controller.tag.thread_tag)
    router.get(`${config.api_prefix}/admin/tag/tag_thread`, jsonp, controller.tag.tag_thread)
    router.post(`${config.api_prefix}/admin/tag`, jsonp, controller.tag.tag_create)
    router.post(`${config.api_prefix}/admin/tag/batch`, jsonp, controller.tag.tag_create_batch)
    router.put(`${config.api_prefix}/admin/tag`, jsonp, controller.tag.tag_update)
    router.delete(`${config.api_prefix}/admin/tag`, jsonp, controller.tag.tag_delete)
    router.get(`${config.api_prefix}/admin/tag/:id`, jsonp, controller.tag.tag_get_one)
    router.post(`${config.api_prefix}/admin/tag/merge`, jsonp, controller.tag.tag_merge)
    //帖子打标签--论坛
    router.get(`${config.api_prefix}/admin/tag/forum/:tid`, jsonp, controller.misc.tag_get_one)
    router.get(`${config.api_prefix}/admin/tag/forum/property/:property`, jsonp, controller.misc.tag_get_property_list)
    router.post(`${config.api_prefix}/admin/tag/forum`, jsonp, controller.misc.tag_create)
    router.delete(`${config.api_prefix}/admin/tag/forum`, jsonp, controller.misc.tag_delete)
    router.post(`${config.api_prefix}/admin/tag/forum/search`, jsonp, controller.misc.tag_search)
    router.post(`${config.api_prefix}/admin/tag/get_added_tids`, jsonp, controller.misc.get_added_tids)
    //微信群监控
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_logout`, controller.wechatGroup.wechat_logout)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor`, controller.wechatGroup.wechat_group_monitor_list)
    router.post(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor/switch`, controller.wechatGroup.wechat_group_monitor_switch)
    router.post(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor/send_post`, controller.wechatGroup.wechat_group_monitor_send_post)
    router.put(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor`, controller.wechatGroup.wechat_group_monitor_update)
    router.delete(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor`, controller.wechatGroup.wechat_group_monitor_delete)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_user`, controller.wechatGroup.wechat_group_monitor_user_list)
    router.put(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_user`, controller.wechatGroup.wechat_group_monitor_user_update)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_account`, controller.wechatGroup.wechat_group_monitor_account_list)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_account/other_group/:wx_id`, controller.wechatGroup.wechat_group_monitor_account_other_group)
    router.put(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_account`, controller.wechatGroup.wechat_group_monitor_account_update)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_message`, controller.wechatGroup.wechat_group_monitor_message_list)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_message/download/:id`, controller.wechatGroup.wechat_group_monitor_message_download)
    router.get(`${config.api_prefix}/admin/wechat_group/wechat_group_monitor_log`, controller.wechatGroup.wechat_group_monitor_log_list)
    router.get(`${config.api_prefix}/admin/wechat_group/majia`, controller.wechatGroup.majia_list)
    router.post(`${config.api_prefix}/admin/wechat_group/majia`, controller.wechatGroup.majia_create)
    router.put(`${config.api_prefix}/admin/wechat_group/majia`, controller.wechatGroup.majia_update)
    router.delete(`${config.api_prefix}/admin/wechat_group/majia`, controller.wechatGroup.majia_delete)
    //招募帖子
    router.get(`${config.api_prefix}/admin/recruit/thread`, controller.recruit.thread_list)
    router.get(`${config.api_prefix}/admin/recruit/thread/:id`, controller.recruit.thread_get_one)
    router.post(`${config.api_prefix}/admin/recruit/thread`, controller.recruit.thread_create)
    router.put(`${config.api_prefix}/admin/recruit/thread`, controller.recruit.thread_update)
    router.delete(`${config.api_prefix}/admin/recruit/thread`, controller.recruit.thread_delete)
    router.post(`${config.api_prefix}/admin/recruit/password`, controller.recruit.get_password)
    //招募表单
    router.get(`${config.api_prefix}/admin/recruit/form`, controller.recruit.form_list)
    router.post(`${config.api_prefix}/admin/recruit/form`, controller.recruit.form_create)
    router.put(`${config.api_prefix}/admin/recruit/form`, controller.recruit.form_update)
    router.delete(`${config.api_prefix}/admin/recruit/form`, controller.recruit.form_delete)
    router.get(`${config.api_prefix}/admin/recruit/form/:id`, controller.recruit.form_get_one)
    //招募发布
    router.get(`${config.api_prefix}/admin/recruit/detail`, controller.recruit.detail_list)
    router.put(`${config.api_prefix}/admin/recruit/detail`, controller.recruit.detail_update)
    router.post(`${config.api_prefix}/admin/recruit/detail/opt`, controller.recruit.detail_opt)
    router.post(`${config.api_prefix}/admin/recruit/detail/report`, controller.recruit.detail_report)
    //招募主理人
    router.get(`${config.api_prefix}/admin/recruit/owner`, controller.recruit.owner_list)
    router.post(`${config.api_prefix}/admin/recruit/owner`, controller.recruit.owner_create)
    router.put(`${config.api_prefix}/admin/recruit/owner`, controller.recruit.owner_update)
    router.delete(`${config.api_prefix}/admin/recruit/owner`, controller.recruit.owner_delete)
    router.post(`${config.api_prefix}/admin/recruit/owner/upload`, controller.recruit.owner_upload)
    //招募发布用户
    router.get(`${config.api_prefix}/admin/recruit/user`, controller.recruit.user_list)
    router.post(`${config.api_prefix}/admin/recruit/user`, controller.recruit.user_create)
    router.put(`${config.api_prefix}/admin/recruit/user`, controller.recruit.user_update)
    router.delete(`${config.api_prefix}/admin/recruit/user`, controller.recruit.user_delete)
    //用户特别行为
    router.get(`${config.api_prefix}/admin/behavior`, controller.behavior.behavior_list)
    router.get(`${config.api_prefix}/admin/behavior/search/:username`, controller.behavior.behavior_search)
    router.get(`${config.api_prefix}/admin/behavior/:id`, controller.behavior.behavior_get_one)
    router.post(`${config.api_prefix}/admin/behavior`, controller.behavior.behavior_create)
    router.post(`${config.api_prefix}/admin/behavior/upload`, controller.behavior.behavior_upload)
    router.put(`${config.api_prefix}/admin/behavior`, controller.behavior.behavior_update)
    router.delete(`${config.api_prefix}/admin/behavior`, controller.behavior.behavior_delete)
    //用户登录日志
    router.get(`${config.api_prefix}/admin/recruit/loginlog`, controller.recruit.loginlog_list)

    //钓鱼
    router.get(`${config.api_prefix}/admin/misc/fish`, jsonp, controller.misc.fish_list)
    //监控注册IP白名单
    router.get(`${config.api_prefix}/admin/misc/monitor_sameip_whitelist`, jsonp, controller.misc.monitor_sameip_whitelist_list)
    router.post(`${config.api_prefix}/admin/misc/monitor_sameip_whitelist`, jsonp, controller.misc.monitor_sameip_whitelist_create)
    router.put(`${config.api_prefix}/admin/misc/monitor_sameip_whitelist`, jsonp, controller.misc.monitor_sameip_whitelist_update)
    router.delete(`${config.api_prefix}/admin/misc/monitor_sameip_whitelist`, jsonp, controller.misc.monitor_sameip_whitelist_delete)
    //会员活动日志
    router.get(`${config.api_prefix}/admin/misc/member_activity_log`, controller.misc.member_activity_log_list)
    //合同盖章
    router.get(`${config.api_prefix}/admin/contract_stamp`, controller.misc.contract_stamp_list)
    router.get(`${config.api_prefix}/admin/contract_stamp/stamps`, controller.misc.contract_stamp_stamps)
    router.get(`${config.api_prefix}/admin/contract_stamp/stamps/roles`, controller.misc.contract_stamp_stamps_roles)
    router.get(`${config.api_prefix}/admin/contract_stamp/stamps/perms`, controller.misc.contract_stamp_stamps_perms)
    router.get(`${config.api_prefix}/admin/contract_stamp/:id`, controller.misc.contract_stamp_get_one)
    router.get(`${config.api_prefix}/admin/contract_stamp/preview/:id`, controller.misc.contract_stamp_preview)
    router.post(`${config.api_prefix}/admin/contract_stamp`, controller.misc.contract_stamp_create)
    router.post(`${config.api_prefix}/admin/contract_stamp/apply`, controller.misc.contract_stamp_apply)
    router.post(`${config.api_prefix}/admin/contract_stamp/approve`, controller.misc.contract_stamp_approve)
    router.post(`${config.api_prefix}/admin/contract_stamp/reject`, controller.misc.contract_stamp_reject)
    router.post(`${config.api_prefix}/admin/contract_stamp/upload`, controller.misc.contract_upload)
    router.post(`${config.api_prefix}/admin/contract_stamp/download`, controller.misc.contract_download)
    router.post(`${config.api_prefix}/admin/contract_stamp/stamps/roles`, controller.misc.contract_stamp_stamps_roles_create)
    router.put(`${config.api_prefix}/admin/contract_stamp`, controller.misc.contract_stamp_update)
    router.delete(`${config.api_prefix}/admin/contract_stamp`, controller.misc.contract_stamp_delete)
    //活动发布展示列表
    router.get(`/event/release`, jsonp, controller.event.show_event_release)
    router.get(`/event/release/push1_www_position`, jsonp, controller.event.push1_www_position_get)
    router.get(`/event/release/www_hot`, jsonp, controller.event.www_hot_get)
    //活动日历展示列表
    router.get(`/event/calendar`, jsonp, controller.event.show_event_calendar)
    router.get(`/event/calendar/expire`, jsonp, controller.event.show_event_calendar_expire)
    router.get(`/event/calendar/all`, jsonp, controller.event.show_event_calendar_all)
    //搜索选项
    router.get(`/event/search_items`, jsonp, controller.event.search_items)
    //招募详情
    router.post(`/recruit`, jsonp, controller.recruit.recruit_detail_create)
    //地理位置查询
    router.get(`/misc/geo`, jsonp, controller.misc.geo)
    router.get(`/misc/geo/:id`, jsonp, controller.misc.geo_get_one)
    router.get(`/misc/ip_location`, jsonp, controller.misc.ip_location)
    router.get(`/misc/timezone`, jsonp, controller.misc.timezone)
    router.post(`/misc/member_activity_log`, jsonp, controller.misc.member_activity_log)
    router.get(`/misc/forum/redirect`, jsonp, controller.misc.fish_create)
    router.get(`/misc/contract/:url`, controller.misc.for_dev_show_img)
    router.get(`/misc/thread/:url`, controller.misc.for_dev_thread_show_img)
    router.get(`/misc/taglist/:tagid`, controller.misc.taglist)
    //用户特别行为记录
    router.get(`/behavior/record_type`, controller.behavior.record_type)
    //oia.chasedream.com
    router.get(`/apple-app-site-association`, controller.apple.apple_app_site_association)
    //CDG
    router.get(`/cdg`, controller.cdg.index)
    //for passport
    router.get(`/auth/validateUser`, controller.misc.validateUser)

    //系统用户管理
    router.get(`${config.api_prefix}/admin/users`, controller.user.list)
    router.post(`${config.api_prefix}/admin/user`, controller.user.create)
    router.put(`${config.api_prefix}/admin/user`, controller.user.update)
    router.delete(`${config.api_prefix}/admin/user`, controller.user.delete)

    router.get(`${config.api_prefix}/admin/user/departments`, controller.user.departments)

    router.get(`${config.api_prefix}/admin/permission/my_menu`, controller.permission.my_menu)
    router.get(`${config.api_prefix}/admin/permission/my_permission`, controller.permission.my_permission)

    router.get(`${config.api_prefix}/admin/permission/menu`, controller.permission.get_menu)
    router.post(`${config.api_prefix}/admin/permission/menu`, controller.permission.create_menu)
    router.put(`${config.api_prefix}/admin/permission/menu`, controller.permission.update_menu)
    router.delete(`${config.api_prefix}/admin/permission/menu`, controller.permission.delete_menu)

    router.get(`${config.api_prefix}/admin/permission/role`, controller.permission.get_role)
    router.post(`${config.api_prefix}/admin/permission/role`, controller.permission.add_role)
    router.put(`${config.api_prefix}/admin/permission/role`, controller.permission.update_role)
    router.delete(`${config.api_prefix}/admin/permission/role`, controller.permission.delete_role)

    router.get(`${config.api_prefix}/admin/permission/role_menu`, controller.permission.get_role_menu)
    router.get(`${config.api_prefix}/admin/permission/role_menu/:role_id`, controller.permission.get_role_menu_role_id)
    router.post(`${config.api_prefix}/admin/permission/role_menu`, controller.permission.update_role_menu)
    router.delete(`${config.api_prefix}/admin/permission/role_menu`, controller.permission.delete_role_menu)

    router.get(`${config.api_prefix}/admin/permission/permission_for_role/:role`, controller.permission.get_permission_for_role)
    router.post(`${config.api_prefix}/admin/permission/permission_for_role`, controller.permission.update_permission_for_role)

    router.get(`${config.api_prefix}/admin/permission/all_permissions`, controller.permission.all_permissions)
}
