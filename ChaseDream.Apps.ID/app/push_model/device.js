module.exports = app => {
    const {STRING, BIGINT, INTEGER, DATE} = app.Sequelize

    const Device = app.model.define("device", {
        uid: {
            type: INTEGER.UNSIGNED,
            primaryKey: true,
            defaultValue: 0
        },
        token: {
            type: STRING(100),
            defaultValue: '',
        },
        platform: {
            type: STRING(10),
            defaultValue: '',
        },
        platform_version: {
            type: STRING(10),
            defaultValue: '',
        },
        app_version: {
            type: STRING(10),
            defaultValue: '',
        },
        timezone: {
            type: STRING(10),
            defaultValue: '',
        },
        save_traffic: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        silent_setting: {
            type: INTEGER.UNSIGNED,
            defaultValue: 0
        },
        alert_sound: {
            type: STRING(20),
            defaultValue: '',
        },
        created_at: DATE,
        updated_at: DATE
    }, {
        tableName: 'tbl_device',
        indexes:[
            {
                unique: true,
                fields: ['uid']
            },
        ]
    })

    return Device
}