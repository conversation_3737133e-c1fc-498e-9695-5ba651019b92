const {VError} = require("verror")

module.exports = {
    set json_body(data) {
        this.assert(data && typeof data === "object")
        this.body = {
            msg: "success",
            data
        }
    },
    error(expression, message, code = 400, status = 400, originalError) {
        if (!(expression)) {
            return
        }

        this.assert(message && typeof message === "string")
        this.assert(code && typeof code === "number")

        this.type = "json"
        const err = Object.assign(new VError({
            name: "custom_server_error",
            cause: originalError
        }, message), {
            code,
            status
        })

        this.throw(err)
    }
}
