const fs = require('fs')
const fse = require('fs-extra')
const util = require('util')
const os = require('os')
const sh = require('shelljs')
const path = require('path')
const moment = require('moment-timezone')
const crypto = require('crypto')
const authcode = require('authcode')
const md5 = require('md5')
const sharp = require('sharp')
const randomstring = require("randomstring")
const jimp = require('jimp')
const await = require('await-stream-ready/lib/await')
const exec = util.promisify(require('child_process').exec)
const CryptoJS = require("crypto-js");

module.exports = {
    resize_spec(source, target, spec, ext) {
        return new Promise(function (resolve, reject) {
            for (let i = 0; i < spec.length; i++) {
                let wh = spec[i]
                const filename = spec.length === 1 ? target : `${target}/${wh.w}`

                sharp(source)
                    .resize(wh.w, wh.h)
                    .toFile(`${filename}.${ext}`)
                    .then(data => {
                        resolve(data)
                    })
                    .catch(err => {
                        reject(err)
                    })
            }
        })
    },
    async pdf_to_images(file, save_path, save_filename, format) {
        const { ctx } = this
        let cmd = []

        cmd.push(`gm convert -density 300 +adjoin -page A4 -quality 100 ${file} ${save_path}/${save_filename}.%d.${format}`)

        try {
            const { stdout, stderr } = await exec(cmd.join('&&'))
        } catch (err) {
            ctx.logger.error(err.message);
        }
    },
    async crop_stamp(stamp_file, num, save_path) {
        let cmd = []

        let stamp = await jimp.read(stamp_file)

        const width = stamp.bitmap.width / num
        const height = stamp.bitmap.height

        for (let i = 0; i < num; i++) {
            const path = `${save_path}/stamp_${i}.png`

            cmd.push(`gm convert ${stamp_file} -crop ${width}x${height}+${i * width}+0 ${path}`)
        }

        await exec(cmd.join('&&'))
    },
    async make_pdf(images, croped, stamp_file, output_file, tmp_path, stamp_positons, cross_page_y) {
        const { ctx, config } = this

        let cmd = []
        const quality = 30
        const ext = '.png'

        let stamp = await jimp.read(stamp_file)
        let image = await jimp.read(images[0])

        const cross_page_x = image.bitmap.width - stamp.bitmap.width / images.length
        //骑缝章
        for (let i = 0; i < images.length; i++) {
            if (images.length > 1 && cross_page_y > 0) {
                cmd.push(`gm composite -density ${config.contract_stamp.width}x${config.contract_stamp.height} -geometry +${cross_page_x}+${cross_page_y} ${croped[i]} ${images[i]} ${`${tmp_path}/stamped_${i.toString().padStart(3, '0')}${ext}`}`)
            } else {
                cmd.push(`cp ${images[i]} ${`${tmp_path}/stamped_${i.toString().padStart(3, '0')}${ext}`}`)
            }
        }
        //普通合同章
        for (let i = 0; i < images.length; i++) {
            const pos = stamp_positons[i]

            if (pos && pos.x && pos.y) {
                cmd.push(`gm composite -density ${config.contract_stamp.width}x${config.contract_stamp.height} -geometry +${pos.x}+${pos.y} ${stamp_file} ${tmp_path}/stamped_${i.toString().padStart(3, '0')}${ext} ${`${tmp_path}/stamped_${i.toString().padStart(3, '0')}${ext}`}`)
            }
        }
        //生成PDF
        cmd.push(`gm convert -compress jpeg -quality ${quality} -page A4+0+0 ${tmp_path}/stamped_*${ext} ${output_file}`)
        cmd.push(`mv ${output_file} ${output_file.replace('.final.pdf', '')}`)

        try {
            const { stdout, stderr } = await exec(cmd.join('&&'))
        } catch (err) {
            ctx.logger.error(err.message);
        }
    },
    async convert_word_to_pdf(word, pdf) {
        const cmd = `soffice --headless --convert-to pdf ${word} --outdir ${pdf}`

        await exec(cmd)
    },
    async shell_exec(cmd, opts = {}) {
        return new Promise(function (resolve, reject) {
            sh.exec(cmd, opts, function (code, stdout, stderr) {
                if (code != 0) return reject(new Error(stderr))
                return resolve(stdout)
            })
        })
    },
    async bark_message(title, desp) {
        const { ctx } = this

        await ctx.curl(`http://**************:8080/HwDF7s932Y9WKXxXwiPDb5/${encodeURIComponent(title)}/${encodeURIComponent(desp)}`, {
            method: 'GET',
            timeout: 3000,
        })
    },
    get_extension(filename) {
        return path.extname(filename)
    },
    randomstring(num) {
        return randomstring.generate(num)
    },
    now() {
        return moment().tz("Asia/Shanghai").unix()
    },
    subtract_timestamp(days) {
        const day = moment().subtract(days, 'days')
        return moment(day).tz("Asia/Shanghai").unix()
    },
    today_begin_timestamp() {
        const today = moment().tz("Asia/Shanghai").format("YYYY-MM-DD")
        return moment(today).tz("Asia/Shanghai").unix()
    },
    format_date(date, format = 'YYYY-MM-DD') {
        return moment.tz(date, "Asia/Shanghai").format(format)
    },
    timestamp_to_date(timestamp, format = 'YYYY-MM-DD') {
        return this.format_date(moment.unix(timestamp), format)
    },
    today(format = 'YYYY-MM-DD HH:mm:ss') {
        return moment().tz("Asia/Shanghai").format(format)
    },
    today_with_timezone(timezone = 'Asia/Shanghai', format = 'YYYY-MM-DD HH:mm:ss') {
        return moment().tz(timezone).format(format)
    },
    timeout(ms) {
        return new Promise((resolve) => {
            setTimeout(resolve, ms);
        })
    },
    rand(a) {
        return Math.floor(Math.random() * a)
    },
    parseInt(string) {
        if (typeof string === 'number') return string
        if (!string) return string
        return parseInt(string, 10) || 0
    },
    get_random_between(min, max) {
        return parseInt(Math.random() * (max - min + 1) + min, 10)
    },
    get_random_num(num) {
        let random_str = ''
        for (let i = 0; i < num; i++) {
            random_str += Math.floor(Math.random() * 10)
        }
        return random_str
    },
    sort_dict(dict) {
        let dict2 = {}, keys = Object.keys(dict).sort()
        for (let i = 0, n = keys.length, key; i < n; ++i) {
            key = keys[i]
            dict2[key] = dict[key]
        }
        return dict2
    },
    azure_sms_sign(content, key) {
        const hash = crypto.createHmac('sha256', key).update(content).digest()
        const sig = encodeURIComponent(hash.toString('base64'))

        return `SharedAccessSignature sig=${sig}&${content}`
    },
    encode_dz_string(str) {
        const { ctx, config } = this

        return encodeURIComponent(authcode(str, 'ENCODE', md5(config.discuz.authkey)))
    },
    decode_dz_string(str) {
        const { ctx, config } = this

        return authcode(decodeURIComponent(str), 'DECODE', md5(`${config.discuz.authkey}`))
    },
    encode_dz_authkey(str, saltkey, write_cookie = true, auto_login = true) {
        const { ctx, config } = this
        const max_age = auto_login ? config.discuz.cookie_max_age : 0

        const cookie = {
            saltkey: saltkey,
            auth: encodeURIComponent(authcode(str, 'ENCODE', md5(config.discuz.authkey + saltkey)))
        }

        if (write_cookie) {
            const domain = config.discuz.domain
            ctx.cookies.set(`${config.discuz.prefix}auth`, cookie.auth, {
                httpOnly: true,
                maxAge: max_age,
                domain
            })
            ctx.cookies.set(`${config.discuz.prefix}saltkey`, cookie.saltkey, {
                httpOnly: false,
                maxAge: max_age,
                domain
            })
        }

        return cookie
    },
    decode_dz_authkey() {
        const { ctx, config } = this

        const auth = ctx.cookies.get(`${config.discuz.prefix}auth`, {
            httpOnly: true,
            signed: false,
        })

        const saltkey = ctx.cookies.get(`${config.discuz.prefix}saltkey`, {
            signed: false,
        })

        if (!auth || !saltkey) return ""

        return authcode(decodeURIComponent(auth), 'DECODE', md5(`${config.discuz.authkey}${saltkey}`))
    },
    decode_local_authkey(auth, saltkey) {
        const { ctx, config } = this

        return authcode(decodeURIComponent(auth), 'DECODE', md5(`${config.discuz.authkey}${saltkey}`))
    },
    logout_forum() {
        const { ctx, config } = this
        const domain = config.discuz.domain

        ctx.cookies.set(`${config.discuz.prefix}auth`, null, {
            httpOnly: true,
            domain
        })
        ctx.cookies.set(`${config.discuz.prefix}saltkey`, null, {
            httpOnly: false,
            domain
        })
    },
    mask_mobile(mobile) {
        mobile = mobile.toString()

        if (mobile.length < 6) return mobile
        const middle = mobile.length / 2

        return `${mobile.substring(0, middle - 2)}****${mobile.substring(middle + 2, mobile.length)}`
    },
    get_userinfo_from_cookie(check_new_system = true) {
        const { ctx } = this

        let userinfo = this.decode_dz_authkey()
        ctx.error(!userinfo, "请先登录系统")

        userinfo = userinfo.split("\t")

        if (check_new_system) {
            ctx.error(userinfo.length < 3, "请先登录新系统")
        }

        return userinfo
    },
    string_utf8_len(str) {
        return Buffer.byteLength(str, 'utf8')
    },
    exclude_special(s) {
        if (!s) return ''

        s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '')
        // s = s.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\"\<\>\?\[\]]/g, '')
        return s
    },
    exclude_special_with_enter(s) {
        if (!s) return ''

        s = s.replace(/[\'\"\\\/\b\f]/g, '')
        // s = s.replace(/[\@\#\$\%\^\&\*\(\)\{\}\:\"\<\>\?\[\]]/g, '')
        return s
    },
    contain_special(s) {
        let special = /[\ \'\"\\\/\b\f\n\r\t\@\#\$\%\^\&\*\(\)\{\}\:\"\<\>\?\[\]]/g
        let emoji = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g

        return special.test(s) || emoji.test(s)
    },
    format_linkedin_string(s) {
        let t = s || ''
        return t.replace('\n', '').trim()
    },
    hostname() {
        return os.hostname()
    },
    format_linkedin_year_month(dateline) {
        let [begin, end] = dateline.includes('-') ? dateline.split('-') : dateline.split('–')
        begin = begin || ''
        end = end || ''

        let [begin_month, begin_year] = begin.trim().split(' ')
        begin_year = begin_year || 0
        begin_month = begin_month || 0

        let [end_month, end_year] = end.includes('Present') ? [99, 9999] : end.trim().split(' ')
        end_year = end_year || 0
        end_month = end_month || 0

        if (dateline.includes('Less than a year')) {
            begin_year = dateline.replace('Less than a year', '').replace('·', '').trim()
            end_year = 0
            begin_month = 0
            end_month = 0
        }

        if (!begin_year) {
            [begin_year, end_year] = dateline.includes('-') ? dateline.split('-') : dateline.split('–')
            begin_year = begin_year.trim() || ''
            end_year = end_year || ''
            end_year = end_year.substring(0, end_year.indexOf('·')).trim()
            begin_year = begin_year || 0
            end_year = end_year || 0
            begin_month = 0
            end_month = 0
        }

        return {
            begin_year,
            begin_month: this.get_month(begin_month),
            end_year,
            end_month: this.get_month(end_month),
        }
    },
    get_month(str) {
        if (typeof str == 'number') return str

        const arr = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        let index = arr.indexOf(str)

        return ++index
    },
    get_cookie(cookies, name) {
        if (name) {
            for (const cookie of cookies) {
                if (cookie.indexOf(name) !== -1) {
                    const t = cookie.split(';')
                    return t[0]
                }
            }

            return arr.join(';')
        } else {
            const arr = []
            for (const cookie of cookies) {
                const t = cookie.split(';')
                arr.push(t[0])
            }

            return arr.join(';')
        }
    },
    between_en_cn_positon(str) {
        if (!str) return []

        const pos = []

        const cn_reg = new RegExp("[\u4E00-\u9FA5]+")
        const en_reg = new RegExp("[A-Za-z0-9]+")

        const cn = 1
        const en = 2

        const len = str.length
        let prev = undefined

        for (let i = 0; i < len; i++) {
            const is_cn = cn_reg.test(str[i])
            const is_en = en_reg.test(str[i])

            if (is_cn) {
                if (i !== 0 && prev !== cn) {
                    pos.push(i)
                }
                prev = cn
            } else if (is_en) {
                if (i !== 0 && prev !== en) {
                    pos.push(i)
                }
                prev = en
            }
        }

        return pos
    },
    insert_str(str, chr = ' ') {
        const pos = this.between_en_cn_positon(str)

        if (pos.length === 0) return str

        const output = []
        const len = str.length

        for (let i = 0; i < len; i++) {
            if (pos.includes(i)) output.push(chr)
            output.push(str[i])
        }

        return output.join('')
    },
    isAdmin(user) {
        return user.role_name.indexOf('管理员') !== -1
    },
    get_dz_avatar(uid) {
        const { config } = this

        const size = ['big', 'middle', 'small']
        let _uid = uid.toString()
        _uid = _uid.padStart(9, '0')

        const dir1 = _uid.substr(0, 3)
        const dir2 = _uid.substr(3, 2)
        const dir3 = _uid.substr(5, 2)
        const path = `${dir1}/${dir2}/${dir3}/${_uid.substr(-2)}`

        return size.map(e => {
            return `${config.baseDir}${config.forum_avatar}/${path}_avatar_${e}.jpg`
        })
    },
    rename_dz_avatar(uid) {
        const { ctx } = this

        if (!uid) return

        try {
            const files = this.get_dz_avatar(uid)

            files.map(e => {
                fse.moveSync(e, `${e}.bak`)
            })
        } catch (err) {
            ctx.logger.error(err.message)
        }
    },
    AESEncrypt(str) {
        const { config } = this
        const txt = str.toString()

        const key = CryptoJS.enc.Utf8.parse(config.aes.key)
        const iv = CryptoJS.enc.Utf8.parse(config.aes.iv)

        const encrypted = CryptoJS.AES.encrypt(txt, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        })

        return encrypted.toString()
    },
    AESDecrypt(str) {
        const { config } = this
        const txt = str.toString()

        const key = CryptoJS.enc.Utf8.parse(config.aes.key)
        const iv = CryptoJS.enc.Utf8.parse(config.aes.iv)

        const decrypted = CryptoJS.AES.decrypt(txt, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        })

        return decrypted.toString(CryptoJS.enc.Utf8)
    }
}
