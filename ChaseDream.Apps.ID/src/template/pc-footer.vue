<template>
    <div class="footer wp">
        <div class="footer-con cl">
            <div class="footer-l">
                <p>ChaseDream 论坛</p>
                <p class="xs0">
                    © 2003-2019 <a href="https://www.chasedream.com" target="_blank">ChaseDream.com.</a>  All Rights Reserved.
                </p>
            </div>
            <div class="footer-r">
                <p>
                    <a href="forum.php?mobile=yes" target="_blank">手机版</a>
                    <span class="pipe">|</span>
                    <a href="archiver/">Archiver</a><span class="pipe">|</span>
                    <strong>
                        <a href="https://www.chasedream.com/" target="_blank">ChaseDream</a>
                    </strong>
                    ( <a href="http://www.miitbeian.gov.cn/" target="_blank">京ICP证101109号</a> )
                    <!-- 网站统计 -->
                    <!--<script>
                        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                                (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
                            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
                        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

                        ga('create', 'UA-778748-7', 'auto');
                        ga('send', 'pageview');

                    </script>

                    &lt;!&ndash; 网站统计结束 &ndash;&gt;
                    <script src="https://forum.chasedream.com/static/chasedream/js/i.js"></script>-->

                    <iframe src="https://mat.chasedream.com/ets/toefl/2019/01/display.html" style="width: 1px; height: 1px; border: none" scrolling="no"></iframe>

                    <iframe src="https://mat.chasedream.com/ets/gre/2018/201811/display.html" style="width:1px; height:1px; border:none" scrolling="no"></iframe>

                </p>
                <p>
                    GMT+8, 2019-7-8 11:24
                    <span id="debuginfo">
                        , Processed in 0.059406 second(s), 3 queries
                        , Memcache On.
                    </span>
                  </p>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        name: 'pc_footer',
        props: {

        },
        methods: {

        }
    };
</script>
<style scoped="scoped">
    /*.footer-con{
        padding: 10px 0;
        width: 98%;
    }*/
</style>