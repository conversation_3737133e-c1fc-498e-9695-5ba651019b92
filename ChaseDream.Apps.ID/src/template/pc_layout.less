.register{
  &-con{
    /*position: absolute;
    right: 160px;
    top: 50%;
    transform: translateY(-60%);
    width: 300px;*/
    /*background: #FFF url(https://forum.chasedream.com/static/image/common/background.png) repeat-x 0 0;*/
    &-header{
      font-size: 16px;
      font-weight: 300;
      text-align: center;
      padding: 30px 0;
    }
    a{
      color: #333;
      text-decoration: none;
    }
    .cl:after{
      content: ".";
      display: block;
      height: 0;
      clear: both;
      visibility: hidden;
    }
    .wp{
      width: 98%;
      margin: 0 auto;
    }

    .header{
      .toptb{
        position: fixed;
        width: 100%;
        z-index: 11;
        min-width: 960px;
        border-bottom: 1px solid #CDCDCD;
        background: #F2F2F2;
        line-height: 28px;
        padding: 5px 0px;
        height: 35px;
        .z{
          float: left;
        }
        .y{
          float: right;
        }
        a{
          padding: 0px 2px;
          float: left;
          height: 28px;
        }
      }
      .hd{
        z-index: 1;
        padding-top: 34px;
        border-bottom: 0 solid #C2D5E3;
        .wp{
          padding: 10px 0 0;
        }
        .hdc{
          min-height: 70px;
          .logobottom{
            position: relative;
            bottom: 3px;
            float: left;
            margin-left: 8px;
          }
        }
        .nv{
          background: url(https://forum.chasedream.com/static/chasedream/image/common/nv.png);
          border-left: 1px solid #e5edf2;
          border-right: 1px solid #e5edf2;
          overflow: hidden;
          height: 33px;

          .qmenu{
            background: url(https://forum.chasedream.com/static/chasedream/image/common/qmenu.png) no-repeat scroll 0 0 transparent;
            display: inline;
            float: right;
            font-weight: 700;
            line-height: 26px;
            margin: 3px 4px 0px;
            overflow: hidden;
            padding: 0px 12px 0px 0px;
            text-align: center;
            text-shadow: 0px 1px 0 #ffffff;
            width: 83px;
            color: #336699;
          }
          li{
            list-style: none;
            float: left;
            padding-right: 1px;
            height: 33px;
            line-height: 33px;
            font-weight: 700;
            font-size: 14px;
            margin-right: 10px;
            a{
              float: left;
              padding: 0 15px;
              height: 33px;
              background: none;
              color: #336699;
              span{
                display: none;
              }
            }
          }
        }
        .scbar{
          overflow: hidden;
          height: 42px;
          line-height: 42px;
          border: solid #E5EDF2;
          border-width: 0 1px 1px;
          background: #E8EFF5 url(https://forum.chasedream.com/static/image/common/search.png) repeat-x 0 0;
          table{
            empty-cells: show;
            border-collapse: collapse;
            .scbar_icon_td{
              width: 50px;
              background: url(https://forum.chasedream.com/static/image/common/search.png) no-repeat 0 -74px;
            }
            .scbar_txt_td, .scbar_type_td{
              background: url(https://forum.chasedream.com/static/image/common/search.png) repeat-x 0 -222px;
            }
            .scbar_type_td{
              background-position: 0 -370px;
              width: 61px;
            }
            .scbar_btn_td{
              background: url(https://forum.chasedream.com/static/image/common/search.png) no-repeat 0 -296px;
              width: 67px;
              text-align: center;
            }
            .scbar_txt{
              font-size: 14px;
              width: 400px;
              border: 1px solid #FFF;
              outline: none;
              color: #999;
              height: 25px;
              margin-top: 10px;
            }
            .scbar_type{
              display: block;
              padding-left: 10px;
              text-align: left;
              text-decoration: none;
            }
            .scbar_btn{
              vertical-align: middle;
              overflow: hidden;
              height: 23px;
              color: #FFFFFF !important;
              font-weight: normal;
              margin: 0;
              padding: 0;
              border: none;
              box-shadow: none;
              background: transparent none;

              strong{
                line-height: 10px;
                color: #000;
                font-size: 14px;
                padding: 0 10px;
              }
            }
          }
        }
      }
    }
    .footer{
      padding: 10px 0 20px;
      border-top: 1px solid #CDCDCD;
      line-height: 1.8;
      color: #666;
      .footer-l{
        float: left;
      }
      .footer-r{
        float: right;
        text-align: right;
      }
    }

    .form-con{
      padding: 10px 0 0;

      .validate_code {
        .ivu-input-group{
          width: 60%;
        }
        .codeImg{
          position: absolute;
          right: 0;
          top: 0;
        }
      }
      .ivu-poptip{
        width: 100%;
        .ivu-poptip-rel{
          width: 100%;
        }
      }
    }
    .login-tip{
      font-size: 10px;
      text-align: center;
      color: #c3c3c3;
    }
    .ivu-spin{
      position: absolute;
      top: 3px;
      left:15px;
    }
    .ivu-alert{
      height: 36px;
      line-height: 20px;
    }
  }
}
