import Main from '@/views/Main.vue';

// 不作为Main组件的子页面展示的页面单独写，如下
export const consoleRouter = {
    path: '/console',
    name: 'console',
    meta: {
        title: 'Login - Admin'
    },
    component: () => import('@/views/admin/login/login.vue')
};
export const loginRouter = {
    path: '/login',
    name: 'login',
    meta: {
        title: 'Login - 登录'
    },
    component: () => import('@/views/login.vue')
};
export const loginPcRouter = {
    path: '/login-pc',
    name: 'login_pc',
    meta: {
        title: 'Login - 登录'
    },
    component: () => import('@/views/login-pc.vue')
};
export const loginWWWPcRouter = {
    path: '/login-www-pc',
    name: 'login_www-pc',
    meta: {
        title: 'Login - 登录'
    },
    component: () => import('@/views/login-www-pc.vue')
};

export const registerRouter = {
    path: '/register',
    name: 'register',
    meta: {
        title: 'Register - 注册'
    },
    component: () => import('@/views/register/register.vue')
};
export const captchaRouter = {
    path: '/captcha',
    name: 'captcha',
    meta: {
        title: 'Register - 验证码'
    },
    component: () => import('@/views/register/captcha.vue')
};

export const bindingRouter = {
    path: '/register/binding',
    name: 'binding',
    meta: {
        title: 'Register - 绑定微信'
    },
    component: () => import('@/views/register/binding.vue')
};

export const scanRouter = {
    path: '/register/scan',
    name: 'scan',
    meta: {
        title: 'Register - 绑定微信'
    },
    component: () => import('@/views/register/scan.vue')
};

export const tipRouter = {
    path: '/register/tip',
    name: 'tip',
    meta: {
        title: 'Register - 提示'
    },
    component: () => import('@/views/register/tip.vue')
};

export const tipunbindRouter = {
    path: '/register/tip-unbind',
    name: 'tip_unbind',
    meta: {
        title: 'Register - 提示'
    },
    component: () => import('@/views/register/tip-unbind.vue')
};

export const nicknameRouter = {
    path: '/register/nickname',
    name: 'nickname',
    meta: {
        title: 'Register - 论坛昵称'
    },
    component: () => import('@/views/register/nickname.vue')
};

export const optRouter = {
    path: '/register/opt',
    name: 'opt',
    meta: {
        title: 'Register - 找回密码'
    },
    component: () => import('@/views/register/opt.vue')
};

export const findpasswordRouter = {
    path: '/register/findpassword/:id',
    name: 'findpassword',
    meta: {
        title: 'Register - 找回密码'
    },
    component: () => import('@/views/register/findpassword.vue')
};

export const findpassword2Router = {
    path: '/register/findpassword2',
    name: 'findpassword2',
    meta: {
        title: 'Register - 找回密码'
    },
    component: () => import('@/views/register/findpassword2.vue')
};

export const modify_mobileRouter = {
    path: '/register/modify-mobile',
    name: 'modify_mobile',
    meta: {
        title: 'Register - 修改手机号'
    },
    component: () => import('@/views/register/modify-mobile.vue')
};

export const modify_mobile2Router = {
    path: '/register/modify-mobile2',
    name: 'modify_mobile2',
    meta: {
        title: 'Register - 修改手机号'
    },
    component: () => import('@/views/register/modify-mobile2.vue')
};

export const bind_mobileRouter = {
    path: '/register/bind_mobile',
    name: 'bind_mobile',
    meta: {
        title: 'Register - 绑定手机号'
    },
    component: () => import('@/views/register/bind_mobile.vue')
};
export const check_mobile_bindingRouter = {
    path: '/register/check_mobile_binding',
    name: 'check_mobile_binding',
    meta: {
        title: 'Register - 绑定手机检测'
    },
    component: () => import('@/views/register/check_mobile_binding.vue')
};
export const securityRouter = {
    path: '/register/security',
    name: 'security',
    meta: {
        title: 'Register - 账户与安全'
    },
    component: () => import('@/views/register/security.vue')
};
export const wechatRouter = {
    path: '/register/wechat',
    name: 'wechat',
    meta: {
        title: 'Register - 微信绑定'
    },
    component: () => import('@/views/register/wechat.vue')
};
export const bind_tipRouter = {
    path: '/register/bind-tip',
    name: 'bind_tip',
    meta: {
        title: 'Register - 微信绑定'
    },
    component: () => import('@/views/register/bind-tip.vue')
};
export const change_passwordRouter = {
    path: '/register/change-password',
    name: 'change_password',
    meta: {
        title: '修改密码'
    },
    component: () => import('@/views/register/change-password.vue')
};
export const logoffRouter = {
    path: '/register/logoff',
    name: 'logoff',
    meta: {
        title: '账户注销'
    },
    component: () => import('@/views/register/logoff.vue')
};
export const forumTag = {
    path: '/forum-tag/:id',
    name: 'forum_tag',
    meta: {
        title: '添加帖子标签'
    },
    component: () => import('@/views/tags/forum.vue')
};
export const forumTagTransit = {
    path: '/forum-tag-transit',
    name: 'forum_tag_transit',
    meta: {
        title: ''
    },
    component: () => import('@/views/tags/transit.vue')
};
export const page404 = {
    path: '/*',
    name: '404',
    meta: {
        title: '404-页面不存在'
    },
    component: () => import('@/views/error-page/404.vue')
};

export const page403 = {
    path: '/403',
    meta: {
        title: '403-权限不足'
    },
    name: 'error-403',
    component: () => import('@/views/error-page/403.vue')
};

export const page500 = {
    path: '/500',
    meta: {
        title: '500-服务端错误'
    },
    name: 'error-500',
    component: () => import('@/views/error-page/500.vue')
};

export const preview = {
    path: '/preview',
    name: 'preview',
    component: () => import('@/views/form/article-publish/preview.vue')
};

export const locking = {
    path: '/locking',
    name: 'locking',
    component: () => import('@/views/main-components/lockscreen/components/locking-page.vue')
};

// 作为Main组件的子页面展示但是不在左侧菜单显示的路由写在otherRouter里
export const otherRouter = {
    path: '/',
    name: 'otherRouter',
    redirect: '/error-404',   //  /rename/index
    component: Main,
    children: [
        { path: 'console', title: {i18n: 'home'}, name: 'home_index', component: () => import('@/views/home/<USER>') }

    ]
};

// 作为Main组件的子页面展示并且在左侧菜单显示的路由写在appRouter里
var routerList = JSON.parse(window.localStorage.getItem('routerList_ID'));
var newRoutes = [];
if(!!routerList){
    menuFilter(newRoutes, routerList)
}
function menuFilter (routers,data){
    data.forEach((item)=>{
        let menu = Object.assign({},item)
        menu.component = lazy(menu.component)
        if(item.children){
            menu.children = []
            menuFilter(menu.children,item.children)
        }
        routers.push(menu)
    })
}
function lazy(name){
    if(name === 'Main'){
        return () => import(`@/views/Main.vue`)
    }else {
        return () => import(`@/views/admin/${name}.vue`)
    }
}
export const appRouter = [...newRoutes]

// 所有上面定义的路由都要写在下面的routers里
export const routers = [
    loginRouter,
    loginPcRouter,
    loginWWWPcRouter,
    registerRouter,
    captchaRouter,
    bindingRouter,
    scanRouter,
    tipRouter,
    tipunbindRouter,
    nicknameRouter,
    change_passwordRouter,
    optRouter,
    findpasswordRouter,
    findpassword2Router,
    modify_mobileRouter,
    modify_mobile2Router,
    bind_mobileRouter,
    check_mobile_bindingRouter,
    securityRouter,
    wechatRouter,
    bind_tipRouter,
    logoffRouter,
    consoleRouter,
    otherRouter,
    preview,
    locking,
    ...appRouter,
    forumTag,
    forumTagTransit,
    page500,
    page403,
    page404
];
