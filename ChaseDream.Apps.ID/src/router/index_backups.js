import Vue from 'vue';
import iView from 'iview';
import Util from '../libs/util';
import VueRouter from 'vue-router';
import Cookies from 'js-cookie';
import {routers, otherRouter, appRouter} from './router';
import store from '../store/index'

Vue.use(VueRouter);
function randerMenu (data,routerList){
    for(var i=0;i<data.length;i++){
        var routerItem = {
            id: data[i].id,
            path: data[i].router,
            icon: data[i].icon,
            name: data[i].name,
            title: data[i].text,
            pid: data[i].pid || 0,
            uAccess: 1,
            isShow: data[i].is_show +'',
            order: data[i].order,
            component: data[i].component,//Main,//() => import('@/views/Main.vue'),//() => import('@/views/Main.vue'),
            comp: data[i].component,
            showCount: 0,
            children: []
        }
        if(!!data[i].sub_menu && data[i].sub_menu.length > 0){
            for(var j=0;j<data[i].sub_menu.length;j++){
                var sub_item ={
                    id: data[i].sub_menu[j].id,
                    path: data[i].sub_menu[j].router,
                    icon: data[i].sub_menu[j].icon,
                    name: data[i].sub_menu[j].name,
                    title: data[i].sub_menu[j].text,
                    pid: data[i].sub_menu[j].pid,
                    isShow: data[i].sub_menu[j].is_show + '',
                    order: data[i].sub_menu[j].order,
                    component: data[i].sub_menu[j].component,
                    comp: data[i].sub_menu[j].component,
                }
                routerItem.children.push(sub_item);

                if(!!data[i].sub_menu[j].is_show){
                    routerItem.showCount =  routerItem.showCount + 1;
                }
            }
        }
        routerList.push(routerItem);
    }
}
// 路由配置
const RouterConfig = {
    mode: 'history',
    routes: routers
};

export const router = new VueRouter(RouterConfig);

router.beforeEach((to, from, next) => {
    iView.LoadingBar.start();
    Util.title(to.meta.title);

    if (Cookies.get('locking') === '1' && to.name !== 'locking') { // 判断当前是否是锁定状态
        next({
            replace: true,
            name: 'locking'
        });
    } else if (Cookies.get('locking') === '0' && to.name === 'locking') {
        next(false);
    } else if(window.location.href.split('/').pop() === 'start'){
        next();
    } else if(window.location.href.split('/')[3] === 'start'){
        next();
    }
    else if(!Cookies.get('access_token') && (window.location.href.split('/').length === 3 || !window.location.href.split('/')[3])){
        console.log(window.location.href.split('/'))
        //window.location = '/console';
        window.location = '/404';
    }
    else {
        if((window.location.href.split('/')[3] === 'console')){ //&& to.name !== 'console'
            Cookies.set('access_token', '');
            Util.ajax({
                url:'/api/v1/auth/check_login',//http://id.chasedream.com:7004
                method:'get'
            }).then(function (res) {
                if(res.data.msg === 'success'){
                    if(res.data.data.uid > 0){
                        Cookies.set('access_token', 'login');
                        Cookies.set('user', res.data.data.username);
                        Cookies.set('uAccess',0);

                        if(!store.state.app.hasInfo) {
                            Util.ajax({
                                url: '/api/v1/admin/permission/my_menu',//  my_menu   menu
                                method: 'GET',
                            }).then(function (res) {
                                if (res.data.msg === 'success') {
                                    //console.log(res.data.data)
                                    if(res.data.data.length === 0){
                                        //  防止论坛iframe引入时，内容跳转到后台管理系统 //////////////////
                                        window.parent.postMessage({register_type:'binding'},'*');  //
                                        window.parent.postMessage({register_type:'login'},'*');   //
                                        ///////////////////////////////////////////////////////////
                                        //alert('没有分配到任何菜单，请重新登录！');
                                        store.commit('logout');
                                        store.commit('clearOpenedSubmenu');
                                        next({
                                            name: 'console'
                                        });
                                    }
                                    var routerList = [];
                                    randerMenu(res.data.data, routerList);
                                    //console.log(routerList)
                                    window.localStorage.setItem('routerList_ID', JSON.stringify(routerList));
                                    store.commit('setMenuRspList', true);

                                    if(window.location.href.split('/').pop() === 'console' && !!Cookies.get('access_token')){
                                        // console.log(routerList)
                                        //debugger
                                        if(to.name === 'rename_index'){
                                            window.location.href = window.location.href;
                                        }else {

                                            //window.location.href = window.location.href.split('/console')[0] + '/console/rename/index';
                                            window.location.href = window.location.href.split('/console')[0] + routerList[0].path;
                                        }
                                    }else if (!Cookies.get('access_token') && to.name !== 'console') { // 判断是否已经登录且前往的页面不是登录页
                                        next({
                                            name: 'console'
                                        });
                                    } else if (to.name === 'console' && !!Cookies.get('access_token')) { // 判断是否已经登录且前往的是登录页
                                        Util.title();
                                        next({
                                            name: routerList[0].children[0].name
                                        });
                                    } else if(!Cookies.get('access_token') && to.name === 'console'){
                                        next();
                                    } else {
                                        const curRouterObj = Util.getRouterObjByName([otherRouter, ...appRouter], to.name);
                                        if (curRouterObj && curRouterObj.access !== undefined) { // 需要判断权限的路由
                                            if (curRouterObj.access === parseInt(Cookies.get('access'))) {
                                                Util.toDefaultPage([otherRouter, ...appRouter], to.name, router, next); // 如果在地址栏输入的是一级菜单则默认打开其第一个二级菜单的页面
                                            } else {
                                                next({
                                                    replace: true,
                                                    name: 'error-403'
                                                });
                                            }
                                        } else { // 没有配置权限的路由, 直接通过
                                            Util.toDefaultPage([...routers], to.name, router, next);
                                        }
                                    }
                                } else {
                                    window.location = '/404';
                                }
                            }).catch(function (err) {
                                console.log(err)
                                //  防止论坛iframe引入时，内容跳转到后台管理系统 //////////////////
                                window.parent.postMessage({register_type:'binding'},'*');  //
                                window.parent.postMessage({register_type:'login'},'*');   //
                                ///////////////////////////////////////////////////////////
                                //alert('没有分配到任何菜单，请重新登录！');
                                store.commit('clearOpenedSubmenu');
                                window.location = '/404';
                            });
                        }else {
                            next();
                        }
                    }else {
                        window.location = '/404';
                    }
                }else{
                    next({
                        name: '404'
                    });
                }
            }).catch(function (err) {
            });

        }else if (!Cookies.get('access_token') && to.name !== 'login') { // 判断是否已经登录且前往的页面不是登录页
            var names = ['404', 'forum_tag', 'forum_tag_transit', 'login_pc', 'register', 'captcha', 'binding', 'scan', 'tip', 'tip_unbind', 'nickname', 'opt', 'findpassword', 'findpassword2', 'modify_mobile', 'modify_mobile2', 'bind_mobile', 'check_mobile_binding', 'security', 'wechat', 'bind_tip', 'change_password', 'logoff']

            for(var i=0;i<names.length;i++){
                if(to.name === names[i]){
                    next();
                    return;
                }
            }

            next({
                name: 'login'
            });
            return;
            /*if(to.name === '404' || to.name === 'forum_tag' || to.name === 'forum_tag_transit'){
                next();
            } else if(to.name === 'login_pc'){
                next();
            }else if(to.name === 'register'){
                next();
            }else if(to.name === 'captcha'){
                next();
            }else if(to.name === 'binding'){
                next();
            }else if(to.name === 'scan'){
                next();
            }else if(to.name === 'tip'){
                next();
            }else if(to.name === 'tip_unbind'){
                next();
            }else if(to.name === 'nickname'){
                next();
            }else if(to.name === 'opt'){
                next();
            }else if(to.name === 'findpassword'){
                next();
            }else if(to.name === 'findpassword2'){
                next();
            }else if(to.name === 'modify_mobile'){
                next();
            }else if(to.name === 'modify_mobile2'){
                next();
            }else if(to.name === 'bind_mobile'){
                next();
            }else if(to.name === 'check_mobile_binding'){
                next();
            }else if(to.name === 'security'){
                next();
            }else if(to.name === 'wechat'){
                next();
            }else if(to.name === 'bind_tip'){
                next();
            }else if(to.name === 'change_password'){
                next();
            }else if(to.name === 'logoff'){
                next();
            }else {
                next({
                    name: 'login'
                });
            }*/
        } else if(to.name === 'recruits'){
            next();
        }else if(!!Cookies.get('access_token')){
            var names = ['404', 'forum_tag', 'forum_tag_transit', 'login_pc', 'register', 'captcha', 'binding', 'scan', 'tip', 'tip_unbind', 'nickname', 'opt', 'findpassword', 'findpassword2', 'modify_mobile', 'modify_mobile2', 'bind_mobile', 'check_mobile_binding', 'security', 'wechat', 'bind_tip', 'change_password', 'logoff', 'login']

            for(var i=0;i<names.length;i++){
                if(to.name === names[i]){
                    next();
                    return;
                }
            }

        }

        /*else if(to.name === '404' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'forum_tag' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'forum_tag_transit' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'login_pc' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'register' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'captcha' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'binding' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'scan' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'tip' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'tip_unbind' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'nickname' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'opt' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'findpassword' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'findpassword2' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'modify_mobile' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'modify_mobile2' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'bind_mobile' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'check_mobile_binding' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'security' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'wechat' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'bind_tip' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'change_password' && !!Cookies.get('access_token')){
            next();
        }else if(to.name === 'logoff' && !!Cookies.get('access_token')){
            next();
        }else if (to.name === 'login' && !!Cookies.get('access_token')) { // 判断是否已经登录且前往的是登录页
            next();
            /!*Util.title();
            next({
                name: 'isp'
            });*!/
        } */

        else if(!Cookies.get('access_token') && to.name === 'login'){
            next();
        } else {
            const curRouterObj = Util.getRouterObjByName([otherRouter, ...appRouter], to.name);
            if (curRouterObj && curRouterObj.access !== undefined) { // 需要判断权限的路由
                if (curRouterObj.access === parseInt(Cookies.get('access'))) {
                    Util.toDefaultPage([otherRouter, ...appRouter], to.name, router, next); // 如果在地址栏输入的是一级菜单则默认打开其第一个二级菜单的页面
                } else {
                    next({
                        replace: true,
                        name: 'error-403'
                    });
                }
            } else { // 没有配置权限的路由, 直接通过
                Util.toDefaultPage([...routers], to.name, router, next);
            }
        }

    }
});

router.afterEach((to) => {
    Util.openNewPage(router.app, to.name, to.params, to.query);
    iView.LoadingBar.finish();
    window.scrollTo(0, 0);
});
