import {otherRouter, appRouter} from '@/router/router';
import Util from '@/libs/util';
import Cookies from 'js-cookie';
import Vue from 'vue';
import store from '../index'

const app = {
    state: {
        cachePage: [],
        lang: '',
        isFullScreen: false,
        openedSubmenuArr: [], // 要展开的菜单数组
        menuTheme: 'dark', // 主题
        themeColor: '',
        pageOpenedList: [
            {
                title: '首页',
                path: '/',
                name: 'home_index'
            }
        ],
        currentPageName: '',
        currentPath: [
            {
                title: '首页',
                path: '/',
                name: 'home_index'
            }
        ], // 面包屑数组
        menuList: [],
        routers: [
            otherRouter,
            ...appRouter
        ],
        tagsList: [...otherRouter.children],
        messageCount: 0,
        dontCache: ['text-editor', 'artical-publish'],// 在这里定义你不想要缓存的页面的name属性值(参见路由配置router.js)
        menuRspList:[],
        hasInfo:false,
        routerList: [],
        ismenuLoad: false
    },
    mutations: {
        getMenuData (state,router) {
            Util.ajax({
                url:'/api/v1/admin/permission/my_menu', // my_menu  menu
                method:'GET',
            }).then(function (res) {
                if(res.data.msg === 'success'){
                    app.mutations.randerMenu(state,{data: res.data.data,router:router})

                    window.localStorage.setItem('routerList_ID',JSON.stringify(state.routerList));
                    state.ismenuLoad = true;
                }else{
                    console.log(res.data);
                }
            }).catch(function (err) {
                console.log(err)
                if(err === 'Forbidden'){
                    alert('没有分配到任何菜单，请重新登录！');
                    store.commit('logout');
                    store.commit('clearOpenedSubmenu');
                    window.location = '/console';
                }
            });
        },
        setMenuRspList (state, n) {
            state.hasInfo = n;
        },
        setTagsList (state, list) {
            state.tagsList.push(...list);
        },
        updateMenulist (state) {
            let accessCode = parseInt(Cookies.get('access'));
            let menuList = [];
            appRouter.forEach((item, index) => {
                if (item.access !== undefined) {
                    if (Util.showThisRoute(item.access, accessCode)) {
                        if (item.children.length <= 1) {
                            menuList.push(item);
                        } else {
                            let len = menuList.push(item);
                            let childrenArr = [];
                            childrenArr = item.children.filter(child => {
                                if (child.access !== undefined) {
                                    if (child.access === accessCode) {
                                        return child;
                                    }
                                } else {
                                    return child;
                                }
                            });
                            menuList[len - 1].children = childrenArr;
                        }
                    }
                } else {
                    if (item.children.length <= 1) {
                        menuList.push(item);
                    } else {
                        let len = menuList.push(item);
                        let childrenArr = [];
                        childrenArr = item.children.filter(child => {
                            if (child.access !== undefined) {
                                if (Util.showThisRoute(child.access, accessCode)) {
                                    return child;
                                }
                            } else {
                                return child;
                            }
                        });
                        if (childrenArr === undefined || childrenArr.length === 0) {
                            menuList.splice(len - 1, 1);
                        } else {
                            let handledItem = JSON.parse(JSON.stringify(menuList[len - 1]));
                            handledItem.children = childrenArr;
                            menuList.splice(len - 1, 1, handledItem);
                        }
                    }
                }
            });
            state.menuList = menuList;
        },
        changeMenuTheme (state, theme) {
            state.menuTheme = theme;
        },
        changeMainTheme (state, mainTheme) {
            state.themeColor = mainTheme;
        },
        addOpenSubmenu (state, name) {
            let hasThisName = false;
            let isEmpty = false;
            if (name.length === 0) {
                isEmpty = true;
            }
            if (state.openedSubmenuArr.indexOf(name) > -1) {
                hasThisName = true;
            }
            if (!hasThisName && !isEmpty) {
                state.openedSubmenuArr.push(name);
            }
        },
        closePage (state, name) {
            state.cachePage.forEach((item, index) => {
                if (item === name) {
                    state.cachePage.splice(index, 1);
                }
            });
        },
        initCachepage (state) {
            if (localStorage.cachePage) {
                state.cachePage = JSON.parse(localStorage.cachePage);
            }
        },
        removeTag (state, name) {
            state.pageOpenedList.map((item, index) => {
                if (item.name === name) {
                    state.pageOpenedList.splice(index, 1);
                }
            });
        },
        pageOpenedList (state, get) {
            let openedPage = state.pageOpenedList[get.index];
            if (get.argu) {
                openedPage.argu = get.argu;
            }
            if (get.query) {
                openedPage.query = get.query;
            }
            state.pageOpenedList.splice(get.index, 1, openedPage);
            localStorage.pageOpenedList = JSON.stringify(state.pageOpenedList);
        },
        clearAllTags (state) {
            state.pageOpenedList.splice(1);
            state.cachePage.length = 0;
            localStorage.pageOpenedList = JSON.stringify(state.pageOpenedList);
        },
        clearOtherTags (state, vm) {
            let currentName = vm.$route.name;
            let currentIndex = 0;
            state.pageOpenedList.forEach((item, index) => {
                if (item.name === currentName) {
                    currentIndex = index;
                }
            });
            if (currentIndex === 0) {
                state.pageOpenedList.splice(1);
            } else {
                state.pageOpenedList.splice(currentIndex + 1);
                state.pageOpenedList.splice(1, currentIndex - 1);
            }
            let newCachepage = state.cachePage.filter(item => {
                return item === currentName;
            });
            state.cachePage = newCachepage;
            localStorage.pageOpenedList = JSON.stringify(state.pageOpenedList);
        },
        setOpenedList (state) {
            state.pageOpenedList = localStorage.pageOpenedList ? JSON.parse(localStorage.pageOpenedList) : [otherRouter.children[0]];
        },
        setCurrentPath (state, pathArr) {
            state.currentPath = pathArr;
        },
        setCurrentPageName (state, name) {
            state.currentPageName = name;
        },
        setAvator (state, path) {
            localStorage.avatorImgPath = path;
        },
        switchLang (state, lang) {
            state.lang = lang;
            Vue.config.lang = lang;
        },
        clearOpenedSubmenu (state) {
            state.openedSubmenuArr.length = 0;
        },
        setMessageCount (state, count) {
            state.messageCount = count;
        },
        increateTag (state, tagObj) {
            if (!Util.oneOf(tagObj.name, state.dontCache)) {
                state.cachePage.push(tagObj.name);
                localStorage.cachePage = JSON.stringify(state.cachePage);
            }
            state.pageOpenedList.push(tagObj);
            localStorage.pageOpenedList = JSON.stringify(state.pageOpenedList);
        },
        randerMenu (state,obj){
            const routerList = [];
            for(var i=0;i<obj.data.length;i++){
                var routerItem = {
                    id: obj.data[i].id,
                    path: obj.data[i].router,
                    icon: obj.data[i].icon,
                    name: obj.data[i].name,
                    title: obj.data[i].text,
                    pid: obj.data[i].pid || 0,
                    uAccess: 1,
                    order: obj.data[i].order,
                    component: obj.data[i].component,
                    comp: obj.data[i].component,
                    isShow: obj.data[i].is_show + '',
                    showCount: 0,
                    children: []
                }
                if(!!obj.data[i].sub_menu && obj.data[i].sub_menu.length > 0){
                    for(var j=0;j<obj.data[i].sub_menu.length;j++){
                        var sub_item ={
                            id: obj.data[i].sub_menu[j].id,
                            path: obj.data[i].sub_menu[j].router,
                            icon: obj.data[i].sub_menu[j].icon,
                            name: obj.data[i].sub_menu[j].name,
                            title: obj.data[i].sub_menu[j].text,
                            pid: obj.data[i].sub_menu[j].pid,
                            order: obj.data[i].order,
                            isShow: obj.data[i].sub_menu[j].is_show + '',
                            component: obj.data[i].sub_menu[j].component,
                            comp: obj.data[i].sub_menu[j].component,
                        }
                        routerItem.children.push(sub_item);
                        if(!!obj.data[i].sub_menu[j].is_show){
                            routerItem.showCount =  routerItem.showCount + 1;
                        }
                    }
                }
                routerList.push(routerItem);
            }
            state.routerList = routerList
        },
        delMenu (state,id){
           /* console.log(id)
            console.log(state.menuList)*/

            for(var i=0;i<state.menuList.length;i++){
                if(state.menuList[i].id === id){
                    state.menuList.splice(i,1);
                    return
                }
                for(var j=0;j<state.menuList[i].children.length;j++){
                    if(state.menuList[i].children[j].id === id){
                        state.menuList[i].children.splice(j,1);
                        return
                    }
                }
            }
            //console.log(state.menuList)
        }
    }
};

export default app;
