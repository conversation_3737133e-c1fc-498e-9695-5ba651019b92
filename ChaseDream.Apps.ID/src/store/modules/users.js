import Cookies from 'js-cookie';

const users = {
    state: {
        currentRow: {}
    },
    mutations: {
        /*deleteAccount (state, vm) {
            Cookies.remove('user');
            Cookies.remove('password');
            Cookies.remove('access');
            // 恢复默认样式
            let themeLink = document.querySelector('link[name="theme"]');
            themeLink.setAttribute('href', '');
            // 清空打开的页面等数据，但是保存主题数据
            let theme = '';
            if (localStorage.theme) {
                theme = localStorage.theme;
            }
            localStorage.clear();
            if (theme) {
                localStorage.theme = theme;
            }
        }*/
        getCurrentRow (state,row){
            state.currentRow = row;
            //console.log(state.currentRow);
            Cookies.set('currentRow',JSON.stringify(row))
        }
    }
};

export default users;
