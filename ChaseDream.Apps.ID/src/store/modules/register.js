import { otherRouter, appRouter } from '@/router/router';
import Util from '@/libs/util';
import Cookies from 'js-cookie';
import Vue from 'vue';

const register = {
    state: {
        ispc: true,
        registerData: {
            mobile: '',
            password: '',
            area_code: 86,
            captcha: '',
            username: '',
            email: '',
        },
        findPasswordDate: {
            mobile: '',
            area_code: 86,
            captcha: '',
            type: 'recovery_password'
        },
        openid: '',
        unionid: '',
        nickname: '',
        showWXp: false,
        unbindInfo: {
            openid: '',
            unionid: '',
            nickname: '',
            uid: '',
            username: '',
            auth: '',
            saltkey: ''
        }

    },
    mutations: {
        isPC(state) {
            var userAgentInfo = navigator.userAgent;
            //console.log(userAgentInfo);
            var Agents = ["Android", "iPhone",
                "SymbianOS", "Windows Phone", "iPod"];
            for (var v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) {
                    state.ispc = false;
                    return;
                }
            }
            state.ispc = true;
        },
        setData(state, data) {
            //console.log(data)
            state.registerData.mobile = data.mobile;
            state.registerData.area_code = data.area_code;
            state.registerData.captcha = data.captcha;
            state.registerData.password = data.password;
            if (!!data.openid) {
                state.registerData.openid = data.openid;
            }
            if (!!data.unionid) {
                state.registerData.unionid = data.unionid;
            }
            if (!!data.nickname) {
                state.registerData.nickname = data.nickname;
            }
        },
        setOpenid(state, str) {
            state.openid = str;
        },
        setUnionid(state, str) {
            state.unionid = str;
        },
        setNickname(state, str) {
            state.nickname = str;
        },
        setPasswordData(state, data) {
            state.findPasswordDate.mobile = data.mobile;
            state.findPasswordDate.area_code = data.area_code;
            state.findPasswordDate.captcha = data.captcha;
            state.findPasswordDate.type = data.type;
        },
        setUnbindInfo(state, info) {
            state.unbindInfo.openid = info.openid;
            state.unbindInfo.uid = info.uid;
            state.unbindInfo.username = info.username;
            state.unbindInfo.saltkey = info.saltkey;
            state.unbindInfo.auth = info.auth;
            if (!!info.unionid) {
                state.unbindInfo.unionid = info.unionid;
            }
            if (!!info.nickname) {
                state.unbindInfo.nickname = info.nickname;
            }
        },
        isWeiXin(state) {
            var ua = window.navigator.userAgent.toLowerCase();
            console.log(ua)
            var iswx = false;
            if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                if (ua.match(/wxwork/i) == 'wxwork') { // 企业微信号
                    iswx = false;
                } else {
                    iswx = true;
                }
            } else {
                iswx = false;
            }
            return iswx;
        },
        wx_url(state, util, vm, str) {
            util.ajax({
                url: 'api/v1/wechat/url',
                method: 'get',
                params: {
                    type: 'id'
                }
            }).then(function (res) {
                if (res.data.msg === 'success') {
                    window.location.href = res.data.data.url;
                } else {
                    vm.$Message.info(res.msg);
                }
            }).catch(function (err) {
                console.log(err)
                vm.$Message.info(res.msg);
            });


        },
        setWXp(state) {
            state.showWXp = true;
        },
        setBodyBg() {

        }
    }
};

export default register;
