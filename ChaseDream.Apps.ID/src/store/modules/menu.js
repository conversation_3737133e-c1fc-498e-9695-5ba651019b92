import {otherRouter, appRouter} from '@/router/router';
import Util from '@/libs/util';
import Cookies from 'js-cookie';
import Vue from 'vue';

const menu = {
    state: {
        currentRow: {
            pid: 0,
            text: '',
            name: '',
            router: '',
            order: '1.0',
            icon: '',
            component: '',
            is_show: '1',
            id:0
        }
    },
    mutations: {
        getmenuCurrent (state,row){
            console.log(row)
            state.currentRow.pid = row.pid;
            state.currentRow.text = row.title;
            state.currentRow.name = row.name;
            state.currentRow.router = row.path;
            state.currentRow.order = row.order;
            state.currentRow.icon = row.icon;
            state.currentRow.component = row.comp;
            state.currentRow.is_show = row.isShow;
            state.currentRow.id = row.id;
        }

    }
};

export default menu;
