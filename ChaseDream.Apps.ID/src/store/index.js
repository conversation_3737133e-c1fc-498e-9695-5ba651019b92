import Vue from 'vue';
import Vuex from 'vuex';

import app from './modules/app';
import menu from './modules/menu'
import user from './modules/user';
import role from './modules/role';
import users from './modules/users';
import register from './modules/register';
import thread from './modules/thread'
import event from './modules/event';
import school from './modules/school';
import stick from './modules/stick';
import recruit from  './modules/recruit';

Vue.use(Vuex);

const store = new Vuex.Store({
    state: {
        //
    },
    mutations: {
        //
    },
    actions: {

    },
    modules: {
        app,
        menu,
        user,
        role,
        users,
        register,
        thread,
        event,
        school,
        stick,
        recruit
    }
});

export default store;
