<style lang="less">
@import "../../styles/common.less";
@import "./recruit.less";
</style>

<template>
  <div class="recruit">
    <Card>
      <Form label-position="top">
        <FormItem label="">
          <Input placeholder="微信号" v-model="form.wechat" width="300"></Input>
        </FormItem>
        <FormItem label="">
          <Input placeholder="QQ号" v-model="form.qq"></Input>
        </FormItem>
        <FormItem label="">
          <FormItem prop="date">
            <DatePicker
              type="date"
              v-model="form.test_date"
              placeholder="选择考试日期"
            ></DatePicker>
          </FormItem>
        </FormItem>
        <FormItem label="计划何时考试（可多选）">
          <CheckboxGroup v-model="form.test_plan">
            <Checkbox label="15天内"></Checkbox>
            <Checkbox label="16天-1个月"></Checkbox>
            <Checkbox label="1-2个月"></Checkbox>
            <Checkbox label="2-3个月"></Checkbox>
            <Checkbox label="3个月以上"></Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="">
          <Select placeholder="请选择城市" v-model="form.city">
            <Option value="beijing">北京</Option>
            <Option value="shanghai">上海</Option>
            <Option value="shenzhen">深圳</Option>
            <Option value="guangzhou">广州</Option>
          </Select>
        </FormItem>
        <FormItem label="当前情况（可多选）">
          <CheckboxGroup v-model="form.status">
            <Checkbox label="正在准备考GMAT"></Checkbox>
            <Checkbox label="正在准备GRE"></Checkbox>
            <br />
            <Checkbox label="正在准备考托福"></Checkbox>
            <Checkbox label="正在准备考雅思"></Checkbox>
            <br />
            <Checkbox label="申请MBA"></Checkbox>
            <Checkbox label="申请Master"></Checkbox>
            <Checkbox label="申请PhD"></Checkbox>
            <Checkbox label="申请完成等待结果"></Checkbox>
            <br />
            <Checkbox label="商学院在读（非本科）"></Checkbox>
            <Checkbox label="已经从商学院毕业（非本科）"></Checkbox>
            <Checkbox label="寻求职业发展"></Checkbox>
            <br />
            <Checkbox label="随便看看"></Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="">
          <Input
            type="textarea"
            v-model="form.ps"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="附言"
          ></Input>
        </FormItem>
      </Form>
    </Card>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

export default {
  name: "recruit",
  components: {},
  data() {
    return {
      form: {
        subject: "",
        type: -1,
        //element: [],
        wechat: "",
        qq: "",
        test_date: 0,
        test_plan: 0,
        city: 0,
        status: [],
        ps: "",
      },
      errMsg: "",
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "recruit") {
        this.form = {
          subject: "",
          type: -1,
          wechat: "",
          qq: "",
          test_date: 0,
          test_plan: 0,
          city: 0,
          status: [],
          ps: "",
        };
        this.errMsg = "";
        this.$refs["form"].resetFields();
      }
    },
  },
  mounted() {},
  methods: {
    handleSubmit() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/form",
          method: "POST",
          data: {
            subject: _this.form.subject,
            type: _this.form.type,
            wechat: _this.form.wechat,
            qq: _this.form.qq,
            test_date: _this.form.test_date,
            test_plan: _this.form.test_plan,
            city: _this.form.city,
            status: _this.form.status,
            ps: _this.form.ps,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.$store.commit("removeTag", "form_add");
            _this.$store.commit("closePage", "form_add");
            _this.$router.push({
              name: "form_list",
            });
          } else {
            //_this.errMsg = res.msg;
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          //_this.errMsg = err.msg;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
  },
};
</script>

<style></style>
