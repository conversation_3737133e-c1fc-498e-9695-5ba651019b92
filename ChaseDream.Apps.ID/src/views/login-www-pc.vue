<style lang="less">
@import "./login-www.less";
@import "../template/pc_layout";
@import "../libs/jigsaw.css";
@import "./register/register.less";
</style>

<template>
  <div
    class="login"
    @keydown.enter="handleSubmit"
    :style="{ backgroundImage: bgImg, overflow: 'hidden' }"
  >
    <div class="login-con">
      <div class="login-left">
        <div class="user" v-if="!showWindow">        
        <div class="form-tabs">
          <div
            :class="loginType == 'mobile' ? 'on' : ''"
            @click="changeType('mobile')"
          >
            手机登录
            <span></span>
          </div>
          <div
            :class="loginType == 'email' ? 'on' : ''"
            @click="changeType('email')"
          >
            邮箱登录
            <span></span>
          </div>
        </div>
        <Form
          ref="loginForm"
          :model="form"
          class="nicknameForm"
          :label-width="0"
        >
          <FormItem
            prop="mobile_email"
            label=""
            class="mobile_email"
            v-if="loginType === 'mobile'"
          >
            <Input v-model="form.mobile_email" placeholder="手机号"></Input>
            <Poptip
              title=""
              content="content"
              placement="bottom-start"
              :transfer="true"
              class="pop_area_code"
              popper-class="pop_area_code"
              v-model="openAreaCode"
            >
              <Button
                >+{{ form.area_code }} <Icon type="arrow-down-b"></Icon
              ></Button>
              <div class="api" slot="content">
                <Select
                  v-model="checkedCity"
                  filterable
                  placeholder="搜索"
                  @on-change="checkAreaCode"
                  ref="store"
                  clearable
                  @on-query-change="searchCity"
                >
                  <Option
                    v-for="item in code_group"
                    :value="item.area_code + item.name"
                    :key="item.index"
                  >
                    <span>{{ item.name }}</span>
                    <span style="float:right;color:#ccc"
                      >+{{ item.area_code }}</span
                    >
                  </Option>                  
                </Select>
                <div class="useCitys">
                  <h4>常用</h4>
                  <span
                    @click="
                      getAreaCode(86);
                      openAreaCode = false;
                    "
                    >大陆<br />+86</span
                  >
                  <span
                    @click="
                      getAreaCode(852);
                      openAreaCode = false;
                    "
                    >香港<br />+852</span
                  >
                  <span
                    @click="
                      getAreaCode(853);
                      openAreaCode = false;
                    "
                    >澳门<br />+853</span
                  >
                  <span
                    @click="
                      getAreaCode(886);
                      openAreaCode = false;
                    "
                    >台湾<br />+886</span
                  >
                  <span
                    @click="
                      getAreaCode(1);
                      openAreaCode = false;
                    "
                    >美/加<br />+1</span
                  >
                  <span
                    @click="
                      getAreaCode(44);
                      openAreaCode = false;
                    "
                    >英国<br />+44</span
                  >
                  <span
                    @click="
                      getAreaCode(33);
                      openAreaCode = false;
                    "
                    >法国<br />+33</span
                  >
                  <span
                    @click="
                      getAreaCode(49);
                      openAreaCode = false;
                    "
                    >德国<br />+49</span
                  >
                  <span
                    @click="
                      getAreaCode(61);
                      openAreaCode = false;
                    "
                    >澳洲<br />+61</span
                  >
                  <span
                    @click="
                      getAreaCode(82);
                      openAreaCode = false;
                    "
                    >韩国<br />+82</span
                  >
                  <span
                    @click="
                      getAreaCode(65);
                      openAreaCode = false;
                    "
                    >新加坡<br />+65</span
                  >
                  <span
                    @click="
                      getAreaCode(81);
                      openAreaCode = false;
                    "
                    >日本<br />+81</span
                  >
                </div>
              </div>
            </Poptip>
          </FormItem>
          <FormItem
            prop="mobile_email"
            label=""
            v-if="loginType == 'email'"
            class=""
          >
            <Input v-model="form.mobile_email" placeholder="邮箱"></Input>
          </FormItem>
          <FormItem prop="password" label="" class="password">
            <Input
              type="password"
              v-model="form.password"
              placeholder="密码"
            ></Input>
          </FormItem>
          <FormItem class="auto-login">
            <Checkbox v-model="form.isagree">同意ChaseDream<a href="javascript:;" @click="showAgreement = true;">网站服务条款</a></Checkbox>
            <a href="javascript:;" @click="findPassword" style="float: right;font-size: 14px;"
              >找回密码</a
            >
          </FormItem>
          <p class="l_err_msg" style="">{{ errMsg }}</p>
          <FormItem class="re-btn">
            <Button type="success" id="login_btn" @click="handleSubmit" :disabled="!form.isagree"
              >登录</Button
            >
          </FormItem>
          <FormItem class="auto-login">
            <Checkbox v-model="form.auto_login">自动登录</Checkbox>
            <a href="javascript:;" @click="toRegister" style="float: right;font-size: 14px;"
              >用户注册</a
            >
          </FormItem>
        </Form>
      </div>
      </div>
      <div class="login-right">
        <div class="code">
          <h3>打开微信扫一扫登录</h3>
        <!--geetest()-->
        <div class="wx_Code">
          <div style="display: inline-block;position: relative;">
            <img :src="wcImg" alt="" width="160" />
            <Spin fix v-if="spinShow">
              <a href="javascript:;" @click="getqrcode" v-if="!isRefresh">
                <Icon type="ios-refresh-empty" size="30"></Icon>
                <div>刷新</div>
              </a>
              <span v-if="!!isRefresh">
                <Icon
                  type="load-c"
                  size="18"
                  class="demo-spin-icon-load"
                ></Icon>
              </span>
            </Spin>
          </div>
        </div>  
        <p class="l_err_msg" style="">{{ wxLogin_errMsg }}</p>      
      </div>
      </div>
      <div class="user o_user" v-if="!!showWindow">
        <!--<h3>用户登录</h3>
                <i class="tab_icon" @click="getqrcode"></i>-->
        <div class="form-tabs">
          <div
            :class="loginType == 'mobile' ? 'on' : ''"
            @click="changeType('mobile')"
          >
            手机登录
            <span></span>
          </div>
          <div
            :class="loginType == 'email' ? 'on' : ''"
            @click="changeType('email')"
          >
            邮箱登录
            <span></span>
          </div>
        </div>
        <Form
          ref="loginForm"
          :model="form"
          class="nicknameForm"
          :label-width="0"
        >
          <FormItem
            prop="mobile_email"
            label=""
            class="mobile_email"
            v-if="loginType === 'mobile'"
          >
            <Input v-model="form.mobile_email" placeholder="手机号"></Input>
            <Poptip
              title=""
              content="content"
              placement="bottom-start"
              :transfer="true"
              class="pop_area_code"
              popper-class="pop_area_code"
              v-model="openAreaCode"
            >
              <Button
                >+{{ form.area_code }} <Icon type="arrow-down-b"></Icon
              ></Button>
              <div class="api" slot="content">
                <Select
                  v-model="checkedCity"
                  filterable
                  placeholder="搜索"
                  @on-change="checkAreaCode"
                  ref="store"
                  clearable
                >
                  <Option
                    v-for="item in code_group"
                    :value="item.name + item.area_code"
                    :key="item.index"
                  >
                    <span>{{ item.name }}</span>
                    <span style="float:right;color:#ccc"
                      >+{{ item.area_code }}</span
                    >
                  </Option>
                </Select>
                <div class="useCitys">
                  <h4>常用</h4>
                  <span
                    @click="
                      getAreaCode(86);
                      openAreaCode = false;
                    "
                    >大陆<br />+86</span
                  >
                  <span
                    @click="
                      getAreaCode(852);
                      openAreaCode = false;
                    "
                    >香港<br />+852</span
                  >
                  <span
                    @click="
                      getAreaCode(853);
                      openAreaCode = false;
                    "
                    >澳门<br />+853</span
                  >
                  <span
                    @click="
                      getAreaCode(886);
                      openAreaCode = false;
                    "
                    >台湾<br />+886</span
                  >
                  <span
                    @click="
                      getAreaCode(1);
                      openAreaCode = false;
                    "
                    >美/加<br />+1</span
                  >
                  <span
                    @click="
                      getAreaCode(44);
                      openAreaCode = false;
                    "
                    >英国<br />+44</span
                  >
                  <span
                    @click="
                      getAreaCode(33);
                      openAreaCode = false;
                    "
                    >法国<br />+33</span
                  >
                  <span
                    @click="
                      getAreaCode(49);
                      openAreaCode = false;
                    "
                    >德国<br />+49</span
                  >
                  <span
                    @click="
                      getAreaCode(61);
                      openAreaCode = false;
                    "
                    >澳洲<br />+61</span
                  >
                  <span
                    @click="
                      getAreaCode(82);
                      openAreaCode = false;
                    "
                    >韩国<br />+82</span
                  >
                  <span
                    @click="
                      getAreaCode(65);
                      openAreaCode = false;
                    "
                    >新加坡<br />+65</span
                  >
                  <span>日本<br />+81</span>
                </div>
              </div>
            </Poptip>
          </FormItem>
          <FormItem
            prop="mobile_email"
            label=""
            v-if="loginType == 'email'"
            class=""
          >
            <Input v-model="form.mobile_email" placeholder="邮箱"></Input>
          </FormItem>
          <FormItem prop="password" label="" class="password">
            <Input
              type="password"
              v-model="form.password"
              placeholder="密码"
            ></Input>
          </FormItem>
          <FormItem class="auto-login">
            <Checkbox v-model="form.auto_login">自动登录</Checkbox>            
          </FormItem>
          <FormItem class="terms">
            <Checkbox v-model="form.isagree">已阅读并同意<a href="javascript:;" @click="showAgreement = true;">网站服务条款</a></Checkbox>
          </FormItem>
          <p class="l_err_msg" style="">{{ errMsg }}</p>
          <FormItem class="re-btn">
            <Button type="success" id="login_btn" @click="handleSubmit" :disabled="!form.isagree"
              >登录</Button
            >
            <a href="javascript:;" @click="toRegister(1)">
              <Button type="ghost">用户注册</Button>
            </a>
          </FormItem>
        </Form>
      </div>
    </div>
    <div class="area-code" v-if="!!showCodep" style="z-index: 99999;">
      <div class="mask" @click="showCodep = false"></div>
      <div class="code-inner">
        <div class="top">
          国家/地区
          <span>代码</span>
        </div>
        <p class="used">常用</p>
        <div class="used-list">
          <Button @click="getAreaCode(86)">大陆<br />+86</Button>
          <Button @click="getAreaCode(852)">香港<br />+852</Button>
          <Button @click="getAreaCode(853)">澳门<br />+853</Button>
          <Button @click="getAreaCode(886)">台湾<br />+886</Button>
          <Button @click="getAreaCode(1)">美/加<br />+1</Button>
          <Button @click="getAreaCode(44)">英国<br />+44</Button>
          <Button @click="getAreaCode(33)">法国<br />+33</Button>
          <Button @click="getAreaCode(49)">德国<br />+49</Button>
          <Button @click="getAreaCode(61)">澳洲<br />+61</Button>
          <Button @click="getAreaCode(82)">韩国<br />+82</Button>
          <Button @click="getAreaCode(65)">新加坡<br />+65</Button>
          <Button @click="getAreaCode(81)">日本<br />+81</Button>
        </div>
        <div class="detail-list">
          <p>详细列表</p>
          <Menu active-name="86" @on-select="getAreaCode">
            <MenuGroup
              :title="code.letter"
              v-for="code in code_group"
              :key="code.index"
            >
              <MenuItem
                :name="list.area_code"
                v-for="list in code.data"
                :key="list.index"
              >
                {{ list.name }}
                <span>+{{ list.area_code }}</span>
              </MenuItem>
            </MenuGroup>
          </Menu>
        </div>
      </div>
    </div>
    <div class="agreement-pop" v-if="!!showAgreement">
      <Icon type="close" size="24" @click="showAgreement = false"></Icon>
      <agreement></agreement>
      <Button type="success" long @click="showAgreement = false"
        >返回登录</Button
      >
    </div>
    <Modal
      v-model="bind_mobile"
      width="80%"
      :closable="false"
      class-name="bind_mobile_modal"
      :scrollable="true"
    >
      <div style="text-align:center">
        <div class="lawCon" v-if="!toBinding">
          <h3>您的账号还未绑定手机</h3>
          <p>
            <img
              src="../images/register/bind_mobile.png"
              height="110"
              width="78"
            />
          </p>
          <p align="left">
            根据国家法律法规对账号实名的要求，我们需要您将此账号与您的手机号码绑定以完成实名制的验证。
          </p>
          <Button type="primary" long @click="toBinding = true">去绑定</Button>
          <div class="law">
            <h4>《中华人民共和国网络安全法》第二十四条</h4>
            <p align="left" v-if="showLaw">
              网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，在与用户签订协议或者确认提供服务时，应当要求用户提供真实身份信息。用户不提供真实身份信息的，网络运营者不得为其提供相关服务。
            </p>
            <p align="left" style="margin-top: 20px;margin-bottom: 10px;">
              <a
                href="http://search.chinalaw.gov.cn/law/searchTitleDetail?LawID=394858&Query"
                target="_blank"
                >相关链接 >
              </a>
            </p>
          </div>
          <div class="btns">
            <span class="toOpen" v-if="!showLaw" @click="showLaw = true"></span>
            <span
              class="toClose"
              v-if="showLaw"
              @click="showLaw = false"
            ></span>
          </div>
        </div>
        <div class="bind" v-if="toBinding">
          <h3>绑定手机</h3>
          <Form
            ref="loginForm"
            :model="bind_form"
            class="nicknameForm"
            :label-width="0"
          >
            <FormItem prop="mobile" label="" class="mobileItem">
              <div class="area_code_btn" @click="showpannel">
                +{{ bind_form.area_code }}
                <Icon type="chevron-down" size="10"></Icon>
              </div>
              <Input v-model="bind_form.mobile" placeholder="手机号"></Input>
            </FormItem>
            <FormItem prop="validate_code" class="validate_code" label="">
              <Input v-model="bind_form.captcha" placeholder="验证码"></Input>
              <Button type="ghost" @click="getCaptcha" v-if="!!isSentcode"
                >获取验证码</Button
              >
              <Button type="ghost" class="wait-btn" v-if="!isSentcode"
                >{{ count }}秒后重发</Button
              >
            </FormItem>
            <p class="l_err_msg">{{ bind_errMsg }}</p>
            <Button type="success" long @click="bingding">绑定</Button>
          </Form>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import jigsaws from "@/libs/jigsaw.js";
import initGeetest from "@/libs/gt.js";
import area from "./area_code";
import agreement from "../template/agreement.vue";
import pc_header from "../template/pc-header.vue";
import pc_footer from "../template/pc-footer.vue";
export default {
  name: "login_www-pc",
  components: {
    agreement,
  },
  data() {
    return {
      form: {
        area_code: 86,
        mobile_email: "", //test
        password: "", //321321
        validate_code: "",
        isagree: false,
        auto_login: false,
      },
      bind_form: {
        area_code: 86,
        mobile: "",
        captcha: "",
      },
      rules: {},
      access_token: this.$store.state.user.access_token,
      bgImg: "",
      errMsg: "",
      wxLogin_errMsg: "",
      codeImg: "",
      isValidate: false,
      addYz: true,
      geetest_challenge: "",
      geetest_validate: "",
      geetest_seccode: "",
      isPc: true,
      loginType: "mobile",
      showCodep: false,
      code_group: [],
      code_val: 86,
      showAgreement: false,
      wcLogin: false,
      wcImg: "",
      timer: "",
      showWXp: true,
      showWX: true,
      bind_mobile: false,
      showLaw: false,
      toBinding: false,
      isSentcode: true,
      count: 60,
      bind_errMsg: "",
      checkedCity: "",
      openAreaCode: false,
      openid: 0,
      unionid: "",
      nickname: "",
      showWindow: false,
      spinShow: false,
      see: false,
    };
  },
  mounted() {    
    this.$store.commit("isPC");
    this.isPc = this.$store.state.register.ispc;

    if (!!Cookies.get("area_code")) {
      this.form.area_code = Cookies.get("area_code");
      this.bind_form.area_code = Cookies.get("area_code");
    }    

    if (!!this.isPc) {
      // 页面加载时获取二维码
      this.getqrcode();
    } else {      
      if (this.isWeiXin()) {
        this.showWXp = this.$store.state.register.showWXp;
        this.showWX = false;
        this.isWXlogin();
      }
    }

    if (!!this.isPc) {
      this.code_group = area.area_code;
      if (window.location.href.indexOf("openid=") != -1) {
        this.showWindow = true;

        if (!!window.localStorage.getItem("openid")) {
          this.openid = window.localStorage.getItem("openid");
          window.localStorage.setItem("openid", null);
        }
        if (!!window.localStorage.getItem("unionid")) {
          this.unionid = window.localStorage.getItem("unionid");
          window.localStorage.setItem("unionid", null);
        }
        if (!!window.localStorage.getItem("nickname")) {
          this.nickname = window.localStorage.getItem("nickname");
          window.localStorage.setItem("nickname", null);
        }
      }
    } else {
      this.code_group = area.area_code_group;
    }    
  },
  methods: {
    changeType(str) {
      this.loginType = str;
      this.form.mobile_email = "";
      this.form.password = "";
      this.errMsg = "";
    },
    showpannel() {
      this.showCodep = true;
    },
    getAreaCode(n) {
      if (!!this.bind_mobile) {
        this.bind_form.area_code = n;
      } else {
        this.form.area_code = n;
      }
      Cookies.set("area_code", n);
      this.showCodep = false;
    },
    checkAreaCode(e) {
      console.log(e);
      if (!!e) {
        this.form.area_code = e.replace(/[^0-9]/gi, "");
        this.checkedCity = "";
        this.$refs.store.clearSingleSelect(); // 清空
        this.openAreaCode = false;
      }
    },
    handleSubmit(captchaObj) {
      var txt_email = document.getElementsByTagName("input")[0].value;
      var txt_password = document.getElementsByTagName("input")[1].value;
      txt_email =
        txt_email.split(" ").length > 1
          ? txt_email.split(" ")[1]
          : txt_email.split(" ")[0];

      if (!this.form.mobile_email) {
        if (!txt_email) {
          if (this.loginType === "mobile") {
            this.errMsg = "手机号不能为空！";
            return;
          } else if (this.loginType === "email") {
            this.errMsg = "邮箱不能为空！";
            return;
          }
        } else {
          this.form.mobile_email = txt_email;
        }
      }
      if (this.form.mobile_email && this.loginType === "mobile") {
        var reg = /^[0-9]*$/;
        if (!reg.test(this.form.mobile_email)) {
          this.errMsg = "手机号只能是数字";
          return false;
        }
      } else if (this.form.mobile_email && this.loginType === "email") {
        var reg = /^([a-zA-Z0-9]+[_|\_|\.|\-]*)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+\.[a-zA-Z]{2,5}$/;
        if (!reg.test(this.form.mobile_email)) {
          this.errMsg = "请输入正确邮箱地址";
          return false;
        }
      }      

      if (!this.form.password) {
        if (!txt_password) {
          this.errMsg = "密码不能为空！";
          return;
        } else {
          this.form.password = txt_password;
        }
      } else if (!this.form.isagree) {
        this.errMsg = "请阅读并同意网站服务条款";
        return;
      }
      
      if (!this.isValidate) {
        this.show_captcha();
        return;
      }
      return false;
    },
    loginSubmit(obj) {
      var dataIn = {};
      dataIn.area_code = this.form.area_code;
      dataIn.mobile_email = this.form.mobile_email;
      dataIn.password = this.form.password;
      dataIn.ticket = obj.ticket;
      dataIn.randstr = obj.randstr;
      dataIn.platform = !!this.isPc ? 1 : 2;
      if (!!this.$store.state.register.openid) {
        dataIn.openid = this.$store.state.register.openid;
      } else if (!!this.openid) {
        dataIn.openid = this.openid;
      }
      if (!!this.$store.state.register.unionid) {
        dataIn.unionid = this.$store.state.register.unionid;
      } else if (!!this.unionid) {
        dataIn.unionid = this.unionid;
      }
      if (!!this.$store.state.register.nickname) {
        dataIn.nickname = this.$store.state.register.nickname;
      } else if (!!this.nickname) {
        dataIn.nickname = this.nickname;
      }
      dataIn.auto_login = !!this.form.auto_login ? 1 : 0;

      console.log(dataIn);

      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/verify/login",
          method: "post",
          data: dataIn,
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            if (_this.isPc) {
              //console.log(res)
              if (!!dataIn.openid) {
                window.parent.postMessage({ register_type: "login" }, "*");
              } else {
                window.parent.postMessage({ register_type: "login" }, "*");
                window.parent.postMessage(
                  { register_type: "login_show_wx" },
                  "*"
                );
              }
            } else {
              window.location.href =
                "https://forum.chasedream.com/forum.php?login=true";
            }
          } else {
            _this.errMsg = res.msg;            
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;          
        });
    },
    getqrcode() {
      if (!!this.timer) {
        clearInterval(this.timer);
      }
      this.spinShow = false;
      this.wcLogin = true;
      var _this = this;
      var url = "api/v1/wechat/qrcode";

      util
        .ajax({
          url: url + "?t=" + new Date().getTime(),
          method: "get",
          dataType: "json",
        })
        .then(function(data) {
          console.log(data);
          if (data.data.msg === "success") {
            _this.wcImg = data.data.data.image;
            var s = 0;
            var s_url =
              "api/v1/wechat/verify_ticket?t=" + data.data.data.ticket;
            _this.timer = setInterval(function() {
              s++;
              if (s === 120) {
                clearInterval(_this.timer);
                _this.spinShow = true;
              }

              util
                .ajax({
                  url: s_url + "&ts=" + new Date().getTime(),
                  method: "get",
                  dataType: "json",
                  params: {
                    type: "login",
                    platform: !!this.isPc ? 1 : 2,
                  },
                })
                .then(function(data) {
                  console.log(data);
                  if (data.data.msg === "success") {
                    if (data.data.data.openid !== "") {
                      _this.openid = data.data.data.openid;
                      _this.unionid = data.data.data.unionid;
                      _this.nickname = data.data.data.nickname;

                      clearInterval(_this.timer);

                      if (!!data.data.data.uid) {
                        window.parent.postMessage({ register_type: 'login' }, "*");
                      } else {
                        _this.wxLogin_errMsg = '微信尚未注册，请先注册账号';                        
                      }
                    }                    
                  } else {
                    _this.errMsg = data.msg;
                  }
                })
                .catch(function(err) {
                  console.log(err);
                  clearInterval(_this.timer);
                  _this.errMsg = err.msg;
                });
            }, 1000);
          } else {
            _this.errMsg = res.msg;
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
        });
    },
    cancel() {
      clearInterval(this.timer);
    },
    isWeiXin() {
      var ua = window.navigator.userAgent.toLowerCase();
      var iswx = false;
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        if (ua.match(/wxwork/i) == "wxwork") {
          // 企业微信号
          iswx = false;
        } else {
          iswx = true;
        }
      } else {
        iswx = false;
      }
      return iswx;
    },
    isWXlogin() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/wechat/check_login",
          method: "GET",
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            if (!!res.data.data.login) {
              window.location.href = "https://forum.chasedream.com/forum.php";
            } else {
              if (!_this.$store.state.register.openid) {
                _this.$store.commit("wx_url", util, _this);
              }
            }
          } else {
            _this.errMsg = res.msg;
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
        });
    },
    toRegister(str) {
      if (parseInt(str) !== 1) {        
        window.open(
          "https://forum.chasedream.com/member.php?mod=register",
          "_blank"
        );
      } else {        
        var dataIn = {
          type: "register",
          openid: this.openid,
          unionid: this.unionid,
          nickname: this.nickname,
        };
        window.parent.postMessage({ register_type: dataIn }, "*");
      }
    },
    findPassword() {
      window.open(
        "https://forum.chasedream.com/member.php?mod=logging&action=login&viewlostpw=1",
        "_blank"
      );
    },
    getCaptcha() {
      if (!this.bind_form.mobile || !this.bind_form.area_code) {
        this.bind_errMsg = "请输入手机号码";
        this.isRegistet = false;
        return;
      }

      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/sms/send",
          method: "post",
          data: {
            mobile: _this.bind_form.mobile,
            area_code: _this.bind_form.area_code,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.bind_errMsg = "";
            _this.countdown();
            _this.$Message.info(res.data.data.message);
          } else {
            _this.bind_errMsg = res.msg;
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.bind_errMsg = err.msg;
        });
    },
    countdown() {
      this.isSentcode = false;
      var _this = this;
      clearInterval(timer);
      var timer = setInterval(function() {
        if (_this.count === 1) {
          clearInterval(timer);
          _this.isSentcode = true;
        } else {
          _this.count--;
        }
      }, 1000);
    },
    bingding() {
      if (!this.bind_form.mobile) {
        this.bind_errMsg = "请输入要绑定的手机号";
        return false;
      }
      if (!this.bind_form.captcha) {
        this.bind_errMsg = "请输入验证码";
        return false;
      }
      var dataIn = {
        area_code: this.bind_form.area_code,
        mobile: this.bind_form.mobile,
        captcha: this.bind_form.captcha,
      };
      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/binding_mobile/change",
          method: "post",
          data: dataIn,
        })
        .then(function(res) {          
          if (res.data.msg === "success") {
            _this.bind_errMsg = "";
          } else {
            _this.bind_errMsg = res.msg;
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.bind_errMsg = err.msg;
        });
    },
    searchCity(e) {
      var Regx = /^[0-9]*$/;
      var text = e.split("+").length > 1 ? "e.split(" + ")[1]" : e;
      if (!!e && Regx.test(text)) {        
      } else {
        return false;
      }
      setTimeout(function() {
        var list = document
          .getElementsByClassName("ivu-select-dropdown-list")[0]
          .getElementsByTagName("li");
        for (var i = 0; i < list.length; i++) {
          list[i].style.display = "block";
          var liText = "+" + list[i].innerText.split("+")[1];
          if (liText.indexOf(e) == -1) {
            list[i].style.display = "none";
          }
        }
      }, 600);
    },
    show_captcha() {
      var _this = this;      
      var captcha = new TencentCaptcha("2032354718", function(res) {
        _this.loginSubmit(res);
      });
      captcha.show();
    },
  },
};
</script>

<style>
.cH {
  overflow: hidden;
}
#captcha-box .geetest_holder.geetest_wind {
  width: 100% !important;
}
</style>
