export const infoColumns = [
    /*{
        //title: '',
        key: 'success',
        width: 80,
        align: 'center',
        render: (h, params) => {
            var style = {}
            if(!params.row.success){
                style.color = '#ed3f14';
            }else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size:14
                }
            })
        }
    },*/
    /*{
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },*/
    {
        title: '主题帖',
        align: 'center',
        key: 'threads',
        //width: 80
    },
    {
        title: '回复帖',
        align: 'center',
        key: 'posts',
        //width: 80
    },
    {
        title: '好友',
        align: 'center',
        key: 'friends',
        //width: 80
    },
    {
        title: '记录',
        align: 'center',
        //width: 80,
        key: 'doings'
    },
    {
        title: '消息',
        align: 'center',
        key: 'pm',
        //width: 80
    },
    {
        title: '收藏',
        align: 'center',
        key: 'favorite',
        //width: 80
    },
    {
        title: 'UCenter',
        align: 'center',
        key: 'uc',
        //width: 80,
        render: (h, params) => {
            var style = {};
            var type = '';
            if (!!params.row.uc) {
                style.color = '#ed3f14';
                type = 'close-round';
            } else {
                style.color = '#19be6b';
                type = 'checkmark-round';
            }
            return h('Icon', {
                style: style,
                props: {
                    type: type,
                    size: 18
                }
            })
        }
    },
    {
        title: '主表',
        align: 'center',
        key: 'cm',
        //width: 80,
        render: (h, params) => {
            var style = {};
            var type = '';
            if (!!params.row.cm) {
                style.color = '#ed3f14';
                type = 'close-round';
            } else {
                style.color = '#19be6b';
                type = 'checkmark-round';
            }
            return h('Icon', {
                style: style,
                props: {
                    type: type,
                    size: 18
                }
            })
        }
    },
    {
        title: '归档表',
        align: 'center',
        //width: 80,
        key: 'cma',
        render: (h, params) => {
            var style = {};
            var type = '';
            if (!!params.row.cma) {
                style.color = '#ed3f14';
                type = 'close-round';
            } else {
                style.color = '#19be6b';
                type = 'checkmark-round';
            }
            return h('Icon', {
                style: style,
                props: {
                    type: type,
                    size: 18
                }
            })
        }
        // handle: ['view']
    },
];
export const blColumns = [
    {
        title: 'ID',
        key: 'id',
        //width: 80,
        align: 'center'
    },
    {
        title: '区号',
        align: 'center',
        key: 'area_code',
        //width: 80
        /*editable: true*/
    },
    {
        title: '手机号码',
        align: 'center',
        key: 'mobile'
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['delete']
    },
]

const infoData = {
    infoColumns: infoColumns,
    blColumns: blColumns
};

export default infoData;
