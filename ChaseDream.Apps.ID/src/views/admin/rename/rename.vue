<style lang="less">
    @import '../../../styles/common.less';
    @import './rename.less';
</style>

<template>
    <div class="rename">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <h3>重命名用户</h3>
                <br>
                <Form ref="form" :model="form" :rules="rules" :label-width="0">
                    <FormItem>
                        <Row>
                            <Col span="11">
                            <FormItem prop="username">
                                <Input v-model="form.username" placeholder="待改用户用户名" @on-change="infoRows = []"></Input>
                            </FormItem>
                            </Col>
                            <Col span="2" style="text-align: center">-</Col>
                            <Col span="11">
                            <FormItem prop="new_username">
                                <Input v-model="form.new_username" placeholder="终修改后用户用户名" @on-change="infoRows = []"></Input>
                            </FormItem>
                            </Col>
                        </Row>
                    </FormItem>
                    <FormItem>
                        <Button type="primary" @click="handleSubmit()">提 交</Button>
                        <Button type="ghost" @click="handleReset()" style="margin-left: 8px">重 置</Button>
                    </FormItem>
                </Form>
                <br>
                <div class="" v-if="infoRows.length > 0">
                    <h3>用户信息</h3>
                    <br>
                    <can-edit-table refs="userInfo" @on-delete="handleDel" v-model="infoRows" :columns-list="infoColumns"></can-edit-table>
                    <br>
                    <Poptip
                            confirm
                            title="您确定修改此用户名吗？"
                            @on-ok="modify()"
                            placement="top-start"
                            >
                        <Button type="primary" :disabled="disabled">修 改</Button>
                    </Poptip>

                </div>
                <!--<div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getblacklist" v-if="showPage"></Page
                </div>>-->
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import infoData from './data/rename_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'rename_index',
        components: {
            canEditTable
        },
        data () {
            return {
                form: {
                    username: '',
                    new_username: ''
                },
                isAccess: true,
                rules: {
                    username: [
                        { required: true, message: '用户名不能为空', trigger: 'blur' },
                        { type: 'string', min: 1, message: '用户名不能小于1个字符', trigger: 'blur' }
                    ],
                    new_username: [
                        { required: true, message: '新用户名不能为空', trigger: 'blur' },
                        { type: 'string', min: 1, message: '新用户名不能小于1个字符', trigger: 'blur' }
                    ]
                },
                infoColumns: [],
                infoRows:[],
                disabled: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'rename_index'){

                }
            }
        },
        mounted () {
            this.infoColumns = infoData.infoColumns
        },
        methods: {
            handleSubmit (){
                this.infoRows = [];
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/rename',
                            method:'GET',
                            params: {
                                username: _this.form.username,
                                new_username: _this.form.new_username
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                //console.log(res.data)
                                //_this.infoRows.push(res.data.data.member_count);
                                //_this.infoRows.push(res.data.data.member_exist);

                                _this.infoRows.push(Object.assign({},res.data.data.member_count,res.data.data.member_exist));
                                if(!_this.infoRows[0].uc && !_this.infoRows[0].cm && !_this.infoRows[0].cma){
                                    _this.disabled = false;
                                }
                            }else{
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            console.log(err)
                            _this.$Notice.error({
                                title: err.msg
                            });
                        });
                    }
                })
            },
            handleReset (){
                this.form.username = '';
                this.form.new_username = '';
                this.infoRows = [];
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            modify (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/rename',
                    method:'POST',
                    data: {
                        username: _this.form.username,
                        new_username: _this.form.new_username
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '重命名成功！'
                        });
                        _this.handleReset();
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            }
        },
        created () {

        }
    };
</script>

<style>

</style>
