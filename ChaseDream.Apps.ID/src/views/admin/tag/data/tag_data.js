export const tColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '主标签',
        align: 'center',
        key: 'name',
        width: 150
    },
    {
        title: '同义词',
        align: 'center',
        key: 'synonym',
        //width: 80，
        render: (h, params) => {
            if (!!params.row.synonym && params.row.synonym.length > 0) {
                var synonym = [];
                for (var i = 0; i < params.row.synonym.length; i++) {
                    synonym.push(params.row.synonym[i].name);
                }
                return h('span', {}, synonym.join('、'))
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '标签属性',
        align: 'center',
        key: 'property',
        width: 100,
        render: (h, params) => {
            if (!!params.row.property) {
                var text = '';
                if (params.row.property === 1) {
                    text = '学校名称';
                } else if (params.row.property === 2) {
                    text = '专业方向';
                } else if (params.row.property === 3) {
                    text = '帖子类型';
                } else if (params.row.property === 4) {
                    text = '适用阶段';
                }

                return h('span', {}, text)
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '标签计数',
        align: 'center',
        key: 'count',
        width: 80,
        render: (h, params) => {
            if (!!params.row.count) {
                return h('a', {
                    attrs: {
                        href: 'tag-thread/list?tagid=' + params.row.id,
                        target: '_blank'
                    }
                }, params.row.count)
            } else {
                return h('span', {}, '0')
            }
        }
    },
    {
        title: '排序',
        align: 'center',
        key: 'handle',
        width: 90,
        handle: ['order']
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'createdAt',
        width: 150,
        render: (h, params) => {
            if (!!params.row.createdAt) {

                return h('span', {}, params.row.createdAt.split('T')[0] + ' ' + params.row.createdAt.split('T')[1].split('+')[0])
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['edit', 'delete']
    },
];
export const s_Columns = [
    {
        title: 'ID',
        key: 'handle',
        width: 60,
        align: 'center',
        handle: ['id']
    },
    {
        title: '主标签',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['name']
    },
    {
        title: '同义词',
        align: 'center',
        key: 'handle',
        handle: ['synonym']
    },
    {
        title: '标签属性',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['property']
    },
    {
        title: '标签计数',
        align: 'center',
        key: 'handle',
        width: 80,
        handle: ['count']
    },
    {
        title: '排序',
        align: 'center',
        key: 'handle',
        width: 90,
        /*handle: ['order']*/
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['createdAt']
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['clear']
    },
];
export const tTColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '帖子',
        align: 'center',
        key: 'tid',
        width: 100,
        render: (h, params) => {
            if (!!params.row.tid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            } else {
                return h('span', {}, '帖子')
            }
        }
    },
    {
        title: '标签',
        align: 'center',
        key: 'tags'
    }
]

export const tagTColumns = [
    {
        title: '帖子',
        align: 'center',
        key: 'tid',
        render: (h, params) => {
            if (!!params.row.tid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            } else {
                return h('span', {}, '帖子')
            }
        }
    }
]

const tagData = {
    tColumns: tColumns,
    s_Columns: s_Columns,
    tTColumns: tTColumns,
    tagTColumns: tagTColumns
};

export default tagData;
