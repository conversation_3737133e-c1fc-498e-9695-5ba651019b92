<style lang="less">
    @import '../../../styles/common.less';
    @import './tag.less';
</style>

<template>
    <div class="batch-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    批量添加
                </p>
                <Form>
                    <FormItem>
                        <Input v-model="tags" type="textarea" :rows="6" placeholder="格式： [1]标签1||标签2||标签3"></Input>
                        <p style="color: #999">注： [1]:学校名称、[2]:专业方向、[3]:帖子类型、[4]:适用阶段</p>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <br><br>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'batch_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                tags: '',
                new_tags: [
                    {
                        name: '',
                        type: 0
                    },
                    {
                        name: '',
                        type: 0
                    }
                ],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'batch_add'){
                    this.tags = '';
                    this.new_tags = [
                        {
                            name: '',
                            type: 0
                        },
                        {
                            name: '',
                            main: 0
                        }
                    ];
                    this.errMsg = '';
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            add_row (){
                var item = {
                    name: '',
                    type: 0
                }
                this.new_tags.push(item);
            },
            del_row (index){
                this.new_tags.splice(index, 1);
            },
            add_tag (){
                console.log(this.new_tags)
                var tag_list = []
                for(var i=0;i<this.new_tags.length;i++){
                    if(!!this.new_tags[i].name && this.new_tags[i].type !== 0){
                        tag_list.push(this.new_tags[i])
                    }else if(!!this.new_tags[i].name && this.new_tags[i].type === 0){
                        this.$Notice.error({
                            title: '请选择标签属性！'
                        });
                        return false;
                    }
                }
                console.log(tag_list)
                if(!tag_list.length){
                    this.$Notice.error({
                        title: '请输入标签！'
                    });
                    return false;
                }
                var tagArr = [];
                for(var i=0;i<tag_list.length;i++){
                    var str = '';
                    str ='['+  tag_list[i].type +']' + tag_list[i].name;
                    tagArr.push(str);
                }
                this.handleSubmit(tagArr)
            },

            clear_tag (){
                this.new_tags = [];
                this.new_tags = [
                    {
                        name: '',
                        type: 0
                    },
                    {
                        name: '',
                        type: 0
                    }
                ]
            },
            handleSubmit (arr) {
                //console.log(arr.join('/n'));
                if(!this.tags){
                    this.$Notice.error({
                        title: '请输入标签！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag/batch',
                    method:'POST',
                    data: {
                        tags: this.tags//arr.join('\r\n')
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'batch_add');
                        _this.$store.commit('closePage', 'batch_add');
                        _this.$router.push({
                            name: 'tag_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.$store.commit('removeTag', 'batch_add');
                this.$router.push({
                    name: 'tag_list'
                });
            }
        }
    };
</script>

<style>

</style>
