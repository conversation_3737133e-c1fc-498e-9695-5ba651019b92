<style lang="less">
@import "../../../../styles/common.less";
@import "../tag.less";
</style>

<template>
  <div class="rename">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <h3>标签帖子</h3>
          <br />
          <div class="edittable-con-1">
            <can-edit-table
              refs="table"
              @on-delete="handleDel"
              v-model="tRows"
              :columns-list="tColumns"
            ></can-edit-table>
          </div>
          <div class="page-bar">
            <Page
              :total="total"
              :page-size="page_size"
              :current="current"
              @on-change="getlist"
              v-if="showPage"
            ></Page>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import util from "@/libs/util.js";
import tagData from "../data/tag_data.js";
import canEditTable from "./components/canEditTable.vue";

export default {
  name: "tag_thread",
  components: {
    canEditTable,
  },
  data() {
    return {
      isAccess: true,
      tColumns: [],
      tRows: [],
      total: 0,
      tagid: "",
      showPage: false,
      page_size: 20,
      current: 1,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "tag_thread") {
        this.isAccess = true;
        this.tColumns = [];
        this.total = 0;
        this.showPage = false;
        this.page_size = 20;
        this.current = 1;
        this.getData();
      }
    },
  },
  mounted() {},
  methods: {
    getData() {
      var params = new URLSearchParams(window.location.href.split("?")[1]);
      this.tColumns = tagData.tagTColumns;
      this.tagid = params.get("tagid");
      this.getlist(1);
    },
    getlist(n) {
      this.tRows = [];
      this.total = 0;
      this.current = n;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/tag/tag_thread",
          method: "GET",
          params: {
            page: _this.current,
            page_size: _this.page_size,
            tagid: _this.tagid,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.tRows = res.data.data.rows;
            _this.total = res.data.data.count;
            if (_this.total > _this.page_size) {
              _this.showPage = true;
            }
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style></style>
