<style lang="less">
    @import '../../../styles/common.less';
    @import './tag.less';
</style>

<template>
    <div class="tag">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <h3>标签列表</h3>
                <br>
                <div class="tool-bar">
                    <router-link to="/console/tag/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <router-link to="/console/tag/batch-add">
                        <Button type="success" icon="plus">批量添加</Button>
                    </router-link>
                    <router-link to="/console/tag/tag-merge">
                        <Button type="success" icon="merge">标签合并</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable sp_table"></can-edit-table>
                    <can-edit-table refs="table" @on-delete="handleDel" @on-order="set_order" v-model="tRows" :columns-list="tColumns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tagData from './data/tag_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'tag_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                tColumns: [],
                tRows:[],
                s_row: [{
                    id: '',
                    name: '',
                    property:''
                }],
                s_columns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'tag_list'){
                    this.isAccess = true;
                    this.tColumns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.tColumns = tagData.tColumns;
                this.s_columns = tagData.s_Columns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].name){
                    s.push('s=' + this.s_row[0].name);
                }
                if(this.s_row[0].property === 1 || this.s_row[0].property === 2 || this.s_row[0].property === 3 || this.s_row[0].property === 4){
                    s.push('property=' + this.s_row[0].property);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.tRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.tRows = res.data.data.rows;
                        for(var i=0;i<_this.tRows.length;i++){
                            _this.tRows[i].isedit = false;
                        }
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            set_order (i,val){
                this.$set(this.tRows[i],'order',val);
                var dataIn = {
                    synonym_id: this.tRows[i].synonym_id,
                    tags: [],
                    //order: this.tRows[i].order
                }
                var m = {
                    id: this.tRows[i].id,
                    name: this.tRows[i].name,
                    main: 1,
                    property: this.tRows[i].property,
                    order: this.tRows[i].order,
                    action: 'update'
                }
                //_this.property = res.data.data.property;
                dataIn.tags.push(m);
                for(var j=0;j<this.tRows[i].synonym.length;j++){
                    var t = {
                        id: this.tRows[i].synonym[j].id,
                        name: this.tRows[i].synonym[j].name,
                        main: 0,
                        property: this.tRows[i].synonym[j].property,
                        order: this.tRows[i].order,
                        action: 'update'
                    }
                    dataIn.tags.push(t);
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$set(_this.tRows[i],'isedit',false);
                        var list_data = [];
                        list_data = JSON.parse(JSON.stringify(_this.tRows));
                        _this.tRows = [];
                        _this.tRows = JSON.parse(JSON.stringify(list_data));
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
