<style lang="less">
    @import '../../../styles/common.less';
    @import './tag.less';
</style>

<template>
    <div class="tag-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="merge"></Icon>
                    标签合并
                </p>
                <Form>
                     <FormItem>
                         <Input v-model="id_1" placeholder="请输标签ID" style="width:200px;margin-right: 20px;"></Input>
                         <Icon type="arrow-left-a" style="color:#19be6b;line-height: 40px;vertical-align: middle;" size="24"></Icon>
                         <Input v-model="id_2" placeholder="请输标签ID" style="width:200px;margin-left: 20px;"></Input>
                     </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <br><br>
                    <FormItem>
                        <Button @click="merge_tag" type="primary">合并</Button>
                        <Button @click="clear_id">重置</Button>
                        <Button @click="closePage" type="ghost">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'tag_merge',
        components: {
            // canEditTable
        },
        data () {
            return {
                id_1: '',
                id_2: '',
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'tag_merge'){
                    this.id_1 = '';
                    this.id_2 = '';
                    this.errMsg = '';
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            clear_id (){
                this.id_1 = '';
                this.id_2 = '';
            },
            merge_tag () {
                if(!this.id_1 || !this.id_2){
                    this.$Notice.error({
                        title: '请输入ID！'
                    });
                    return false;
                }
                var dataIn ={
                    left: this.id_1,
                    right: this.id_2
                }
                //console.log(dataIn)
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag/merge',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'tag_merge');
                        _this.$store.commit('closePage', 'tag_merge');
                        _this.$router.push({
                            name: 'tag_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.id_1 = '';
                this.id_2 = '';
                this.errMsg = '';
                this.$store.commit('removeTag', 'tag_merge');
                this.$router.push({
                    name: 'tag_list'
                });
            }
        }
    };
</script>

<style>

</style>
