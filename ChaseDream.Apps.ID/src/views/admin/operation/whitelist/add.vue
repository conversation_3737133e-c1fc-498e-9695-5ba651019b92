<style lang="less">
    @import '../../../../styles/common.less';
    @import './css.less';
</style>

<template>
    <div class="whitelist">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    创建IP白名单
                </p>
                <Form ref="form" :model="form" :label-width="90">
                    <FormItem prop="ip" label="IP：" required>
                        <Input v-model="form.ip" placeholder="请输入IP" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="remark" label="备注：">
                        <Input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注" style="width:500px;"></Input>

                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">创建</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from './data.js';

    export default {
        name: 'white_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    ip: '',
                    remark: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'white_add'){
                    this.$refs.form.resetFields();
                    this.form = {
                        ip: '',
                        remark: ''
                    };
                    this.errMsg = '';
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                if(!this.form.ip){
                    this.$Notice.error({
                        title: '请输入IP！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/monitor_sameip_whitelist',
                    method:'POST',
                    data: {
                        ip: this.form.ip,
                        remark: this.form.remark
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'white_add');
                        _this.$store.commit('closePage', 'white_add');
                        _this.$refs.form.resetFields();
                        _this.$router.push({
                            name: 'white_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {
                    ip: '',
                    remark: ''
                };
                this.$store.commit('removeTag', 'white_add');
                this.$refs.form.resetFields();
                this.$router.push({
                    name: 'white_list'
                });
            }
        }
    };
</script>

<style>

</style>
