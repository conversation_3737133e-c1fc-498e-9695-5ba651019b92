<style lang="less">
    @import './editable-table.less';
</style>

<template>
    <div>
        <Table :ref="refs" :columns="columnsList" :data="thisTableData" border></Table>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

const viewButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'eye',//currentRow.editting ? 'success' : 'primary', //'test',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            click: () => {
                vm.$emit('details', currentRow);
            }
        }
    }); //, currentRow.editting ? '保存' : '编辑'
};
const editButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'edit',//currentRow.editting ? 'success' : 'primary', //'test',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                let cr = currentRow;
                vm.$store.commit('getCurrentRow', cr);
                vm.$router.push({
                    name: 'white_edit'
                });
            }
        }
    }); //, currentRow.editting ? '保存' : '编辑'
};
const deleteButton = (vm, h, currentRow, index) => {
    return h('Poptip', {
        props: {
            confirm: true,
            title: '您确定要删除这条数据吗?',
            transfer: true
        },
        on: {
            'on-ok': () => {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/monitor_sameip_whitelist',
                    method:'DELETE',
                    data: {
                        id: currentRow.id
                    },
                }).then(function (res) {
                    //console.log(res.data);
                    if(res.data.msg === 'success'){
                        vm.thisTableData.splice(index, 1);
                        vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                        vm.$emit('on-delete', vm.handleBackdata(vm.thisTableData), index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        }
    }, [
        h('Icon', {
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            props: {
                type: 'trash-a', // error
                placement: 'top',
                //icon: 'trash-a'
                size: 24
            }
        }) //, '删除'
    ]);
};
const incellEditBtn = (vm, h, param) => {
    if (vm.hoverShow) {
        return h('div', {
            'class': {
                'show-edit-btn': vm.hoverShow
            }
        }, [
            h('Button', {
                props: {
                    type: 'text',
                    icon: 'edit'
                },
                on: {
                    click: (event) => {
                        vm.edittingStore[param.index].edittingCell[param.column.key] = true;
                        vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
                    }
                }
            })
        ]);
    } else {
        return h('Button', {
            props: {
                type: 'text',
                icon: 'edit'
            },
            on: {
                click: (event) => {
                    vm.edittingStore[param.index].edittingCell[param.column.key] = true;
                    vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
                }
            }
        });
    }
};
const saveIncellEditBtn = (vm, h, param) => {
    return h('Button', {
        props: {
            type: 'text',
            icon: 'checkmark'
        },
        on: {
            click: (event) => {
                vm.edittingStore[param.index].edittingCell[param.column.key] = false;
                vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
                vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-cell-change', vm.handleBackdata(vm.thisTableData), param.index, param.column.key);
            }
        }
    });
};
const cellInput = (vm, h, param, item) => {
    return h('Input', {
        props: {
            type: 'text',
            value: vm.edittingStore[param.index][item.key]
        },
        on: {
            'on-change' (event) {
                let key = item.key;
                vm.edittingStore[param.index][key] = event.target.value;
            }
        }
    });
};
const ipInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.ip,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'ip', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'ip', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const clearButton = (vm, h, currentRow, index) => {
    return h('span', {
        props: {},
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$set(vm.thisTableData[0], 'ip', '');
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    }, '清除');
};
const remarkInput = (vm, h, currentRow, index) => {
    var remark = currentRow.remark;
    currentRow.isEdit = false;
    return h('div',{
        style: {
            position: 'relative',
            maxheight: '36px',
            overflow: 'hidden',
            minHeight: '18px'
        }
    },[
        h('p',{
            attrs:{
                title: currentRow.remark
            },
            style: {
                marginRight: '15px',
                cursor: 'pointer',
                maxHeight: '70px',
                overflow: 'hidden',
                marginTop: '7px',
                marginBottom: '7px'
            }
        },currentRow.remark),
        h('Poptip', {
            props: {
                vModel: currentRow.show,
                transfer: true,
                placement: 'bottom-end',
                width: '330'
            },
            style: {
                position: 'absolute',
                bottom: '0',
                right: '0'
            },
            on: {
                'on-popper-hide': () => {
                }
            }
        }, [
            h('Icon',{
                props: {
                    type: 'edit',
                    size: 12
                },
                style: {
                    color: '#2d8cf0',
                    cursor: 'pointer',

                },
                attrs:{
                    title: '编辑备注'
                },
            }),
            h('div', {
                slot: 'content'
            },[
                h('h3',{
                    style: {
                        height: '30px',
                        lineHeight: '30px;',
                        borderBottom: '1px solid #E9EAEC',
                        color: '#495060',
                        fontSize: '14px',
                        marginBottom: '10px',
                        fontWeight: 'normal'
                    }
                },'编辑备注'),
                h('Input',{
                    style: {
                        display: 'block', //!!currentRow.isEdit ? 'block' : 'none',  //'block',
                        marginTop: '20px',
                        marginBottom: '14px',
                        background: '#F5F6F7',
                        width: '100%',
                    },
                    props:{
                        type: 'textarea',
                        rows: 4,
                        placeholder: '备注',
                        value: remark
                    },
                    on:{
                        'on-change':(obj)=>{
                            //console.log(obj.target.value)
                            remark = obj.target.value;
                        }
                    }
                }),
                h('Button',{
                    style:{
                        display: 'block',
                        width: '100%',
                        height: '34px',
                        background: '#2D8CF0',
                        color: '#fff',
                        fontSize: '14px'
                    },
                    /*props: {
                     loading: currentRow.loading
                     },*/
                    on: {
                        click: () => {
                            //vm.thisTableData[index].loading = true;
                            //vm.$emit('set-loading',index);
                            var _this = this;
                            util.ajax({
                                url:'/api/v1/admin/misc/monitor_sameip_whitelist',
                                method:'PUT',
                                data: {
                                    id: currentRow.id,
                                    remark: remark,
                                },
                            }).then(function (res) {
                                //console.log(res.data);
                                if(res.data.msg === 'success'){
                                    //vm.$emit('on-updata');
                                    vm.$emit('set-remark',currentRow,index,remark);

                                }else{

                                }
                            }).catch(function (err) {
                                console.log(err)
                            });
                        }
                    }
                },'确定')
            ])
        ])
    ])
};
export default {
    name: 'canEditTable',
    props: {
        refs: String,
        columnsList: Array,
        value: Array,
        url: String,
        editIncell: {
            type: Boolean,
            default: false
        },
        hoverShow: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            columns: [],
            thisTableData: [],
            edittingStore: []
        };
    },
    created () {
        this.init();
    },
    methods: {
        init () {
            let vm = this;
            let editableCell = this.columnsList.filter(item => {
                if (item.editable) {
                    if (item.editable === true) {
                        return item;
                    }
                }
            });
            let cloneData = JSON.parse(JSON.stringify(this.value));
            let res = [];
            res = cloneData.map((item, index) => {
                let isEditting = false;
                if (this.thisTableData[index]) {
                    if (this.thisTableData[index].editting) {
                        isEditting = true;
                    } else {
                        for (const cell in this.thisTableData[index].edittingCell) {
                            if (this.thisTableData[index].edittingCell[cell] === true) {
                                isEditting = true;
                            }
                        }
                    }
                }
                if (isEditting) {
                    return this.thisTableData[index];
                } else {
                    this.$set(item, 'editting', false);
                    let edittingCell = {};
                    editableCell.forEach(item => {
                        edittingCell[item.key] = false;
                    });
                    this.$set(item, 'edittingCell', edittingCell);
                    return item;
                }
            });
            this.thisTableData = res;
            this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
            this.columnsList.forEach(item => {
                if (item.editable) {
                    item.render = (h, param) => {
                        let currentRow = this.thisTableData[param.index];
                        if (!currentRow.editting) {
                            if (this.editIncell) {
                                return h('Row', {
                                    props: {
                                        type: 'flex',
                                        align: 'middle',
                                        justify: 'center'
                                    }
                                }, [
                                    h('Col', {
                                        props: {
                                            span: '22'
                                        }
                                    }, [
                                        !currentRow.edittingCell[param.column.key] ? h('span', currentRow[item.key]) : cellInput(this, h, param, item)
                                    ]),
                                    h('Col', {
                                        props: {
                                            span: '2'
                                        }
                                    }, [
                                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                                    ])
                                ]);
                            } else {
                                return h('span', currentRow[item.key]);
                            }
                        } else {
                            return h('Input', {
                                props: {
                                    type: 'text',
                                    value: currentRow[item.key]
                                },
                                on: {
                                    'on-change' (event) {
                                        let key = param.column.key;
                                        vm.edittingStore[param.index][key] = event.target.value;
                                    }
                                }
                            });
                        }
                    };
                }
                if (item.handle) {
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.handle.forEach(item => {
                            if (item === 'edit') {
                                children.push(editButton(this, h, currentRowData, param.index));
                            } else if (item === 'delete') {
                                children.push(deleteButton(this, h, currentRowData, param.index));
                            } else if (item === 'view') {
                                children.push(viewButton(this, h, currentRowData, param.index));
                            } else if (item === 'ip'){
                                children.push(ipInput(this, h,currentRowData, param.index))
                            } else if (item === 'clear'){
                                children.push(clearButton(this, h,currentRowData, param.index))
                            } else if (item === 'remark'){
                                children.push(remarkInput(this, h,currentRowData, param.index))
                            }
                        });
                        return h('div', children);
                    };
                }
            });
        },
        handleBackdata (data) {
            let clonedData = JSON.parse(JSON.stringify(data));
            clonedData.forEach(item => {
                delete item.editting;
                delete item.edittingCell;
                delete item.saving;
            });
            return clonedData;
        }
    },
    watch: {
        value (data) {
            this.init();
        }
    }
};
</script>
