<style lang="less">
    @import '../../../../styles/common.less';
    @import './css.less';
</style>

<template>
    <div class="log">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/operation/white-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>

                <div class="edittable-con-1">
                   <!-- <can-edit-table refs="table1" v-model="s_rows" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>-->
                    <can-edit-table refs="table" @details="showDetails" @on-delete="handleDel" v-model="rows" :columns-list="columns" @set-remark="setRemark"></can-edit-table>
                    <!--class="listTable"-->
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>

        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import data from './data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'white_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                columns: [],
                s_columns: [],
                rows: [],
                s_rows: [{
                    ip: ''
                }],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                log_details: {},
                details: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'white_list'){
                    this.columns = [];
                    this.rows = [];
                    this.s_columns = [];
                    this.s_rows = [{
                        ip: ''
                    }];
                    this.total = 0;
                    this.showPage = false;
                    this.current = 1;
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData () {
                this.columns = data.l_Columns;
                this.s_columns = data.ls_Columns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_rows[0].ip){
                    s.push('ip=' + this.s_rows[0].ip);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/monitor_sameip_whitelist',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            showDetails (row){
                //console.log(row)
                var b_str = row.platform_response.replace(/\{/g,'\{\<br\>\&nbsp\;\&nbsp\;').replace(/\}/g,'\<br\>\}').replace(/\,/g,'\,<br>\&nbsp\;\&nbsp\;').replace(/\[/g,'\[\<br\>\<br\>').replace(/\]/g,'\<br\>\]');
                var str = JSON.stringify(JSON.parse(row.platform_response),null,4);
                this.log_details = row;
                this.log_details.platform_response = str;
                this.log_details.b_platform_response = b_str;
                this.details = true;
            },
            set_s_data (obj){
                this.s_rows = obj;
            },
            close (){
                this.log_details = {};
                this.details = false;
            },
            setRemark (row, i, remark){
                this.$set(this.rows[i],'remark',remark);
                var list_data = [];
                list_data = JSON.parse(JSON.stringify(this.rows));
                this.rows = [];
                this.rows = JSON.parse(JSON.stringify(list_data));
            },
        },
        created () {
            //this.getData();
        }
    };
</script>

<style>

</style>
