.row_details{

  p{
    display: flex;
    font-size: 14px;

    strong{
      width: 30%;
      text-align: right;
      padding-right:20px;
    }
    span{
      width: 70%;
      word-break: break-word;
      position: relative;

      textarea{
        width: 100%;
        position: absolute;
        left: 0;
        top:0;
        bottom:0;
        resize: none;
        border:none;
        box-shadow: none;
        /*background: #000;
        color: #fff;*/
      }
    }
  }
  &.msg_details{
    .ivu-modal{
      width: 800px !important;
    }
    strong{
      width: 25%;
    }
    span{
      width:75%;
    }
  }
}
.listTable{
  .ivu-table-wrapper{
    border-top: none;
  }
  .ivu-table-header{
    display: none;
  }
}
.main {
  .searchTable .ivu-table{
    td{
      height: 50px;
      background-color: #f2f3f6;

      input.ivu-input{
        height:28px;
        line-height: 28px;
      }
    }
    .ivu-table-cell{
      padding-left: 6px;
      padding-right: 6px;
    }
    .ivu-select-single{
      .ivu-select-selection,.ivu-select-placeholder{
        height:28px;
        line-height: 28px;
        border-radius: 0;
      }
      .ivu-select-selected-value{
        height:28px;
        line-height: 28px;
      }
    }
  }
}