<style lang="less">
    @import '../../../../styles/common.less';
    @import './css.less';
</style>

<template>
    <div class="whitelist">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑IP白名单
                </p>
                <Form :model="form" :label-width="90">
                    <FormItem prop="ip" label="IP：" required>
                        <Input v-model="form.ip" placeholder="请输入IP" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="remark" label="备注：">
                        <Input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注" style="width:500px;"></Input>

                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'white_edit',
        components: {

        },
        data () {
            return {
                form:{},
                errMsg: '',
                access_token: this.$store.state.user.access_token,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wxmonitor_group_edit'){
                    this.form = {};
                    this.errMsg = '';
                    this.getData();
                }
            },
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData (){
                this.form = this.$store.state.users.currentRow;
            },
            handleSubmit () {
                if(!this.form.ip){
                    this.$Notice.error({
                        title: '请输入IP！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/monitor_sameip_whitelist',
                    method:'PUT',
                    data: {
                        id: this.form.id,
                        ip: this.form.ip,
                        remark: this.form.remark
                    }
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'white_edit');
                        _this.$store.commit('closePage', 'white_edit');
                        _this.$router.push({
                            name: 'white_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {}
                this.$store.commit('removeTag', 'white_edit');
                this.$store.commit('closePage', 'white_edit');
                this.$router.push({
                    name: 'white_list'
                });
            }

        }
    };
</script>

<style>

</style>
