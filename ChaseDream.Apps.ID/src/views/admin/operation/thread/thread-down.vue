<style lang="less">
    @import '../../../../styles/common.less';
    @import './thread-down.less';
</style>

<template>
    <div class="thread-down">
        <Row>
            <Col span="24" class="padding-left-10">
            <Card>
                <h3>批量沉贴</h3>
                <br>
                <Form ref="form" :model="form" :label-width="0">
                    <FormItem prop="suject">
                        <Button type="primary" @click="alldown()" v-if="total > 0">批量沉贴</Button>
                        <Button type="primary" @click="alldown()" disabled v-if="total <= 0">批量沉贴</Button>
                        <span v-if="total > 0">（共<strong style="color: #00a050"> {{total}} </strong>条记录）</span>
                        <Input v-model="form.subject" placeholder="请输入帖子标题" @on-enter="handleSubmit()">
                            <Button slot="append" type="primary" @click="handleSubmit()">查询</Button>
                        </Input>
                    </FormItem>
                </Form>
                <br>
                <div class="edittable-con-1">
                    <can-edit-table refs="threadInfo" v-model="threadRows" :columns-list="threadColumns"></can-edit-table>
                    <!--@on-delete="handleDel"-->
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getblacklist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import threadData from './data/thread_down_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'thread_down',
        components: {
            canEditTable
        },
        data () {
            return {
                form: {
                    subject: ''
                },
                tid: [],
                isAccess: true,
                threadColumns: [],
                threadRows:[],
                current: 1,
                total: 0,
                showPage: false,
                page_size: 20
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'thread_down'){
                    this.threadRows = [];
                    this.form = {
                        subject: ''
                    };
                    this.tid = [];
                    this.current = 1;
                }
            }
        },
        mounted () {
            this.threadColumns = threadData.threadColumns
            //console.log(threadData)
        },
        methods: {
            handleSubmit (){
                this.threadRows = [];
                if (!!this.form.subject) {
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/user/thread_down?s=' + this.form.subject,
                        method:'GET',
                        params: {
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            console.log(res.data)
                            _this.threadRows = res.data.data.thread.rows;
                            _this.tid = res.data.data.tid;
                            _this.total = res.data.data.thread.count;
                            if(_this.total > _this.page_size){
                                _this.showPage = true;
                            }
                            //_this.current = 1;
                        }else{
                            _this.$Notice.error({
                                title: res.data.msg
                            });
                        }
                    }).catch(function (err) {
                        console.log(err)
                        _this.$Notice.error({
                            title: err.msg
                        });
                    });
                }else {
                    this.$Notice.error({
                        title: '请输入帖子标题！'
                    });
                }
            },
            handleReset (){
                this.threadRows = [];
                /*this.form = {
                    subject: ''
                };*/
                this.tid = [];
                this.current = 1;
                this.total = 0;
                this.showPage = false;
            },
            alldown (){
                if(this.tid.length <= 0){
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/user/thread_down?s=' + this.form.subject,
                    method:'POST',
                    data: {
                        tid: this.tid,
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '提交成功！'
                        });
                        _this.handleReset();
                        _this.handleSubmit();
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            }
        },
        created () {

        }
    };
</script>

<style>

</style>
