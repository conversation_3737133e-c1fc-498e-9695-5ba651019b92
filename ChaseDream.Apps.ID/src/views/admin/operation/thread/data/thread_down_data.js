export const threadColumns = [
    {
        title: 'TID',
        key: 'tid',
        align: 'center',
        width: 100
    },
    {
        title: '帖子标题',
        key: 'subject',
        align: 'center',
        render: (h, params) => {
            return h('a',{
                attrs: {
                    href: 'https://forum.chasedream.com/thread-'+ params.row.tid +'-1-1.html',
                    target: '_blank'
                }
            },params.row.subject)
        }
    },
    {
        title: '发帖用户',
        key: 'author',
        width: 150,
        align: 'center',
        render: (h, params) => {
            return h('a',{
                attrs: {
                    href: 'https://forum.chasedream.com/space-uid-'+ params.row.authorid +'.html',
                    target: '_blank'
                }
            },params.row.author)
        }
    },
    {
        title: 'UID',
        align: 'center',
        key: 'authorid',
        width: 100
    }
];

const threadData = {
    threadColumns: threadColumns
};

export default threadData;
