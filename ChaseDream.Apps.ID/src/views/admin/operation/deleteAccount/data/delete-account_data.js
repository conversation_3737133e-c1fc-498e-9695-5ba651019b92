export const infoColumns = [
    /*{
        //title: '',
        key: 'success',
        width: 80,
        align: 'center',
        render: (h, params) => {
            var style = {}
            if(!params.row.success){
                style.color = '#ed3f14';
            }else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size:14
                }
            })
        }
    },*/
    {
        title: '用户名',
        key: 'username',
        align: 'center',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/space-uid-' + params.row.uid + '.html',
                    target: '_blank'
                }
            }, params.row.username)
        }
    },
    {
        title: '头像',
        key: 'avatar_url',
        width: 80,
        align: 'center',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/space-uid-' + params.row.uid + '.html',
                    target: '_blank'
                }
            }, [
                h('img', {
                    style: {
                        width: '40px'
                    },
                    attrs: {
                        src: params.row.avatar_url
                    }
                })
            ])
        }
    },
    {
        title: 'UID',
        key: 'uid',
        width: 100,
        align: 'center',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/space-uid-' + params.row.uid + '.html',
                    target: '_blank'
                }
            }, params.row.uid)
        }
    },
    {
        title: '主题帖',
        align: 'center',
        key: 'threads',
        width: 100,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/home.php?mod=space&uid=' + params.row.uid + '&do=thread&view=me&type=thread&from=space',
                    target: '_blank'
                }
            }, params.row.threads)
        }
    },
    {
        title: '回复帖',
        align: 'center',
        key: 'posts',
        width: 100,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/home.php?mod=space&uid=' + params.row.uid + '&do=thread&view=me&type=reply&from=space',
                    target: '_blank'
                }
            }, params.row.posts)
        }
    },
    {
        title: '好友',
        align: 'center',
        key: 'friends',
        width: 100,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/home.php?mod=space&uid=' + params.row.uid + '&do=friend&view=me&from=space',
                    target: '_blank'
                }
            }, params.row.friends)
        }
    },
    {
        title: '记录',
        align: 'center',
        width: 100,
        key: 'doings',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/home.php?mod=space&uid=' + params.row.uid + '&do=doing&view=me&from=space',
                    target: '_blank'
                }
            }, params.row.doings)
        }
    },
    {
        title: '消息',
        align: 'center',
        key: 'pm',
        width: 100,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/admin.php?frames=yes&action=mynav&operation=pmmngr&senderid=' + params.row.uid,
                    target: '_blank'
                }
            }, params.row.pm)
        }
    },
    {
        title: '收藏',
        align: 'center',
        key: 'favorite',
        width: 100
    },
    {
        title: '微信',
        align: 'center',
        key: 'wechat',
        width: 100,
        render: (h, params) => {
            return h('span', {}, !!params.row.wechat ? '有' : '无')
        }
    }

];
export const blColumns = [
    {
        title: 'ID',
        key: 'id',
        //width: 80,
        align: 'center'
    },
    {
        title: '区号',
        align: 'center',
        key: 'area_code',
        //width: 80
        /*editable: true*/
    },
    {
        title: '手机号码',
        align: 'center',
        key: 'mobile'
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['delete']
    },
]

const infoData = {
    infoColumns: infoColumns,
    blColumns: blColumns
};

export default infoData;
