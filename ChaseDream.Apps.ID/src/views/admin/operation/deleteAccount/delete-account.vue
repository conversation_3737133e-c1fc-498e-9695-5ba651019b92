<style lang="less">
@import "../../../../styles/common.less";
@import "./delete-account.less";
</style>

<template>
  <div class="logout">
    <Row>
      <Col span="24" class="padding-left-10">
        <Card>
          <h3>账户注销</h3>
          <br />
          <Form ref="form" :model="form" :rules="rules" :label-width="0">
            <FormItem prop="username">
              <Input
                v-model="form.username"
                placeholder="请输入用户名"
                @on-enter="handleSubmit()"
              ></Input>
            </FormItem>
            <FormItem>
              <Button type="primary" @click="handleSubmit()">下一步</Button>
              <!--<Button type="ghost" @click="handleReset()" style="margin-left: 8px">重 置</Button>-->
            </FormItem>
          </Form>
          <br />
          <div class="" v-if="current === 1 && infoRows.length > 0">
            <h3>用户信息</h3>
            <br />
            <can-edit-table
              refs="userInfo"
              v-model="infoRows"
              :columns-list="infoColumns"
            ></can-edit-table>
            <br />

            <div class="logInfo">
              <h4>删除：</h4>
              <span
                ><Checkbox
                  :indeterminate="indeterminate"
                  :value="checkAll"
                  @click.prevent.native="CheckAll"
                  >全选</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.threads"
                  :true-value="1"
                  :false-value="0"
                  :disabled="infoRows[0].threads > 0 ? false : true"
                  @on-change="checkAllGroupChange"
                  >主题</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.posts"
                  :true-value="1"
                  :false-value="0"
                  :disabled="infoRows[0].posts > 0 ? false : true"
                  @on-change="checkAllGroupChange"
                  >回复</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.friends"
                  :true-value="1"
                  :false-value="0"
                  :disabled="infoRows[0].friends > 0 ? false : true"
                  @on-change="checkAllGroupChange"
                  >好友</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.doings"
                  :true-value="1"
                  :false-value="0"
                  :disabled="infoRows[0].doings > 0 ? false : true"
                  @on-change="checkAllGroupChange"
                  >记录</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.pm"
                  :true-value="1"
                  :false-value="0"
                  :disabled="infoRows[0].pm > 0 ? false : true"
                  @on-change="checkAllGroupChange"
                  >消息</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.favorite"
                  :true-value="1"
                  :false-value="0"
                  :disabled="infoRows[0].favorite > 0 ? false : true"
                  @on-change="checkAllGroupChange"
                  >收藏</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.wechat"
                  :true-value="1"
                  :false-value="0"
                  :disabled="!!infoRows[0].wechat ? false : true"
                  @on-change="checkAllGroupChange"
                  >微信</Checkbox
                ></span
              >
              <span
                ><Checkbox
                  v-model="del_info.avatar"
                  :true-value="1"
                  :false-value="0"
                  :disabled="!!infoRows[0].avatar ? false : true"
                  @on-change="checkAllGroupChange"
                  >头像</Checkbox
                ></span
              >
              <br /><br />
              <!--<p><Button type="primary" @click="delInfo()">下一步</Button></p>-->
              <Poptip
                v-if="current === 1"
                confirm
                title="您确定注销此账户吗？"
                @on-ok="delInfo()"
                placement="top-start"
              >
                <Button type="primary">确定</Button>
              </Poptip>
            </div>
          </div>

          <Alert type="success" show-icon v-if="current === 2">
            注销完成
            <div slot="desc">
              <p>
                用户名：
                <a :href="logout_info.space" target="_blank">{{
                  logout_info.new_username
                }}</a>
              </p>
              <p>邮箱： {{ logout_info.new_email }}</p>
            </div>
          </Alert>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import infoData from "./data/delete-account_data.js";
import canEditTable from "./components/canEditTable.vue";

export default {
  name: "delete_account",
  components: {
    canEditTable,
  },
  data() {
    return {
      form: {
        username: "",
      },
      isAccess: true,
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
          {
            type: "string",
            min: 1,
            message: "用户名不能小于1个字符",
            trigger: "blur",
          },
        ],
      },
      infoColumns: [],
      infoRows: [],
      disabled: true,
      del_info: {
        threads: 0,
        posts: 0,
        friends: 0,
        doings: 0,
        pm: 0,
        favorite: 0,
        wechat: 0,
        avatar: 0,
      },
      logout_info: {
        new_username: "",
        new_email: "",
        space: "",
      },
      current: 1,
      indeterminate: true,
      checkAll: false,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "delete_account") {
        this.del_info = {
          threads: 0,
          posts: 0,
          friends: 0,
          doings: 0,
          pm: 0,
          favorite: 0,
          wechat: 0,
          avatar: 0,
        };
        this.infoRows = [];
        this.form = {
          username: "",
        };
        this.current = 1;
        this.indeterminate = true;
        this.checkAll = false;
      }
    },
  },
  mounted() {
    this.infoColumns = infoData.infoColumns;

    let param = new URLSearchParams(window.location.search);
    let username = param.get("username");

    if (username) {
      this.form.username = username;
      this.handleSubmit();
    }
  },
  methods: {
    handleSubmit() {
      this.infoRows = [];
      this.$refs.form.validate((valid) => {
        if (valid) {
          var _this = this;
          util
            .ajax({
              url:
                "/api/v1/admin/user/account_close?username=" +
                encodeURIComponent(this.form.username),
              method: "GET",
              params: {},
            })
            .then(function(res) {
              if (res.data.msg === "success") {
                _this.infoRows.push(res.data.data);
                _this.current = 1;
                if (!!_this.infoRows[0].wechat) {
                  _this.del_info.wechat = 1;
                }
                if (!!_this.infoRows[0].avatar) {
                  _this.del_info.avatar = 1;
                }
              } else {
                _this.$Notice.error({
                  title: res.data.msg,
                });
              }
            })
            .catch(function(err) {
              console.log(err);
              _this.$Notice.error({
                title: err.msg,
              });
            });
        }
      });
    },
    handleReset() {
      this.del_info = {
        threads: 0,
        posts: 0,
        friends: 0,
        doings: 0,
        pm: 0,
        favorite: 0,
        wechat: 0,
        avatar: 0,
      };
      this.infoRows = [];
      this.form = {
        username: "",
      };
      this.indeterminate = true;
      this.checkAll = false;
    },
    delInfo() {
      this.logout_info = [];
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/user/account_close",
          method: "POST",
          data: {
            username: encodeURIComponent(this.form.username),
            threads: this.del_info.threads,
            posts: this.del_info.posts,
            friends: this.del_info.friends,
            doings: this.del_info.doings,
            pm: this.del_info.pm,
            favorite: this.del_info.favorite,
            wechat: this.del_info.wechat,
            avatar: this.del_info.avatar,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            /*_this.$Notice.success({
                            title: '重命名成功！'
                        });*/
            _this.current = 2;
            _this.logout_info = res.data.data;
            _this.handleReset();
          } else {
            //_this.errMsg = res.msg;
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          //_this.errMsg = err.msg;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    logoutInfo() {
      this.current = 2;
      this.del_info = {
        threads: 0,
        posts: 0,
        friends: 0,
        doings: 0,
        pm: 0,
        favorite: 0,
        wechat: 0,
        avatar: 0,
      };
    },
    CheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        if (this.infoRows[0].threads > 0) {
          this.del_info.threads = 1;
        }
        if (this.infoRows[0].posts > 0) {
          this.del_info.posts = 1;
        }
        if (this.infoRows[0].friends > 0) {
          this.del_info.friends = 1;
        }
        if (this.infoRows[0].doings > 0) {
          this.del_info.doings = 1;
        }
        if (this.infoRows[0].pm > 0) {
          this.del_info.pm = 1;
        }
        if (this.infoRows[0].favorite > 0) {
          this.del_info.favorite = 1;
        }
        if (!!this.infoRows[0].wechat) {
          this.del_info.wechat = 1;
        }
        if (!!this.infoRows[0].avatar) {
          this.del_info.avatar = 1;
        }
      } else {
        this.del_info.threads = 0;
        this.del_info.posts = 0;
        this.del_info.friends = 0;
        this.del_info.doings = 0;
        this.del_info.pm = 0;
        this.del_info.favorite = 0;
        this.del_info.wechat = 0;
        this.del_info.avatar = 0;
      }
    },
    checkAllGroupChange(data) {
      var n = 0;
      var m = 0;
      if (this.infoRows[0].threads > 0) {
        n++;
      }
      if (this.infoRows[0].posts > 0) {
        n++;
      }
      if (this.infoRows[0].friends > 0) {
        n++;
      }
      if (this.infoRows[0].doings > 0) {
        n++;
      }
      if (this.infoRows[0].pm > 0) {
        n++;
      }
      if (this.infoRows[0].favorite > 0) {
        n++;
      }
      if (!!this.infoRows[0].wechat) {
        n++;
      }
      if (!!this.infoRows[0].avatar) {
        n++;
      }

      if (this.del_info.threads > 0) {
        m++;
      }
      if (this.del_info.posts > 0) {
        m++;
      }
      if (this.del_info.friends > 0) {
        m++;
      }
      if (this.del_info.doings > 0) {
        m++;
      }
      if (this.del_info.pm > 0) {
        m++;
      }
      if (this.del_info.wechat > 0) {
        m++;
      }
      if (this.del_info.wechat > 0) {
        m++;
      }
      if (this.del_info.avatar > 0) {
        m++;
      }

      if (m === n) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (m > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
  },
  created() {},
};
</script>

<style></style>
