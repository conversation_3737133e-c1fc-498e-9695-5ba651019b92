<style lang="less">
    @import '../../../../styles/common.less';
    @import './fish.less';
</style>

<template>
    <div class="log">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_rows" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>
                    <can-edit-table refs="table" @details="showDetails" v-model="rows" :columns-list="columns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <!--<Modal v-model="details" title="日志详情" class="log_details">
            <p>
                <strong>ID</strong>
                <span>{{log_details.id}}</span>
            </p>
            <p>
                <strong>区号</strong>
                <span>{{log_details.area_code}}</span>
            </p>
            <p>
                <strong>手机号</strong>
                <span>{{log_details.mobile}}</span>
            </p>
            &lt;!&ndash;<p>
                <strong>biz_id</strong>
                <span>{{log_details.aliyun_biz_id}}</span>
            </p>
            <p>
                <strong>阿里云码</strong>
                <span>{{log_details.aliyun_code}}</span>
            </p>
            <p>
                <strong>阿里云信息</strong>
                <span>{{log_details.aliyun_message}}</span>
            </p>
            <p>
                <strong>阿里云请求ID</strong>
                <span>{{log_details.aliyun_request_id}}</span>
            </p>&ndash;&gt;
            <p>
                <strong>IP地址</strong>
                <span>{{log_details.ip}}</span>
            </p>
            <p>
                <strong>平台</strong>
                <span>{{log_details.platform}}</span>
            </p>
            <p>
                <strong>平台返回信息</strong>
                <span>
                    <code v-html="log_details.b_platform_response" style="opacity: 0"></code>
                    <textarea disabled>{{log_details.platform_response}}</textarea>
                </span>
            </p>
            <p>
                <strong>验证码</strong>
                <span>{{log_details.captcha}}</span>
            </p>
            <p>
                <strong>状态</strong>
                <span>{{log_details.success}}</span>s
            </p>
            <p>
                <strong>特征码</strong>
                <span>{{log_details.token}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span>{{log_details.created_at}}</span>
            </p>

            &lt;!&ndash;<p>
                <strong>时间戳</strong>
                <span>{{log_details.timestamp}}</span>
            </p>
            <p>
                <strong>更新时间</strong>
                <span>{{log_details.updated_at}}</span>
            </p>&ndash;&gt;
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>-->
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import data from './fish_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'fish_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                columns: [],
                s_columns: [],
                rows: [],
                s_rows: [{
                    username: '',
                    ip: ''
                }],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                log_details: {},
                details: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'fish_list'){
                    this.columns = [];
                    this.rows = [];
                    this.s_columns = [];
                    this.s_rows = [{
                        username: '',
                        ip: ''
                    }];
                    this.total = 0;
                    this.showPage = false;
                    this.current = 1;
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData () {
                this.columns = data.f_Columns;
                this.s_columns = data.s_Columns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                /*if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }*/
                if(!!this.s_rows[0].username){
                    s.push('username=' + this.s_rows[0].username);
                }
                if(!!this.s_rows[0].ip){
                    s.push('ip=' + this.s_rows[0].ip);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'api/v1/admin/misc/fish' + text,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            showDetails (row){
                //console.log(row)
                var b_str = row.platform_response.replace(/\{/g,'\{\<br\>\&nbsp\;\&nbsp\;').replace(/\}/g,'\<br\>\}').replace(/\,/g,'\,<br>\&nbsp\;\&nbsp\;').replace(/\[/g,'\[\<br\>\<br\>').replace(/\]/g,'\<br\>\]');
                var str = JSON.stringify(JSON.parse(row.platform_response),null,4);
                this.log_details = row;
                this.log_details.platform_response = str;
                this.log_details.b_platform_response = b_str;
                this.details = true;
            },
            set_s_data (obj){
                this.s_rows = obj;
            },
            close (){
                this.log_details = {};
                this.details = false;
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
