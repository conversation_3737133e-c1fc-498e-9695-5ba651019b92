export const f_Columns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    {
        title: '用户名',
        key: 'username',
        align: 'center',
        render: (h, params) => {
            return h('a',{
                attrs: {
                    href: 'https://forum.chasedream.com/space-username-'+ params.row.username +'.html',
                    target: '_blank'
                }
            },params.row.username)
        }
    },
    {
        title: 'IP',
        key: 'ip',
        width: 120,
        align: 'center'
    },
    {
        title: '创建时间',
        align: 'center',
        width: 160,
        key: 'created_at',
        render: (h, params) => {
            if(!!params.row.created_at){
                var d_t = new Date(parseInt(params.row.created_at + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 131,
        key: 'handle',
        handle: []
    }
];
export const s_Columns = [
    {
        title: 'ID',
        key: 'handle',
        width: 80,
        align: 'center',
        handle: ['id']
    },
    {
        title: '用户名',
        key: 'handle',
        align: 'center',
        handle: ['username']
    },
    {
        title: 'IP',
        key: 'handle',
        width: 120,
        align: 'center',
        handle: ['ip']
    },
    {
        title: '创建时间',
        align: 'center',
        width: 160,
        key: 'handle',
        handle: ['created_at']

    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['clear']
    }
];

const f_data = {
    f_Columns: f_Columns,
    s_Columns: s_Columns
};

export default f_data;
