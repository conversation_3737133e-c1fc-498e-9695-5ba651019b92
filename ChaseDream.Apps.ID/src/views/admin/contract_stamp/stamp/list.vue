<style lang="less">
    @import '../../../../styles/common.less';
    @import '../stamp.less';
</style>

<template>
    <div class="stamp" style="min-width:1100px;">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="edittable-con-1">
                    <can-edit-table refs="table" @on-delete="handleDel" v-model="tRows" :columns-list="tColumns" @details="showDetails"  @set-remark="setRemark" @role-stamp="role_stamp"></can-edit-table>
                    <Spin size="large" fix v-if="spinShow"></Spin>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import stampData from '../data/stamp_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'stamp_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                tColumns: [],
                tRows:[],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                stamps: [],
                users: [],
                spinShow: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'stamp_list'){
                    this.isAccess = true;
                    this.tColumns = [];
                    this.tRows = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.stamps = [];
                    this.users = [];
                    this.spinShow = false;
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.get_roles();
            },
            set_cloumns (){
                this.tColumns.push({
                    title: '#',
                    key: 'realname',
                    width: 130,
                    align: 'center',
                    render: (h, params) => {
                        return h('div',{},[
                            h('p',{},params.row.realname),
                            h('p',{
                                style: {
                                    color: '#2d8cf0'
                                }
                            },params.row.username)
                        ]);
                    }
                });
              for(var i=0;i<this.stamps.length;i++){
                  var item = {
                      title: this.stamps[i].name,
                      align: 'center',
                      key: 'handle',
                      /*width: '',*/
                      handle: ['stamp_' + this.stamps[i].id]
                  }
                  this.tColumns.push(item);
              }
            },
            set_rows (){
                for(var i=0;i<this.users.length;i++){
                    var item = {
                        uid: !!this.users[i] ? this.users[i].id : 0,
                        username: !!this.users[i] ? this.users[i].username : '',
                        realname: !!this.users[i] ? this.users[i].realname : '',
                        stamps: this.users[i].stamps
                    }

                    this.tRows.push(item);
                }
            },
            get_roles (){
                this.stamps = [];
                this.users = [];
                this.tColumns = [];
                this.tRows = [];
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/contract_stamp/stamps/roles',
                    method: 'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.stamps = res.data.data.stamps;
                        _this.set_cloumns();
                        _this.users = res.data.data.users;
                        _this.set_rows();

                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].apply_username){
                    s.push('apply_username=' + this.s_row[0].apply_username)
                }
                if(!!this.s_row[0].contract_name){
                    s.push('contract_name=' + this.s_row[0].contract_name)
                }
                if(!!this.s_row[0].remark){
                    s.push('remark=' + this.s_row[0].remark)
                }
                if(!!this.s_row[0].approve_username){
                    s.push('approve_username=' + this.s_row[0].approve_username)
                }
                if(this.s_row[0].department === 1 || this.s_row[0].department === 2 || this.s_row[0].department === 3 || this.s_row[0].department === 4){
                    s.push('department=' + this.s_row[0].department);
                }
                if(this.s_row[0].status === -2 || this.s_row[0].status === -1 || this.s_row[0].status === 3 || this.s_row[0].status === 4 || this.s_row[0].status === 5){
                    s.push('status=' + this.s_row[0].status);
                }
                if(!!this.s_row[0].begin){
                    var str = Math.round(new Date(this.s_row[0].begin.substring(0,4) +'/'+ this.s_row[0].begin.substring(4)).getTime()/1000);
                    s.push('begin=' + str);
                }
                if(!!this.s_row[0].end){
                    var str = Math.round(new Date(this.s_row[0].end.substring(0,4) +'/'+ this.s_row[0].end.substring(4)).getTime()/1000);
                    s.push('end=' + str);
                }
                if(s.length > 0){
                    text ='?' + s.join('&');
                }

                this.tRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/contract_stamp' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.tRows = res.data.data.rows;
                        for(var i=0;i<_this.tRows.length;i++){
                            _this.tRows[i].isedit = false;
                        }
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            showDetails (row){
                console.log(row)
                this.list_details = row;
                this.list_details.c_at = ''
                this.list_details.a_at = ''
                if(!!this.list_details.created_at){
                    var d_t = new Date(parseInt(this.list_details.created_at + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    this.list_details.c_at = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;
                }
                if(!!this.list_details.approve_at){
                    var d_t = new Date(parseInt(this.list_details.approve_at + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    this.list_details.a_at = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;
                }
                this.details = true;
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            setRemark(row, i, remark){
                this.tRows[i].remark = remark;
                var list_data = [];
                list_data = JSON.parse(JSON.stringify(this.tRows));
                this.tRows = [];
                this.tRows = JSON.parse(JSON.stringify(list_data));
            },
            get_row_data (n){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/contract_stamp/' + this.tRows[n].id,
                    method: 'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.tRows[n] = res.data.data;

                        var list_data = [];
                        list_data = JSON.parse(JSON.stringify(_this.tRows));
                        _this.tRows = [];
                        _this.tRows = JSON.parse(JSON.stringify(list_data));

                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            role_stamp (row,index,stamp_id,val1,val2){
                this.spinShow = true;
                if(!row.uid){
                    this.$Notice.error({
                        title: '设置失败'
                    });
                    return false;
                }
                var dataIn = {
                    uid: row.uid,
                    stamp_id: stamp_id,
                    apply: val1,
                    approve: val2
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/contract_stamp/stamps/roles',
                    method: 'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.spinShow = false;
                        _this.$Notice.success({
                            title: '设置成功'
                        });
                        _this.getData();
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.spinShow = false;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            reset_row (index,stamp_id,str, val){
                for(var i=0;i<this.tRows[index].stamps.length;i++){
                    if(this.tRows[index].stamps[i].id === stamp_id){
                        if(str === 'apply'){
                            this.tRows[index].stamps[i].apply = val;
                        }else if(str === 'approve'){
                            this.tRows[index].stamps[i].approve = val;
                        }
                    }
                }

                var list_data = [];
                list_data = JSON.parse(JSON.stringify(this.tRows));
                this.tRows = [];
                this.tRows = JSON.parse(JSON.stringify(list_data));
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
