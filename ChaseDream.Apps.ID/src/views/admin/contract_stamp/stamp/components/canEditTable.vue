<style lang="less">
@import "editable-table.less";
</style>

<template>
  <div>
    <Table
      :ref="refs"
      :columns="columnsList"
      :data="thisTableData"
      border
    ></Table>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

const stamp_1 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 1) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 1, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 1, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_2 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 2) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 2, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 2, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_3 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 3) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 3, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 3, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_4 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 4) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 4, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 4, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_5 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 5) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 5, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 5, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_6 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 6) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 6, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 6, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_7 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 7) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 7, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 7, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_8 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 8) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 8, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 8, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_9 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 9) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 9, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 9, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_10 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 10) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 10, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 10, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};
const stamp_11 = (vm, h, currentRow, index) => {
  var apply = 0;
  var approve = 0;
  for (var i = 0; i < currentRow.stamps.length; i++) {
    if (currentRow.stamps[i].stamp_id === 11) {
      apply = currentRow.stamps[i].apply;
      approve = currentRow.stamps[i].approve;
    }
  }
  return h("div", [
    h(
      "Checkbox",
      {
        props: {
          value: !!apply ? true : false,
        },
        style: {
          marginRight: "15px",
        },
        on: {
          "on-change": (e) => {
            if (!!e) {
              apply = 1;
            } else {
              apply = 0;
            }

            vm.$emit("role-stamp", currentRow, index, 11, apply, approve);
          },
        },
      },
      "申请"
    ),
    h(
      "Checkbox",
      {
        props: {
          value: !!approve ? true : false,
        },
        on: {
          "on-change": (e) => {
            //approve = e;
            if (!!e) {
              approve = 1;
            } else {
              approve = 0;
            }
            vm.$emit("role-stamp", currentRow, index, 11, apply, approve);
          },
        },
      },
      "批准"
    ),
  ]);
};

const clearButton = (vm, h, currentRow, index) => {
  return h(
    "span",
    {
      props: {},
      style: {
        margin: "0 8px",
        color: "#2d8cf0",
        cursor: "pointer",
      },
      on: {
        click: () => {
          vm.$set(vm.thisTableData[0], "apply_username", "");
          vm.$set(vm.thisTableData[0], "contract_name", "");
          vm.$set(vm.thisTableData[0], "remark", "");
          vm.$set(vm.thisTableData[0], "approve_username", "");
          vm.$set(vm.thisTableData[0], "department", 0);
          vm.$set(vm.thisTableData[0], "status", 0);
          vm.$set(vm.thisTableData[0], "begin", "");
          vm.$set(vm.thisTableData[0], "end", "");
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    "清除"
  );
};
export default {
  name: "canEditTable",
  props: {
    refs: String,
    columnsList: Array,
    value: Array,
    url: String,
    editIncell: {
      type: Boolean,
      default: false,
    },
    hoverShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      columns: [],
      thisTableData: [],
      edittingStore: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let vm = this;
      let editableCell = this.columnsList.filter((item) => {
        if (item.editable) {
          if (item.editable === true) {
            return item;
          }
        }
      });
      let cloneData = JSON.parse(JSON.stringify(this.value));
      let res = [];
      res = cloneData.map((item, index) => {
        let isEditting = false;
        if (this.thisTableData[index]) {
          if (this.thisTableData[index].editting) {
            isEditting = true;
          } else {
            for (const cell in this.thisTableData[index].edittingCell) {
              if (this.thisTableData[index].edittingCell[cell] === true) {
                isEditting = true;
              }
            }
          }
        }
        if (isEditting) {
          return this.thisTableData[index];
        } else {
          this.$set(item, "editting", false);
          let edittingCell = {};
          editableCell.forEach((item) => {
            edittingCell[item.key] = false;
          });
          this.$set(item, "edittingCell", edittingCell);
          return item;
        }
      });
      this.thisTableData = res;
      this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
      this.columnsList.forEach((item) => {
        if (item.editable) {
          item.render = (h, param) => {
            let currentRow = this.thisTableData[param.index];
            if (!currentRow.editting) {
              if (this.editIncell) {
                return h(
                  "Row",
                  {
                    props: {
                      type: "flex",
                      align: "middle",
                      justify: "center",
                    },
                  },
                  [
                    h(
                      "Col",
                      {
                        props: {
                          span: "22",
                        },
                      },
                      [
                        !currentRow.edittingCell[param.column.key]
                          ? h("span", currentRow[item.key])
                          : cellInput(this, h, param, item),
                      ]
                    ),
                    h(
                      "Col",
                      {
                        props: {
                          span: "2",
                        },
                      },
                      [
                        currentRow.edittingCell[param.column.key]
                          ? saveIncellEditBtn(this, h, param)
                          : incellEditBtn(this, h, param),
                      ]
                    ),
                  ]
                );
              } else {
                return h("span", currentRow[item.key]);
              }
            } else {
              return h("Input", {
                props: {
                  type: "text",
                  value: currentRow[item.key],
                },
                on: {
                  "on-change"(event) {
                    let key = param.column.key;
                    vm.edittingStore[param.index][key] = event.target.value;
                  },
                },
              });
            }
          };
        }
        if (item.handle) {
          item.render = (h, param) => {
            let currentRowData = this.thisTableData[param.index];
            let children = [];
            item.handle.forEach((item) => {
              if (item === "edit") {
                children.push(editButton(this, h, currentRowData, param.index));
              } else if (item === "delete") {
                children.push(
                  deleteButton(this, h, currentRowData, param.index)
                );
              } else if (item === "view") {
                children.push(viewButton(this, h, currentRowData, param.index));
              } else if (item === "stamp_1") {
                children.push(stamp_1(this, h, currentRowData, param.index));
              } else if (item === "stamp_2") {
                children.push(stamp_2(this, h, currentRowData, param.index));
              } else if (item === "stamp_3") {
                children.push(stamp_3(this, h, currentRowData, param.index));
              } else if (item === "stamp_4") {
                children.push(stamp_4(this, h, currentRowData, param.index));
              } else if (item === "stamp_5") {
                children.push(stamp_5(this, h, currentRowData, param.index));
              } else if (item === "stamp_6") {
                children.push(stamp_6(this, h, currentRowData, param.index));
              } else if (item === "stamp_7") {
                children.push(stamp_7(this, h, currentRowData, param.index));
              } else if (item === "stamp_8") {
                children.push(stamp_8(this, h, currentRowData, param.index));
              } else if (item === "stamp_9") {
                children.push(stamp_9(this, h, currentRowData, param.index));
              } else if (item === "stamp_10") {
                children.push(stamp_10(this, h, currentRowData, param.index));
              } else if (item === "stamp_11") {
                children.push(stamp_11(this, h, currentRowData, param.index));
              } else if (item === "clear") {
                children.push(
                  clearButton(this, h, currentRowData, param.index)
                );
              }
            });
            return h("div", children);
          };
        }
      });
    },
    handleBackdata(data) {
      let clonedData = JSON.parse(JSON.stringify(data));
      clonedData.forEach((item) => {
        delete item.editting;
        delete item.edittingCell;
        delete item.saving;
      });
      return clonedData;
    },
  },
  watch: {
    value(data) {
      this.init();
    },
  },
};
</script>
