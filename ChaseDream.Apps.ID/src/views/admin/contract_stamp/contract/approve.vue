<style lang="less">
@import "../../../../styles/common.less";
@import "../stamp.less";
</style>

<template>
  <div class="contract-approve" id="pageContent">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <p slot="title">
            <Icon type="flash-off"></Icon>
            {{
              !!contract_info.isAdmin || !!contract_info.isApprove
                ? "盖章/批准"
                : "申请印章位置"
            }}
          </p>
          <div class="approve">
            <div id="" class="pdf-box" ref="pdfBox">
              <div id="pdf-container">
                <div class="pdf-header">
                  <h3>合同预览</h3>
                  <div class="pages">
                    <Page
                      :current="showIndex"
                      :total="pdfs.length"
                      :page-size="1"
                      simple
                      @on-change="showPdfs"
                    ></Page>
                    <Button type="ghost" class="to-last" @click="to_last"
                      >跳转至最后一页</Button
                    >
                  </div>
                  <div
                    class="approve_status"
                    v-if="!!contract_info.isAdmin || !!contract_info.isApprove"
                  >
                    <Checkbox v-model="status1" @on-change="change_status(-1)"
                      >拒绝</Checkbox
                    >
                    <Checkbox v-model="status2" @on-change="change_status(-2)"
                      >发回修改</Checkbox
                    >
                    <Modal
                      v-model="show_reason"
                      :mask-closable="false"
                      :closable="false"
                    >
                      <p slot="header">
                        <Icon
                          type="information-circled"
                          style="color:#f60;"
                        ></Icon>
                        <span>原因</span>
                      </p>
                      <div>
                        <Input
                          v-model="reason"
                          type="textarea"
                          :autosize="{ minRows: 4, maxRows: 5 }"
                          placeholder="请输入驳回原因"
                        ></Input>
                      </div>
                      <div slot="footer">
                        <Button @click="sub_reason" type="primary">提交</Button>
                        <Button @click="cancel_status">取消</Button>
                      </div>
                    </Modal>
                  </div>
                </div>
                <div class="pdf-container" style="" v-if="show_pdf">
                  <img
                    :src="pdf"
                    alt=""
                    class="page_img"
                    v-for="(pdf, index) in pdfs"
                    v-if="showIndex === index + 1"
                  />

                  <div
                    :class="'sign-img mark common_mark page_' + index"
                    style="position: absolute; cursor: pointer;"
                    v-for="(stamp, index) in stamp_positons"
                    :key="stamp.index"
                    v-if="
                      index + 1 === showIndex && (stamp.x > 0 || stamp.y > 0)
                    "
                  >
                    <!--v-if="isCommonShow"-->
                    <div class="delete" style="display: none;">删除</div>
                    <img
                      :src="sealList[0].url"
                      alt=""
                      style="width: 154px;"
                      v-if="!!sealList[0]"
                    />
                  </div>
                  <div
                    class="sign-img mark corss_mark"
                    style="position: absolute; cursor: pointer;display: none"
                    v-if="isCrossShow"
                  >
                    <div class="delete" style="display: none;">删除</div>
                    <img
                      :src="sealList[0].url"
                      alt=""
                      style="width: 154px;"
                      v-if="!!sealList[0]"
                    />
                  </div>
                </div>
              </div>
              <div class="side-bar">
                <div class="side-barinfo">
                  <h2>印章</h2>
                  <div style="padding: 0 5px;">
                    <div
                      v-for="(item, index) in sealList"
                      @click="check_stamp(index)"
                      :key="item.id"
                      :class="
                        !!item.checked ? 'drag_seal checked' : 'drag_seal'
                      "
                      :sealId="item.id"
                      @mousedown="signPic"
                      v-if="!!item.checked || !!contract_info.isAdmin"
                    >
                      <div
                        class="delete"
                        @click.stop="deleteIt"
                        style="display: none;"
                      >
                        删除
                      </div>
                      <img :src="item.url" alt="" style="width: 154px;" />
                      <span class="stamp_name">{{ item.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="sub-btns">
              <Button
                @click="handleSubmit"
                type="primary"
                style="position: relative;"
                v-if="
                  !!contract_info.isAdmin ||
                    !!contract_info.isApprove ||
                    !!contract_info.isApply
                "
              >
                {{
                  !!contract_info.isAdmin || !!contract_info.isApprove
                    ? "批准"
                    : "提交"
                }}
                <Spin fix v-if="spinShow">
                  <Icon
                    type="load-c"
                    size="18"
                    class="demo-spin-icon-load"
                  ></Icon>
                </Spin>
              </Button>
              <Button @click="closePage">取消</Button>
            </div>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

export default {
  name: "contract_approve",
  components: {},
  data() {
    return {
      pdfs: [],
      sealList: [],
      scrollTop: 0,
      s_t: 0,
      id: 0,
      showIndex: 1,
      contract_info: {},
      isCommonShow: false,
      isCrossShow: false,
      stamp_type: 0,
      status: false,
      status1: false,
      status2: false,
      status3: false,
      old_status: -9,
      show_reason: false,
      reason: "",
      roles: [],
      stamp_positons: [],
      stamps: [],
      stamp: 0,
      isApprove: false,
      spinShow: false,
      access_token: this.$store.state.user.access_token,
      isAccess: true,
      show_pdf: false,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "contract_approve") {
        this.pdfs = [];
        this.sealList = [];
        this.scrollTop = 0;
        this.s_t = 0;
        this.showIndex = 1;
        this.id = 0;
        this.contract_info = {};
        this.isCommonShow = false;
        this.isCrossShow = false;
        this.stamp_type = 0;
        this.status = false;
        this.status1 = false;
        this.status2 = false;
        this.status3 = false;
        this.old_status = -9;
        this.show_reason = false;
        this.reason = "";
        this.roles = [];
        this.isApprove = false;
        this.isApply = false;
        this.spinShow = false;
        this.stamp_positons = [];
        this.stamps = [];
        this.stamp = 0;
        this.show_pdf = false;
        this.get_seal();
        this.get_pdf();
      }
    },
  },
  mounted() {
    this.get_seal();
    this.get_pdf();

    var scrollDom = document.getElementsByClassName("single-page-con")[0];
    scrollDom.addEventListener("scroll", this.handleScroll, false);
  },
  methods: {
    get_pdf() {
      this.id = window.location.href.split("/").pop();
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/preview/" + this.id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.show_pdf = true;
            _this.pdfs = res.data.data.images;
            setTimeout(function() {
              _this.show_common_mark(0);
              _this.show_corss_mark();
            }, 600);
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    get_seal() {
      this.id = window.location.href.split("/").pop();
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/" + this.id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.contract_info = res.data.data;
            _this.old_status = _this.contract_info.status;
            if (_this.contract_info.status === -1) {
              _this.status1 = true;
            } else if (_this.contract_info.status === -2) {
              _this.status2 = true;
            } else if (_this.contract_info.status === 4) {
              _this.status3 = true;
            }
            _this.reason = _this.contract_info.message;
            _this.showIndex = !!_this.contract_info.stamp_page
              ? _this.contract_info.stamp_page
              : 1;
            _this.stamp_type = _this.contract_info.stamp_type;
            _this.isCommonShow =
              !!_this.contract_info.common_x || !!_this.contract_info.common_y
                ? true
                : false;
            _this.isCrossShow = !!_this.contract_info.cross_page_y
              ? true
              : false;
            _this.stamp_positons = !!_this.contract_info.stamp_positons
              ? _this.contract_info.stamp_positons
              : [];
            _this.stamp_positons.isAdmin = false;
            _this.stamp_positons.isApply = false;
            _this.stamp_positons.isApprove = false;
            _this.get_stamp(_this.contract_info.stamp);
            _this.stamp = _this.contract_info.stamp;

            _this.get_perms();
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    get_stamp(stamp) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/stamps",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            for (var i = 0; i < res.data.data.length; i++) {
              var seal = {
                url: res.data.data[i].url,
                id: "seal" + res.data.data[i].id,
                checked: false,
                name: res.data.data[i].name,
              };
              if (res.data.data[i].id === stamp) {
                seal.checked = true;
              }
              _this.sealList.push(seal);
            }
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    showPdfs(n) {
      this.showIndex = n;
      var _this = this;
      setTimeout(function() {
        _this.show_common_mark(n - 1);
      }, 600);
      var marks = document.querySelectorAll(".mark");
      for (var i = 0; i < marks.length; i++) {
        if (
          marks[i].className.split("_")[1] === "mark" ||
          parseInt(marks[i].className.split("_")[1]) === this.showIndex
        ) {
          marks[i].style.display = "block";
        } else {
          marks[i].style.display = "none";
        }
      }
    },
    show_common_mark() {
      let dom = document.querySelector(".common_mark");
      if (
        !!dom &&
        (this.stamp_positons[this.showIndex - 1].x > 0 ||
          this.stamp_positons[this.showIndex - 1].y > 0)
      ) {
        dom.style.left = this.stamp_positons[this.showIndex - 1].x / 3 + "px";
        dom.style.top = this.stamp_positons[this.showIndex - 1].y / 3 + "px";
        dom.className = "sign-img mark common_mark page_" + this.showIndex;
        dom.style.display = "block";
      }
      if (!!dom) {
        dom.onmousedown = this.moveTo;
        dom.onmouseenter = this.mouseenter;
        dom.onmouseleave = this.mouseleave;
        var _this = this;
        dom.childNodes[0].onclick = this.deleteIt;
      }
    },
    show_corss_mark() {
      let cross_dom = document.querySelector(".corss_mark");
      if (!!cross_dom && !!this.contract_info.cross_page_y) {
        cross_dom.style.left = "inherit";
        cross_dom.style.right = 0;
        cross_dom.style.top = this.contract_info.cross_page_y / 3 + "px";
        cross_dom.style.display = "block";
      }
      if (cross_dom) {
        cross_dom.onmousedown = this.moveTo;
        cross_dom.onmouseenter = this.mouseenter;
        cross_dom.onmouseleave = this.mouseleave;
        var _this = this;
        cross_dom.childNodes[0].onclick = this.deleteIt;
      }
    },
    change_status(val) {
      if (
        (val === -1 && !this.status1) ||
        (val === -2 && !this.status2) ||
        (val === 4 && !this.status3)
      ) {
        return false;
      }
      this.status = val;
      this.status1 = false;
      this.status2 = false;
      this.status3 = false;
      if (val === -1) {
        this.status1 = true;
      } else if (val === -2) {
        this.status2 = true;
      } else if (val === 4) {
        this.status3 = true;
      }

      if (val === 4) {
        this.sub_reason();
      } else if (val === -1 || val === -2) {
        this.show_reason = true;
        this.status = val;
      }
    },
    sub_reason() {
      var dataIn = {
        id: this.id,
        status: this.status,
        message: this.status === 4 ? "" : this.reason,
      };
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/reject",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.show_reason = false;
            _this.old_status = _this.status;
            _this.$Notice.success({
              title: "操作成功！",
            });
            _this.$store.commit("removeTag", "contract_approve");
            _this.$store.commit("closePage", "contract_approve");
            _this.$router.push({
              name: "contract_list",
            });
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    cancel_status() {
      this.status1 = false;
      this.status2 = false;
      this.status3 = false;
      if (this.old_status === -1) {
        this.status1 = true;
      } else if (this.old_status === -2) {
        this.status2 = true;
      } else if (this.old_status === 4) {
        this.status3 = true;
      }
      this.show_reason = false;
    },
    handleSubmit() {
      var positons = [];
      if (this.stamps.length > 0) {
        if (this.stamp_positons.length <= 0) {
          for (var i = 0; i < this.pdfs.length; i++) {
            var item = {
              x: 0,
              y: 0,
            };
            this.stamp_positons.push(item);
          }
        }
        for (var i = 0; i < this.stamp_positons.length; i++) {
          selection(i, this.stamps, this.stamp_positons);
        }
        var _this = this;
        function selection(n, obj, obj1) {
          for (var i = 0; i < obj.length; i++) {
            if (obj[i].page === n) {
              positons.push({
                x: obj[i].x,
                y: obj[i].y,
              });
              return;
            }
          }
          positons.push(obj1[n]);
        }
      } else {
        positons = this.stamp_positons;
      }
      var marks = document.querySelectorAll(".mark");
      if (marks.length === 0 && !check_stamp_num()) {
        this.$Notice.error({
          title: "请盖上印章！",
        });
        return false;
      }
      function check_stamp_num() {
        for (var i = 0; i < positons.length; i++) {
          if (positons[i].x > 0 || positons[i].y > 0) {
            return true;
          }
        }
        return false;
      }

      this.spinShow = true;
      var dataIn = {};
      dataIn.id = parseInt(this.id);
      dataIn.stamp_positons = positons;
      for (var i = 0; i < marks.length; i++) {
        if (marks[i].className.indexOf("corss_mark") != -1) {
          dataIn.cross_page_y = parseInt(marks[i].style.top) * 3;
        }
      }
      var url = "";
      //url = '/api/v1/admin/contract_stamp/apply'
      if (!!this.contract_info.isApprove || !!this.contract_info.isAdmin) {
        dataIn.stamp = this.stamp;
        url = "/api/v1/admin/contract_stamp/approve";
      } else {
        url = "/api/v1/admin/contract_stamp/apply";
      }
      var _this = this;
      util
        .ajax({
          url: url,
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          _this.spinShow = false;
          if (res.data.msg === "success") {
            _this.$store.commit("removeTag", "contract_approve");
            _this.$store.commit("closePage", "contract_approve");
            _this.$router.push({
              name: "contract_list",
            });
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.spinShow = false;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    mouseenter(e) {
      e.currentTarget.childNodes[0].style.display = "block";
    },
    mouseleave(e) {
      e.currentTarget.childNodes[0].style.display = "none";
    },
    deleteIt(e) {
      document
        .querySelector(".pdf-container")
        .removeChild(e.currentTarget.parentNode);
      var stamp_item = {
        x: 0,
        y: 0,
        page: this.showIndex - 1,
      };
      if (this.isIn(this.stamps, stamp_item)) {
        this.stamps.push(stamp_item);
      } else {
        for (var i = 0; i < this.stamps.length; i++) {
          if (this.stamps[i].page === stamp_item.page) {
            this.stamps[i].x = stamp_item.x;
            this.stamps[i].y = stamp_item.y;
          }
        }
        this.stamps.push(stamp_item);
      }
    },
    signPic(e) {
      if (e.composedPath()[1].className.indexOf("checked") === -1) {
        return false;
      }

      var _this = this;
      if (document.querySelectorAll(".page_" + this.showIndex).length >= 2) {
        this.$Message.error("每页最多可以盖一个章印！");
        return;
      }
      let dom = e.currentTarget.cloneNode(true);
      console.log(dom);
      dom.getElementsByClassName("stamp_name")[0].remove();
      document.querySelector(".pdf-box").appendChild(dom);
      document.onmousemove = (e) => {
        dom.style.position = "absolute";
        dom.style.cursor = "pointer";
        dom.style.left =
          this.getPos(e).x - parseInt(dom.clientWidth / 2) - 246 + "px";
        dom.style.top =
          this.getPos(e).y -
          parseInt(dom.clientHeight / 2) +
          this.scrollTop -
          170 +
          "px";
        dom.className = "sign-img mark page_" + this.showIndex;
      };
      // 鼠标抬开
      document.onmouseup = (e) => {
        dom.style.left =
          this.getPos(e).x +
          document.querySelector(".pdf-box").scrollLeft -
          parseInt(dom.clientWidth / 2) -
          document.querySelector(".pdf-box").offsetLeft -
          240 +
          "px";
        dom.style.top =
          this.getPos(e).y +
          this.scrollTop -
          parseInt(dom.clientHeight / 2) -
          227 +
          "px";
        if (parseInt(dom.style.left) > 680) {
          dom.className = "sign-img mark corss_mark";
        }
        //限制区域
        if (parseInt(dom.style.left) < 0) {
          document.querySelector(".pdf-box").removeChild(dom);
          this.$Message.error("请将印章拖拽到合同区域");
          return;
        } else if (
          parseInt(dom.style.left) >
          document.querySelector(".pdf-container").clientWidth -
            dom.clientWidth +
            100
        ) {
          document.querySelector(".pdf-box").removeChild(dom);
          this.$Message.error("请将印章拖拽到合同区域");
          return;
        }

        if (parseInt(dom.style.top) < 0) {
          document.querySelector(".pdf-box").removeChild(dom);
          this.$Message.error("请将印章拖拽到合同区域");
          return;
        } else if (
          parseInt(dom.style.top) >
          document.querySelector(".pdf-box").clientHeight - dom.clientWidth
        ) {
          document.querySelector(".pdf-box").removeChild(dom);
          this.$Message.error("请将印章拖拽到合同区域");
          return;
        }

        if (parseInt(dom.style.left) > 680) {
        } else {
          var stamp_item = {
            x: parseInt(dom.style.left) * 3,
            y: parseInt(dom.style.top) * 3,
            page: this.showIndex - 1,
          };
          this.stamps.push(stamp_item);
        }

        document.querySelector(".pdf-box").removeChild(dom);
        if (
          document.querySelectorAll(".page_" + this.showIndex).length >= 1 &&
          parseInt(dom.style.left) <= 680
        ) {
          this.$Message.error("每页最多可以盖一个章印！");
          return false;
        } else if (
          document.querySelectorAll(".corss_mark").length >= 1 &&
          parseInt(dom.style.left) > 680
        ) {
          this.$Message.error("最多可以盖一个骑缝章！");
          return false;
        }

        document.querySelector(".pdf-container").appendChild(dom);
        document.onmousemove = null;
        document.onmouseup = null;
        dom.onmousedown = this.moveTo;
        dom.onmouseenter = this.mouseenter;
        dom.onmouseleave = this.mouseleave;
        dom.childNodes[0].onclick = this.deleteIt;
      };
    },
    moveTo(e) {
      this.s_t = this.scrollTop;
      let odiv = e.currentTarget; //获取目标元素
      //算出鼠标相对元素的位置
      let disX = e.clientX - odiv.offsetLeft;
      let disY = e.clientY - odiv.offsetTop;
      document.onmousemove = (e) => {
        let left = e.clientX - disX;
        let top = e.clientY - disY;

        //限制区域
        if (left <= 0) {
          left = 0;
        } else if (
          left >=
          document.querySelector(".pdf-container").clientWidth -
            odiv.clientWidth
        ) {
          left =
            document.querySelector(".pdf-container").clientWidth -
            odiv.clientWidth;
        } else {
          left = left - 10;
        }

        if (top <= 0) {
          top = 0;
        } else if (
          top >=
          document.querySelector(".pdf-container").clientHeight -
            odiv.clientHeight
        ) {
          top =
            document.querySelector(".pdf-container").clientHeight -
            odiv.clientHeight;
        } else {
          top = top - 10 + (this.scrollTop - this.s_t);
        }

        //移动当前元素
        odiv.style.right = "inherit";
        odiv.style.left = left + "px";
        odiv.style.top = top + "px";
        if (parseInt(odiv.style.left) > 680) {
          odiv.className = "sign-img mark corss_mark";
        } else {
          odiv.className = "sign-img mark page_" + this.showIndex;
          var stamp_item = {
            x: left * 3,
            y: top * 3,
            page: this.showIndex - 1,
          };

          if (this.isIn(this.stamps, stamp_item)) {
            this.stamps.push(stamp_item);
          } else {
            for (var i = 0; i < this.stamps.length; i++) {
              if (this.stamps[i].page === stamp_item.page) {
                this.stamps[i].x = stamp_item.x;
                this.stamps[i].y = stamp_item.y;
              }
            }
          }
        }
      };
      document.onmouseup = (e) => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    },
    isIn(arr, obj) {
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].page === obj.page) {
          return false;
        }
      }
      return true;
    },
    getPos(ev) {
      return { x: ev.clientX, y: ev.clientY };
    },
    handleScroll(e) {
      this.scrollTop = e.target.scrollTop;
      if (!!document.querySelector(".side-barinfo")) {
        document.querySelector(".side-barinfo").style.top =
          this.scrollTop + "px";
      }
    },
    to_last() {
      this.showPdfs(this.pdfs.length);
    },
    get_perms() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/stamps/perms",
          method: "GET",
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.get_user_info(res.data.data);
          } else {
            console.log(res);
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    get_user_info(data) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/info",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            var applies = data.applies;
            var approves = data.approves;
            var user_info = res.data.data.user;
            _this.set_perms(applies, approves, user_info);
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_perms(applies, approves, info) {
      if (info.role_name.indexOf("管理员") !== -1) {
        this.$set(this.contract_info, "isAdmin", true);
      }
      for (var i = 0; i < applies.length; i++) {
        if (this.contract_info.stamp === applies[i]) {
          if (this.contract_info.apply_uid === info.id) {
            if (
              this.contract_info.status === -2 ||
              this.contract_info.status === 2 ||
              this.contract_info.status === 3
            ) {
              this.$set(this.contract_info, "isApply", true);
            }
          }
        }
      }
      for (var i = 0; i < approves.length; i++) {
        if (this.contract_info.stamp === approves[i]) {
          if (this.contract_info.department === info.department) {
            this.$set(this.contract_info, "isApprove", true);
          }
        }
      }
      console.log(this.contract_info);
    },
    check_stamp(index) {
      console.log(this.sealList[index]);
      this.stamp = parseInt(this.sealList[index].id.split("seal")[1]);
      for (var i = 0; i < this.sealList.length; i++) {
        this.$set(this.sealList[i], "checked", false);
      }
      this.$set(this.sealList[index], "checked", true);
      var marks = document.querySelectorAll(".mark");
      for (var i = 0; i < marks.length; i++) {
        var stamp_img = marks[i].getElementsByTagName("img")[0];
        stamp_img.src = this.sealList[index].url;
      }
    },
    closePage() {
      this.pdfs = [];
      this.sealList = [];
      this.scrollTop = 0;
      this.s_t = 0;
      this.showIndex = 1;
      this.id = 0;
      this.contract_info = {};
      this.isCommonShow = false;
      this.isCrossShow = false;
      this.stamp_type = 0;
      this.status = false;
      this.status1 = false;
      this.status2 = false;
      this.status3 = false;
      this.old_status = -9;
      this.show_reason = false;
      this.reason = "";
      this.roles = [];
      this.isApprove = false;
      this.spinShow = false;
      this.stamp_positons = [];
      this.stamps = [];
      this.stamp = 0;
      this.show_pdf = false;
      this.$store.commit("removeTag", "contract_approve");
      this.$router.push({
        name: "contract_list",
      });
    },
  },
};
</script>

<style></style>
