<style lang="less">
@import "../../../../styles/common.less";
@import "../stamp.less";
</style>

<template>
  <div class="contract-add">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <p slot="title">
            <Icon type="plus"></Icon>
            添加合同
          </p>
          <Form :label-width="90">
            <FormItem
              prop="contract"
              label="合同："
              class="ivu-form-item-required upload_contract"
            >
              <Upload
                action="/api/v1/admin/event/upload"
                :before-upload="checkFile"
                :show-upload-list="true"
                v-if="files.length === 0"
              >
                <Button type="ghost" icon="ios-cloud-upload">上传</Button>
              </Upload>
              <div class="file-list" v-if="files.length > 0">
                <div v-for="file in files">
                  {{ file.name }}
                  <p
                    class="del"
                    style="display: inline-block;padding-left: 15px;"
                  >
                    <Icon type="trash-a" @click="delImg(file)" size="22"
                      >删除</Icon
                    >
                  </p>
                </div>
              </div>
            </FormItem>
            <FormItem
              prop="contract_name"
              label="文件名："
              class="ivu-form-item-required"
            >
              <Input
                v-model="form.contract_name"
                placeholder="请输入标题"
              ></Input>
            </FormItem>
            <FormItem
              prop="stamp"
              label="印章："
              class="ivu-form-item-required stamp_type"
            >
              <RadioGroup v-model="form.stamp">
                <!--<Button :class="stamp.val === form.stamp ? 'stamp_' + stamp.val + ' on' : 'stamp_' + stamp.val" v-for="stamp in stamps" :key="stamp.index" @click="check_stamp(stamp.val)"></Button>-->
                <Radio
                  v-for="stamp in stamps"
                  :label="stamp.id"
                  :key="stamp.index"
                  >{{ stamp.name }}</Radio
                >
              </RadioGroup>
            </FormItem>
            <FormItem
              prop="department"
              label="部门："
              required
              v-show="depEnabled"
            >
              <RadioGroup v-model="form.department">
                <Radio
                  :label="department.id"
                  v-for="department in departments"
                  :key="department.index"
                  >{{ department.name }}</Radio
                >
              </RadioGroup>
            </FormItem>
            <FormItem prop="remark" label="备注：">
              <Input
                v-model="form.remark"
                type="textarea"
                :autosize="{ minRows: 5, maxRows: 5 }"
                placeholder="请输入标题"
              ></Input>
            </FormItem>
            <br /><br />
            <FormItem>
              <Button
                @click="handleSubmit"
                type="primary"
                style="position: relative;"
              >
                下一步
                <Spin fix v-if="spinShow">
                  <Icon
                    type="load-c"
                    size="18"
                    class="demo-spin-icon-load"
                  ></Icon>
                </Spin>
              </Button>
              <Button @click="closePage">取消</Button>
            </FormItem>
          </Form>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

export default {
  name: "contract_add",
  components: {
    // canEditTable
  },
  data() {
    return {
      form: {
        aid: 0,
        stamp: 0,
        contract_name: "",
        department: 0,
        remark: "",
      },
      files: [],
      aid: 0,
      stamps: [],
      spinShow: false,
      departments: [],
      id: 0,
      timing: 0,
      access_token: this.$store.state.user.access_token,
      isAccess: true,
      depEnabled: false,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "contract_add") {
        this.form = {
          logo_url: "",
          stamp: 0,
          contract_name: "",
          department: 0,
          remark: "",
        };
        this.aid = 0;
        this.files = [];
        this.stamps = [];
        this.departments = [];
        this.id = 0;
        this.timing = 0;
        this.spinShow = false;
        this.get_stamps();
        this.get_departments();
        this.get_user_info();
      }
    },
  },
  mounted() {
    if (parseInt(Cookies.get("uAccess")) === 1) {
      this.isAccess = false;
    }
    this.get_stamps();
    this.get_departments();
    this.get_user_info();
  },
  methods: {
    get_stamps() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/stamps",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.stamps = res.data.data;

            if (_this.stamps.length === 1) {
              _this.form.stamp = _this.stamps[0].id;
            }
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    get_departments() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/user/departments",
          method: "GET",
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.departments = res.data.data;
          } else {
            console.log(err);
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    handleSubmit() {
      if (parseInt(this.form.stamp) <= 0) {
        this.$Notice.error({
          title: "请上选择印章！",
        });
        return false;
      }
      if (parseInt(this.form.aid) <= 0) {
        this.$Notice.error({
          title: "请上传合同！",
        });
        return false;
      }
      if (!this.form.contract_name) {
        this.$Notice.error({
          title: "请输入标题！",
        });
        return false;
      }
      if (parseInt(this.form.department) <= 0) {
        this.$Notice.error({
          title: "请上选择合同部门！",
        });
        return false;
      }
      this.spinShow = true;
      var dataIn = {
        raw_aid: this.form.aid,
        stamp: this.form.stamp,
        contract_name: this.form.contract_name,
        department: this.form.department,
        remark: this.form.remark,
      };
      this.handlePost(dataIn);
    },
    handlePost(dataIn) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp",
          method: "POST", //
          data: dataIn,
        })
        .then(function(res) {
          //_this.spinShow = false;
          if (res.data.msg === "success") {
            //_this.create_contract(_this.form.aid);
            _this.check_status(res.data.data.id);
          } else {
            _this.spinShow = false;
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.spinShow = false;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    check_status(id) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/" + id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (res.data.data.status === 2) {
              _this.$router.push(
                "/console/contract_stamp/contract/approve/" + id
              );
            } else {
              setTimeout(function() {
                if (_this.timing >= 30) {
                  _this.$Notice.error({
                    title: "请求超时，请从列表入口进入盖章页进行盖章！",
                  });
                  _this.spinShow = false;
                } else {
                  _this.timing++;
                  _this.check_status(id);
                }
              }, 2000);
            }
          } else {
            _this.spinShow = false;
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.spinShow = false;
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    checkFile(file, str) {
      var url = window.URL || window.webkitURL;
      var _this = this;
      _this.isMath = false;
      var check = function() {
        if (
          file.type === "application/pdf" ||
          file.name.split(".").pop() === "docx" ||
          file.name.split(".").pop() === "doc"
        ) {
          clearInterval(set);
          _this.uploadFile(file);
        } else {
          clearInterval(set);
          _this.$Notice.error({
            title: "请上传PDF格式的文件！",
          });
          return false;
        }
      };
      var set = setInterval(check, 40);
      if (!this.isMath) {
        return false;
      }
    },
    uploadFile(file, str) {
      var form = new FormData();
      form.append("file", file);
      if (!!this.form.contract_name) {
        form.append("name", this.form.contract_name);
        form.append("save_org_name", this.form.contract_name);
      }
      const _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/upload",
          method: "POST",
          dataType: "JSON",
          contentType: false,
          processData: false,
          data: form,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.form.aid = res.data.data.aid;
            _this.aid = res.data.data.aid;
            _this.files.push({
              aid: res.data.data.aid,
              name: file.name,
            });
            if (!_this.form.contract_name) {
              if (
                file.name
                  .split(".")
                  .pop()
                  .toLocaleLowerCase() === "pdf"
              ) {
                _this.form.contract_name = file.name.replace(/\.pdf/gi, "");
              } else if (
                file.name
                  .split(".")
                  .pop()
                  .toLocaleLowerCase() === "docx"
              ) {
                _this.form.contract_name = file.name.replace(/\.docx/gi, "");
              } else if (
                file.name
                  .split(".")
                  .pop()
                  .toLocaleLowerCase() === "doc"
              ) {
                _this.form.contract_name = file.name.replace(/\.doc/gi, "");
              }
            }
            _this.$Notice.success({
              title: "上传成功",
            });
            _this.aid = res.data.data.aid;
            //_this.create_contract(res.data.data.aid);
          } else {
            // console.log(res)
            _this.$Notice.error({
              title: res.data.errors.message,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
      return false;
    },
    create_contract() {
      var dataIn = {
        raw_aid: this.form.aid,
      };
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.id = res.data.data.id;
            //_this.handleSubmit();
            _this.check_status();
          } else {
            _this.spinShow = false;
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.spinShow = false;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    delImg(file, str, index) {
      this.files = [];
      this.form.image = "";
    },
    check_stamp(val) {
      if (this.form.stamp === val) {
        this.form.stamp = 0;
      } else {
        this.form.stamp = val;
      }
    },
    get_user_info() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/info",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.form.department = res.data.data.user.department;
            _this.depEnabled =
              res.data.data.user.role_name.indexOf("管理员") !== -1;
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    closePage() {
      this.form = {
        logo_url: "",
        stamp: 0,
        contract_name: "",
        department: 0,
        remark: "",
      };
      this.aid = 0;
      this.files = [];
      this.stamps = [];
      this.departments = [];
      this.spinShow = false;
      this.id = 0;
      this.timing = 0;
      this.$store.commit("removeTag", "contract_add");
      this.$router.push({
        name: "contract_list",
      });
    },
  },
};
</script>

<style></style>
