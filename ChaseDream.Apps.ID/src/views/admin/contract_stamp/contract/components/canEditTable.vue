<style lang="less">
@import "editable-table.less";
</style>

<template>
  <div>
    <Table
      :ref="refs"
      :columns="columnsList"
      :data="thisTableData"
      border
    ></Table>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

const viewButton = (vm, h, currentRow, index) => {
  if (!!currentRow.isApprove) {
    return h("Icon", {
      props: {
        type: "eye",
        loading: currentRow.saving,
        size: 22,
      },
      style: {
        margin: "0 8px",
        color: "#2d8cf0",
        cursor: "pointer",
      },
      on: {
        click: () => {
          vm.$emit("details", currentRow);
        },
      },
    });
  } else {
    return h("span", {}, "");
  }
};
const approveButton = (vm, h, currentRow, index) => {
  if (
    !!currentRow.isAdmin ||
    !!currentRow.isApprove ||
    (!!currentRow.isApply &&
      (currentRow.status === -2 ||
        currentRow.status === 2 ||
        currentRow.status === 3))
  ) {
    var isDisabled = false;
    if (currentRow.status === 0 || currentRow.status === 1) {
      isDisabled = true;
    }
    return h("Icon", {
      props: {
        type: "stamp_btn",
        loading: currentRow.saving,
        size: 22,
      },
      style: {
        margin: "0 8px",
        color: "#F68989",
        cursor: !!isDisabled ? "not-allowed" : "pointer",
      },
      on: {
        click: () => {
          if (!isDisabled) {
            let cr = currentRow;
            vm.$store.commit("getCurrentRow", cr);
            vm.$router.push(
              "/console/contract_stamp/contract/approve/" + currentRow.id
            );
          }
        },
      },
    });
  } else {
    return h("span", {}, "");
  }
};
const deleteButton = (vm, h, currentRow, index) => {
  if (
    !!currentRow.isAdmin ||
    (currentRow.status < 3 && currentRow.status >= 0)
  ) {
    //3
    return h(
      "Poptip",
      {
        props: {
          confirm: true,
          title: "您确定要删除这条数据吗?",
          transfer: true,
        },
        on: {
          "on-ok": () => {
            var _this = this;
            util
              .ajax({
                url: "/api/v1/admin/contract_stamp",
                method: "DELETE",
                data: {
                  id: currentRow.id,
                },
              })
              .then(function(res) {
                if (res.data.msg === "success") {
                  vm.thisTableData.splice(index, 1);
                  vm.$emit("input", vm.handleBackdata(vm.thisTableData));
                  vm.$emit(
                    "on-delete",
                    vm.handleBackdata(vm.thisTableData),
                    index
                  );
                } else {
                }
              })
              .catch(function(err) {
                console.log(err);
              });
          },
        },
      },
      [
        h("Icon", {
          style: {
            margin: "0 8px",
            color: "#ed3f14",
            cursor: "pointer",
          },
          props: {
            type: "trash-a",
            placement: "top",
            size: 24,
          },
        }),
      ]
    );
  } else {
    return h("span", {}, "");
  }
};
const status = (vm, h, currentRow, index) => {
  if (currentRow.status === -1) {
    return h(
      "Poptip",
      {
        props: {
          title: "",
          transfer: true,
          trigger: "hover",
        },
      },
      [
        h(
          "span",
          {
            style: {
              cursor: "pointer",
              color: "#ed3f14",
            },
            props: {},
          },
          "已拒绝"
        ),
        h(
          "div",
          {
            slot: "content",
            style: {},
          },
          currentRow.message
        ),
      ]
    );
  } else {
    var text = "";
    var color = "";
    if (currentRow.status === -2) {
      text = "请修改";
      color = "#e73da9";
    } else if (currentRow.status === 0) {
      text = "等待系统预处理";
      color = "#f5be2b";
    } else if (currentRow.status === 1) {
      text = "系统预处理中";
      color = "#bcd149";
    } else if (currentRow.status === 2) {
      text = "申请盖章";
      color = "#ff861b";
    } else if (currentRow.status === 3) {
      text = "待批准";
      color = "#3c96ea";
    } else if (currentRow.status === 4) {
      text = "已批准";
      color = "#3d4fd3";
    } else if (currentRow.status === 5) {
      text = "盖章中";
      color = "#954fe3";
    } else if (currentRow.status === 6) {
      text = "已盖章";
      color = "#19be6b";
    }
    // return h('span', {
    //     style: {
    //         color: color
    //     },
    //     props: {
    //     }
    // }, text)
    //给状态添加id
    return h(
      "span",
      {
        style: {},
        props: {},
      },
      [
        h(
          "span",
          {
            style: {},
          },
          currentRow.id
        ),
        h(
          "div",
          {
            slot: "content",
            style: {
              color,
            },
          },
          text
        ),
      ]
    );
  }
};
const raw_att = (vm, h, currentRow, index) => {
  var contract_name = currentRow.contract_name;
  var isShow = false;
  if (
    currentRow.status !== 4 &&
    currentRow.status !== 5 &&
    currentRow.status !== 6
  ) {
    isShow = true;
  }
  return h(
    "div",
    {
      style: {
        position: "relative",
        maxheight: "36px",
        overflow: "hidden",
        minHeight: "18px",
      },
    },
    [
      h(
        "span",
        {
          style: {
            color: "#2d8cf0",
            cursor: "pointer",
          },
          on: {
            click: () => {
              vm.$emit("download_raw", currentRow);
            },
          },
        },
        !!currentRow.contract_name ? currentRow.contract_name : ""
      ),
      h(
        "Upload",
        {
          props: {
            type: "select",
            icon: "android-upload",
            action: "//127.0.0.1",
            "before-upload": vm.re_upload, //vm.$emit('re-upload', currentRow),
          },
          style: {
            display: "inline-block",
            marginLeft: "10px",
          },
        },
        [
          h("Icon", {
            style: {
              color: "#3399ff",
              cursor: "pointer",
              display: !!isShow ? "inline-block" : "none",
            },
            props: {
              type: "android-upload",
              placement: "top",
              size: 18,
            },
            on: {
              click: () => {
                vm.set_row_id(index);
              },
            },
          }),
        ]
      ),
      h(
        "Poptip",
        {
          props: {
            vModel: currentRow.show_contract_name,
            transfer: true,
            placement: "bottom-end",
            width: "330",
          },
          style: {
            position: "absolute",
            bottom: "0",
            right: "0",
          },
          on: {
            "on-popper-hide": () => {},
          },
        },
        [
          h("Icon", {
            props: {
              type: "edit",
              size: 12,
            },
            style: {
              color: "#2d8cf0",
              cursor: "pointer",
            },
            attrs: {
              title: "编辑文件名",
            },
          }),
          h(
            "div",
            {
              slot: "content",
            },
            [
              h(
                "h3",
                {
                  style: {
                    height: "30px",
                    lineHeight: "30px;",
                    borderBottom: "1px solid #E9EAEC",
                    color: "#495060",
                    fontSize: "14px",
                    marginBottom: "10px",
                    fontWeight: "normal",
                  },
                },
                "编辑文件名"
              ),
              h("Input", {
                style: {
                  display: "block",
                  marginTop: "20px",
                  marginBottom: "14px",
                  background: "#F5F6F7",
                  width: "100%",
                },
                props: {
                  placeholder: "备注",
                  value: contract_name,
                },
                on: {
                  "on-change": (obj) => {
                    contract_name = obj.target.value;
                  },
                },
              }),
              h(
                "Button",
                {
                  style: {
                    display: "block",
                    width: "100%",
                    height: "34px",
                    background: "#2D8CF0",
                    color: "#fff",
                    fontSize: "14px",
                  },
                  on: {
                    click: () => {
                      var _this = this;
                      util
                        .ajax({
                          url: "/api/v1/admin/contract_stamp",
                          method: "PUT",
                          data: {
                            id: currentRow.id,
                            contract_name: contract_name,
                          },
                        })
                        .then(function(res) {
                          if (res.data.msg === "success") {
                            currentRow.contract_name = contract_name;
                            vm.$emit(
                              "set-edit",
                              currentRow,
                              index,
                              contract_name,
                              "contract_name"
                            );
                          } else {
                          }
                        })
                        .catch(function(err) {
                          console.log(err);
                        });
                    },
                  },
                },
                "确定"
              ),
            ]
          ),
        ]
      ),
    ]
  );

  /*return h('div', {},[
        h('span',{
            style: {
                color: '#2d8cf0',
                cursor: 'pointer'
            },
            on: {
                click: () => {
                    vm.$emit('download_raw', currentRow);
                }
            }
        },!!currentRow.contract_name ? currentRow.contract_name : ''),
        h('Upload',{
            props:{
                type: 'select',
                icon: 'android-upload',
                action: '//127.0.0.1',
                'before-upload': vm.re_upload //vm.$emit('re-upload', currentRow),
            },
            style: {
                display: 'inline-block',
                marginLeft: '10px'
            },
        },[
            h('Icon',{
                style: {
                    color: '#3399ff',
                    cursor: 'pointer',
                    display: !!isShow ? 'inline-block' :'none'
                },
                props: {
                    type: 'android-upload',
                    placement: 'top',
                    size: 18
                },
                on: {
                    click: () => {
                        vm.set_row_id(index);
                    }
                }
            })
        ])
    ]);*/
};
const final_att = (vm, h, currentRow, index) => {
  if (
    !!currentRow.final_aid &&
    currentRow.status !== -1 &&
    (!!currentRow.isAdmin || !!currentRow.isApprove || !!currentRow.isApply)
  ) {
    return h("Icon", {
      style: {
        margin: "0 0 0 8px",
        color: "black",
        cursor: "pointer",
      },
      props: {
        type: "final_att_log",
        placement: "top",
        size: 24,
      },
      on: {
        click: () => {
          vm.$emit("download_final", currentRow);
        },
      },
    }); //'下载'
  } else {
    return h("span", {}, "");
  }
};
const remarkInput = (vm, h, currentRow, index) => {
  var remark = currentRow.remark;
  return h(
    "div",
    {
      style: {
        position: "relative",
        maxheight: "36px",
        overflow: "hidden",
        minHeight: "18px",
      },
    },
    [
      h(
        "p",
        {
          attrs: {
            title: currentRow.remark,
          },
          style: {
            marginRight: "15px",
            cursor: "pointer",
            maxHeight: "70px",
            overflow: "hidden",
            marginTop: "7px",
            marginBottom: "7px",
          },
        },
        currentRow.remark
      ),
      h(
        "Poptip",
        {
          props: {
            vModel: currentRow.show_remark,
            transfer: true,
            placement: "bottom-end",
            width: "330",
          },
          style: {
            position: "absolute",
            bottom: "0",
            right: "0",
          },
          on: {
            "on-popper-hide": () => {},
          },
        },
        [
          h("Icon", {
            props: {
              type: "edit",
              size: 12,
            },
            style: {
              color: "#2d8cf0",
              cursor: "pointer",
            },
            attrs: {
              title: "编辑备注",
            },
          }),
          h(
            "div",
            {
              slot: "content",
            },
            [
              h(
                "h3",
                {
                  style: {
                    height: "30px",
                    lineHeight: "30px;",
                    borderBottom: "1px solid #E9EAEC",
                    color: "#495060",
                    fontSize: "14px",
                    marginBottom: "10px",
                    fontWeight: "normal",
                  },
                },
                "编辑备注"
              ),
              h("Input", {
                style: {
                  display: "block",
                  marginTop: "20px",
                  marginBottom: "14px",
                  background: "#F5F6F7",
                  width: "100%",
                },
                props: {
                  type: "textarea",
                  rows: 4,
                  placeholder: "备注",
                  value: remark,
                },
                on: {
                  "on-change": (obj) => {
                    remark = obj.target.value;
                  },
                },
              }),
              h(
                "Button",
                {
                  style: {
                    display: "block",
                    width: "100%",
                    height: "34px",
                    background: "#2D8CF0",
                    color: "#fff",
                    fontSize: "14px",
                  },
                  on: {
                    click: () => {
                      var _this = this;
                      util
                        .ajax({
                          url: "/api/v1/admin/contract_stamp",
                          method: "PUT",
                          data: {
                            id: currentRow.id,
                            remark: remark,
                          },
                        })
                        .then(function(res) {
                          if (res.data.msg === "success") {
                            currentRow.remark = remark;
                            vm.$emit(
                              "set-edit",
                              currentRow,
                              index,
                              remark,
                              "remark"
                            );
                          } else {
                          }
                        })
                        .catch(function(err) {
                          console.log(err);
                        });
                    },
                  },
                },
                "确定"
              ),
            ]
          ),
        ]
      ),
    ]
  );
};

const apply_username = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.apply_username,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "apply_username", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "apply_username", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const contract_name = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.contract_name,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "contract_name", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "contract_name", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const remark_s = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.remark,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "remark", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "remark", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const approve_username = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h("Input", {
      props: {
        value: currentRow.apply_username,
        placeholder: "申请人",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "apply_username", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "apply_username", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
    h("Input", {
      props: {
        value: currentRow.approve_username,
        placeholder: "批准人",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "approve_username", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "approve_username", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
  ]);
};
const department = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h(
      "Select",
      {
        props: {
          value: currentRow.department, // 获取选择的下拉框的值
        },
        style: {
          width: "100%",
          height: "28px",
          lineHeight: "28px",
        },
        on: {
          "on-change": (e) => {
            vm.$set(vm.thisTableData[0], "department", e);
            vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
            vm.$emit("on-search");
          },
        },
      },
      currentRow.typeList.map((item) => {
        return h("Option", {
          // 下拉框的值
          props: {
            value: item.id,
            label: item.name,
          },
        });
      })
    ),
    h(
      "Select",
      {
        props: {
          value: currentRow.stamp, // 获取选择的下拉框的值
        },
        style: {
          width: "100%",
          height: "28px",
          lineHeight: "28px",
        },
        on: {
          "on-change": (e) => {
            vm.$set(vm.thisTableData[0], "stamp", e);
            vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
            vm.$emit("on-search");
          },
        },
      },
      currentRow.stamps.map((item) => {
        return h("Option", {
          // 下拉框的值
          props: {
            value: item.id,
            label: item.name,
          },
        });
      })
    ),
  ]);

  //[
  /* h('Option', { // 下拉框的值
     props: {
     value: 0,
     label: '全部'
     }
     }),*/
};
const department_l = (vm, h, currentRow, index) => {
  if (!!currentRow.isAdmin) {
    var text = "";
    if (currentRow.department === 1) {
      text = "MBA";
    } else if (currentRow.department === 2) {
      text = "Master";
    } else if (currentRow.department === 3) {
      text = "网站";
    } else if (currentRow.department === 4) {
      text = "开发测试";
    } else if (currentRow.department === 5) {
      text = "mba";
    } else if (currentRow.department === 6) {
      text = "总办";
    } else if (currentRow.department === 7) {
      text = "Agent Sim";
    } else if (currentRow.department === 8) {
      text = "Agent FB";
    } else if (currentRow.department === 9) {
      text = "Agent CL";
    } else if (currentRow.department === 10) {
      text = "GMAT";
    } else if (currentRow.department === 11) {
      text = "YY";
    } else if (currentRow.department === 12) {
      text = "老王";
    }

    var department = currentRow.department;
    var stamp_name = currentRow.stamp_name;
    var stamp = currentRow.stamp;
    //console.log(currentRow.stamps)
    return h("div", {}, [
      h(
        "div",
        {
          style: {
            position: "relative",
            maxheight: "36px",
            overflow: "hidden",
            minHeight: "18px",
          },
        },
        [
          h(
            "span",
            {
              attrs: {
                title: text,
              },
              style: {
                marginRight: "15px",
                cursor: "pointer",
                maxHeight: "70px",
                overflow: "hidden",
                marginTop: "7px",
                marginBottom: "7px",
              },
            },
            text
          ),
          h(
            "Poptip",
            {
              props: {
                vModel: currentRow.show_department,
                transfer: true,
                placement: "bottom-start",
                width: "200",
              },
              style: {
                position: "absolute",
                bottom: "0",
                right: "0",
              },
              on: {
                "on-popper-hide": () => {},
              },
            },
            [
              h("Icon", {
                props: {
                  type: "edit",
                  size: 12,
                },
                style: {
                  color: "#2d8cf0",
                  cursor: "pointer",
                },
                attrs: {
                  title: "选择部门",
                },
              }),
              h(
                "div",
                {
                  slot: "content",
                },
                [
                  h(
                    "h3",
                    {
                      style: {
                        height: "30px",
                        lineHeight: "30px;",
                        borderBottom: "1px solid #E9EAEC",
                        color: "#495060",
                        fontSize: "14px",
                        marginBottom: "10px",
                        fontWeight: "normal",
                      },
                    },
                    "选择部门"
                  ),
                  h(
                    "RadioGroup",
                    {
                      style: {
                        marginTop: "14px",
                        marginBottom: "20px",
                      },
                      props: {
                        value: department,
                        vertical: true,
                      },
                      on: {
                        "on-change": (val) => {
                          department = val;
                        },
                      },
                    },
                    [
                      h(
                        "Radio",
                        {
                          props: {
                            label: 1,
                          },
                        },
                        "MBA"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 5,
                          },
                        },
                        "mba"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 2,
                          },
                        },
                        "Master"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 3,
                          },
                        },
                        "网站"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 4,
                          },
                        },
                        "开发测试"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 6,
                          },
                        },
                        "总办"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 12,
                          },
                        },
                        "老王"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 11,
                          },
                        },
                        "YY"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 7,
                          },
                        },
                        "Agent Sim"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 8,
                          },
                        },
                        "Agent FB"
                      ),
                      h(
                        "Radio",
                        {
                          props: {
                            label: 9,
                          },
                        },
                        "Agent CL"
                      ),
                    ]
                  ),
                  h(
                    "Button",
                    {
                      style: {
                        display: "block",
                        width: "100%",
                        height: "34px",
                        background: "#2D8CF0",
                        color: "#fff",
                        fontSize: "14px",
                      },
                      on: {
                        click: () => {
                          var _this = this;
                          util
                            .ajax({
                              url: "/api/v1/admin/contract_stamp",
                              method: "PUT",
                              data: {
                                id: currentRow.id,
                                department: department,
                              },
                            })
                            .then(function(res) {
                              if (res.data.msg === "success") {
                                currentRow.department = department;
                                vm.$emit(
                                  "set-edit",
                                  currentRow,
                                  index,
                                  department,
                                  "department"
                                );
                              } else {
                              }
                            })
                            .catch(function(err) {
                              console.log(err);
                            });
                        },
                      },
                    },
                    "确定"
                  ),
                ]
              ),
            ]
          ),
        ]
      ),
      h(
        "div",
        {
          style: {
            position: "relative",
            maxheight: "36px",
            overflow: "hidden",
            minHeight: "18px",
          },
        },
        [
          h(
            "span",
            {
              attrs: {
                title: currentRow.stamp_name,
              },
              style: {
                marginRight: "15px",
                cursor: "pointer",
                maxHeight: "70px",
                overflow: "hidden",
                marginTop: "7px",
                marginBottom: "7px",
              },
            },
            stamp_name
          ),
          h(
            "Poptip",
            {
              props: {
                vModel: currentRow.show_stamp_name,
                transfer: true,
                placement: "bottom-start",
                width: "200",
              },
              style: {
                position: "absolute",
                bottom: "0",
                right: "0",
              },
            },
            [
              h("Icon", {
                props: {
                  type: "edit",
                  size: 12,
                },
                style: {
                  color: "#2d8cf0",
                  cursor: "pointer",
                },
                attrs: {
                  title: "选择印章",
                },
              }),
              h(
                "div",
                {
                  slot: "content",
                },
                [
                  h(
                    "h3",
                    {
                      style: {
                        height: "30px",
                        lineHeight: "30px;",
                        borderBottom: "1px solid #E9EAEC",
                        color: "#495060",
                        fontSize: "14px",
                        marginBottom: "10px",
                        fontWeight: "normal",
                      },
                    },
                    "选择印章"
                  ),
                  h(
                    "RadioGroup",
                    {
                      style: {
                        marginTop: "14px",
                        marginBottom: "20px",
                      },
                      props: {
                        value: stamp,
                        vertical: true,
                      },
                      on: {
                        "on-change": (val) => {
                          stamp = val;
                          for (var i = 0; i < currentRow.stamps.length; i++) {
                            if (currentRow.stamps[i].id === val) {
                              stamp_name = currentRow.stamps[i].name;
                            }
                          }
                        },
                      },
                    },
                    [
                      currentRow.stamps.map((el) => {
                        return h(
                          "Radio",
                          {
                            props: {
                              label: el.id,
                            },
                          },
                          el.name
                        );
                      }),
                    ]
                  ),
                  h(
                    "Button",
                    {
                      style: {
                        display: "block",
                        width: "100%",
                        height: "34px",
                        background: "#2D8CF0",
                        color: "#fff",
                        fontSize: "14px",
                      },
                      on: {
                        click: () => {
                          var _this = this;
                          util
                            .ajax({
                              url: "/api/v1/admin/contract_stamp",
                              method: "PUT",
                              data: {
                                id: currentRow.id,
                                stamp: stamp,
                              },
                            })
                            .then(function(res) {
                              if (res.data.msg === "success") {
                                currentRow.stamp = stamp;
                                currentRow.stamp_name = stamp_name;
                                vm.$emit(
                                  "set-edit",
                                  currentRow,
                                  index,
                                  stamp,
                                  "stamp"
                                );
                              } else {
                              }
                            })
                            .catch(function(err) {
                              console.log(err);
                            });
                        },
                      },
                    },
                    "确定"
                  ),
                ]
              ),
            ]
          ),
        ]
      ),
      /*h('p',{
                style: {

                }
            }, currentRow.stamp_name)*/
    ]);
    //return h('span',{},text);
  } else {
    return h("p", {}, "");
  }
};
// const apply_username_l = (vm, h, currentRow, index) => {
//     if(!!currentRow.isAdmin){
//         return h('div',{},[
//             h('div',{},currentRow.apply_username),
//             h('div',{},currentRow.approve_username)
//         ]);
//     }else {
//         return h('span',{},'');
//     }
// };
//将审批人和申请人合并
const approve_username_l = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h("div", {}, !!currentRow.apply_username ? currentRow.apply_username : "-"),
    h(
      "div",
      {},
      !!currentRow.approve_username ? currentRow.approve_username : "-"
    ),
  ]);
};
const statusInput = (vm, h, currentRow, index) => {
  var typeList = [
    {
      id: 0,
      name: "全部",
    },
    {
      id: 3,
      name: "待批准",
    },
    {
      id: 5,
      name: "盖章中",
    },
    {
      id: 4,
      name: "已批准",
    },
    {
      id: -1,
      name: "已拒绝",
    },
    {
      id: -2,
      name: "请修改",
    },
  ];
  return h(
    "Select",
    {
      props: {
        value: currentRow.status, // 获取选择的下拉框的值
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
      },
      on: {
        "on-change": (e) => {
          vm.$set(vm.thisTableData[0], "status", e);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    typeList.map((item) => {
      return h("Option", {
        // 下拉框的值
        props: {
          value: item.id,
          label: item.name,
        },
      });
    })
  );
};
const timeInput = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h("Input", {
      ref: "timeInput1",
      props: {
        value: currentRow.begin,
        placeholder: "20220101",
        "element-id": "timeInput1",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "begin", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "begin", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
        "on-keydown": (e) => {
          if (e.key === "Tab") {
            setTimeout(() => {
              vm.$refs.table1.$refs.tbody.$children[0].$children[5].$children[1].$refs.input.focus();
            }, 300);
          }
        },
      },
    }),
    h("Input", {
      ref: "timeInput2",
      props: {
        value: currentRow.end,
        placeholder: "20220130",
        "element-id": "timeInput2",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "end", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "end", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
  ]);
};
const clearButton = (vm, h, currentRow, index) => {
  return h(
    "span",
    {
      props: {},
      style: {
        margin: "0 8px",
        color: "#2d8cf0",
        cursor: "pointer",
      },
      on: {
        click: () => {
          vm.$set(vm.thisTableData[0], "apply_username", "");
          vm.$set(vm.thisTableData[0], "contract_name", "");
          vm.$set(vm.thisTableData[0], "remark", "");
          vm.$set(vm.thisTableData[0], "approve_username", "");
          vm.$set(vm.thisTableData[0], "department", 0);
          vm.$set(vm.thisTableData[0], "stamp", 0);
          vm.$set(vm.thisTableData[0], "status", 0);
          vm.$set(vm.thisTableData[0], "begin", "");
          vm.$set(vm.thisTableData[0], "end", "");
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    "清除"
  );
};
export default {
  name: "canEditTable",
  props: {
    refs: String,
    columnsList: Array,
    value: Array,
    url: String,
    editIncell: {
      type: Boolean,
      default: false,
    },
    hoverShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      columns: [],
      thisTableData: [],
      edittingStore: [],
      roles: [],
      row_id: 0,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let vm = this;
      let editableCell = this.columnsList.filter((item) => {
        if (item.editable) {
          if (item.editable === true) {
            return item;
          }
        }
      });
      let cloneData = JSON.parse(JSON.stringify(this.value));
      let res = [];
      res = cloneData.map((item, index) => {
        let isEditting = false;
        if (this.thisTableData[index]) {
          if (this.thisTableData[index].editting) {
            isEditting = true;
          } else {
            for (const cell in this.thisTableData[index].edittingCell) {
              if (this.thisTableData[index].edittingCell[cell] === true) {
                isEditting = true;
              }
            }
          }
        }
        if (isEditting) {
          return this.thisTableData[index];
        } else {
          this.$set(item, "editting", false);
          let edittingCell = {};
          editableCell.forEach((item) => {
            edittingCell[item.key] = false;
          });
          this.$set(item, "edittingCell", edittingCell);
          return item;
        }
      });
      this.thisTableData = res;
      this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
      this.columnsList.forEach((item) => {
        if (item.editable) {
          item.render = (h, param) => {
            let currentRow = this.thisTableData[param.index];
            if (!currentRow.editting) {
              if (this.editIncell) {
                return h(
                  "Row",
                  {
                    props: {
                      type: "flex",
                      align: "middle",
                      justify: "center",
                    },
                  },
                  [
                    h(
                      "Col",
                      {
                        props: {
                          span: "22",
                        },
                      },
                      [
                        !currentRow.edittingCell[param.column.key]
                          ? h("span", currentRow[item.key])
                          : cellInput(this, h, param, item),
                      ]
                    ),
                    h(
                      "Col",
                      {
                        props: {
                          span: "2",
                        },
                      },
                      [
                        currentRow.edittingCell[param.column.key]
                          ? saveIncellEditBtn(this, h, param)
                          : incellEditBtn(this, h, param),
                      ]
                    ),
                  ]
                );
              } else {
                return h("span", currentRow[item.key]);
              }
            } else {
              return h("Input", {
                props: {
                  type: "text",
                  value: currentRow[item.key],
                },
                on: {
                  "on-change"(event) {
                    let key = param.column.key;
                    vm.edittingStore[param.index][key] = event.target.value;
                  },
                },
              });
            }
          };
        }
        if (item.handle) {
          item.render = (h, param) => {
            let currentRowData = this.thisTableData[param.index];
            let children = [];
            item.handle.forEach((item) => {
              if (item === "edit") {
                children.push(editButton(this, h, currentRowData, param.index));
              } else if (item === "delete") {
                children.push(
                  deleteButton(this, h, currentRowData, param.index)
                );
              } else if (item === "view") {
                children.push(viewButton(this, h, currentRowData, param.index));
              } else if (item === "approve") {
                children.push(
                  approveButton(this, h, currentRowData, param.index)
                );
              } else if (item === "status") {
                children.push(status(this, h, currentRowData, param.index));
              } else if (item === "raw_att") {
                children.push(raw_att(this, h, currentRowData, param.index));
              } else if (item === "final_att") {
                children.push(final_att(this, h, currentRowData, param.index));
              } else if (item === "remark") {
                children.push(
                  remarkInput(this, h, currentRowData, param.index)
                );
              } else if (item === "apply_username") {
                children.push(
                  apply_username(this, h, currentRowData, param.index)
                );
              } else if (item === "contract_name") {
                children.push(
                  contract_name(this, h, currentRowData, param.index)
                );
              } else if (item === "remark_s") {
                children.push(remark_s(this, h, currentRowData, param.index));
              } else if (item === "department") {
                children.push(department(this, h, currentRowData, param.index));
              } else if (item === "department_l") {
                children.push(
                  department_l(this, h, currentRowData, param.index)
                );
              } else if (item === "status_s") {
                children.push(
                  statusInput(this, h, currentRowData, param.index)
                );
              } else if (item === "approve_username") {
                children.push(
                  approve_username(this, h, currentRowData, param.index)
                );
              } else if (item === "createdAt") {
                children.push(timeInput(this, h, currentRowData, param.index));
              } else if (item === "clear") {
                children.push(
                  clearButton(this, h, currentRowData, param.index)
                );
              } else if (item === "apply_username_l") {
                children.push(
                  apply_username_l(this, h, currentRowData, param.index)
                );
              } else if (item === "approve_username_l") {
                children.push(
                  approve_username_l(this, h, currentRowData, param.index)
                );
              }
            });
            return h("div", children);
          };
        }
      });
    },
    handleBackdata(data) {
      let clonedData = JSON.parse(JSON.stringify(data));
      clonedData.forEach((item) => {
        delete item.editting;
        delete item.edittingCell;
        delete item.saving;
      });
      return clonedData;
    },
    re_upload(file) {
      var _this = this;
      setTimeout(function() {
        _this.$emit("re-upload", file, _this.row_index);
        return false;
      }, 100);
      return false;
    },
    set_row_id(index) {
      this.row_index = index;
    },
  },
  watch: {
    value(data) {
      this.init();
    },
  },
};
</script>
