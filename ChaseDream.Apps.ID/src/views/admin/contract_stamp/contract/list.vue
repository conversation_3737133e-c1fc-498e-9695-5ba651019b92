<style lang="less">
@import "../../../../styles/common.less";
@import "../stamp.less";
</style>

<template>
  <div class="tag" style="min-width:1100px;">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <div class="tool-bar">
            <router-link to="/console/contract_stamp/contract/add">
              <Button type="success" icon="plus">添 加</Button>
            </router-link>
            <br /><br />
          </div>
          <div class="edittable-con-1">
            <can-edit-table
              refs="table1"
              v-model="s_row"
              @on-search="getData"
              @on-updata="set_s_data"
              :columns-list="s_columns"
              class="searchTable sp_table"
            ></can-edit-table>
            <!-- <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable sp_table"></can-edit-table> -->
            <can-edit-table
              refs="table"
              @on-delete="handleDel"
              v-model="tRows"
              :columns-list="tColumns"
              @details="showDetails"
              @download_raw="download_raw"
              @download_final="download_final"
              @set-edit="setEdit"
              @re-upload="re_upload"
              class="listTable"
            ></can-edit-table>
          </div>
          <div class="page-bar">
            <Page
              :total="total"
              :page-size="page_size"
              :current="current"
              @on-change="getlist"
              v-if="showPage"
            ></Page>
          </div>
        </Card>
      </Col>
    </Row>
    <Modal v-model="details" title="详情" class="contract_details">
      <p>
        <strong>ID</strong>
        <span>{{ list_details.id }}</span>
      </p>
      <p>
        <strong>申请员</strong>
        <span>{{ list_details.apply_username }}</span>
      </p>
      <p>
        <strong>原始合同</strong>
        <span
          v-if="list_details.contract_name"
          style="cursor: pointer;color: rgb(45, 140, 240)"
          @click="download_raw(list_details)"
        >
          {{ list_details.contract_name }}
        </span>
      </p>
      <p>
        <strong>备注</strong>
        <span>{{ list_details.remark }}</span>
      </p>
      <p>
        <strong>批准时间</strong>
        <span>{{ list_details.a_at }}</span>
      </p>
      <p>
        <strong>批准员</strong>
        <span>{{ list_details.approve_username }}</span>
      </p>
      <p>
        <strong>盖章合同</strong>
        <span
          v-if="list_details.final_att"
          style="cursor: pointer;color: rgb(45, 140, 240)"
          @click="download_final(list_details)"
        >
          下载
        </span>
      </p>
      <p>
        <strong>驳回原因</strong>
        <span>{{ list_details.message }}</span>
      </p>
      <p>
        <strong>状态</strong>
        <span v-if="list_details.status === -2">发回修改</span>
        <span v-if="list_details.status === -1">拒绝</span>
        <span v-if="list_details.status === 0">等待系统预处理</span>
        <span v-if="list_details.status === 1">系统预处理中</span>
        <span v-if="list_details.status === 2">申请盖章</span>
        <span v-if="list_details.status === 3">等待批准</span>
        <span v-if="list_details.status === 4">已批准</span>
        <span v-if="list_details.status === 5">系统盖章中</span>
        <span v-if="list_details.status === 6">已盖章</span>
      </p>
      <p>
        <strong>创建时间</strong>
        <span>{{ list_details.c_at }}</span>
      </p>
      <div slot="footer">
        <Button type="primary" @click="details = false">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="reupload" title="重新上传合同" class="reupload">
      <div></div>
      <div slot="footer">
        <Button type="primary" @click="reupload = false">关闭</Button>
      </div>
    </Modal>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import stampData from "../data/stamp_data.js";
import canEditTable from "./components/canEditTable.vue";

export default {
  name: "contract_list",
  components: {
    canEditTable,
  },
  data() {
    return {
      isAccess: true,
      tColumns: [],
      // 传递给组件待处理的数据
      tRows: [],
      s_row: [
        {
          apply_username: "",
          contract_name: "",
          remark: "",
          approve_username: "",
          department: 0,
          stamp: 0,
          status: -9,
          begin: "",
          end: "",
          typeList: [],
          stamps: [],
        },
      ],
      s_columns: [],
      total: 0,
      showPage: false,
      page_size: 20,
      current: 1,
      list_details: {},
      details: false,
      isHandle: true,
      reupload: false,
      stamps: [],
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "contract_list") {
        this.isAccess = true;
        this.tColumns = [];
        this.tRows = [];
        this.s_row = [
          {
            apply_username: "",
            contract_name: "",
            remark: "",
            approve_username: "",
            department: 0,
            stamp: 0,
            status: -9,
            begin: "",
            end: "",
            typeList: [],
            stamps: [],
          },
        ];
        this.s_columns = [];
        this.total = 0;
        this.showPage = false;
        this.page_size = 20;
        this.current = 1;
        this.list_details = {};
        this.details = false;
        this.isHandle = false;
        this.reupload = false;
        this.stamps = [];
        this.getData();
      }
    },
  },
  mounted() {},
  methods: {
    getData() {
      //表格头数据
      this.tColumns = stampData.tColumns;
      this.s_columns = stampData.s_Columns;
      // 查询列表数据
      this.get_departments();
      this.get_stamps();
      // 获取数据后根据权限判断展示
      this.get_perms();
    },
    getlist(n) {
      var s = [];
      var text = "";

      if (!!this.s_row[0].apply_username) {
        s.push("apply_username=" + this.s_row[0].apply_username);
      }
      if (!!this.s_row[0].contract_name) {
        s.push("contract_name=" + this.s_row[0].contract_name);
      }
      if (!!this.s_row[0].remark) {
        s.push("remark=" + this.s_row[0].remark);
      }
      if (!!this.s_row[0].approve_username) {
        s.push("approve_username=" + this.s_row[0].approve_username);
      }
      if (
        this.s_row[0].department === 1 ||
        this.s_row[0].department === 2 ||
        this.s_row[0].department === 3 ||
        this.s_row[0].department === 4 ||
        this.s_row[0].department === 5 ||
        this.s_row[0].department === 6
      ) {
        s.push("department=" + this.s_row[0].department);
      }
      if (!!this.s_row[0].stamp) {
        s.push("stamp=" + this.s_row[0].stamp);
      }
      if (
        this.s_row[0].status === -2 ||
        this.s_row[0].status === -1 ||
        this.s_row[0].status === 3 ||
        this.s_row[0].status === 4 ||
        this.s_row[0].status === 5
      ) {
        s.push("status=" + this.s_row[0].status);
      }
      if (!!this.s_row[0].begin) {
        var str = Math.round(
          new Date(
            this.s_row[0].begin.substring(0, 4) +
              "/" +
              this.s_row[0].begin.substring(4, 6) +
              "/" +
              this.s_row[0].begin.substring(6)
          ).getTime() / 1000
        );
        s.push("begin=" + str);
      }
      if (!!this.s_row[0].end) {
        var str = Math.round(
          new Date(
            this.s_row[0].end.substring(0, 4) +
              "/" +
              this.s_row[0].end.substring(4, 6) +
              "/" +
              this.s_row[0].end.substring(6)
          ).getTime() / 1000
        );
        s.push("end=" + str);
      }
      if (s.length > 0) {
        text = "?" + s.join("&");
      }

      this.tRows = [];
      this.total = 0;
      this.current = n;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp" + text,
          method: "GET",
          params: {
            page: _this.current,
            page_size: _this.page_size,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.tRows = res.data.data.rows;
            for (var i = 0; i < _this.tRows.length; i++) {
              _this.tRows[i].isedit = false;
            }
            _this.total = res.data.data.count;
            if (_this.total > _this.page_size) {
              _this.showPage = true;
            }
            _this.get_perms();
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    showDetails(row) {
      this.list_details = row;
      this.list_details.c_at = "";
      this.list_details.a_at = "";
      if (!!this.list_details.created_at) {
        var d_t = new Date(parseInt(this.list_details.created_at + "000"));
        var y = d_t.getFullYear();
        var M =
          d_t.getMonth() + 1 < 10
            ? "0" + (d_t.getMonth() + 1)
            : d_t.getMonth() + 1;
        var d = d_t.getDate() < 10 ? "0" + d_t.getDate() : d_t.getDate();
        var hh = d_t.getHours() < 10 ? "0" + d_t.getHours() : d_t.getHours();
        var m =
          d_t.getMinutes() < 10 ? "0" + d_t.getMinutes() : d_t.getMinutes();
        var s =
          d_t.getSeconds() < 10 ? "0" + d_t.getSeconds() : d_t.getSeconds();
        this.list_details.c_at =
          y + "-" + M + "-" + d + " " + hh + ":" + m + ":" + s;
      }
      if (!!this.list_details.approve_at) {
        var d_t = new Date(parseInt(this.list_details.approve_at + "000"));
        var y = d_t.getFullYear();
        var M =
          d_t.getMonth() + 1 < 10
            ? "0" + (d_t.getMonth() + 1)
            : d_t.getMonth() + 1;
        var d = d_t.getDate() < 10 ? "0" + d_t.getDate() : d_t.getDate();
        var hh = d_t.getHours() < 10 ? "0" + d_t.getHours() : d_t.getHours();
        var m =
          d_t.getMinutes() < 10 ? "0" + d_t.getMinutes() : d_t.getMinutes();
        var s =
          d_t.getSeconds() < 10 ? "0" + d_t.getSeconds() : d_t.getSeconds();
        this.list_details.a_at =
          y + "-" + M + "-" + d + " " + hh + ":" + m + ":" + s;
      }
      this.details = true;
    },
    set_s_data(obj) {
      this.s_row = obj;
    },
    download_raw(row) {
      var dataIn = {
        id: row.id,
        type: 0,
        aid: row.raw_aid,
      };
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/download",
          method: "POST",
          data: dataIn,
          responseType: "blob",
        })
        .then(function(res) {
          if (res.status === 200) {
            console.log(res);
            const pdfUrl = window.URL.createObjectURL(
              new Blob([res.data], {
                type: `application/${row.raw_att.ext.replace(".", "")}`,
              })
            );
            //window.open(pdfUrl); //  预览
            const fname = row.contract_name + row.raw_att.ext; // 下载文件的名字
            const link = document.createElement("a");
            link.href = pdfUrl;
            link.setAttribute("download", fname);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          } else {
            _this.$Notice.error({
              title: res.message,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: "下载失败！",
          });
          // console.log(err)
        });
    },
    download_final(row) {
      var dataIn = {
        id: row.id,
        type: 1,
        aid: row.final_aid,
      };
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/download",
          method: "POST",
          data: dataIn,
          responseType: "blob",
        })
        .then(function(res) {
          if (res.status === 200) {
            const pdfUrl = window.URL.createObjectURL(
              new Blob([res.data], {
                type: `application/pdf`,
              })
            );
            //window.open(pdfUrl); //  预览
            const fname = "[已盖章] " + row.contract_name + ".pdf"; // 下载文件的名字
            const link = document.createElement("a");
            link.href = pdfUrl;
            link.setAttribute("download", fname);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          } else {
            _this.$Notice.error({
              title: res.message,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: "下载失败！",
          });
          // console.log(err)
        });
    },
    setEdit(row, i, remark, str) {
      /*if(str === 'remark'){
                    this.tRows[i].remark = remark;
                }else */
      this.tRows[i] = row;

      var list_data = [];
      list_data = JSON.parse(JSON.stringify(this.tRows));
      this.tRows = [];
      this.tRows = JSON.parse(JSON.stringify(list_data));
    },
    re_upload(file, n) {
      var _this = this;
      _this.isMath = false;
      var check = function() {
        if (
          file.type === "application/pdf" ||
          file.name.split(".").pop() === "docx" ||
          file.name.split(".").pop() === "doc"
        ) {
          clearInterval(set);
          _this.uploadFile(file, n);
        } else {
          clearInterval(set);
          _this.$Notice.error({
            title: "请上传PDF格式的文件！",
          });
          return false;
        }
      };
      var set = setInterval(check, 40);
      if (!this.isMath) {
        return false;
      }
    },
    uploadFile(file, i) {
      var form = new FormData();
      form.append("file", file);
      form.append("id", this.tRows[i].id);
      const _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/upload",
          method: "POST",
          dataType: "JSON",
          contentType: false,
          processData: false,
          data: form,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.$Notice.success({
              title: "上传成功",
            });
            _this.getlist(_this.current);
          } else {
            _this.$Notice.error({
              title: res.data.errors.message,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
      return false;
    },
    get_row_data(n) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/" + this.tRows[n].id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.tRows[n] = res.data.data;
            var list_data = [];
            list_data = JSON.parse(JSON.stringify(_this.tRows));
            _this.tRows = [];
            _this.tRows = JSON.parse(JSON.stringify(list_data));
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    get_departments() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/user/departments",
          method: "GET",
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.s_row[0].typeList = res.data.data;
            if (_this.s_row[0].typeList.length > 1) {
              var item = {
                id: 0,
                name: "全部",
              };
              _this.s_row[0].typeList.unshift(item);
              _this.getlist(1);
            } else {
              _this.s_row[0].department = _this.s_row[0].typeList[0].id;
              _this.getlist(1);
            }

            /* var list_data = [];
                        list_data = JSON.parse(JSON.stringify(_this.s_row));
                        _this.s_row = [];
                        _this.s_row = JSON.parse(JSON.stringify(list_data));*/
          } else {
            console.log(err);
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    get_perms() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/stamps/perms",
          method: "GET",
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            //_this.set_perms(res.data.data);
            _this.get_user_info(res.data.data);
          } else {
            console.log(err);
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    get_user_info(data) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/info",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            var applies = data.applies;
            var approves = data.approves;
            var user_info = res.data.data.user;

            if (user_info.role_name.indexOf("管理员") === -1) {
              //判断权限后删除列
              if (_this.tColumns[1].title == "部门") {
                _this.tColumns.splice(1, 1);
                _this.s_columns.splice(1, 1);
              }
            }
            _this.set_perms(applies, approves, user_info);
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_perms(applies, approves, info) {
      if (this.tRows.length > 0) {
        for (var i = 0; i < this.tRows.length; i++) {
          this.tRows[i].isApply = false;
          this.tRows[i].isApprove = false;
          this.tRows[i].isAdmin = false;
          if (info.role_name.indexOf("管理员") !== -1) {
            this.tRows[i].isAdmin = true;
          }
          for (var j = 0; j < applies.length; j++) {
            if (this.tRows[i].stamp === applies[j]) {
              if (this.tRows[i].apply_uid === info.id) {
                this.tRows[i].isApply = true;
              }
            }
          }
          for (var j = 0; j < approves.length; j++) {
            if (this.tRows[i].stamp === approves[j]) {
              if (this.tRows[i].department === info.department) {
                this.tRows[i].isApprove = true;
              }
            }
          }
          this.tRows[i].stamps = this.stamps;
        }

        var list_data = [];
        list_data = JSON.parse(JSON.stringify(this.tRows));
        this.tRows = [];
        this.tRows = JSON.parse(JSON.stringify(list_data));
      } else {
        var _this = this;
        setTimeout(function() {
          _this.set_perms(applies, approves, info);
        }, 600);
      }
    },
    get_stamps() {
      this.stamps = [];
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/contract_stamp/stamps/roles",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.stamps = res.data.data.stamps;
            _this.s_row[0].stamps = JSON.parse(JSON.stringify(_this.stamps));
            var item = {
              id: 0,
              name: "全部",
            };
            _this.s_row[0].stamps.unshift(item);

            var list_data = [];
            list_data = JSON.parse(JSON.stringify(_this.s_row));
            _this.s_row = [];
            _this.s_row = JSON.parse(JSON.stringify(list_data));
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style></style>
