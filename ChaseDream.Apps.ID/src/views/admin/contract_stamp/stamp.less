.listTable{
  .ivu-table-wrapper{
    border-top: none;
  }
  .ivu-table-header{
    display: none;
  }
  .ivu-table-cell{
    .ivu-input{
      height: 28px;
      line-height: 28px;
    }
  }
}
.searchTable .ivu-table{
  td{
    height: 50px;
    background-color: #f2f3f6;

    input.ivu-input{
      height:28px;
      line-height: 28px;
    }
  }
  .ivu-table-cell{
    padding-left: 6px;
    padding-right: 6px;
  }
  .ivu-select-single{
    .ivu-select-selection,.ivu-select-placeholder{
      height:28px;
      line-height: 28px;
      border-radius: 0;
    }
    .ivu-select-selected-value{
      height:28px;
      line-height: 28px;
    }
  }
}

.stamp_type{
  .ivu-radio-group{
    .stamp_1,.stamp_2,.stamp_3,.stamp_4{
      width: 100px;
      height: 100px;
      background-size: 90% !important;
      margin-right: 10px;
      border:1px solid #e9ebf0;
      position: relative;
      &.on:after{
        content: '';
        width: 20px;
        height: 20px;
        background-image: url("../../../images/admin/dg.png");
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 140%;
        position: absolute;
        right: 0;
        bottom: 0;
      }
      /*&.on{
        background-color: #fec2c2;
      }*/
    }
    .stamp_1{
      background: url("https://static.chasedream.com/contract/stamp/1.png") no-repeat center center;
    }
    .stamp_2{
      background: url("https://static.chasedream.com/contract/stamp/1.png") no-repeat center center;
    }
    .stamp_3{
      background: url("https://static.chasedream.com/contract/stamp/1.png") no-repeat center center;
    }
    .stamp_4{
      background: url("https://static.chasedream.com/contract/stamp/1.png") no-repeat center center;
    }
  }
}
.upload_contract{
  .ivu-upload.ivu-upload-select{
    border: 1px solid #e9ebf0;
  }
  .ivu-icon-trash-a{
    color: #ff0101;
    cursor: pointer;
  }
}
#pdf-container {
  min-height: 1000px;
  .pdf-container{
    width: 793.33px;
    position: relative;
    background: url("../../../images/pdf/ye.png") top right;
    padding-right: 90px;
    box-sizing: content-box;
    min-height: 900px;
    img.page_img{
      width: 100%;
      height:100%;
      background-color: #fff;
      vertical-align: top;
      min-height: 900px;
    }
    .mark{
      position: relative;
      .delete{
        position: absolute;
        bottom: -20px;
        left: 0;
        display: block;
        width: 100%;
        text-align: center;
        background: #e9ebf0;
      }
      .stamp_name{
        display: none;
      }
    }
  }
  .pdf-header{
    position: relative;
    font-size: 16px;
    padding: 11px;
    background: #fff;
    width: 883.33px;
    border-bottom: 1px solid #e9ebf0;
    h3{
      font-size: 16px;
    }
    .pages{
      position: absolute;
      left: 50%;
      top: 10px;
      margin-top: 0;
      width: 280px;
      margin-left: -140px;
      .ivu-page-simple-pager input{
        width: 40px;
      }
      .ivu-page.ivu-page-simple{
        margin-top: 0;
      }
      .to-last{
        position: absolute;
        top: -3px;
        right: 0;
        font-size: 12px;
        border: 1px solid #e9ebf0;
      }
    }
    .approve_status{
      position: absolute;
      right: 10px;
      top: 6px;
      font-size: 12px;
    }
  }
}
.pdf-box{
  background: #efefef;padding: 10px;position: relative;display: flex;
  .side-bar{
    width: 120px;
    position: relative;
    margin-left: 10px;
    .side-barinfo{
      width: 184px;
      min-height: 180px;
      background: #fff;
      box-sizing: content-box;
      padding: 2px;
      padding-right: 22px;
      /*position: absolute;
      left: 0px;
      top: 0px;*/
      text-align: center;
      h2{
        font-size: 16px;
        padding: 10px;
        text-align: center;
        margin-bottom: 10px;
        border-bottom: 1px solid #e9ebf0;
      }
      .drag_seal{
        margin-bottom: 10px;
        background: #EDF0F5;
        border-radius: 5px;
        position: relative;
        /*padding-right: 20px;*/

        &.checked{
          background: #fff;
          border: 1px solid #EDF0F5;
          box-sizing: border-box;
          position: relative;
          &:after{
            content: '';
            width: 20px;
            height: 20px;
            background-image: url("../../../images/admin/dg.png");
            background-repeat: no-repeat;
            background-position: center center;
            background-size: 140%;
            position: absolute;
            right: 0;
            bottom: 0;
          }
        }
        img{
          vertical-align: top;
        }
        .stamp_name{
          position: absolute;right: -20px;top: 0;width: 20px;font-size: 16px;text-align: center;align-items: center;display: flex;height: 100%;color:#7C7E80
        }
      }
    }

  }
}
.approve{
  .sub-btns{
    margin-top: 20px;
  }
}
.contract_details{
  p{
    display: flex;

    strong{
      width: 20%;
      text-align: right;
      padding-right:20px;
    }
    span{
      width: 80%;
      word-break: break-word;
      position: relative;

      textarea{
        width: 100%;
        position: absolute;
        left: 0;
        top:0;
        bottom:0;
        resize: none;
        border:none;
        box-shadow: none;
        /*background: #000;
        color: #fff;*/
      }
    }
  }
  .ivu-modal{
    width: 700px !important;
  }
}
.contract-add,.contract-approve{
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
}
.main .contract-add .ivu-upload .ivu-btn{
  width: auto;
  height: auto;
  border: none;
}
