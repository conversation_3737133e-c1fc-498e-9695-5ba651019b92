export const  tColumns = [
    {
        title: 'ID/状态',
        align: 'center',
        key: 'handle',
        width: 85,
        handle: ['status']
    },
    {
        title: '部门/印章',
        align: 'center',
        key: 'handle',
        width: 110,
        handle: ['department_l']
    },
    {
        title: '文件名',
        align: 'center',
        key: 'handle',
        width: 300,
        handle: ['raw_att']
    },
    {
        title: '备注',
        align: 'center',
       
        key: 'handle',
        handle: ['remark']
    },
    {
        title: '批准人',
        align: 'center',
        key: 'handle',
        width: 110,
        handle: ['approve_username_l']
    },
    /*{
        title: '盖章合同',
        align: 'center',
        key: 'handle',
        width: 95,
        handle: ['final_att']
    },*/
   
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 100,
        render: (h, params) => {
            if(!!params.row.created_at){
                var d_t = new Date(parseInt(params.row.created_at + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('div',{},[
                    h('p',{}, y + '-' + M + '-' + d),
                    h('p',{},hh + ':' + m + ':' + s)
                ])
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 140,
        key: 'handle',
        handle: ['approve','view','delete','final_att']
    },
];
export const  s_Columns = [
    {
        title: 'ID/状态',
        align: 'center',
        key: 'handle',
        width: 85,
        handle: ['status_s']
    },
    // {
    //     title: 'ID',
    //     key: 'handle',
    //     width: 60,
    //     align: 'center',
    //     handle: ['id']
    // },
    {
        title: '部门/印章',
        align: 'left',
        key: 'handle',
        width: 110,
        handle: ['department']
    },
    // {
    //     title: '申请人',
    //     align: 'center',
    //     key: 'handle',
    //     width: 110,
    //     handle: ['apply_username']
    // },
    {
        title: '文件名',
        align: 'center',
        key: 'handle',
        width: 300,
        handle: ['contract_name']
    },
    {
        title: '备注',
        align: 'center',
       
        key: 'handle',
        handle: ['remark_s']
    },
    {
        title: '申请人/批准人',
        align: 'center',
        key: 'handle',
        width: 110,
        handle: ['approve_username']
    },
   /* {
        title: '盖章合同',
        align: 'center',
        key: 'handle',
        width: 95,
        handle: ['final_att_s']
    },*/
  
    {
        title: '创建时间',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['createdAt']
    },
    {
        title: '操作',
        align: 'center',
        width: 140,
        key: 'handle',
        handle: ['clear']
    },
];

const tagData = {
    tColumns: tColumns,
    s_Columns: s_Columns
};

export default tagData;
