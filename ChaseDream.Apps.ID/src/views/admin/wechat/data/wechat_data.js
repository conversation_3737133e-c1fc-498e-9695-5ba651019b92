export const gColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '群名',
        align: 'center',
        key: 'name',
        width: 150
    },
    {
        title: '群管理员',
        align: 'center',
        key: 'admin',
        width: 120
    },
    {
        title: 'TID',
        align: 'center',
        key: 'tid',
        width: 90,
        render: (h, params) => {
            if (!!params.row.tid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            } else {
                return h('span', {}, params.row.tid)
            }
        }
    },
    /*{
        title: 'FID',
        align: 'center',
        key: 'fid',
        width: 80
    },*/
    {
        title: '帖子标题',
        align: 'center',
        key: 'subject',
        //width: 80，
        render: (h, params) => {
            if (!!params.row.tid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.subject)
            } else {
                return h('span', {}, params.row.subject)
            }
        }
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 100,
        render: (h, params) => {
            if (!!params.row.created_at) {

                return h('span', {}, params.row.created_at.split('T')[0] + ' ' + params.row.created_at.split('T')[1].split('.')[0])
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '开始/停止',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['status']
    },
    {
        title: '操作',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['edit', 'view', 'delete']
    }
];
export const aColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '微信名',
        align: 'center',
        key: 'wechat',
        width: 150,
    },
    {
        title: '论坛用户名',
        align: 'center',
        key: 'forum_username'
    },
    {
        title: '论坛UID',
        align: 'center',
        key: 'forum_uid'
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['edit', 'delete']
    },
]
export const s_Columns = [
    {
        title: 'ID',
        key: 'handle',
        width: 60,
        align: 'center',
        handle: ['id']
    },
    {
        title: '微信名',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['wechat']
    },
    {
        title: '论坛用户名',
        align: 'center',
        key: 'handle',
        handle: ['forum_username']
    },
    {
        title: '论坛UID',
        align: 'center',
        key: 'handle',
        handle: ['forum_uid']
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['clear']
    },
]
export const mColumns = [
    /*{
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },*/
    {
        title: '状态',
        align: 'center',
        key: 'status',
        width: 60,
        render: (h, params) => {
            var style = {};
            var text = '';
            if (params.row.status === 0) {
                style.color = '#ff9900';
                text = '待处理';
            } else if (params.row.status === 1) {
                style.color = '#19be6b';
                text = '已转发';
            } else if (params.row.status === 2) {
                style.color = '#ed3f14';
                text = '失败';
            }
            return h('Tooltip', {
                props: {
                    content: text,
                    placement: 'right'
                }
            }, [
                h('Icon', {
                    style: style,
                    props: {
                        type: 'record',
                        size: 14
                    }
                })
            ])
        }
    },
    {
        title: 'TID',
        align: 'center',
        key: 'tid',
        width: 90,
        render: (h, params) => {
            if (!!params.row.tid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            } else {
                return h('span', {}, '')
            }
        }
    },
    /* {
         title: 'FID',
         align: 'center',
         key: 'fid',
         width: 80
     },*/
    {
        title: '群名称',
        align: 'center',
        key: 'topic',
        width: 120
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'wx_id',
        width: 90
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'wx_name',
        width: 120
    },
    {
        title: '文本',
        align: 'center',
        key: 'text',
        render: (h, params) => {
            return h('p', {
                domProps: {
                    innerHTML: params.row.text
                }
            })
        }
    },
    {
        title: '类型',
        align: 'center',
        key: 'type',
        width: 100,
        render: (h, params) => {
            var text = '';
            if (params.row.type === 5) {
                text = '表情';
            } else if (params.row.type === 6) {
                text = '图片';
            } else if (params.row.type === 7) {
                text = '文本';
            } else if (params.row.type === 14) {
                text = '链接';
            } else if (params.row.type === 1) {
                text = '附件';
            } else if (params.row.type === 2) {
                text = '语音';
            } else if (params.row.type === 15) {
                text = '视频';
            }
            return h('span', {}, text)
        }
    },
    /*{
        title: '错误信息',
        align: 'center',
        key: 'error_message',
        width: 120
    },*/
    {
        title: '操作',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: ['view']//'edit','delete'
    },
]

const tagData = {
    gColumns: gColumns,
    aColumns: aColumns,
    mColumns: mColumns,
    s_Columns: s_Columns
};

export default tagData;
