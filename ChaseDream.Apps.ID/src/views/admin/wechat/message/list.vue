<style lang="less">
    @import '../../../../styles/common.less';
    @import '../wechat.less';
</style>

<template>
    <div class="wechat">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <br>
                <!--<div class="tool-bar">
                    <router-link to="/console/wechat/group/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>-->
                <div class="edittable-con-1">
                    <can-edit-table refs="table" @on-delete="handleDel" @details="show_details" v-model="Rows" :columns-list="Columns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="详情" class="row_details msg_details">
            <p>
                <strong>ID</strong>
                <span>{{row_details.id}}</span>
            </p>
            <p>
                <strong>TID</strong>
                <span>{{row_details.tid}}</span>
            </p>
            <p>
                <strong>FID</strong>
                <span>{{row_details.fid}}</span>
            </p>
            <p>
                <strong>群名称</strong>
                <span>{{row_details.topic}}</span>
            </p>
            <p>
                <strong>微信ID</strong>
                <span>{{row_details.wx_id}}</span>
            </p>
            <p>
                <strong>微信昵称</strong>
                <span>{{row_details.wx_name}}</span>
            </p>
            <p>
                <strong>文本</strong>
                <span>{{row_details.text}}</span>
            </p>
            <p>
                <strong>类型</strong>
                <span>{{row_details.type_text}}</span>
            </p>
            <p>
                <strong>文件名</strong>
                <span>{{row_details.filename}}</span>
            </p>
            <p>
                <strong>文件大小</strong>
                <span>{{row_details.filesize}}</span>
            </p>
            <p>
                <strong>附件名</strong>
                <span>{{row_details.attachment}}</span>
            </p>
            <p>
                <strong>图片（是、否）</strong>
                <span>{{!!row_details.isimage ? '是' : '否'}}</span>
            </p>
            <p>
                <strong>宽度</strong>
                <span>{{row_details.width}}</span>
            </p>
            <p>
                <strong>高度</strong>
                <span>{{row_details.height}}</span>
            </p>
            <p>
                <strong>缩列图（是否）</strong>
                <span>{{!!row_details.thumb ? '是' : '否'}}</span>
            </p>
            <p>
                <strong>缩列图宽度</strong>
                <span>{{row_details.thumbwidth}}</span>
            </p>
            <p>
                <strong>缩列图高度</strong>
                <span>{{row_details.thumbheight}}</span>
            </p>
            <p>
                <strong>状态</strong>
                <span>{{row_details.status_text}}</span>
            </p>
            <p>
                <strong>错误信息</strong>
                <span>{{row_details.error_message}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span v-if="!!row_details.created_at">{{row_details.created_at.split('T')[0] +' '+ row_details.created_at.split('T')[1].split('.')[0]}}</span>
            </p>
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from '../data/wechat_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'wechat_group_account_message',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                Columns: [],
                Rows:[],
                row_details: {},
                details: false,
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wechat_group_account_message'){
                    this.isAccess = true;
                    this.Columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.Columns = wechatData.mColumns;
                this.getlist(1);
            },
            getlist (n){
                this.Rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/account_message',
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.Rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            show_details (row){
                //console.log(row)
                this.row_details = row;
                this.row_details.type_text = '';
                if(this.row_details.type === 5){
                    this.row_details.type_text = '表情';
                }else if(this.row_details.type === 6){
                    this.row_details.type_text = '图片';
                }else if(this.row_details.type === 7){
                    this.row_details.type_text = '文本';
                }else if(this.row_details.type === 14){
                    this.row_details.type_text = '链接';
                }else if(this.row_details.type === 1){
                    this.row_details.type_text = '附件';
                }else if(this.row_details.type === 2){
                    this.row_details.type_text = '语音';
                }else if(this.row_details.type === 15){
                    this.row_details.type_text = '视频';
                }
                this.row_details.status_text= '';
                if(this.row_details.status === 0){
                    this.row_details.status_text = '待处理';
                }else if(this.row_details.status === 1){
                    this.row_details.status_text = '已转发';
                }else if(this.row_details.status === 2){
                    this.row_details.status_text = '失败';
                }
                this.details = true;
            },
            close (){
                this.row_details = {};
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
