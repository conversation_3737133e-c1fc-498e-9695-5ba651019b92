<style lang="less">
    @import '../../../../styles/common.less';
    @import '../wechat.less';
</style>

<template>
    <div class="wechat-account-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加关联帐号
                </p>
                <Form :model="form" :label-width="150">
                    <FormItem prop="wechat" label="微信名：" required>
                        <Input v-model="form.wechat" placeholder="请输入微信名" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="forum_username" label="论坛用户名：" required>
                        <Input v-model="form.forum_username" placeholder="请输入论坛用户名" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="forum_uid" label="论坛UID：" required>
                        <Input v-model="form.forum_uid" placeholder="请输入论坛UID" style="width:300px;"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'wechat_account_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    wechat: '',
                    forum_username: '',
                    forum_uid: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wechat_account_add'){
                    this.form = {
                        wechat: '',
                        forum_username: '',
                        forum_uid: ''
                    }
                    this.$refs['form'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                if(!this.form.wechat){
                    this.$Notice.error({
                        title: '微信名不能为空！'
                    });
                    return false;
                }
                if(!this.form.forum_username && !this.form.forum_uid){
                    this.$Notice.error({
                        title: '论坛用户名和论坛UID至少填写一个！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/account',
                    method:'POST',
                    data: this.form,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'wechat_account_add');
                        _this.$store.commit('closePage', 'wechat_account_add');
                        _this.$router.push({
                            name: 'wechat_account_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {
                    wechat: '',
                    forum_username: '',
                    forum_uid: ''
                }
                this.$store.commit('removeTag', 'wechat_account_add');
                this.$router.push({
                    name: 'wechat_account_list'
                });
            }
        }
    };
</script>

<style>

</style>
