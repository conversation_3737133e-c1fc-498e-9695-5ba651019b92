<style lang="less">
    @import '../../../../styles/common.less';
    @import '../wechat.less';
</style>

<template>
    <div class="wechat-account-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑关联帐号
                </p>
                <Form :model="form" :label-width="150">
                    <FormItem prop="wechat" label="微信名：" required>
                        <Input v-model="form.wechat" placeholder="请输入微信名" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="forum_username" label="论坛用户名：" required>
                        <Input v-model="form.forum_username" placeholder="请输入论坛用户名" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="forum_uid" label="论坛UID：" required>
                        <Input v-model="form.forum_uid" placeholder="请输入论坛UID" style="width:300px;"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'wechat_account_edit',
        components: {

        },
        data () {
            return {
                form: {},
                access_token: this.$store.state.user.access_token,
                isAccess: true,
                errMsg:''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wechat_account_edit'){
                    this.form = {}
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getData();
        },
        methods: {
            getData (){
                this.form = this.$store.state.users.currentRow;
                if(!this.form.id){
                    this.$router.push({
                        name: 'wechat_account_list'
                    });
                }
            },
            handleSubmit () {
                if(!this.form.wechat){
                    this.$Notice.error({
                        title: '微信名不能为空！'
                    });
                    return false;
                }
                if(!this.form.forum_username && !this.form.forum_uid){
                    this.$Notice.error({
                        title: '论坛用户名和论坛UID至少填写一个！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/account',
                    method:'PUT',
                    data: this.form,
                }).then(function (res) {
                    //console.log(res.data);
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'wechat_account_edit');
                        _this.$store.commit('closePage', 'wechat_account_edit');
                        _this.$router.push({
                            name: 'wechat_account_list'
                        });
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            closePage () {
                this.form = {}
                this.$store.commit('removeTag', 'wechat_account_edit');
                this.$store.commit('closePage', 'wechat_account_edit');
                this.$router.push({
                    name: 'wechat_account_list'
                });
            }

        }
    };
</script>

<style>

</style>
