<style lang="less">
    @import '../../../../styles/common.less';
    @import '../wechat.less';
</style>

<template>
    <div class="wechat">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑标签
                </p>
                <Form :model="form" :label-width="90">
                    <FormItem prop="name" label="群名：" required>
                        <Input v-model="form.name" placeholder="请输入群名" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="admin" label="群管理员：" required>
                        <Input v-model="form.admin" placeholder="请输入群管理员" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="tid" label="TID：" required>
                        <Input v-model="form.tid" placeholder="请输入TID" style="width:300px;"></Input>
                    </FormItem>
                    <!--<FormItem prop="fid" label="帖子版块：" required>
                        <Select v-model="form.fid" style="width:300px" :label-in-value="true">&lt;!&ndash;@on-change="getType" &ndash;&gt;
                            <OptionGroup :label="item.name" v-for="item in forum_nav" :key="item.index">
                                <Option v-for="opt in item.forums" :value="opt.fid" :key="opt.index">{{ opt.name }}</Option>
                            </OptionGroup>
                        </Select>
                        &lt;!&ndash;<Select v-model="form.typeid" style="width:200px">
                            <Option v-for="item in types" :value="item.typeid" :key="item.index">{{ item.name }}</Option>
                        </Select>&ndash;&gt;
                    </FormItem>
                    <FormItem prop="subject" label="帖子标题：" required>
                        <Input v-model="form.subject" placeholder="请输入帖子标题" style="width:300px;"></Input>
                    </FormItem>-->
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'wechat_group_edit',
        components: {

        },
        data () {
            return {
                form:{},
                forum_nav: [],
                types: [],
                errMsg: '',
                access_token: this.$store.state.user.access_token,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wechat_group_edit'){
                    this.form = {};
                    this.forum_nav = [];
                    this.types = [];
                    this.errMsg = '';
                    this.getData();
                }
            },
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData (){
                this.form = this.$store.state.users.currentRow;
                this.form.fid = this.form.fid + '';
                //this.getForum_nav();
            },
            getForum_nav (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/forum_nav',
                    method:'GET',
                    params: {
                        page: _this.current,
                        pageSize: _this.pageSize
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.forum_nav = res.data.data;
                        /*setTimeout(function () {
                            _this.getType({value: _this.form.fid, label:_this.form.forum});
                        },300);*/
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getType (e){
                this.form.forum = e.label;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/forum_nav_sub',
                    method:'GET',
                    params: {
                        fid: e.value
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.types = res.data.data;
                        _this.form.typeid = _this.form.typeid + '';
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {
                if(!this.form.name){
                    this.$Notice.error({
                        title: '群名不能为空！'
                    });
                    return false;
                }
                if(!this.form.admin){
                    this.$Notice.error({
                        title: '群管理员不能为空！'
                    });
                    return false;
                }
                if(!this.form.tid){
                    this.$Notice.error({
                        title: 'TID不能为空！'
                    });
                    return false;
                }
                /*if(!this.form.fid){
                    this.$Notice.error({
                        title: '请选择帖子版块！'
                    });
                    return false;
                }
                if(!this.form.subject){
                    this.$Notice.error({
                        title: '帖子标题不能为空！'
                    });
                    return false;
                }*/
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group',
                    method:'PUT',
                    data: this.form,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'wechat_group_edit');
                        _this.$store.commit('closePage', 'wechat_group_edit');
                        _this.$router.push({
                            name: 'wechat_group_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {}
                this.$store.commit('removeTag', 'wechat_group_edit');
                this.$store.commit('closePage', 'wechat_group_edit');
                this.$router.push({
                    name: 'wechat_group_list'
                });
            }

        }
    };
</script>

<style>

</style>
