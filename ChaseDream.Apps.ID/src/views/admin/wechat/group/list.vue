<style lang="less">
    @import '../../../../styles/common.less';
    @import '../wechat.less';
</style>

<template>
    <div class="wechat">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <br>
                <div class="tool-bar">
                    <router-link to="/console/wechat/group/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <Button type="text" icon="eye" @click="show_status" v-if="!status" style="float: right;color: #19be6b">查看微信账号状态</Button>
                    <span v-if="!!status" style="float: right;color: #19be6b;margin-right: 20px;">{{status}}</span>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table" @on-delete="handleDel" @details="show_details" v-model="Rows" :columns-list="Columns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="详情" class="row_details">
            <p>
                <strong>ID</strong>
                <span>{{row_details.id}}</span>
            </p>
            <p>
                <strong>群名</strong>
                <span>{{row_details.name}}</span>
            </p>
            <p>
                <strong>群管理员</strong>
                <span>{{row_details.admin}}</span>
            </p>
            <p>
                <strong>TID</strong>
                <span>{{row_details.tid}}</span>
            </p>
            <p>
                <strong>FID</strong>
                <span>{{row_details.fid}}</span>
            </p>
            <p>
                <strong>帖子标题</strong>
                <span>{{row_details.subject}}</span>
            </p>
            <p>
                <strong>qr_code</strong>
                <span>{{row_details.qr_code}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span v-if="!!row_details.created_at">{{row_details.created_at.split('T')[0] +' '+ row_details.created_at.split('T')[1].split('.')[0]}}</span>
            </p>
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from '../data/wechat_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'wechat_group_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                Columns: [],
                Rows:[],
                row_details: {},
                details: false,
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                status: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wechat_group_list'){
                    this.isAccess = true;
                    this.Columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.status = '';
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.Columns = wechatData.gColumns;
                this.getlist(1);
            },
            getlist (n){
                this.Rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group',
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.Rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            show_details (row){
                //console.log(row)
                this.row_details = row;
                this.details = true;
            },
            show_status (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/status',
                    method: 'GET',
                    params: {
                       /* page: _this.current,
                        page_size: _this.page_size*/
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.status = res.data.data.status;

                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            close (){
                this.row_details = {};
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
