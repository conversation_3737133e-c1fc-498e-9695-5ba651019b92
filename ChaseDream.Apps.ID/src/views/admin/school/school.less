.university-add,.university-edit,.college-add,.college-edit{
  #city_check .searchIpt{
    width: auto;
  }
  #city_check button{
    line-height: 20px;
  }

  .logo-item{
    .ivu-btn{
      width: 120px;
      height: 120px;
      border: 1px dashed #DDDEE1;
      .ivu-icon-plus{
        width: 24px;
        height: 24px;
        font-size: 24px;
        color: #DDDEE1;
      }
    }
    .ivu-upload-list{
      display: none;
    }
  }
  .campus-item{
    background: #F5F7FC;
    width: 100%;
    height: 120px;
    font-size: 12px;
    color: #495060;
    padding: 10px;
    margin-bottom: 20px;
    position: relative;
    cursor: pointer;
    p{
      line-height: 32px;
    }
    .ivu-poptip{
      position: absolute;
      right: 10px;
      top: 5px;
      z-index: 999;
    }
    .ivu-icon-trash-a{
      color: #ed3f14;
      display: none;
    }
    &:hover{
      .ivu-icon-trash-a{
        display: block;
      }
    }
  }
  .campus-add{
    width: 100%;
    height: 40px;
    border: 1px dashed #DDDEE1;
    .ivu-icon-plus{
      width: 24px;
      height: 24px;
      font-size: 24px;
      color: #DDDEE1;
    }
  }
  .campus,.logo-item{
    .ivu-upload{
      .ivu-btn{
        width: 120px;
        height: 120px;
        border: 1px dashed #DDDEE1;
        .ivu-icon-plus{
          width: 24px;
          height: 24px;
          font-size: 24px;
          color: #DDDEE1;
        }
      }
    }
    .file-list{
      width: 120px;
      height: 120px;
      position: relative;

      img{
        border-style: none;
        width: 120px;
        height: 120px;
      }
      p.del{
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        cursor: pointer;
        background: rgba(0,0,0,0.3);
        display: none;

        .ivu-icon-trash-a{
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -8px;
          margin-top: -11px;
          width: 16px;
          height: 22px;
          color: #ed3f14;
        }
      }
      div:hover{
        .del{
          display: block;
        }
      }
    }
  }
}
.ivu-form-item-content{
  .line{
    position: relative;
    height: 32px;

    strong{
      position: relative;
      z-index: 2;
      left: -50px;
    }
    span{
      position: absolute;
      width: 100%;
      height: 1px;
      background: #f9f9f9;
      z-index: 1;
      top:16px;
      left: 0;
    }
  }
}
.university-add,.university-edit,.college-add,.college-edit{
  .campus{
    .ivu-upload{
      width: 60px;
      height: 60px;
      display: inline-block;
      .ivu-btn{
        width: 60px;
        height: 60px;
      }
    }
    .file-list{
      width: auto;
      height: auto;
      display: inline-block;
      vertical-align: top;
      div{
        display: inline-block;
        width: 60px;
        height: 60px;
        position: relative;
        margin-right: 10px;
        margin-bottom: 10px;
        border: 1px solid #efefef;
        img{
          width: 100%;
          height: auto;
         /* display: block;*/
        }
        .del{
          display: none;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          margin: 0;
          text-align: center;
          line-height: 60px;
          background: rgba(0,0,0,.3);
        }
      }
      div:hover{
        .del{
          display: block;
        }
      }
    }
  }
}
.college-program{
  .programs{
    .program{
      position: relative;
      .delItem{
        position: absolute;
        right: 20px;
        top: 20px;
        color: #bbbec4;
        &:hover{
          color: #ed3f14;
        }
      }
      .plusItem{
        border: 1px dashed #efefef;
      }
    }
  }
}
