<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="university-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    编辑大学
                </p>
                <Form ref="form" :model="form" :label-width="150" @submit.native.prevent>
                    <Row>
                        <Col span="24">
                            <FormItem label="大学名（英）：">
                                <Input v-model="form.university_name_en" placeholder="请输入大学名（英）"></Input>
                            </FormItem>
                            <FormItem label="大学名（中）：">
                                <Input v-model="form.university_name_cn" placeholder="请输入大学名（中）"></Input>
                            </FormItem>
                            <FormItem label="关键词：">
                                <Input v-model="form.keyword" placeholder="请输入关键词"></Input>
                            </FormItem>
                            <FormItem label="官网：">
                                <Input v-model="form.website" placeholder="请输入官网"></Input>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                        <FormItem label="校区：" class="ivu-form-item-required">
                            <div class="campus-item" v-for="item,index in campus_list" @click="edit_campus(item,index)">

                                <Poptip
                                        confirm
                                        transfer
                                        title="您确定删除这条内容吗？"
                                        @on-ok="del_campus(index)"
                                >
                                    <Icon type="trash-a" size="20"></Icon>
                                </Poptip>
                                <p>{{item.name}}</p>
                                <p>{{!!item.city ? item.city : item.province}}</p>
                                <p>{{item.timezone}}</p>
                            </div>
                            <Button type="ghost" icon="plus" class="campus-add" v-if="!isAdd" @click="add_campus"></Button>
                        </FormItem>
                        <div class="campus" v-if="isAdd && !isEdit">
                            <FormItem label="校区名称：">
                                <Input v-model="new_campus.name" placeholder="请输入校区名称"></Input>
                            </FormItem>
                            <FormItem label="区域：" class="ivu-form-item-required">
                                <Input v-model="new_campus.area" placeholder="请输入区域"></Input>
                            </FormItem>
                            <FormItem label="城市：" class="ivu-form-item-required">
                                <div id="city_check"></div>
                            </FormItem>
                            <FormItem label="时区：" class="ivu-form-item-required">
                                <Input v-model="new_campus.timezone" placeholder="请输入时区"></Input>
                            </FormItem>
                            <FormItem label="详细地址：" class="ivu-form-item-required">
                                <Input v-model="new_campus.address" placeholder="请输入详细地址"></Input>
                            </FormItem>
                            <FormItem label="邮编：">
                                <Input v-model="new_campus.postcode" placeholder="请输入邮编"></Input>
                            </FormItem>
                            <FormItem label="电话：">
                                <Input v-model="new_campus.phone" placeholder="请输入电话"></Input>
                            </FormItem>
                            <FormItem label="照片：" class="ivu-form-item-required">
                                <div class="file-list" v-if="new_campus.photo.length > 0"> <!--p_files-->
                                    <div v-for="file,index in new_campus.photo">
                                        <img :src="file.fullpath" alt="">
                                        <p class="del">
                                            <Icon type="trash-a" @click="delImg(file,'p',index)" size="22">删除</Icon>
                                        </p>
                                    </div>
                                </div>
                                <Upload action="/api/v1/admin/school_area/photo_upload/?school_area_id=1"
                                        :before-upload="checkImg_p"
                                        :show-upload-list="true"
                                >
                                    <Button type="ghost" icon="plus"></Button>
                                </Upload>
                            </FormItem>
                            <p class="f_err_msg">{{errMsg}}</p>
                            <FormItem>
                                <Button @click="handle_campus" type="success">添加</Button>
                                <Button @click="cancel_campus">取消</Button>
                            </FormItem>
                        </div>
                        </Col>
                        <Col span="12">
                        <div class="campus" v-if="isEdit">
                            <FormItem label="校区名称：">
                                <Input v-model="e_campus.name" placeholder="请输入校区名称"></Input>
                            </FormItem>
                            <FormItem label="区域：" class="ivu-form-item-required">
                                <Input v-model="e_campus.area" placeholder="请输入区域"></Input>
                            </FormItem>
                            <FormItem label="城市：" class="ivu-form-item-required">
                                <div id="city_check"></div>
                            </FormItem>
                            <FormItem label="时区：" class="ivu-form-item-required">
                                <Input v-model="e_campus.timezone" placeholder="请输入时区"></Input>
                            </FormItem>
                            <FormItem label="详细地址：" class="ivu-form-item-required">
                                <Input v-model="e_campus.address" placeholder="请输入详细地址"></Input>
                            </FormItem>
                            <FormItem label="邮编：">
                                <Input v-model="e_campus.postcode" placeholder="请输入邮编"></Input>
                            </FormItem>
                            <FormItem label="电话：">
                                <Input v-model="e_campus.phone" placeholder="请输入电话"></Input>
                            </FormItem>
                            <FormItem label="照片：" class="ivu-form-item-required">
                                <div class="file-list" v-if="e_campus.photo.length > 0">
                                    <div v-for="file,index in e_campus.photo">
                                        <img :src="file.fullpath" alt="">
                                        <p class="del">
                                            <Icon type="trash-a" @click="delImg(file,'e',index)" size="22">删除</Icon>
                                        </p>
                                    </div>
                                </div>
                                <Upload action="/api/v1/admin/school_area/photo_upload/?school_area_id=1"
                                        :before-upload="checkImg_e"
                                        :show-upload-list="true"
                                >
                                    <Button type="ghost" icon="plus"></Button>
                                </Upload>
                            </FormItem>
                            <FormItem>
                                <Button @click="update_campus" type="success">保存</Button>
                                <Button @click="cancel_campus">取消</Button>
                            </FormItem>
                        </div>
                        </Col>
                    </Row>
                    <FormItem style="text-align: center">
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'university_edit',
        components: {
        },
        data () {
            return {
                form: {},
                I_index: 0,
                campus_list: [],
                new_campus: {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: [],
                    del_photo: [],
                    action: 'create'
                },
                e_campus: {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: [],
                    del_photo: [],
                    action: 'create'
                },
                d_campus: [],
                isMath: false,
                e_index: -1,
                isAdd: false,
                isEdit: false,
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                id: -1,
                n: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'university_edit'){
                    this.form = {};
                    this.id = -1;
                    this.new_campus = {
                        name: '',
                        area: '',
                        country_id: 0,
                        province_id: 0,
                        city_id: 0,
                        country: '',
                        province: '',
                        city: '',
                        timezone: '',
                        address: '',
                        postcode: '',
                        phone: '',
                        photo: [],
                        del_photo: [],
                        action: 'create'
                    };
                    this.e_campus = {
                        name: '',
                        area: '',
                        country_id: 0,
                        province_id: 0,
                        city_id: 0,
                        country: '',
                        province: '',
                        city: '',
                        timezone: '',
                        address: '',
                        postcode: '',
                        phone: '',
                        photo: [],
                        del_photo: [],
                        action: 'create'
                    };
                    this.d_campus = [];
                    this.I_index = 0;
                    this.campus_list = [];
                    this.e_index = -1;
                    this.isMath = false;
                    this.isAdd = false;
                    this.isEdit = false;
                    this.n = 0;
                    this.id = 0;
                    this.$refs['form'].resetFields();
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData(){
                //this.form = this.$store.state.school.currentRow;


                //console.log(this.form)
                this.id = window.location.href.split('/').pop();
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/university/' + this.id,
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        console.log(res.data)
                        _this.form = res.data.data;
                        _this.campus_list = res.data.data.school_area;
                        if(_this.campus_list.length > 0){
                            for(var i=0;i<_this.campus_list.length;i++){
                                _this.campus_list[i].photo = _this.campus_list[i].school_area_photo;
                                _this.campus_list[i].del_photo = [];
                                _this.campus_list[i].action = 'update';
                            }
                            _this.get_city_info(_this.campus_list[_this.n].country_id,'country_id',_this.n);
                        }
                    }else{
                        console.log(res.data.msg)
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            get_city_info(id,str,index){
                var _this = this;
                util.ajax({
                    url: 'https://id.chasedream.com/misc/geo/' + id,
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        if(str === 'country_id'){
                            _this.campus_list[index].country = res.data.data.name;
                            //_this.$set(_this.campus_list[_this.n],'country',res.data.data.name);
                            if(!!_this.campus_list[index].province_id){
                                _this.get_city_info(_this.campus_list[index].province_id,'province_id',index);
                            }else {
                                _this.get_city_info(_this.campus_list[index].city_id,'city_id',index);
                            }

                        }else if(str === 'province_id'){
                            _this.campus_list[index].province = res.data.data.name;
                            //_this.$set(_this.campus_list[_this.n],'province',res.data.data.name);
                            if(!!_this.campus_list[index].city_id){
                                _this.get_city_info(_this.campus_list[index].city_id,'city_id',index);
                            }else {
                                _this.n++;
                                if(_this.n < _this.campus_list.length){
                                    _this.get_city_info(_this.campus_list[_this.n].country_id,'country_id',_this.n);
                                }


                            }
                        }else if(str === 'city_id'){
                            //_this.campus_list[index].city = res.data.data.name;
                            _this.$set(_this.campus_list[index],'city',res.data.data.name);
                            _this.n++;
                            if(_this.n < _this.campus_list.length){
                                _this.get_city_info(_this.campus_list[_this.n].country_id,'country_id',_this.n);
                            }


                            //console.log(_this.campus_list[index])
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                        console.log(res)
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });

            },
            rander_city(str){
                document.getElementsByTagName('form')[0].setAttribute('onkeypress','return event.keyCode != 13;');
                var link = document.createElement("link");
                link.setAttribute("rel", "stylesheet");
                link.setAttribute("type", "test/css");
                link.setAttribute("href", 'https://forum.chasedream.com/static/chasedream/css/citys.css');

                var script = document.createElement("script");
                script.setAttribute("type", "test/javascript");
                script.setAttribute("src", "https://forum.chasedream.com/static/chasedream/js/citys.js");

                var heads = document.getElementsByTagName("head");
                if(heads.length){
                    heads[0].appendChild(link);
                    heads[0].appendChild(script);
                }else{
                    document.documentElement.appendChild(link);
                    document.documentElement.appendChild(script);
                }
                if(!!str && str === 'edit'){
                    var _this = this;
                    setTimeout(function () {
                        var city_id = [];
                        var city = [];
                        var city_text = [];
                        if(!!_this.e_campus.country_id){
                            city_id.push(_this.e_campus.country_id);
                            city.push(_this.e_campus.country)
                            if(_this.e_campus.country !== '中国' && _this.e_campus.country !== '大陆'){
                                city_text.push(_this.e_campus.country);
                            }
                        }
                        if(!!_this.e_campus.province_id){
                            city_id.push(_this.e_campus.province_id);
                            city.push(_this.e_campus.province);
                            city_text.push(_this.e_campus.province);
                        }
                        if(!!_this.e_campus.city_id){
                            city_id.push(_this.e_campus.city_id);
                            city.push(_this.e_campus.city);
                            city_text.push(_this.e_campus.city);
                        }
                        var city_control = document.getElementById('city_ipt');
                        city_control.setAttribute('data',city_id.join('-'));
                        city_control.setAttribute('data_name',city.join('-'));
                        city_control.innerHTML = city_text.join('-');
                        city_control.nextElementSibling.style.display = 'none';
                    },600)
                }
            },
            add_campus(){
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: [],
                    del_photo: [],
                    action: 'create'
                }
                this.e_campus = {};
                this.isAdd = true;
                this.isEdit = false;
                this.rander_city();
            },
            cancel_campus(){
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: [],
                    del_photo: [],
                    action: 'create'
                }
                this.e_campus = {};
                this.e_index = -1;
                this.isAdd = false;
                this.isEdit = false;
            },
            handle_campus(){
                /*if(!this.new_campus.name){
                 this.$Notice.error({
                 title: '请填写校区名称！'
                 });
                 return false;
                 }*/
                if(!this.new_campus.area){
                    this.$Notice.error({
                        title: '请填写区域！'
                    });
                    return false;
                }
                var city_info = document.getElementById('city_ipt').getAttribute('data').split('-');
                if(city_info.length === 0){//this.new_campus.city
                    this.$Notice.error({
                        title: '请选择城市！'
                    });
                    return false;
                }else {
                    var city_text = document.getElementById('city_ipt').getAttribute('data_name').split('-');
                    this.new_campus.country_id = city_info[0];
                    this.new_campus.country = city_text[0];
                    if(city_info.length === 3){
                        this.new_campus.province_id = city_info[1];
                        this.new_campus.city_id = city_info[2];
                        this.new_campus.province = city_text[1];
                        this.new_campus.city = city_text[2];
                    }else if(city_info.length === 2){
                        this.new_campus.city_id = city_info[1];
                        this.new_campus.city = city_text[1];
                    }
                }
                if(!this.new_campus.timezone){
                    this.$Notice.error({
                        title: '请填写时区！'
                    });
                    return false;
                }
                if(!this.new_campus.address){
                    this.$Notice.error({
                        title: '请填写详细地址！'
                    });
                    return false;
                }
                /*if(!this.new_campus.postcode){
                 this.$Notice.error({
                 title: '请填写邮编！'
                 });
                 return false;
                 }*/
                /*if(!this.new_campus.phone){
                 this.$Notice.error({
                 title: '请填写电话！'
                 });
                 return false;
                 }*/
                if(!this.new_campus.photo){
                    this.$Notice.error({
                        title: '请上传照片！'
                    });
                    return false;
                }

                this.campus_list.push(this.new_campus);
                this.cancel_campus();
            },
            edit_campus(item,index){
                this.e_campus = {};
                this.e_index = index;
                this.e_campus = {
                    name: item.name,
                    area: item.area,
                    country_id: item.country_id,
                    province_id: item.province_id,
                    city_id: item.city_id,
                    country: item.country,
                    province: item.province,
                    city: item.city,
                    timezone: item.timezone,
                    address: item.address,
                    postcode: item.postcode,
                    phone: item.phone,
                    photo: item.photo ? item.photo : [],
                    del_photo: item.del_photo,
                    id: item.id,
                    action: item.action
                };
                /*this.e_files=[{
                    src: this.e_campus.photo
                }]*/
                this.isEdit = true;
                this.isAdd = false;
                this.rander_city('edit');

            },
            update_campus(){
                /*if(!this.e_campus.name){
                 this.$Notice.error({
                 title: '请填写校区名称！'
                 });
                 return false;
                 }*/
                if(!this.e_campus.area){
                    this.$Notice.error({
                        title: '请填写区域！'
                    });
                    return false;
                }
                var city_info = document.getElementById('city_ipt').getAttribute('data').split('-');
                if(city_info.length === 0){//this.new_campus.city
                    this.$Notice.error({
                        title: '请选择城市！'
                    });
                    return false;
                }else {
                    var city_text = document.getElementById('city_ipt').getAttribute('data_name').split('-');
                    this.e_campus.country_id = city_info[0];
                    this.e_campus.country = city_text[0];
                    if(city_info.length === 3){
                        this.e_campus.province_id = city_info[1];
                        this.e_campus.city_id = city_info[2];
                        this.e_campus.province = city_text[1];
                        this.e_campus.city = city_text[2];
                    }else if(city_info.length === 2){
                        this.e_campus.city_id = city_info[1];
                        this.e_campus.city = city_text[1];
                    }
                }
                if(!this.e_campus.timezone){
                    this.$Notice.error({
                        title: '请填写时区！'
                    });
                    return false;
                }
                if(!this.e_campus.address){
                    this.$Notice.error({
                        title: '请填写详细地址！'
                    });
                    return false;
                }
                /*if(!this.e_campus.postcode){
                 this.$Notice.error({
                 title: '请填写邮编！'
                 });
                 return false;
                 }*/
                /*if(!this.e_campus.phone){
                 this.$Notice.error({
                 title: '请填写电话！'
                 });
                 return false;
                 }*/
                if(this.e_campus.photo.length < 1){
                    this.$Notice.error({
                        title: '请上传照片！'
                    });
                    return false;
                }

                //this.campus_list.push(this.e_campus);
                console.log(this.e_index)
                this.campus_list[this.e_index] = this.e_campus;

                this.cancel_campus();
            },
            del_campus(index){
                if(this.campus_list[index].id > 0){
                    this.d_campus.push(this.campus_list[index])
                }
                this.campus_list.splice(index, 1);
            },
            checkImg_p (file,str){
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            _this.uploadImg(file, 'p');
                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            checkImg_e (file,str){
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            _this.uploadImg(file, 'e');
                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            uploadImg (file,str){
                var form = new FormData();
                form.append('file', file);
                var school_area_id = 0;
                if(!!this.e_campus.id){
                    school_area_id = this.e_campus.id;
                }
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/school_area/photo_upload/?school_area_id=' + school_area_id,
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        if(str === 'e'){
                            _this.e_campus.photo.push(res.data.data[0]);
                            _this.errMsg = '';
                            return;
                        }else if(!!str && str === 'p'){
                            _this.new_campus.photo.push(res.data.data[0]);
                            _this.errMsg = '';
                            return;
                        }
                    }else{
                        // console.log(res)
                        _this.$Notice.error({
                            title: '上传失败，请重新上传！'
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (file,str,index){
                if(!!str && str === 'p'){
                    this.new_campus.del_photo.push(this.new_campus.photo[index]);
                    this.new_campus.photo.splice(index, 1);
                }if(!!str && str === 'e'){
                    this.e_campus.del_photo.push(this.e_campus.photo[index]);
                    this.e_campus.photo.splice(index, 1);
                }
            },
            handleSubmit () {
                var dataIn = {
                    university_name_en: this.form.university_name_en,
                    university_name_cn: this.form.university_name_cn,
                    keyword: this.form.keyword,
                    website: this.form.website,
                    id: this.form.id,
                    school_area: []
                };
                if(!dataIn.university_name_en && !dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '大学名称（英）和大学名称（中）至少填写一个！'
                    });
                    return false;
                }
                /*if(!dataIn.university_name_en){
                    this.$Notice.error({
                        title: '请填写商学院名称（英）！'
                    });
                    return false;
                }
                if(!dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '请填写商学院名称（中）！'
                    });
                    return false;
                }*/
                /*if(!dataIn.school_name){
                    this.$Notice.error({
                        title: '请填写大学名称（英）！'
                    });
                    return false;
                }
                if(!dataIn.school_name_cn){
                    this.$Notice.error({
                        title: '请填写大学名称（中）！'
                    });
                    return false;
                }*/
                /*if(!dataIn.keyword){
                    this.$Notice.error({
                        title: '请填写关键词！'
                    });
                    return false;
                }
                if(!dataIn.website){
                    this.$Notice.error({
                        title: '请填写商学院首页！'
                    });
                    return false;
                }*/

                for(var i=0;i<this.campus_list.length;i++){
                    var ph = [];
                    /*for(var j=0;j<this.campus_list[i].photo.length;j++){
                        //ph.push(this.campus_list[i].photo[j].fullpath);
                        console.log(this.campus_list[i].photo[j])
                        ph.push(!!this.campus_list[i].photo[j].aid ? this.campus_list[i].photo[j].aid : this.campus_list[i].photo[j].id);
                    }*/
                    for(var j=0;j<this.campus_list[i].photo.length;j++){
                        for(var j=0;j<this.campus_list[i].photo.length;j++){
                            if(!!this.campus_list[i].photo[j].aid){
                                this.campus_list[i].photo[j].action = 'create';
                                this.campus_list[i].photo[j].id = this.campus_list[i].photo[j].aid
                            }
                        }
                    }
                    for(var j=0;j<this.campus_list[i].del_photo.length;j++){
                        for(var j=0;j<this.campus_list[i].del_photo.length;j++){
                            if(!!this.campus_list[i].del_photo[j].id){
                                this.campus_list[i].del_photo[j].action = 'delete'
                                this.campus_list[i].photo.push(this.campus_list[i].del_photo[j]);
                            }
                        }
                    }
                    var item = {
                        name: this.campus_list[i].name,
                        area: this.campus_list[i].area,
                        country_id: this.campus_list[i].country_id,
                        province_id: this.campus_list[i].province_id,
                        city_id: this.campus_list[i].city_id,
                        country: this.campus_list[i].country,
                        province: this.campus_list[i].province,
                        city: this.campus_list[i].city,
                        timezone: this.campus_list[i].timezone,
                        address: this.campus_list[i].address,
                        postcode: this.campus_list[i].postcode,
                        phone: this.campus_list[i].phone,
                        photo: this.campus_list[i].photo,
                        //photoid:ph,
                        //del_photo: this.campus_list[i].del_photo,
                        google_map: '',
                        baidu_map: '',
                        id: this.campus_list[i].id > 0 ? this.campus_list[i].id : null,
                        school_id: this.campus_list[i].school_id,
                        action: this.campus_list[i].action
                    }
                    dataIn.school_area.push(item);
                }
                for(var i=0;i<this.d_campus.length;i++){
                    var ph = [];
                    for(var j=0;j<this.d_campus[i].photo.length;j++){
                        //ph.push(this.campus_list[i].photo[j].fullpath);
                        ph.push(this.d_campus[i].photo[j].aid ? this.d_campus[i].photo[j].aid : this.d_campus[i].photo[j].id);
                    }
                    var item = {
                        name: this.d_campus[i].name,
                        area: this.d_campus[i].area,
                        country_id: this.d_campus[i].country_id,
                        province_id: this.d_campus[i].province_id,
                        city_id: this.d_campus[i].city_id,
                        country: this.d_campus[i].country,
                        province: this.d_campus[i].province,
                        city: this.d_campus[i].city,
                        timezone: this.d_campus[i].timezone,
                        address: this.d_campus[i].address,
                        postcode: this.d_campus[i].postcode,
                        phone: this.d_campus[i].phone,
                        photo: this.d_campus[i].photo,
                        //photoid:ph,
                        del_photo: this.campus_list[i].del_photo,
                        google_map: '',
                        baidu_map: '',
                        id: this.d_campus[i].id,
                        school_id: this.d_campus[i].school_id,
                        action: 'delete'
                    }
                    dataIn.school_area.push(item);
                }
                console.log(dataIn);
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/university',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                       // console.log(res.data)
                        _this.$Notice.success({
                            title: '提交成功！！'
                        });
                        _this.$store.commit('removeTag', 'university_edit');
                        _this.$store.commit('closePage', 'university_edit');
                        _this.$router.push({
                            name: 'university_list'
                        });
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {};
                this.id = -1;
                this.I_index = 0;
                this.campus_list = [];
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: [],
                    action: 'create'
                };
                this.e_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: [],
                    action: 'create'
                };
                this.d_campus = [];
                this.isMath = false;
                this.e_index = -1;
                this.isAdd = false;
                this.isEdit = false;
                this.n = 0;
                this.id = -1;
                this.$store.commit('removeTag', 'university_edit');
                this.$router.push({
                    name: 'university_list'
                });
            }
        },
        created () {


        }
    };
</script>