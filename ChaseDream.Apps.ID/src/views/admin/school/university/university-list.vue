<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="university-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar" style="position: relative;margin-bottom: 20px;">
                    <router-link to="/console/school/university-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <Input v-model="keyword" @on-enter="searchKW('search')" placeholder="请输入关键字" style="width:400px;margin-bottom: 10px;position: absolute;right: 0;top:0;">
                    <Button slot="append" icon="search" style="color: #fff;font-size: 20px;background-color: #2d8cf0;font-weight: bold;" @click="searchKW('search')"></Button>
                    </Input>

                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="sTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="sRows" :columns-list="sColumns"></can-edit-table>
                    <div class="page-bar">
                        <Page :total="s_total" :page-size="page_size" :current="s_current" @on-change="changePage" v-if="s_showPage"></Page>
                    </div>
                </div>

            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import eData from '../data/school_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'university_list',
        components: {
            canEditTable
        },
        data () {
            return {
                sRows: [],
                allData: [],
                sColumns: [],
                s_total: 0,
                s_showPage: false,
                page_size: 20,
                s_current: 1,
                isAccess: true,
                keyword: '',
                schools: [],
                pre_current: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'university_list'){
                    this.getData(this.pre_current);
                }
            }
        },
        mounted () {
            //this.getData();
        },
        methods: {
            getData (n) {
                this.sColumns = eData.uColumns;
                if(!!n){
                    this.getSchools(n);
                }else {
                    this.getSchools(1);
                }
            },
            getSchools (n){
                this.sRows = [];
                this.s_total = 0;
                this.s_current = n;
                var kw = '';
                if(!!this.keyword){
                    kw = '?s=' + this.keyword
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/university' + kw,
                    method:'GET',
                    params: {
                        page: _this.s_current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                       // console.log(res.data)
                        //_this.allData = res.data.data;
                        _this.sRows = res.data.data.rows;
                        //_this.schools = res.data.data.rows;//Object.assign({}, _this.sRows)
                        _this.s_total = res.data.data.count;
                        if(_this.s_total > _this.page_size){
                            _this.s_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            searchKW(){
               /* if(this.keyword === '' || this.keyword === ' '){
                    this.sRows = this.schools;
                }else {
                    this.sRows = this.schools;
                    var newArr =[];
                    for(var i=0;i<this.sRows.length;i++){
                        if(this.sRows[i].keyword.toLowerCase().indexOf(this.keyword.toLowerCase()) > -1){
                            newArr.push(this.sRows[i]);
                        }
                    }
                    this.sRows = newArr;
                }*/
                this.getSchools(1);
            },
            changePage(n){
                this.sRows = [];
                this.pre_current = n;
                this.getData(n);
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
                this.getSchools(1);
            },
            handleUpdata (){
                this.getData();
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
