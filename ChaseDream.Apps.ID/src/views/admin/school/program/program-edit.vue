<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="program-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    编辑专业信息
                </p>
                <div style="width: 90%;padding: 10px 40px 20px;">
                    <Row style="padding: 10px 15px;color: #bbbec4">
                        <Col span="13">专业全称（英） / 专业全称（中）</Col>
                        <Col span="9">专业简称 / 所属分类</Col>
                        <Col span="2" align="center">操作</Col>
                    </Row>
                    <Row v-for="major,index in majorData" :key="major.index" type="flex" justify="center" align="middle"  style="background: #f8f8f9;padding: 10px 15px;margin-bottom: 10px;">
                        <Col span="13">
                            {{major.fullname_english}}
                            <br>
                            {{major.fullname_chinese}}
                        </Col>
                        <Col span="9">
                            {{major.short}}
                            <br>
                            {{major.event_major.field}}
                        </Col>
                        <Col span="2" align="center">
                            <Icon type="edit" size="20" style="color: #19be6b;cursor: pointer" @click="getRowData(major)"></Icon>
                        </Col>
                    </Row>
                </div>
                <Modal
                        v-model="toEdit"
                        :mask-closable="false"
                        :title="form.school_name">
                    <Form :label-width="120">
                        <FormItem label="专业全称（英）：" class="ivu-form-item-required">
                            <Input v-model="editData.fullname_english" placeholder="请输入专业全称（英）"></Input>
                        </FormItem>
                        <FormItem label="专业全称（中）：" class="ivu-form-item-required">
                            <Input v-model="editData.fullname_chinese" placeholder="请输入专业全称（中）"></Input>
                        </FormItem>
                        <FormItem label="专业简称：" class="ivu-form-item-required">
                            <Input v-model="editData.short" placeholder="请输入专业简称"></Input>
                        </FormItem>
                        <FormItem label="所属分类：" class="ivu-form-item-required">
                            <!--<Select v-model="editData.major_id" placeholder="请选择所属分类">
                                <OptionGroup :label="item.g" v-for="item in majorList" :key="item.index">
                                    <Option v-for="opt in item.names" :value="opt.id" :key="opt.index" v-if="!!opt">{{ opt.display_name }}</Option>
                                </OptionGroup>
                            </Select>-->
                            <div v-for="item in majorList" :key="item.index" style="position: relative">
                                <span style="padding-right: 10px;position: absolute;left: 0;top: 0;">{{item.g}}</span>
                                <RadioGroup v-model="editData.major_id" style="padding-left: 50px;" >
                                    <Radio :label="opt.id" v-for="opt in item.names" :key="opt.index" v-if="!!opt">{{opt.display_name}}</Radio>
                                </RadioGroup>
                            </div>
                        </FormItem>
                    </Form>
                    <div slot="footer">
                        <Button type="text" size="large" @click="toEdit=false">取消</Button>
                        <Button type="primary" size="large" @click="pushMajor">确定</Button>
                    </div>
                </Modal>
                <Form ref="form" :model="form" :label-width="150">
                    <div class="majors">
                        <h3 style="border-bottom: 1px solid #efefef;margin-bottom: 10px;padding-left: 40px;">新增专业</h3>
                        <p style="margin-bottom: 10px;">
                            <span style="display: inline-block;width: 140px;text-align: right;margin-right: 10px;color: #bbbec4;">所属学院：</span>
                            <span>{{form.school_name}}</span>
                        </p>
                        <div class="major" v-for="major,index in majors">
                            <FormItem label="专业全称（英）：" class="ivu-form-item-required">
                                <Input v-model="major.fullname_english" placeholder="请输入专业全称（英）" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="专业全称（中）：" class="ivu-form-item-required">
                                <Input v-model="major.fullname_chinese" placeholder="请输入专业全称（中）" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="专业简称：" class="ivu-form-item-required">
                                <Input v-model="major.short" placeholder="请输入专业简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="所属分类：" class="ivu-form-item-required">
                                <!--<Select v-modprogramajor.major_id" placeholder="请选择所属分类" style="width: 400px;">
                                    <OptionGroup :label="item.g" v-for="item in majorList" :key="item.index">
                                        <Option v-for="opt in item.names" :value="opt.id" :key="opt.index" v-if="!!opt">{{ opt.display_name }}</Option>
                                    </OptionGroup>
                                </Select>-->
                                <div v-for="item in majorList" :key="item.index" style="position: relative">
                                    <span style="padding-right: 10px;position: absolute;left: 0;top: 0;">{{item.g}}</span>
                                    <RadioGroup v-model="major.major_id" style="padding-left: 50px;" >
                                        <Radio :label="opt.id" v-for="opt in item.names" :key="opt.index" v-if="!!opt">{{opt.display_name}}</Radio>
                                    </RadioGroup>
                                </div>
                            </FormItem>
                            <FormItem label="" v-if="index !== majors.length-1">
                                <div style="width: 100%;height: 1px;background: #efefef;"></div>
                            </FormItem>
                            <Icon type="trash-a" size="24" class="delItem" @click="delRow(index)" v-if="majors.length > 1"></Icon>
                        </div>
                    </div>
                    <FormItem>
                        <Button type="dashed" icon="plus" class="plusItem" @click="addRow"></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'program_edit',
        components: {
            //canEditTable
        },
        data () {
            return {
                form: {
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                majors: [
                    {
                        fullname_english: '',
                        fullname_chinese: '',
                        short: '',
                        major_id: 0
                    }
                ],
                editData:{
                    fullname_english: '',
                    fullname_chinese: '',
                    short: '',
                    major_id: 0
                },
                majorList: [],
                majorData: [],
                id: 0,
                toEdit: false,
                loading: true,
                I_index: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'program_edit'){
                    this.majors = [{
                        fullname_english: '',
                        fullname_chinese: '',
                        short: '',
                        major_id: 0
                    }];
                    this.editData = {
                        fullname_english: '',
                        fullname_chinese: '',
                        short: '',
                        major_id: 0
                    };
                    this.majorList = [];
                    this.majorData = [];
                    this.$refs['form'].resetFields();
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData(){
                this.getMajors();
                this.form = this.$store.state.event.currentRow;
                //console.log(this.form)
                this.id = window.location.href.split('/').pop();
                //console.log(this.id)
                this.getSchoolMajor();
            },
            addRow (){  // 添加一行活地点
                var item = {
                    fullname_english: '',
                    fullname_chinese: '',
                    short: '',
                    major_id: 0
                }
                this.majors.push(item);
            },
            delRow (i){  // 删除一行活动地点
                if(i === 0){
                    this.majors.splice(0, 1);
                }else {
                    this.majors.splice(i, 1);
                }
                console.log(i)

            },
            getMajors(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/program',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {

                        _this.majorList = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getSchoolMajor(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/university/program/' + this.id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        //console.log(res.data.data)
                        _this.majorData = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getRowData(item){
                //this.editData = item;
                this.editData.fullname_english = item.fullname_english;
                this.editData.fullname_chinese = item.fullname_chinese;
                this.editData.short = item.short;
                this.editData.major_id = item.major_id;
                this.editData.school_id = item.school_id;
                this.editData.id = item.id;
                this.toEdit = true;
            },
            pushMajor (){
                this.loading = true;
                var dataIn = {
                    school_id: this.editData.school_id,
                    id: this.editData.id,
                    fullname_english: this.editData.fullname_english,
                    fullname_chinese: this.editData.fullname_chinese,
                    short: this.editData.short,
                    major_id: this.editData.major_id
                }
                console.log(dataIn)
                if(!dataIn.fullname_english){
                    this.$Notice.error({
                        title: '专业全称（英）不能为空！'
                    });
                }
                if(!dataIn.fullname_chinese){
                    this.$Notice.error({
                        title: '专业全称（中）不能为空！'
                    });
                }
                if(!dataIn.short){
                    this.$Notice.error({
                        title: '专业简称不能为空！'
                    });
                }
                if(!dataIn.major_id){
                    this.$Notice.error({
                        title: '请选择所属分类！'
                    });
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/university/program',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        // console.log(res.data)
                        _this.getSchoolMajor();
                        _this.$Notice.success({
                            title: '提交成功！！'
                        });
                        _this.toEdit = false;
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            handleSubmit () {
                var dataIn = {
                    majors: this.majors
                };
                //console.log(dataIn);
                for(var i=0;i<dataIn.majors.length;i++){
                    if(!dataIn.majors[i].fullname_english){
                        this.$Notice.error({
                            title: '请填写专业全称（英）！'
                        });
                        return false;
                    }
                    if(!dataIn.majors[i].fullname_chinese){
                        this.$Notice.error({
                            title: '请填写专业全称（中！'
                        });
                        return false;
                    }
                    if(!dataIn.majors[i].short){
                        this.$Notice.error({
                            title: '请填写专业简称！'
                        });
                        return false;
                    }
                    if(!dataIn.majors[i].major_id){
                        this.$Notice.error({
                            title: '请选择所属分类！'
                        });
                        return false;
                    }
                }
                //console.log(dataIn)
                for(var i=0;i<dataIn.majors.length;i++){
                    dataIn.majors[i].school_id = this.id;
                    this.submitAjax(dataIn.majors[i]);
                }
            },
            submitAjax (dataIn){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/university/program',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        // console.log(res.data)
                        _this.I_index++;
                        if(_this.I_index >= _this.majors.length){
                            _this.$Notice.success({
                                title: '提交成功！！'
                            });
                            _this.$store.commit('removeTag', 'program_edit');
                            _this.$store.commit('closePage', 'program_edit');
                            _this.$router.push({
                                name: 'program_list'
                            });
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.majors = [{
                    fullname_english: '',
                    fullname_chinese: '',
                    short: '',
                    major_id: 0
                }];
                this.editData = {
                    fullname_english: '',
                    fullname_chinese: '',
                    short: '',
                    major_id: 0
                };
                this.majorList = [];
                this.majorData = [];
                this.$store.commit('removeTag', 'program_edit');
                this.$router.push({
                    name: 'program_list'
                });
            }
        },
        created () {

        }
    };
</script>