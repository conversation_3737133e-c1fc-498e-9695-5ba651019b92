<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="program-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar" style="position: relative;margin-bottom: 20px;">
                    <router-link to="/console/school/program-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <!--<Input v-model="keyword" @on-enter="searchKW('search')" placeholder="请输入关键字" style="width:400px;margin-bottom: 10px;position: absolute;right: 0;top:0;">
                    <Button slot="append" icon="search" style="color: #fff;font-size: 20px;background-color: #2d8cf0;font-weight: bold;" @click="searchKW('search')"></Button>
                    </Input>-->

                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="mTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="pRows" :columns-list="pColumns"></can-edit-table>
                    <div class="page-bar">
                        <Page :total="p_total" :page-size="page_size" :current="p_current" @on-change="changePage" v-if="p_showPage"></Page>
                    </div>
                </div>

            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import eData from '../data/school_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'program_list',
        components: {
            canEditTable
        },
        data () {
            return {
                pRows: [],
                allData: [],
                pColumns: [],
                p_total: 0,
                p_showPage: false,
                page_size: 20,
                p_current: 1,
                isAccess: true,
                keyword: '',
                majors: []
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'program_list'){
                    this.getData();
                }
            }
        },
        mounted () {
            //this.getData();
        },
        methods: {
            getData () {
                this.pColumns = eData.pColumns;
                this.get_major_list(1);
            },
            get_major_list (n){
                this.pRows = [];
                this.p_total = 0;
                this.p_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/program',
                    method:'GET',
                    params: {
                        page: _this.p_current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        //_this.allData = res.data.data;
                        var majors_arr = [];
                        for(var i=0;i<res.data.data.length;i++){
                            majors_arr = [...majors_arr,...res.data.data[i].names]
                        }
                        _this.pRows = majors_arr;
                        //_this.mRows = res.data.data;
                        _this.majors = res.data.data;//Object.assign({}, _this.sRows)
                        _this.p_total = res.data.data.length;
                        if(_this.p_total > _this.page_size){
                            _this.p_showPage = true;
                        }
                        //_this.changePage(_this.s_current);
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            changePage(n){
                this.pRows = [];
                for(var i=n*this.page_size;i<this.page_size*(n+1);i++){
                    this.pRows.push(this.allData[i]);
                }
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
                this.get_major_list(1);
            },
            handleUpdata (){
                this.getData();
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
