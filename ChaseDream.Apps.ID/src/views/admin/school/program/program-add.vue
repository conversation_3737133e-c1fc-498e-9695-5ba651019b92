<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="program-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加项目
                </p>
                <Form ref="form" :model="form" :label-width="150">
                    <div class="programs">
                        <FormItem label="中文全称" class="ivu-form-item-required">
                            <Input v-model="form.program_name_cn" placeholder="请输入中文全称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="英文全称" class="ivu-form-item-required">
                            <Input v-model="form.program_name_en" placeholder="请输入英文全称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="展示名：" class="ivu-form-item-required">
                            <Input v-model="form.display_name" placeholder="请输入中文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="中文简称：" class="ivu-form-item-required">
                            <Input v-model="form.abbr_cn" placeholder="请输入中文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="英文简称：" class="ivu-form-item-required">
                            <Input v-model="form.abbr_en" placeholder="请输入英文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="关键字：" class="ivu-form-item-required">
                            <Input v-model="form.keyword" placeholder="请输入英文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="简介：">
                            <Input type="textarea" :autosize="{minRows: 5,maxRows: 6}" style="width: 400px;" v-model="form.introduction" placeholder="简介"></Input>
                        </FormItem>
                        <!--<FormItem label="学位：" class="ivu-form-item-required">
                            <RadioGroup v-model="form.program_type">
                                <Radio :label="1">学士学位</Radio>
                                <Radio :label="2">硕士学位</Radio>
                                <Radio :label="3">博士学位</Radio>
                            </RadioGroup>
                        </FormItem>-->
                        <FormItem label="项目方向：" class="ivu-form-item-required">
                            <RadioGroup v-model="form.program_direction_id">
                                <Radio :label="item.id" v-for="item in program_direction" :key="item.index">{{item.display_name}}</Radio>
                                <!--<Radio :label="1">本科</Radio>
                                <Radio :label="2">研究生</Radio>
                                <Radio :label="3">进修</Radio>
                                <Radio :label="4">培训</Radio>
                                <Radio :label="5">服务</Radio>-->
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="项目学位类型：" class="ivu-form-item-required">
                                <RadioGroup v-model="form.program_degree_category_id">
                                    <Radio :label="item.id" v-for="item in program_degree_category" :key="item.index">{{item.education_level_cn}}</Radio>
                                    <!--<Radio :label="1">本科</Radio>
                                    <Radio :label="2">研究生</Radio>
                                    <Radio :label="3">进修</Radio>
                                    <Radio :label="4">培训</Radio>
                                    <Radio :label="5">服务</Radio>-->
                                </RadioGroup>
                        </FormItem>
                        <FormItem label="项目类型：" class="ivu-form-item-required">
                            <RadioGroup v-model="form.program_type">
                                <Radio :label="1">全日制</Radio>
                                <Radio :label="2">非全日制</Radio>
                                <Radio :label="3">其它</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="授课方式：" class="ivu-form-item-required">
                            <RadioGroup v-model="form.teaching_method">
                                <Radio :label="1">面授</Radio>
                                <Radio :label="2">在线</Radio>
                                <Radio :label="3">其它</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="时长：" class="ivu-form-item-required">
                            <Input v-model="form.duration" placeholder="请输入时长" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="招生人数：" class="ivu-form-item-required">
                            <Input v-model="form.enrollment" placeholder="请输入招生人数" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="开学时间：" class="ivu-form-item-required">
                            <Input v-model="form.school_starttime" placeholder="请输入开学时间" style="width: 400px;"></Input>
                        </FormItem>
                        <!--<FormItem label="时区：" class="ivu-form-item-required">
                            <Input v-model="form.timezone" placeholder="请输入时区" style="width: 400px;"></Input>
                        </FormItem>-->
                        <FormItem label="" class="ivu-form-item-required">
                            <p class="line"><strong>费用相关</strong> <span></span></p>
                        </FormItem>
                        <FormItem label="申请费：" class="ivu-form-item-required">
                            <Input v-model="form.application_fee" placeholder="请输入申请费" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="学费：" class="ivu-form-item-required">
                            <Input v-model="form.tuition" placeholder="请输入学费" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="生活费：" class="ivu-form-item-required">
                            <Input v-model="form.living_expenses" placeholder="请输入生活费" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="保险费：" class="ivu-form-item-required">
                            <Input v-model="form.insurance_premiums" placeholder="请输入保险费" style="width: 400px;"></Input>
                        </FormItem>

                        <FormItem label="" class="ivu-form-item-required">
                            <p class="line"><strong>送分代码</strong> <span></span></p>
                        </FormItem>
                        <FormItem label="GMAT：" class="ivu-form-item-required">
                            <Input v-model="form.gmat_code" placeholder="请输入GMAT送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="EA：" class="ivu-form-item-required">
                            <Input v-model="form.ea_code" placeholder="请输入EA送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="GRE：" class="ivu-form-item-required">
                            <Input v-model="form.gre_code" placeholder="请输入GRE送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="TOEFL：" class="ivu-form-item-required">
                            <Input v-model="form.toefl_code" placeholder="请输入TOEFL送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="IELTS：" class="ivu-form-item-required">
                            <Input v-model="form.ielts_code" placeholder="请输入IELTS送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="PTE：" class="ivu-form-item-required">
                            <Input v-model="form.pte_code" placeholder="请输入PTE送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="GRE_MAJOR_CORD：" class="ivu-form-item-required">
                            <Input v-model="form.gre_major_code" placeholder="请输入GRE_MAJOR_CORD送分代码" style="width: 400px;"></Input>
                        </FormItem>

                        <FormItem label="" class="ivu-form-item-required">
                            <p class="line"><strong>成绩要求</strong> <span></span></p>
                        </FormItem>
                        <FormItem label="GMAT：" class="ivu-form-item-required">
                            <Input v-model="form.gmat_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="form.gmat_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="EA：" class="ivu-form-item-required">
                            <Input v-model="form.ea_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="form.ea_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="GRE：" class="ivu-form-item-required">
                            <Input v-model="form.gre_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="form.gre_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="TOEFL：" class="ivu-form-item-required">
                            <Input v-model="form.toefl_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="form.toefl_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="IELTS：" class="ivu-form-item-required">
                            <Input v-model="form.ielts_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="form.ielts_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="PTE：" class="ivu-form-item-required">
                            <Input v-model="form.pte_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="form.pte_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>

                        <!--<FormItem label="Deadline：" class="ivu-form-item-required">
                            <Input v-model="form.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="排名：" class="ivu-form-item-required">
                            <Input v-model="form.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="奖学金：" class="ivu-form-item-required">
                            <Input v-model="form.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="Class Profile：" class="ivu-form-item-required">
                            <Input v-model="form.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="社交媒体：" class="ivu-form-item-required">
                            <Input v-model="form.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>-->


                    </div>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'program_add',
        components: {
            //canEditTable
        },
        data () {
            return {
                form: {
                    program_direction_id: 0,
                    program_degree_category_id: 0,
                    program_name_en: '',
                    program_name_cn: '',
                    abbr_cn: '',
                    abbr_en: '',
                    display_name: '',
                    program_type: 0,
                    teaching_method: '',
                    introduction: '',
                    program_duration: '',
                    application_fee: '',
                    tuition: '',
                    living_expenses: '',
                    insurance_premiums: '',
                    gmat_code: '',
                    ea_code: '',
                    gre_code: '',
                    gre_major_code: '',
                    ielts_code: '',
                    pte_code: '',
                    multi_school: ''
                },
                program_direction: [],
                program_degree_category: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'program_add'){
                    this.form = {
                        program_direction_id: 0,
                        program_degree_category_id: 0,
                        program_name_en: '',
                        program_name_cn: '',
                        short_name_cn: '',  //  *** 无
                        short_name_en: '',  //  *** 无
                        display_name: '',
                        program_type: 0,
                        teaching_method: '',
                        introduction: '',
                        program_duration: '',
                        application_fee: '',
                        tuition: '',
                        living_expenses: '',
                        insurance_premiums: '',
                        gmat_code: '',
                        ea_code: '',
                        gre_code: '',
                        gre_major_code: '',
                        ielts_code: '',
                        pte_code: '',
                        multi_school: ''
                    };
                    this.program_direction = [];
                    this.program_degree_category = [];
                    this.$refs['form'].resetFields();
                    //this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData(){
                this.get_program_direction();
                this.get_program_degree_category();
            },
            get_program_direction (){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/program_direction',
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        //console.log(res.data)
                        _this.program_direction = res.data.data;
                        //this.program_direction
                        //_this.form = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            get_program_degree_category(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/program_degree_category',
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        console.log(res.data.data)
                        _this.program_degree_category = res.data.data;
                       // _this.form = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {
                var dataIn = {
                    majors: this.majors
                };
                //console.log(dataIn);
                for(var i=0;i<dataIn.majors.length;i++){
                    if(!dataIn.majors[i].fullname_english){
                        this.$Notice.error({
                            title: '请填写专业全称（英）！'
                        });
                        return false;
                    }
                    if(!dataIn.majors[i].fullname_chinese){
                        this.$Notice.error({
                            title: '请填写专业全称（中！'
                        });
                        return false;
                    }
                    if(!dataIn.majors[i].short){
                        this.$Notice.error({
                            title: '请填写专业简称！'
                        });
                        return false;
                    }
                    if(!dataIn.majors[i].major_id){
                        this.$Notice.error({
                            title: '请选择所属分类！'
                        });
                        return false;
                    }
                }
                //console.log(dataIn)
                for(var i=0;i<dataIn.majors.length;i++){
                    dataIn.majors[i].school_id = this.id;
                    this.submitAjax(dataIn.majors[i]);
                }
            },
            submitAjax (dataIn){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/program',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        // console.log(res.data)
                        _this.I_index++;
                        if(_this.I_index >= _this.majors.length){
                            _this.$Notice.success({
                                title: '提交成功！！'
                            });
                            _this.$store.commit('removeTag', 'program_add');
                            _this.$store.commit('closePage', 'program_add');
                            _this.$router.push({
                                name: 'program_list'
                            });
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {
                    program_direction_id: 0,
                    program_degree_category_id: 0,
                    program_name_en: '',
                    program_name_cn: '',
                    short_name_cn: '',  //  *** 无
                    short_name_en: '',  //  *** 无
                    display_name: '',
                    program_type: 0,
                    teaching_method: '',
                    introduction: '',
                    program_duration: '',
                    application_fee: '',
                    tuition: '',
                    living_expenses: '',
                    insurance_premiums: '',
                    gmat_code: '',
                    ea_code: '',
                    gre_code: '',
                    gre_major_code: '',
                    ielts_code: '',
                    pte_code: '',
                    multi_school: ''
                };
                this.program_direction = [];
                this.program_degree_category = [];
                this.$store.commit('removeTag', 'program_add');
                this.$router.push({
                    name: 'program_list'
                });
            }
        },
        created () {

        }
    };
</script>