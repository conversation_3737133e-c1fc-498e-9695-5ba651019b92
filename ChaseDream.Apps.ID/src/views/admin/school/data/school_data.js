import util from '@/libs/util.js';
export const uColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '大学名（英）/ 大学名（中）',
        align: 'left',
        key: 'name',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.university_name_en),
                h('p', {}, params.row.university_name_cn)
            ])
        }
    },
    {
        title: '关键字',
        align: 'center',
        width: 200,
        key: 'keyword'
    },
    {
        title: '官网',
        align: 'center',
        width: 100,
        key: 'website',
        render: (h, params) => {
            if (!!params.row.website) {
                return h('a', {
                    attrs: {
                        href: params.row.website,
                        target: '_blank'
                    }
                }, '官网')
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];
export const sColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '显示全称/显示简称',
        align: 'left',
        key: 'name',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.school_name),
                h('p', {}, params.row.university_name)
            ])
        }
    },
    {
        title: '地区/国家',
        align: 'center',
        width: 150,
        key: 'location',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.area),
                h('p', {}, params.row.country)
            ])
        }
    },
    {
        title: '官网',
        align: 'center',
        width: 100,
        key: 'website',
        render: (h, params) => {
            if (!!params.row.website) {
                return h('a', {
                    attrs: {
                        href: params.row.website,
                        target: '_blank'
                    }
                }, '官网')
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['edit', 'edit_m', 'delete']
    }
];
export const pColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '专业全称（英）/专业全称（中）',
        align: 'left',
        key: 'name',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.english_name),
                h('p', {}, params.row.display_name)
            ])
        }
    },
    {
        title: '专业简称',
        align: 'center',
        width: 150,
        key: 'field',
        /*render: (h, params) => {
            return h('a',{
                attrs: {
                    href: params.row.short,
                    target: '_blank'
                }
            },'官网')
        }*/
    },
    {
        title: '所属分类',
        align: 'center',
        width: 150,
        key: 'type',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.type),
                //h('p',{},params.row.country)
            ])
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];

const tData = {
    uColumns: uColumns,
    sColumns: sColumns,
    pColumns: pColumns
};

export default tData;
