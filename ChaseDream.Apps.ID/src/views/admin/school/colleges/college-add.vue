<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="college-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加学校
                </p>
                <Form ref="form" :model="form" :label-width="150" @submit.native.prevent>
                    <Row>
                        <Col span="12">
                            <FormItem label="LOGO：" class="logo-item">
                                <Upload action="/api/v1/admin/event/upload"
                                        :before-upload="checkImg"
                                        :show-upload-list="true"
                                        v-if="files.length === 0"
                                >
                                    <Button type="ghost" icon="plus"></Button>
                                </Upload>
                                <div class="file-list" v-if="files.length > 0">
                                    <div v-for="file in files">
                                        <img :src="file.src" alt="">
                                        <p class="del">
                                            <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                        </p>
                                    </div>
                                </div>
                            </FormItem>
                        </Col>
                        <Col span="12" style="margin-top: 0px;">
                            <FormItem label="官网：">
                                <Input v-model="form.website" placeholder="请输入官网"></Input>
                            </FormItem>
                            <FormItem label="类型：">
                                <RadioGroup v-model="form.type">
                                    <Radio :label="1">大学内商学院</Radio>
                                    <Radio :label="2">独立商学院</Radio>
                                    <Radio :label="3">机构</Radio>
                                    <Radio :label="4">测试</Radio>
                                </RadioGroup>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                            <FormItem label="文件目录：" class="ivu-form-item-required">
                                <Input v-model="form.directory" placeholder="请输入文件目录"></Input>
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="关键词：">
                                <Input v-model="form.keyword" placeholder="请输入关键词"></Input>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                            <FormItem label="学院名（英）：">
                                <Input v-model="form.school_name" placeholder="请输入学院名（英）"></Input>
                            </FormItem>
                        </Col>
                        <Col span="12">
                        <FormItem label="学院名（中）：">
                            <Input v-model="form.school_name_cn" placeholder="请输入学院名（中）"></Input>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <!--<Col span="24">
                            <FormItem label="大学：" class="ivu-form-item-required">
                                <Select v-model="form.university_id" filterable>
                                    <Option v-for="item,index in universitys" :value="item.id" :key="item.index">{{ item.university_name_en +' - '+ item.university_name_cn }}</Option>
                                </Select>
                            </FormItem>
                        </Col>-->
                        <Col span="12">
                        <FormItem label="大学（英）：">
                            <Input v-model="form.university_name" placeholder="请输入大学（英）"></Input>
                        </FormItem>
                        </Col>
                        <Col span="12">
                        <FormItem label="大学（中）：">
                            <Input v-model="form.university_name_cn" placeholder="请输入大学（中）"></Input>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                            <FormItem label="显示名（英）：">
                                <Input v-model="form.display_name" placeholder="请输入显示名（英）"></Input>
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="显示名（简）：">
                                <Input v-model="form.short" placeholder="请输入显示名（简）"></Input>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                            <FormItem label="校区：" class="ivu-form-item-required">
                                <div class="campus-item" v-for="item,index in campus_list" @click="edit_campus(item,index)">

                                    <Poptip
                                            confirm
                                            transfer
                                            title="您确定删除这条内容吗？"
                                            @on-ok="del_campus(index)"
                                    >
                                        <Icon type="trash-a" size="20"></Icon>
                                    </Poptip>
                                    <p>{{item.name}}</p>
                                    <p>{{!!item.city ? item.city : item.province}}</p>
                                    <p>{{item.timezone}}</p>
                                </div>
                                <Button type="ghost" icon="plus" class="campus-add" v-if="!isAdd" @click="add_campus"></Button>
                            </FormItem>
                            <div class="campus" v-if="isAdd && !isEdit">
                                <FormItem label="校区名称：">
                                    <Input v-model="new_campus.name" placeholder="请输入校区名称"></Input>
                                </FormItem>
                                <FormItem label="区域：" class="ivu-form-item-required">
                                    <Input v-model="new_campus.area" placeholder="请输入区域"></Input>
                                </FormItem>
                                <FormItem label="城市：" class="ivu-form-item-required">
                                    <div id="city_check"></div>
                                </FormItem>
                                <FormItem label="时区：" class="ivu-form-item-required">
                                    <Input v-model="new_campus.timezone" placeholder="请输入时区"></Input>
                                </FormItem>
                                <FormItem label="详细地址：">
                                    <Input v-model="new_campus.address" placeholder="请输入详细地址"></Input>
                                </FormItem>
                                <FormItem label="邮编：">
                                    <Input v-model="new_campus.postcode" placeholder="请输入邮编"></Input>
                                </FormItem>
                                <FormItem label="电话：">
                                    <Input v-model="new_campus.phone" placeholder="请输入电话"></Input>
                                </FormItem>
                                <FormItem label="照片：">
                                    <div class="file-list" v-if="new_campus.photo.length > 0">  <!-- v-if="p_files.length > 0"-->
                                        <div v-for="file,index in new_campus.photo">
                                            <img :src="file.fullpath" alt="">
                                            <p class="del">
                                                <Icon type="trash-a" @click="delImg(file,'p',index)" size="22">删除</Icon>
                                            </p>
                                        </div>
                                    </div>
                                    <Upload action="/api/v1/admin/school_area/photo_upload/?school_area_id=1"
                                            :before-upload="checkImg_p"
                                            :show-upload-list="true"
                                    >
                                        <!--v-if="p_files.length === 0"-->
                                        <Button type="ghost" icon="plus"></Button>
                                    </Upload>
                                </FormItem>
                                <p class="f_err_msg">{{errMsg}}</p>
                                <FormItem>
                                    <Button @click="handle_campus" type="primary">添加</Button>
                                    <Button @click="cancel_campus">取消</Button>
                                </FormItem>
                            </div>
                        </Col>
                        <Col span="12">
                        <div class="campus" v-if="isEdit">
                            <FormItem label="校区名称：">
                                <Input v-model="e_campus.name" placeholder="请输入校区名称"></Input>
                            </FormItem>
                            <FormItem label="区域：" class="ivu-form-item-required">
                                <Input v-model="e_campus.area" placeholder="请输入区域"></Input>
                            </FormItem>
                            <FormItem label="城市：" class="ivu-form-item-required">
                                <!--<Input v-model="form.city" placeholder="请输入城市"></Input>-->
                                <div id="city_check"></div>
                            </FormItem>
                            <FormItem label="时区：" class="ivu-form-item-required">
                                <Input v-model="e_campus.timezone" placeholder="请输入时区"></Input>
                            </FormItem>
                            <FormItem label="详细地址：">
                                <Input v-model="e_campus.address" placeholder="请输入详细地址"></Input>
                            </FormItem>
                            <FormItem label="邮编：">
                                <Input v-model="e_campus.postcode" placeholder="请输入邮编"></Input>
                            </FormItem>
                            <FormItem label="电话：">
                                <Input v-model="e_campus.phone" placeholder="请输入电话"></Input>
                            </FormItem>
                            <FormItem label="照片：">
                                <div class="file-list" v-if="e_campus.photo.length > 0"> <!-- v-if="e_files.length > 0"-->
                                    <div v-for="file,index in e_campus.photo">
                                        <img :src="file.fullpath" alt="">
                                        <p class="del">
                                            <Icon type="trash-a" @click="delImg(file,'e',index)" size="22">删除</Icon>
                                        </p>
                                    </div>
                                </div>
                                <Upload action="/api/v1/admin/school_area/photo_upload/?school_area_id=1"
                                        :before-upload="checkImg_e"
                                        :show-upload-list="true"
                                >
                                    <!--v-if="e_files.length === 0"-->
                                    <Button type="ghost" icon="plus"></Button>
                                </Upload>
                            </FormItem>
                            <FormItem>
                                <Button @click="update_campus" type="primary">添加</Button>
                                <Button @click="cancel_campus">取消</Button>
                            </FormItem>
                        </div>
                        </Col>
                    </Row>
                    <FormItem label="介绍：">
                        <Input type="textarea" :autosize="{minRows: 5,maxRows: 5}" v-model="form.introduction" placeholder=""></Input>
                    </FormItem>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
                <!--<div id="city_check"></div>-->
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'college_add',
        components: {
            //canEditTable
        },
        data () {
            return {
                form: {
                    directory: '',
                    university_name: '',
                    university_name_cn: '',
                    university_id: 0,
                    school_name: '',
                    school_name_cn: '',
                    display_name: '',
                    short: '',
                    logo_url: '',
                    keyword: '',
                    website: '',
                    type: 1,
                    /*area: '',
                    country: '',*/
                    introduction: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                p_files: [],
                e_files: [],
                I_index: 0,
                campus_list: [],
                new_campus: {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: []
                },
                e_campus: {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: []
                },
                isMath: false,
                e_index: -1,
                isAdd: false,
                isEdit: false,
                universitys: []
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'college_add'){
                    this.form.directory = '';
                    this.form.university_name = '';
                    this.form.university_name_cn = '';
                    this.university_id = 0;
                    this.form.school_name = '';
                    this.form.school_name_cn = '';
                    this.form.display_name = '';
                    this.form.short = '';
                    this.form.logo_url = '';
                    this.form.keyword = '';
                    this.form.website = '';
                    this.form.type = 1;
                    /*this.form.area = '';
                    this.form.country = '';*/
                    this.form.introduction = '';
                    /*this.majors = [
                        {
                            fullname_english: '',
                            fullname_chinese: '',
                            short: '',
                            major_id: 0
                        }
                    ];*/
                    this.new_campus = {
                        name: '',
                        area: '',
                        country_id: 0,
                        province_id: 0,
                        city_id: 0,
                        country: '',
                        province: '',
                        city: '',
                        timezone: '',
                        address: '',
                        postcode: '',
                        phone: '',
                        photo: []
                    };
                    this.e_campus = {
                        name: '',
                        area: '',
                        country_id: 0,
                        province_id: 0,
                        city_id: 0,
                        country: '',
                        province: '',
                        city: '',
                        timezone: '',
                        address: '',
                        postcode: '',
                        phone: '',
                        photo: []
                    };
                    this.files = [];
                    this.p_files = [];
                    this.e_files = [];
                    this.I_index = 0;
                    this.campus_list = [];
                    this.e_index = -1;
                    this.isAdd = false;
                    this.isEdit = false;
                    this.universitys = [];
                    this.$refs['form'].resetFields();
                    //this.getMajors();
                    //this.getGeo();
                    //this.getUniversity();
                }
            }
        },
        mounted () {
            //this.getMajors();
            //this.getGeo();
            //this.getUniversity();
        },
        methods: {
            rander_city(str){
                document.getElementsByTagName('form')[0].setAttribute('onkeypress','return event.keyCode != 13;');
                var link = document.createElement("link");
                link.setAttribute("rel", "stylesheet");
                link.setAttribute("type", "test/css");
                link.setAttribute("href", 'https://forum.chasedream.com/static/chasedream/css/citys.css');

                var script = document.createElement("script");
                script.setAttribute("type", "test/javascript");
                script.setAttribute("src", "https://forum.chasedream.com/static/chasedream/js/citys.js");

                var heads = document.getElementsByTagName("head");
                if(heads.length){
                    heads[0].appendChild(link);
                    heads[0].appendChild(script);
                }else{
                    document.documentElement.appendChild(link);
                    document.documentElement.appendChild(script);
                }
                if(!!str && str === 'edit'){
                    var _this = this;
                    setTimeout(function () {
                        var city_id = [];
                        var city = [];
                        var city_text = [];
                        if(!!_this.e_campus.country_id){
                            city_id.push(_this.e_campus.country_id);
                            city.push(_this.e_campus.country)
                            if(_this.e_campus.country !== '中国' && _this.e_campus.country !== '大陆'){
                                city_text.push(_this.e_campus.country);
                            }
                        }
                        if(!!_this.e_campus.province_id){
                            city_id.push(_this.e_campus.province_id);
                            city.push(_this.e_campus.province);
                            city_text.push(_this.e_campus.province);
                        }
                        if(!!_this.e_campus.city_id){
                            city_id.push(_this.e_campus.city_id);
                            city.push(_this.e_campus.city);
                            city_text.push(_this.e_campus.city);
                        }
                        var city_control = document.getElementById('city_ipt');
                        city_control.setAttribute('data',city_id.join('-'));
                        city_control.setAttribute('data_name',city.join('-'));
                        city_control.innerHTML = city_text.join('-');
                        city_control.nextElementSibling.style.display = 'none';
                    },600)
                }
            },
            getUniversity(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/university',
                    method:'GET',
                    params: {
                        /*page: _this.s_current,
                        page_size: _this.page_size*/
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.universitys = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            add_campus(){
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: []
                }
                this.e_campus = {};
                this.p_files = [];
                this.isAdd = true;
                this.isEdit = false;
                this.rander_city();
            },
            cancel_campus(){
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: []
                }
                this.e_campus = {};
                this.e_index = -1;
                this.isAdd = false;
                this.isEdit = false;
                this.p_files = [];
            },
            handle_campus(){
                /*if(!this.new_campus.name){
                    this.$Notice.error({
                        title: '请填写校区名称！'
                    });
                    return false;
                }*/
                if(!this.new_campus.area){
                    this.$Notice.error({
                        title: '请填写区域！'
                    });
                    return false;
                }
                var city_info = document.getElementById('city_ipt').getAttribute('data').split('-');
                if(city_info.length === 0){//this.new_campus.city
                    this.$Notice.error({
                        title: '请选择城市！'
                    });
                    return false;
                }else {
                    var city_text = document.getElementById('city_ipt').getAttribute('data_name').split('-');
                    this.new_campus.country_id = city_info[0];
                    this.new_campus.country = city_text[0];
                    if(city_info.length === 3){
                        this.new_campus.province_id = city_info[1];
                        this.new_campus.city_id = city_info[2];
                        this.new_campus.province = city_text[1];
                        this.new_campus.city = city_text[2];
                    }else if(city_info.length === 2){
                        this.new_campus.city_id = city_info[1];
                        this.new_campus.city = city_text[1];
                    }
                }
                if(!this.new_campus.timezone){
                    this.$Notice.error({
                        title: '请填写时区！'
                    });
                    return false;
                }
                /*if(!this.new_campus.address){
                    this.$Notice.error({
                        title: '请填写详细地址！'
                    });
                    return false;
                }*/
                /*if(!this.new_campus.postcode){
                    this.$Notice.error({
                        title: '请填写邮编！'
                    });
                    return false;
                }*/
                /*if(!this.new_campus.phone){
                    this.$Notice.error({
                        title: '请填写电话！'
                    });
                    return false;
                }*/
                /*if(this.new_campus.photo.length < 1){
                    this.$Notice.error({
                        title: '请上传照片！'
                    });
                    return false;
                }*/

                this.campus_list.push(this.new_campus);
                this.cancel_campus();
            },
            edit_campus(item,index){
                this.e_campus = {};
                this.e_index = index;
                this.e_campus = {
                    name: item.name,
                    area: item.area,
                    country_id: item.country_id,
                    province_id: item.province_id,
                    city_id: item.city_id,
                    country: item.country,
                    province: item.province,
                    city: item.city,
                    timezone: item.timezone,
                    address: item.address,
                    postcode: item.postcode,
                    phone: item.phone,
                    photo: item.photo
                };
                this.e_files=[{
                    src: this.e_campus.photo
                }]
                this.isEdit = true;
                this.isAdd = false;
                this.rander_city('edit');

            },
            update_campus(){
                /*if(!this.e_campus.name){
                    this.$Notice.error({
                        title: '请填写校区名称！'
                    });
                    return false;
                }*/
                if(!this.e_campus.area){
                    this.$Notice.error({
                        title: '请填写区域！'
                    });
                    return false;
                }
                var city_info = document.getElementById('city_ipt').getAttribute('data').split('-');
                if(city_info.length === 0){//this.new_campus.city
                    this.$Notice.error({
                        title: '请选择城市！'
                    });
                    return false;
                }else {
                    var city_text = document.getElementById('city_ipt').getAttribute('data_name').split('-');
                    this.e_campus.country_id = city_info[0];
                    this.e_campus.country = city_text[0];
                    if(city_info.length === 3){
                        this.e_campus.province_id = city_info[1];
                        this.e_campus.city_id = city_info[2];
                        this.e_campus.province = city_text[1];
                        this.e_campus.city = city_text[2];
                    }else if(city_info.length === 2){
                        this.e_campus.city_id = city_info[1];
                        this.e_campus.city = city_text[1];
                    }
                }
                if(!this.e_campus.timezone){
                    this.$Notice.error({
                        title: '请填写时区！'
                    });
                    return false;
                }
                /*if(!this.e_campus.address){
                    this.$Notice.error({
                        title: '请填写详细地址！'
                    });
                    return false;
                }*/
                /*if(!this.e_campus.postcode){
                    this.$Notice.error({
                        title: '请填写邮编！'
                    });
                    return false;
                }*/
                /*if(!this.e_campus.phone){
                    this.$Notice.error({
                        title: '请填写电话！'
                    });
                    return false;
                }*/
                /*if(this.e_campus.photo < 1){
                    this.$Notice.error({
                        title: '请上传照片！'
                    });
                    return false;
                }*/

                //this.campus_list.push(this.e_campus);
                console.log(this.e_index)
                this.campus_list[this.e_index] = this.e_campus;

                this.cancel_campus();
            },
            del_campus(index){
                this.campus_list.splice(index, 1);
            },
            addRow (){  // 添加一行活地点
                var item = {
                    fullname_english: '',
                    fullname_chinese: '',
                    short: '',
                    major_id: 0
                }
                this.majors.push(item);
            },
            delRow (i){  // 删除一行活动地点
                if(i === 0){
                    this.majors.splice(0, 1);
                }else {
                    this.majors.splice(i, 1);
                }
                console.log(i)

            },
            getMajors(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/major',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        //console.log(res.data)
                        /*var majors = [];
                        for(var i=0;i<res.data.data.length;i++){
                            var item = {};
                            item.id = res.data.data[i].id;
                            item.name = res.data.data[i].short;
                            item.major_id = res.data.data[i].major_id;
                            majors.push(item);
                        }*/
                        _this.majorList = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getGeo (){  //  获取国家列表
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.countrys = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            checkImg_p(file){
                //this.checkImg(file,'p');
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            /*if(!!str && str === 'p'){
                                _this.uploadImg(file, str);
                            }else if(!!str && str === 'e') {
                                _this.uploadImg(file, str);
                            }else {
                                _this.uploadImg(file);
                            }*/
                            _this.uploadImg(file, 'p');

                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            checkImg_e(file){
                //this.checkImg(file,'e');
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            /*if(!!str && str === 'p'){
                             _this.uploadImg(file, str);
                             }else if(!!str && str === 'e') {
                             _this.uploadImg(file, str);
                             }else {
                             _this.uploadImg(file);
                             }*/
                            _this.uploadImg(file, 'e');

                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            checkImg (file,str){
                if(!str && !this.form.directory){
                    this.$Notice.error({
                        title: '请先填写文件目录'
                    });
                    return false;
                }
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (!str && (img.width != img.height || img.width != Math_Width)) {
                            _this.$Notice.error({
                                title: '请上传1024*1024的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            if(!!str && str === 'p'){
                                _this.uploadImg(file, str);
                            }else if(!!str && str === 'e') {
                                _this.uploadImg(file, str);
                            }else {
                                _this.uploadImg(file);
                            }

                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            uploadImg (file,str){
                var form = new FormData();
                form.append('file', file);
                var url = '/api/v1/admin/school_area/photo_upload/?school_area_id=0';
                if(!str){
                    url = '/api/v1/admin/event/school/logo?directory=' + this.form.directory;
                }
                const _this = this;
                util.ajax({
                    url: url,
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        if(!!str && str === 'p'){
                            /*_this.new_campus.photo = res.data.data.fullpath;
                            _this.p_files.push({
                                id: res.data.data.aid,
                                src: res.data.data.fullpath
                            })*/
                            _this.new_campus.photo.push(res.data.data[0]);
                            _this.errMsg = '';
                            return;
                        }if(!!str && str === 'e'){
                           /* _this.e_campus.photo = res.data.data.fullpath;
                            _this.e_files.push({
                                id: res.data.data.aid,
                                src: res.data.data.fullpath
                            })*/
                            _this.e_campus.photo.push(res.data.data[0]);
                            _this.errMsg = '';
                            return;
                        }else {
                            _this.form.logo_url = res.data.data.fullpath;
                            _this.files.push({
                                id: res.data.data.aid,
                                src: res.data.data.fullpath
                            })
                            _this.errMsg = '';
                        }
                    }else{
                        // console.log(res)
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (file,str,index){
                if(!!str && str === 'p'){
                    /*this.p_files = [];
                    this.new_campus.photo = '';*/
                    this.new_campus.photo.splice(index, 1);
                }if(!!str && str === 'e'){
                    /*this.e_files = [];
                    this.e_campus.photo = '';*/
                    this.e_campus.photo.splice(index, 1);
                }else {
                    this.files = [];
                    this.form.image = '';
                }
            },
            handleSubmit () {
                var dataIn = {
                    directory: this.form.directory,
                    university_name: this.form.university_name,
                    university_name_cn: this.form.university_name_cn,
                    university_id: this.form.university_id,
                    school_name: this.form.school_name,
                    school_name_cn: this.form.school_name_cn,
                    display_name: this.form.display_name,
                    short: this.form.short,
                    logo_url: this.form.logo_url,
                    keyword: this.form.keyword,
                    website: this.form.website,
                    type: this.form.type,
                    //area: '',//this.form.area,
                    //country: this.form.country,
                    //majors: this.majors
                    introduction: this.form.introduction,
                    school_area: []//this.campus_list
                };
                if(!dataIn.directory){
                    this.$Notice.error({
                        title: '请填写文件目录！'
                    });
                    return false;
                }
                if(!dataIn.school_name && !dataIn.school_name_cn){
                    this.$Notice.error({
                        title: '学院名称（英）和学院名称（中）至少填写一个！'
                    });
                    return false;
                }
               /* if(!dataIn.university_name && !dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '大学名称（英）和大学名称（中）至少填写一个！'
                    });
                    return false;
                }*/
                /*if(!dataIn.school_name_cn){
                    this.$Notice.error({
                        title: '请填写学院名称（中）！'
                    });
                    return false;
                }*/
                /*if(dataIn.university_id < 1){
                    this.$Notice.error({
                        title: '请选择大学！'
                    });
                    return false;
                }*/
               /* if(!dataIn.university_name){
                    this.$Notice.error({
                        title: '请填写大学名称（英）！'
                    });
                    return false;
                }
                if(!dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '请填写大学名称（中）！'
                    });
                    return false;
                }*/
                /*if(!dataIn.display_name){
                    this.$Notice.error({
                        title: '请填写显示全称！'
                    });
                    return false;
                }
                if(!dataIn.short){
                    this.$Notice.error({
                        title: '请填写显示简称！'
                    });
                    return false;
                }
                if(!dataIn.logo_url){
                    this.$Notice.error({
                        title: '请上传学校LOGO！'
                    });
                    return false;
                }
                if(!dataIn.keyword){
                    this.$Notice.error({
                        title: '请填写关键词！'
                    });
                    return false;
                }
                if(!dataIn.website){
                    this.$Notice.error({
                        title: '请填写商学院首页！'
                    });
                    return false;
                }*/
                /*if(!dataIn.area){
                    this.$Notice.error({
                        title: '请填写所属地区！'
                    });
                    return false;
                }*/
                /*if(!dataIn.country){
                    this.$Notice.error({
                        title: '请填写所属国家！'
                    });
                    return false;
                }*/
                /*if(!dataIn.introduction){
                    this.$Notice.error({
                        title: '请填写学校介绍！'
                    });
                    return false;
                }*/
                for(var i=0;i<this.campus_list.length;i++){
                    var item = {
                        name: this.campus_list[i].name,
                        area: this.campus_list[i].area,
                        country_id: this.campus_list[i].country_id,
                        province_id: this.campus_list[i].province_id,
                        city_id: this.campus_list[i].city_id,
                        country: this.campus_list[i].country,
                        province: this.campus_list[i].province,
                        city: this.campus_list[i].city,
                        timezone: this.campus_list[i].timezone,
                        address: this.campus_list[i].address,
                        postcode: this.campus_list[i].postcode,
                        phone: this.campus_list[i].phone,
                        photo: this.campus_list[i].photo,
                        photoid:[],
                        google_map: '',
                        baidu_map: ''
                    }

                    for(var j=0;j<item.photo.length;j++){
                        item.photoid.push(item.photo[j].aid);
                    }

                    dataIn.school_area.push(item);
                }
                //console.log(dataIn);
                //return false;
                /*for(var i=0;i<this.majors.length;i++){
                    if(!this.majors[i].fullname_english){
                        this.$Notice.error({
                            title: '请填写专业全称（英）！'
                        });
                        return false;
                    }
                    if(!this.majors[i].fullname_chinese){
                        this.$Notice.error({
                            title: '请填写专业全称（中！'
                        });
                        return false;
                    }
                    if(!this.majors[i].short){
                        this.$Notice.error({
                            title: '请填写专业简称！'
                        });
                        return false;
                    }
                    if(!this.majors[i].major_id){
                        this.$Notice.error({
                            title: '请选择所属分类！'
                        });
                        return false;
                    }
                }*/
                this.postSchool(dataIn);
            },
            postSchool (dataIn){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/school',//'/api/v1/admin/event/school',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        //var school_id = res.data.data.id;
                        /*for(var i=0;i<_this.majors.length;i++){
                            var dataMajor = {};
                            dataMajor.fullname_english = _this.majors[i].fullname_english;
                            dataMajor.fullname_chinese = _this.majors[i].fullname_chinese;
                            dataMajor.short = _this.majors[i].short;
                            dataMajor.major_id = _this.majors[i].major_id;
                            dataMajor.school_id = school_id;

                            _this.postMajor(dataMajor);
                        }*/
                        _this.$Notice.success({
                            title: '提交成功！！'
                        });
                        _this.$store.commit('removeTag', 'college_add');
                        _this.$store.commit('closePage', 'gollege_add');
                        _this.$router.push({
                            name: 'college_list'
                        });
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            postMajor (dataIn){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school/major',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.I_index++;
                        // console.log(res.data)
                        if(_this.I_index >= _this.majors.length){
                            _this.$Notice.success({
                                title: '提交成功！！'
                            });
                            _this.$store.commit('removeTag', 'college_add');
                            _this.$store.commit('closePage', 'college_add');
                            _this.$router.push({
                                name: 'college_list'
                            });
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form.directory = '';
                this.form.university_name = '';
                this.form.university_name_cn = '';
                this.form.school_name = '';
                this.form.school_name_cn = '';
                this.form.display_name = '';
                this.form.short = '';
                this.form.logo_url = '';
                this.form.keyword = '';
                this.form.website = '';
                this.form.type = 1,
                /*this.form.area = '';
                 this.form.country = '';*/
                this.form.introduction = '';
                /*this.majors = [
                 {
                 fullname_english: '',
                 fullname_chinese: '',
                 short: '',
                 major_id: 0
                 }
                 ];*/
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: ''
                };
                this.e_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: ''
                };
                this.files = [];
                this.p_files = [];
                this.e_files = [];
                this.I_index = 0;
                this.campus_list = [];
                this.e_index = -1;
                this.isAdd = false;
                this.isEdit = false;
                this.universitys = [];
                this.$store.commit('removeTag', 'college_add');
                this.$router.push({
                    name: 'college_list'
                });
            }
        },
        created () {
        }
    };
</script>