<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="college-program">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    编辑项目信息
                </p>
                <div style="width: 90%;padding: 10px 40px 20px;">
                    <Row style="padding: 10px 15px;color: #bbbec4">
                        <Col span="13">英文全称 / 中文全称</Col>
                        <Col span="9">展示名 / 项目方向</Col>
                        <Col span="2" align="center">操作</Col>
                    </Row>
                    <Row v-for="program,index in programData" :key="program.index" type="flex" justify="center" align="middle"  style="background: #f8f8f9;padding: 10px 15px;margin-bottom: 10px;">
                        <Col span="13">
                            {{program.program_name_en}}
                            <br>
                            {{program.program_name_cn}}
                        </Col>
                        <Col span="9">
                            {{program.display_name}}
                            <br>
                            {{program.program_direction_name}}
                        </Col>
                        <Col span="2" align="center">
                            <Icon type="edit" size="20" style="color: #19be6b;cursor: pointer" @click="getRowData(program)"></Icon>
                        </Col>
                    </Row>
                </div>
                <Modal
                        v-model="toEdit"
                        :mask-closable="false"
                        :title="form.school_name"
                        :width="600"
                >
                    <Form :label-width="120">
                       <!-- <FormItem label="专业全称（英）：" class="ivu-form-item-required">
                            <Input v-model="editData.fullname_english" placeholder="请输入专业全称（英）"></Input>
                        </FormItem>-->
                        <FormItem label="中文全称" class="ivu-form-item-required">
                            <Input v-model="editData.program_name_cn" placeholder="请输入中文全称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="英文全称" class="ivu-form-item-required">
                            <Input v-model="editData.program_name_en" placeholder="请输入英文全称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="展示名：" class="ivu-form-item-required">
                            <Input v-model="editData.display_name" placeholder="请输入中文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="中文简称：" class="ivu-form-item-required">
                            <Input v-model="editData.abbr_cn" placeholder="请输入中文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="英文简称：" class="ivu-form-item-required">
                            <Input v-model="editData.abbr_en" placeholder="请输入英文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="关键字：" class="ivu-form-item-required">
                            <Input v-model="editData.keyword" placeholder="请输入英文简称" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="简介：">
                            <Input type="textarea" :autosize="{minRows: 5,maxRows: 6}" style="width: 400px;" v-model="editData.introduction" placeholder="简介"></Input>
                        </FormItem>
                        <FormItem label="项目方向：" class="ivu-form-item-required">
                            <RadioGroup v-model="editData.program_direction_id">
                                <Radio :label="item.id" v-for="item in program_direction" :key="item.index">{{item.display_name}}</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="项目学位类型：" class="ivu-form-item-required">
                            <RadioGroup v-model="editData.program_degree_category_id">
                                <Radio :label="item.id" v-for="item in program_degree_category" :key="item.index">{{item.education_level_cn}}</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="项目类型：" class="ivu-form-item-required">
                            <RadioGroup v-model="editData.program_type">
                                <Radio :label="1">全日制</Radio>
                                <Radio :label="2">非全日制</Radio>
                                <Radio :label="3">其它</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="授课方式：" class="ivu-form-item-required">
                            <RadioGroup v-model="editData.teaching_method">
                                <Radio :label="1">面授</Radio>
                                <Radio :label="2">在线</Radio>
                                <Radio :label="4">面授/在线</Radio>
                                <Radio :label="3">其它</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="时长：">
                            <Input v-model="editData.program_duration" placeholder="请输入时长" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="招生人数：">
                            <Input v-model="editData.enrollment" placeholder="请输入招生人数" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="开学时间：">
                            <Input v-model="editData.school_starttime" placeholder="请输入开学时间" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="">
                            <p class="line"><strong>费用相关</strong> <span></span></p>
                        </FormItem>
                        <FormItem label="申请费：">
                            <Input v-model="editData.application_fee" placeholder="请输入申请费" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="学费：">
                            <Input v-model="editData.tuition" placeholder="请输入学费" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="生活费：">
                            <Input v-model="editData.living_expenses" placeholder="请输入生活费" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="保险费：">
                            <Input v-model="editData.insurance_premiums" placeholder="请输入保险费" style="width: 400px;"></Input>
                        </FormItem>

                        <FormItem label="">
                            <p class="line"><strong>送分代码</strong> <span></span></p>
                        </FormItem>
                        <FormItem label="GMAT：">
                            <Input v-model="editData.gmat_code" placeholder="请输入GMAT送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="EA：">
                            <Input v-model="editData.ea_code" placeholder="请输入EA送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="GRE：">
                            <Input v-model="editData.gre_code" placeholder="请输入GRE送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="TOEFL：">
                            <Input v-model="editData.toefl_code" placeholder="请输入TOEFL送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="IELTS：">
                            <Input v-model="editData.ielts_code" placeholder="请输入IELTS送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="PTE：">
                            <Input v-model="editData.pte_code" placeholder="请输入PTE送分代码" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="GRE_MAJOR_CORD：">
                            <Input v-model="editData.gre_major_code" placeholder="请输入GRE_MAJOR_CORD送分代码" style="width: 400px;"></Input>
                        </FormItem>

                        <FormItem label="">
                            <p class="line"><strong>成绩要求</strong> <span></span></p>
                        </FormItem>
                        <FormItem label="GMAT：">
                            <Input v-model="editData.gmat_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="editData.gmat_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="EA：">
                            <Input v-model="editData.ea_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="editData.ea_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="GRE：">
                            <Input v-model="editData.gre_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="editData.gre_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="TOEFL：">
                            <Input v-model="editData.toefl_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="editData.toefl_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="IELTS：">
                            <Input v-model="editData.ielts_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="editData.ielts_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="PTE：">
                            <Input v-model="editData.pte_score" placeholder="具体要求" style="width: 400px;"></Input>
                            <Input v-model="editData.pte_remark" placeholder="备注" style="width: 400px;"></Input>
                        </FormItem>

                        <!--<FormItem label="Deadline：">
                            <Input v-model="editData.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="排名：">
                            <Input v-model="editData.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="奖学金：">
                            <Input v-model="editData.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="Class Profile：">
                            <Input v-model="editData.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>
                        <FormItem label="社交媒体：">
                            <Input v-model="editData.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                        </FormItem>-->
                    </Form>
                    <div slot="footer">
                        <Button type="text" size="large" @click="toEdit=false">取消</Button>
                        <Button type="primary" size="large" @click="pushProgram">确定</Button>
                    </div>
                </Modal>
                <Form ref="form" :model="form" :label-width="150">
                    <div class="programs">
                        <h3 style="border-bottom: 1px solid #efefef;margin-bottom: 10px;padding-left: 40px;">新增项目</h3>
                        <p style="margin-bottom: 10px;">
                            <span style="display: inline-block;width: 140px;text-align: right;margin-right: 10px;color: #bbbec4;">所属学院：</span>
                            <span>{{form.school_name}}</span>
                        </p>
                        <div class="program" v-for="program,index in programs">
                            <FormItem label="中文全称" class="ivu-form-item-required">
                                <Input v-model="program.program_name_cn" placeholder="请输入中文全称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="英文全称" class="ivu-form-item-required">
                                <Input v-model="program.program_name_en" placeholder="请输入英文全称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="展示名：" class="ivu-form-item-required">
                                <Input v-model="program.display_name" placeholder="请输入中文简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="中文简称：" class="ivu-form-item-required">
                                <Input v-model="program.abbr_cn" placeholder="请输入中文简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="英文简称：" class="ivu-form-item-required">
                                <Input v-model="program.abbr_en" placeholder="请输入英文简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="关键字：" class="ivu-form-item-required">
                                <Input v-model="program.keyword" placeholder="请输入英文简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="简介：">
                                <Input type="textarea" :autosize="{minRows: 5,maxRows: 6}" style="width: 400px;" v-model="program.introduction" placeholder="简介"></Input>
                            </FormItem>
                            <FormItem label="项目方向：" class="ivu-form-item-required">
                                <RadioGroup v-model="program.program_direction_id">
                                    <Radio :label="item.id" v-for="item in program_direction" :key="item.index">{{item.display_name}}</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="项目学位类型：" class="ivu-form-item-required">
                                <RadioGroup v-model="program.program_degree_category_id">
                                    <Radio :label="item.id" v-for="item in program_degree_category" :key="item.index">{{item.education_level_cn}}</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="项目类型：" class="ivu-form-item-required">
                                <RadioGroup v-model="program.program_type">
                                    <Radio :label="1">全日制</Radio>
                                    <Radio :label="2">非全日制</Radio>
                                    <Radio :label="3">其它</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="授课方式：" class="ivu-form-item-required">
                                <RadioGroup v-model="program.teaching_method">
                                    <Radio :label="1">面授</Radio>
                                    <Radio :label="2">在线</Radio>
                                    <Radio :label="4">面授/在线</Radio>
                                    <Radio :label="3">其它</Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem label="时长：">
                                <Input v-model="program.program_duration" placeholder="请输入时长" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="招生人数：">
                                <Input v-model="program.enrollment" placeholder="请输入招生人数" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="开学时间：">
                                <Input v-model="program.school_starttime" placeholder="请输入开学时间" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="">
                                <p class="line"><strong>费用相关</strong> <span></span></p>
                            </FormItem>
                            <FormItem label="申请费：">
                                <Input v-model="program.application_fee" placeholder="请输入申请费" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="学费：">
                                <Input v-model="program.tuition" placeholder="请输入学费" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="生活费：">
                                <Input v-model="program.living_expenses" placeholder="请输入生活费" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="保险费：">
                                <Input v-model="program.insurance_premiums" placeholder="请输入保险费" style="width: 400px;"></Input>
                            </FormItem>

                            <FormItem label="">
                                <p class="line"><strong>送分代码</strong> <span></span></p>
                            </FormItem>
                            <FormItem label="GMAT：">
                                <Input v-model="program.gmat_code" placeholder="请输入GMAT送分代码" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="EA：">
                                <Input v-model="program.ea_code" placeholder="请输入EA送分代码" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="GRE：">
                                <Input v-model="program.gre_code" placeholder="请输入GRE送分代码" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="TOEFL：">
                                <Input v-model="program.toefl_code" placeholder="请输入TOEFL送分代码" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="IELTS：">
                                <Input v-model="program.ielts_code" placeholder="请输入IELTS送分代码" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="PTE：">
                                <Input v-model="program.pte_code" placeholder="请输入PTE送分代码" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="GRE_MAJOR_CORD：">
                                <Input v-model="program.gre_major_code" placeholder="请输入GRE_MAJOR_CORD送分代码" style="width: 400px;"></Input>
                            </FormItem>

                            <FormItem label="">
                                <p class="line"><strong>成绩要求</strong> <span></span></p>
                            </FormItem>
                            <FormItem label="GMAT：">
                                <Input v-model="program.gmat_score" placeholder="具体要求" style="width: 400px;"></Input>
                                <Input v-model="program.gmat_remark" placeholder="备注" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="EA：">
                                <Input v-model="program.ea_score" placeholder="具体要求" style="width: 400px;"></Input>
                                <Input v-model="program.ea_remark" placeholder="备注" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="GRE：">
                                <Input v-model="program.gre_score" placeholder="具体要求" style="width: 400px;"></Input>
                                <Input v-model="program.gre_remark" placeholder="备注" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="TOEFL：">
                                <Input v-model="program.toefl_score" placeholder="具体要求" style="width: 400px;"></Input>
                                <Input v-model="program.toefl_remark" placeholder="备注" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="IELTS：">
                                <Input v-model="program.ielts_score" placeholder="具体要求" style="width: 400px;"></Input>
                                <Input v-model="program.ielts_remark" placeholder="备注" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="PTE：">
                                <Input v-model="program.pte_score" placeholder="具体要求" style="width: 400px;"></Input>
                                <Input v-model="program.pte_remark" placeholder="备注" style="width: 400px;"></Input>
                            </FormItem>

                            <!--<FormItem label="Deadline：">
                                <Input v-model="program.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="排名：">
                                <Input v-model="program.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="奖学金：">
                                <Input v-model="program.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="Class Profile：">
                                <Input v-model="program.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="社交媒体：">
                                <Input v-model="program.Deadline" placeholder="具体要求" style="width: 400px;"></Input>
                            </FormItem>-->

                            <FormItem label="" v-if="index !== programs.length-1" style="margin-left:-150px;">
                                <div style="width: 100%;height: 1px;background: #efefef;"></div>
                            </FormItem>
                            <Icon type="trash-a" size="24" class="delItem" @click="delRow(index)" v-if="programs.length > 1"></Icon>
                        </div>
                    </div>
                    <FormItem>
                        <Button type="dashed" icon="plus" class="plusItem" style="border: 1px dashed #efefef;" @click="addRow"></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'college_program',
        components: {
            //canEditTable
        },
        data () {
            return {
                form: {
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                programs: [
                    {
                        program_name_en: '',
                        program_name_cn: '',
                        display_name: '',
                        abbr_cn: '',
                        abbr_en: '',
                        keyword: '',
                        introduction: '',
                        program_direction_id: 0,
                        program_degree_category_id: 0,
                        program_type: 0,
                        teaching_method: 0,
                        program_duration: '',
                        enrollment: '',
                        school_starttime: '',
                        application_fee: '',
                        tuition: '',
                        living_expenses: '',
                        insurance_premiums: '',
                        gmat_code: '',
                        ea_code: '',
                        gre_code: '',
                        toefl_code: '',
                        ielts_code: '',
                        pte_code: '',
                        gre_major_code: '',
                        gmat_score: '',
                        gmat_remark: '',
                        ea_score: '',
                        ea_remark: '',
                        gre_score: '',
                        gre_remark: '',
                        toefl_score: '',
                        toefl_remark: '',
                        ielts_score: '',
                        ielts_remark: '',
                        pte_score: '',
                        pte_remark: '',
                        id: 0
                    }
                ],
                editData:{
                    program_name_en: '',
                    program_name_cn: '',
                    display_name: '',
                    abbr_cn: '',
                    abbr_en: '',
                    keyword: '',
                    introduction: '',
                    program_direction_id: 0,
                    program_degree_category_id: 0,
                    program_type: 0,
                    teaching_method: 0,
                    program_duration: '',
                    enrollment: '',
                    school_starttime: '',
                    application_fee: '',
                    tuition: '',
                    living_expenses: '',
                    insurance_premiums: '',
                    gmat_code: '',
                    ea_code: '',
                    gre_code: '',
                    toefl_code: '',
                    ielts_code: '',
                    pte_code: '',
                    gre_major_code: '',
                    gmat_score: '',
                    gmat_remark: '',
                    ea_score: '',
                    ea_remark: '',
                    gre_score: '',
                    gre_remark: '',
                    toefl_score: '',
                    toefl_remark: '',
                    ielts_score: '',
                    ielts_remark: '',
                    pte_score: '',
                    pte_remark: ''
                },

                program_direction: [],
                program_degree_category: [],

                //programList: [],
                programData: [],
                school_id: 0,
                id: 0,
                toEdit: false,
                loading: true,
                I_index: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'college_program'){
                    this.form = {};
                    this.programs = [{
                        program_name_en: '',
                        program_name_cn: '',
                        display_name: '',
                        abbr_cn: '',
                        abbr_en: '',
                        keyword: '',
                        introduction: '',
                        program_direction_id: 0,
                        program_degree_category_id: 0,
                        program_type: 0,
                        teaching_method: 0,
                        program_duration: '',
                        enrollment: '',
                        school_starttime: '',
                        application_fee: '',
                        tuition: '',
                        living_expenses: '',
                        insurance_premiums: '',
                        gmat_code: '',
                        ea_code: '',
                        gre_code: '',
                        toefl_code: '',
                        ielts_code: '',
                        pte_code: '',
                        gre_major_code: '',
                        gmat_score: '',
                        gmat_remark: '',
                        ea_score: '',
                        ea_remark: '',
                        gre_score: '',
                        gre_remark: '',
                        toefl_score: '',
                        toefl_remark: '',
                        ielts_score: '',
                        ielts_remark: '',
                        pte_score: '',
                        pte_remark: ''
                    }];
                    this.editData = {
                        program_name_en: '',
                        program_name_cn: '',
                        display_name: '',
                        abbr_cn: '',
                        abbr_en: '',
                        keyword: '',
                        introduction: '',
                        program_direction_id: 0,
                        program_degree_category_id: 0,
                        program_type: 0,
                        teaching_method: 0,
                        duration: '',
                        enrollment: '',
                        school_starttime: '',
                        application_fee: '',
                        program_duration: '',
                        living_expenses: '',
                        insurance_premiums: '',
                        gmat_code: '',
                        ea_code: '',
                        gre_code: '',
                        toefl_code: '',
                        ielts_code: '',
                        pte_code: '',
                        gre_major_code: '',
                        gmat_score: '',
                        gmat_remark: '',
                        ea_score: '',
                        ea_remark: '',
                        gre_score: '',
                        gre_remark: '',
                        toefl_score: '',
                        toefl_remark: '',
                        ielts_score: '',
                        ielts_remark: '',
                        pte_score: '',
                        pte_remark: '',
                        id: 0
                    };
                    this.program_direction = [];
                    this.program_degree_category = [];

                    this.programList = [];
                    this.programData = [];
                    this.school_id = 0;
                    this.id = 0;
                    this.toEdit = false;
                    this.loading = true;
                    this.I_index = 0;
                    this.$refs['form'].resetFields();
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData(){
               // this.getPrograms();
                this.get_program_direction();
                this.get_program_degree_category();
                this.form = this.$store.state.event.currentRow;
                //console.log(this.form)
                this.id = window.location.href.split('/').pop();
                //console.log(this.id)
                this.school_id = this.id;
                this.getSchoolProgram();
            },
            addRow (){  // 添加一行活地点
                var item = {
                    program_name_en: '',
                    program_name_cn: '',
                    display_name: '',
                    abbr_cn: '',
                    abbr_en: '',
                    keyword: '',
                    introduction: '',
                    program_direction_id: 0,
                    program_degree_category_id: 0,
                    program_type: 0,
                    teaching_method: 0,
                    program_duration: '',
                    enrollment: '',
                    school_starttime: '',
                    application_fee: '',
                    program_duration: '',
                    living_expenses: '',
                    insurance_premiums: '',
                    gmat_code: '',
                    ea_code: '',
                    gre_code: '',
                    toefl_code: '',
                    ielts_code: '',
                    pte_code: '',
                    gre_major_code: '',
                    gmat_score: '',
                    gmat_remark: '',
                    ea_score: '',
                    ea_remark: '',
                    gre_score: '',
                    gre_remark: '',
                    toefl_score: '',
                    toefl_remark: '',
                    ielts_score: '',
                    ielts_remark: '',
                    pte_score: '',
                    pte_remark: ''
                }
                this.programs.push(item);
            },
            delRow (i){  // 删除一行活动地点
                if(i === 0){
                    this.programs.splice(0, 1);
                }else {
                    this.programs.splice(i, 1);
                }
                console.log(i)

            },
            getPrograms(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/program',//'/api/v1/admin/event/major',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {

                        _this.programList = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getSchoolProgram(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/misc/school/' + this.id,//'/api/v1/admin/event/school/major/' + this.id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        //console.log(res.data.data)
                        _this.form = res.data.data;
                        _this.programData = res.data.data.program;
                        console.log(_this.program_direction)
                        for(var i=0;i<_this.programData.length;i++){
                            _this.programData[i].teaching_method = parseInt(_this.programData[i].teaching_method);
                            _this.programData[i].program_type = parseInt(_this.programData[i].program_type);

                            for(var j=0;j<_this.program_direction.length;j++){

                                if(_this.programData[i].program_direction_id === _this.program_direction[j].id){
                                    _this.programData[i].program_direction_name = _this.program_direction[j].display_name;
                                }
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getRowData(item){
                this.editData = {};

                this.editData.program_name_en = item.program_name_en;
                this.editData.program_name_cn = item.program_name_cn;
                this.editData.display_name = item.display_name;
                this.editData.abbr_cn = item.abbr_cn;
                this.editData.abbr_en = item.abbr_en;
                this.editData.keyword = item.keyword;
                this.editData.introduction = item.introduction;
                this.editData.program_direction_id = item.program_direction_id;
                this.editData.program_degree_category_id = item.program_degree_category_id;
                this.editData.program_type = item.program_type;
                this.editData.teaching_method = item.teaching_method;
                this.editData.duration = item.duration;
                this.editData.enrollment = item.enrollment;
                this.editData.school_starttime = item.school_starttime;
                this.editData.application_fee = item.application_fee;
                this.editData.program_duration = item.program_duration;
                this.editData.living_expenses = item.living_expenses;
                this.editData.insurance_premiums = item.insurance_premiums;
                this.editData.gmat_code = item.gmat_code;
                this.editData.ea_code = item.ea_code;
                this.editData.gre_code = item.gre_code;
                this.editData.toefl_code = item.toefl_code;
                this.editData.ielts_code = item.ielts_code;
                this.editData.pte_code = item.pte_code;
                this.editData.gre_major_code = item.gre_major_code;
                this.editData.gmat_score = item.gmat_score;
                this.editData.gmat_remark = item.gmat_remark;
                this.editData.ea_score = item.ea_score;
                this.editData.ea_remark = item.ea_remark;
                this.editData.gre_score = item.gre_score;
                this.editData.gre_remark = item.gre_remark;
                this.editData.toefl_score = item.toefl_score;
                this.editData.toefl_remark = item.toefl_remark;
                this.editData.ielts_score = item.ielts_score;
                this.editData.ielts_remark = item.ielts_remark;
                this.editData.pte_score = item.pte_score;
                this.editData.pte_remark = item.pte_remark;
                this.editData.id = item.id;
                this.toEdit = true;
            },
            get_program_direction (){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/program_direction',
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        //console.log(res.data)
                        _this.program_direction = res.data.data;
                        console.log(_this.programData)
                        for(var i=0;i<_this.programData.length;i++){
                            for(var j=0;j<_this.program_direction.length;j++){
                                if(_this.programData[i].program_direction_id === _this.program_direction[j].id){
                                    _this.programData[i].program_direction_name = _this.program_direction[j].display_name;
                                }
                            }
                        }
                        //this.program_direction
                        //_this.form = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            get_program_degree_category(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/program_degree_category',
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        console.log(res.data.data)
                        _this.program_degree_category = res.data.data;
                        // _this.form = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            pushProgram (){
                this.loading = true;
                var dataIn = {
                    school_id: this.editData.school_id,
                    id: this.editData.id,
                    program_name_en: this.editData.program_name_en,
                    program_name_cn: this.editData.program_name_cn,
                    display_name: this.editData.display_name,
                    abbr_cn: this.editData.abbr_cn,
                    abbr_en: this.editData.abbr_en,
                    keyword: this.editData.keyword,
                    introduction: this.editData.introduction,
                    program_direction_id: this.editData.program_direction_id,
                    program_degree_category_id: this.editData.program_degree_category_id,
                    program_type: this.editData.program_type,
                    teaching_method: this.editData.teaching_method,
                    program_duration: this.editData.program_duration,
                    enrollment: this.editData.enrollment,
                    school_starttime: this.editData.school_starttime,
                    application_fee: this.editData.application_fee,
                    tuition: this.editData.tuition,
                    living_expenses: this.editData.living_expenses,
                    insurance_premiums: this.editData.insurance_premiums,
                    gmat_code: this.editData.gmat_code,
                    ea_code: this.editData.ea_code,
                    gre_code: this.editData.gre_code,
                    toefl_code: this.editData.toefl_code,
                    ielts_code: this.editData.ielts_code,
                    pte_code: this.editData.pte_code,
                    gre_major_code: this.editData.gre_major_code,
                    gmat_score: this.editData.gmat_score,
                    gmat_remark: this.editData.gmat_remark,
                    ea_score: this.editData.ea_score,
                    ea_remark: this.editData.ea_remark,
                    gre_score: this.editData.gre_score,
                    gre_remark: this.editData.gre_remark,
                    toefl_score: this.editData.toefl_score,
                    toefl_remark: this.editData.toefl_remark,
                    ielts_score: this.editData.ielts_score,
                    ielts_remark: this.editData.ielts_remark,
                    pte_score: this.editData.pte_score,
                    pte_remark: this.editData.pte_remark,

                    /*fullname_english: this.editData.fullname_english,
                    fullname_chinese: this.editData.fullname_chinese,
                    short: this.editData.short,
                    major_id: this.editData.major_id*/
                }
                console.log(dataIn)
                if(!dataIn.program_name_cn){
                    this.$Notice.error({
                        title: '中文全称不能为空！'
                    });
                    return false;
                }
                if(!dataIn.program_name_en){
                    this.$Notice.error({
                        title: '英文全称不能为空！'
                    });
                    return false;
                }
                if(!dataIn.display_name){
                    this.$Notice.error({
                        title: '展示名不能为空！'
                    });
                    return false;
                }
                if(!dataIn.abbr_cn){
                    this.$Notice.error({
                        title: '中文简称不能为空！'
                    });
                    return false;
                }
                if(!dataIn.abbr_en){
                    this.$Notice.error({
                        title: '英文简称不能为空！'
                    });
                    return false;
                }
                if(!dataIn.keyword){
                    this.$Notice.error({
                        title: '关键字不能为空！'
                    });
                    return false;
                }
                if(!dataIn.program_direction_id){
                    this.$Notice.error({
                        title: '请选择项目方向！'
                    });
                    return false;
                }
                if(!dataIn.program_degree_category_id){
                    this.$Notice.error({
                        title: '请选择项目学位类型！'
                    });
                    return false;
                }
                if(!dataIn.program_type){
                    this.$Notice.error({
                        title: '请选择项目类型！'
                    });
                    return false;
                }
                if(!dataIn.teaching_method){
                    this.$Notice.error({
                        title: '请选择授课方式！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/program',//'/api/v1/admin/event/school/major',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        // console.log(res.data)
                        _this.getSchoolProgram();
                        _this.$Notice.success({
                            title: '提交成功！！'
                        });
                        _this.toEdit = false;
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            handleSubmit () {
                var dataIn = {
                    school_id: this.school_id,
                    program: this.programs
                };
                console.log(dataIn);
                //return false;
                for(var i=0;i<dataIn.program.length;i++){
                    /*if(!dataIn.program[i].fullname_english){
                        this.$Notice.error({
                            title: '请填写专业全称（英）！'
                        });
                        return false;
                    }*/
                    if(!dataIn.program[i].program_name_cn){
                        this.$Notice.error({
                            title: '中文全称不能为空！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].program_name_en){
                        this.$Notice.error({
                            title: '英文全称不能为空！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].display_name){
                        this.$Notice.error({
                            title: '展示名不能为空！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].abbr_cn){
                        this.$Notice.error({
                            title: '中文简称不能为空！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].abbr_en){
                        this.$Notice.error({
                            title: '英文简称不能为空！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].keyword){
                        this.$Notice.error({
                            title: '关键字不能为空！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].program_direction_id){
                        this.$Notice.error({
                            title: '请选择项目方向！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].program_degree_category_id){
                        this.$Notice.error({
                            title: '请选择项目学位类型！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].program_type){
                        this.$Notice.error({
                            title: '请选择项目类型！'
                        });
                        return false;
                    }
                    if(!dataIn.program[i].teaching_method){
                        this.$Notice.error({
                            title: '请选择授课方式！'
                        });
                        return false;
                    }
                }
                console.log(dataIn)
                this.submitAjax(dataIn);
                /*for(var i=0;i<dataIn.majors.length;i++){
                    dataIn.majors[i].school_id = this.id;
                    this.submitAjax(dataIn.majors[i]);
                }*/
            },
            submitAjax (dataIn){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/program',//'/api/v1/admin/event/school/major',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        // console.log(res.data)
                        _this.I_index++;
                        if(_this.I_index >= _this.programs.length){
                            _this.$Notice.success({
                                title: '提交成功！！'
                            });
                            _this.$store.commit('removeTag', 'college_program');
                            _this.$store.commit('closePage', 'college_program');
                            _this.$router.push({
                                name: 'college_list'
                            });
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.programs = [{
                    program_name_en: '',
                    program_name_cn: '',
                    display_name: '',
                    abbr_cn: '',
                    abbr_en: '',
                    keyword: '',
                    introduction: '',
                    program_direction_id: 0,
                    program_degree_category_id: 0,
                    program_type: 0,
                    teaching_method: 0,
                    duration: '',
                    enrollment: '',
                    school_starttime: '',
                    application_fee: '',
                    tuition: '',
                    living_expenses: '',
                    insurance_premiums: '',
                    gmat_code: '',
                    ea_code: '',
                    gre_code: '',
                    toefl_code: '',
                    ielts_code: '',
                    pte_code: '',
                    gre_major_code: '',
                    gmat_score: '',
                    gmat_remark: '',
                    ea_score: '',
                    ea_remark: '',
                    gre_score: '',
                    gre_remark: '',
                    toefl_score: '',
                    toefl_remark: '',
                    ielts_score: '',
                    ielts_remark: '',
                    pte_score: '',
                    pte_remark: ''
                }];
                this.editData = {
                    program_name_en: '',
                    program_name_cn: '',
                    display_name: '',
                    abbr_cn: '',
                    abbr_en: '',
                    keyword: '',
                    introduction: '',
                    program_direction_id: 0,
                    program_degree_category_id: 0,
                    program_type: 0,
                    teaching_method: 0,
                    duration: '',
                    enrollment: '',
                    school_starttime: '',
                    application_fee: '',
                    tuition: '',
                    living_expenses: '',
                    insurance_premiums: '',
                    gmat_code: '',
                    ea_code: '',
                    gre_code: '',
                    toefl_code: '',
                    ielts_code: '',
                    pte_code: '',
                    gre_major_code: '',
                    gmat_score: '',
                    gmat_remark: '',
                    ea_score: '',
                    ea_remark: '',
                    gre_score: '',
                    gre_remark: '',
                    toefl_score: '',
                    toefl_remark: '',
                    ielts_score: '',
                    ielts_remark: '',
                    pte_score: '',
                    pte_remark: '',
                    id: 0
                };
                this.program_direction = [];
                this.program_degree_category = [];

                this.programList = [];
                this.programData = [];
                this.school_id = 0;
                this.id = 0;
                this.toEdit = false;
                this.loading = true;
                this.I_index = 0;
                this.$store.commit('removeTag', 'college_program');
                this.$router.push({
                    name: 'college_list'
                });
            }
        },
        created () {

        }
    };
</script>