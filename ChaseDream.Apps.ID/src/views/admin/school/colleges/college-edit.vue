<style lang="less">
    @import '../../../../styles/common.less';
    @import '../school.less';
</style>

<template>
    <div class="college-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    编辑学院信息
                </p>
                <Form ref="form" :model="form" :label-width="150" @submit.native.prevent>
                    <Row>
                        <Col span="12">
                        <FormItem label="LOGO：" class="logo-item">
                            <Upload action="/api/v1/admin/event/upload"
                                    :before-upload="checkImg"
                                    :show-upload-list="true"
                                    v-if="files.length === 0"
                            >
                                <Button type="ghost" icon="plus"></Button>
                            </Upload>
                            <div class="file-list" v-if="files.length > 0">
                                <div v-for="file in files">
                                    <img :src="file.src" alt="">
                                    <p class="del">
                                        <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                    </p>
                                </div>
                            </div>
                        </FormItem>
                        </Col>
                        <Col span="12" style="margin-top: 0px;">
                            <FormItem label="官网：">
                                <Input v-model="form.website" placeholder="请输入官网"></Input>
                            </FormItem>
                            <FormItem label="类型：">
                                <RadioGroup v-model="form.type">
                                    <Radio :label="1">大学内商学院</Radio>
                                    <Radio :label="2">独立商学院</Radio>
                                    <Radio :label="3">机构</Radio>
                                    <Radio :label="4">测试</Radio>
                                </RadioGroup>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                        <FormItem label="文件目录：" class="ivu-form-item-required">
                            <Input v-model="form.directory" placeholder="请输入文件目录"></Input>
                        </FormItem>
                        </Col>
                        <Col span="12">
                        <FormItem label="关键词：">
                            <Input v-model="form.keyword" placeholder="请输入关键词"></Input>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                        <FormItem label="学院名（英）：">
                            <Input v-model="form.school_name" placeholder="请输入学院名（英）"></Input>
                        </FormItem>
                        </Col>
                        <Col span="12">
                        <FormItem label="学院名（中）：">
                            <Input v-model="form.school_name_cn" placeholder="请输入学院名（中）"></Input>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <!--<Col span="24">
                        <FormItem label="大学：" class="ivu-form-item-required">
                            <Select v-model="form.university_id" filterable>
                                <Option v-for="item,index in universitys" :value="item.id" :key="item.index">{{ item.university_name_en +' - '+ item.university_name_cn }}</Option>
                            </Select>
                        </FormItem>
                        </Col>-->
                        <Col span="12">
                        <FormItem label="大学（英）：">
                            <Input v-model="form.university_name" placeholder="请输入大学（英）"></Input>
                        </FormItem>
                        </Col>
                        <Col span="12">
                        <FormItem label="大学（中）：">
                            <Input v-model="form.university_name_cn" placeholder="请输入大学（中）"></Input>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="12">
                        <FormItem label="显示名（英）：">
                            <Input v-model="form.display_name" placeholder="请输入显示名（英）"></Input>
                        </FormItem>
                        </Col>
                        <Col span="12">
                        <FormItem label="显示名（简）：">
                            <Input v-model="form.short" placeholder="请输入显示名（简）"></Input>
                        </FormItem>
                        </Col>
                    </Row>
                    <!--<Row>
                        <Col span="12">
                        <FormItem label="LOGO：" class="ivu-form-item-required logo-item">
                            <Upload action="/api/v1/admin/event/upload"
                                    :before-upload="checkImg"
                                    :show-upload-list="true"
                                    v-if="files.length === 0"
                            >
                                <Button type="ghost" icon="plus"></Button>
                            </Upload>
                            <div class="file-list" v-if="files.length > 0">
                                <div v-for="file in files">
                                    <img :src="file.src" alt="">
                                    <p class="del">
                                        <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                    </p>
                                </div>
                            </div>
                        </FormItem>
                        <FormItem label="文件目录：" class="ivu-form-item-required">
                            <Input v-model="form.directory" placeholder="请输入文件目录"></Input>
                        </FormItem>
                        <FormItem label="学院名（英）：" class="ivu-form-item-required">
                            <Input v-model="form.school_name" placeholder="请输入学院名（英）"></Input>
                        </FormItem>
                        <FormItem label="大学名（英）：" class="ivu-form-item-required">
                            <Input v-model="form.university_name" placeholder="请输入大学名（英）"></Input>
                        </FormItem>
                        <FormItem label="显示名（英）：" class="ivu-form-item-required">
                            <Input v-model="form.display_name" placeholder="请输入显示名（英）"></Input>
                        </FormItem>
                        </Col>
                        <Col span="12" style="margin-top: 80px;">
                        <FormItem label="官网：" class="ivu-form-item-required">
                            <Input v-model="form.website" placeholder="请输入官网"></Input>
                        </FormItem>
                        <FormItem label="关键词：" class="ivu-form-item-required">
                            <Input v-model="form.keyword" placeholder="请输入关键词"></Input>
                        </FormItem>
                        <FormItem label="学院名（中）：" class="ivu-form-item-required">
                            <Input v-model="form.school_name_cn" placeholder="请输入学院名（中）"></Input>
                        </FormItem>
                        <FormItem label="大学名（中）：" class="ivu-form-item-required">
                            <Input v-model="form.university_name_cn" placeholder="请输入大学名（中）"></Input>
                        </FormItem>
                        <FormItem label="显示名（简）：" class="ivu-form-item-required">
                            <Input v-model="form.short" placeholder="请输入显示名（简）"></Input>
                        </FormItem>
                        </Col>
                    </Row>-->

                    <!--<FormItem label="LOGO：" class="ivu-form-item-required">
                        <Input v-model="form.logo_url" placeholder=""></Input>
                    </FormItem>-->
                    <!-- <FormItem label="商学院首页：" class="ivu-form-item-required">
                         <Input v-model="form.website" placeholder="请输入商学院首页URL"></Input>
                     </FormItem>-->
                    <Row>
                        <Col span="12">
                        <FormItem label="校区：" class="ivu-form-item-required">
                            <div class="campus-item" v-for="item,index in campus_list">
                                <Poptip
                                        confirm
                                        transfer
                                        title="您确定删除这条内容吗？"
                                        @on-ok="del_campus(index)"
                                >
                                    <Icon type="trash-a" size="20" @clikk="isEdit = false"></Icon>
                                </Poptip>
                                <div @click="edit_campus(item,index)" style="width: 100%;height: 100px;position: absolute;left: 0;top: 0;padding: 10px;">
                                    <p>{{item.name}}</p>
                                    <p>{{!!item.city ? item.city : item.province}}</p>
                                    <p>{{item.timezone}}</p>
                                </div>
                            </div>
                            <Button type="ghost" icon="plus" class="campus-add" v-if="!isAdd" @click="add_campus"></Button>
                        </FormItem>
                        <div class="campus" v-if="isAdd && !isEdit">
                            <FormItem label="校区名称：">
                                <Input v-model="new_campus.name" placeholder="请输入校区名称"></Input>
                            </FormItem>
                            <FormItem label="区域：" class="ivu-form-item-required">
                                <Input v-model="new_campus.area" placeholder="请输入区域"></Input>
                            </FormItem>
                            <FormItem label="城市：" class="ivu-form-item-required">
                                <!--<Input v-model="form.city" placeholder="请输入城市"></Input>-->
                                <div id="city_check"></div>
                            </FormItem>
                            <FormItem label="时区：" class="ivu-form-item-required">
                                <Input v-model="new_campus.timezone" placeholder="请输入时区"></Input>
                            </FormItem>
                            <FormItem label="详细地址：">
                                <Input v-model="new_campus.address" placeholder="请输入详细地址"></Input>
                            </FormItem>
                            <FormItem label="邮编：">
                                <Input v-model="new_campus.postcode" placeholder="请输入邮编"></Input>
                            </FormItem>
                            <FormItem label="电话：">
                                <Input v-model="new_campus.phone" placeholder="请输入电话"></Input>
                            </FormItem>
                            <FormItem label="照片：">
                                <div class="file-list" v-if="p_files.length > 0">
                                    <div v-for="file in p_files">
                                        <img :src="file.src" alt="">
                                        <p class="del">
                                            <Icon type="trash-a" @click="delImg(file,'p')" size="22">删除</Icon>
                                        </p>
                                    </div>
                                </div>
                                <Upload action="/api/v1/admin/event/upload"
                                        :before-upload="checkImg_p"
                                        :show-upload-list="true"
                                >
                                    <Button type="ghost" icon="plus"></Button>
                                </Upload>
                            </FormItem>
                            <p class="f_err_msg">{{errMsg}}</p>
                            <FormItem>
                                <Button @click="handle_campus" type="primary">添加</Button>
                                <Button @click="cancel_campus">取消</Button>
                            </FormItem>
                        </div>
                        </Col>
                        <Col span="12">
                        <div class="campus" v-if="isEdit">
                            <FormItem label="校区名称：" class="ivu-form-item-required">
                                <Input v-model="e_campus.name"></Input>
                            </FormItem>
                            <FormItem label="区域：" class="ivu-form-item-required">
                                <Input v-model="e_campus.area" placeholder="请输入区域"></Input>
                            </FormItem>
                            <FormItem label="城市：" class="ivu-form-item-required">
                                <!--<Input v-model="form.city" placeholder="请输入城市"></Input>-->
                                <div id="city_check"></div>
                            </FormItem>
                            <FormItem label="时区：" class="ivu-form-item-required">
                                <Input v-model="e_campus.timezone" placeholder="请输入时区"></Input>
                            </FormItem>
                            <FormItem label="详细地址：">
                                <Input v-model="e_campus.address" placeholder="请输入详细地址"></Input>
                            </FormItem>
                            <FormItem label="邮编：">
                                <Input v-model="e_campus.postcode" placeholder="请输入邮编"></Input>
                            </FormItem>
                            <FormItem label="电话：">
                                <Input v-model="e_campus.phone" placeholder="请输入电话"></Input>
                            </FormItem>
                            <FormItem label="照片：">
                                <div class="file-list" v-if="e_files.length > 0">
                                    <div v-for="file in e_files">
                                        <img :src="file.src" alt="">
                                        <p class="del">
                                            <Icon type="trash-a" @click="delImg(file,'e')" size="22">删除</Icon>
                                        </p>
                                    </div>
                                </div>
                                <Upload action="/api/v1/admin/event/upload"
                                        :before-upload="checkImg_e"
                                        :show-upload-list="true"
                                >
                                    <Button type="ghost" icon="plus"></Button>
                                </Upload>
                            </FormItem>
                            <FormItem>
                                <Button @click="update_campus" type="primary">添加</Button>
                                <Button @click="cancel_campus">取消</Button>
                            </FormItem>
                        </div>
                        </Col>
                    </Row>
                    <FormItem label="介绍：">
                        <Input type="textarea" :autosize="{minRows: 5,maxRows: 5}" v-model="form.introduction" placeholder=""></Input>
                    </FormItem>
                    <!--<FormItem label="属地区：" class="ivu-form-item-required">
                        &lt;!&ndash;<Input v-model="form.area" placeholder="请输入所属地区"></Input>&ndash;&gt;
                        <Select v-model="form.area" placeholder="请选择所属地区" style="width: 400px;">
                            <Option value="北美洲">北美洲</Option>
                            <Option value="南美洲">南美洲</Option>
                            <Option value="亚洲">亚洲</Option>
                            <Option value="欧洲">欧洲</Option>
                            <Option value="大洋洲">大洋洲</Option>
                            <Option value="非洲">非洲</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="所属国家：" class="ivu-form-item-required">
                        &lt;!&ndash;<Input v-model="form.country" placeholder="请输入所属国家"></Input>&ndash;&gt;
                        <Select v-model="form.country" filterable placeholder="请选择所属国家" style="width: 400px;">
                            <Option v-for="country in countrys" :value="country.name" :key="country.index">{{ country.name }}</Option>
                        </Select>
                    </FormItem>

                    <div class="majors">
                        <h3 style="border-bottom: 1px solid #efefef;margin-bottom: 10px;padding-left: 90px;">专业</h3>
                        <div class="major" v-for="major,index in majors" style="position: relative">
                            <FormItem label="专业全称（英）：" class="ivu-form-item-required">
                                <Input v-model="major.fullname_english" placeholder="请输入专业全称（英）" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="专业全称（中）：" class="ivu-form-item-required">
                                <Input v-model="major.fullname_chinese" placeholder="请输入专业全称（中）" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="专业简称：" class="ivu-form-item-required">
                                <Input v-model="major.short" placeholder="请输入专业简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="所属分类：" class="ivu-form-item-required">
                                &lt;!&ndash;<Input v-model="major.type" placeholder=""></Input>&ndash;&gt;
                                &lt;!&ndash;<Select v-model="major.major_id" placeholder="请选择所属分类" style="width: 400px;">
                                    <OptionGroup :label="item.g" v-for="item in majorList" :key="item.index">
                                        <Option v-for="opt in item.names" :value="opt.id" :key="opt.index" v-if="!!opt">{{ opt.display_name }}</Option>
                                    </OptionGroup>
                                </Select>&ndash;&gt;
                                <div v-for="item in majorList" :key="item.index" style="position: relative">
                                    <span style="padding-right: 10px;position: absolute;left: 0;top: 0;">{{item.g}}</span>
                                    <RadioGroup v-model="major.major_id" style="padding-left: 50px;" >
                                        <Radio :label="opt.id" v-for="opt in item.names" :key="opt.index" v-if="!!opt">{{opt.display_name}}</Radio>
                                    </RadioGroup>
                                </div>
                            </FormItem>
                            <FormItem label="" v-if="index !== majors.length-1">
                                <div style="width: 100%;height: 1px;background: #efefef;"></div>
                            </FormItem>
                            <Icon type="trash-a" size="24" class="delItem" style="position: absolute;right: 350px;top: 50%;margin-top: -12px;color: #ed3f14;cursor: pointer" @click="delRow(index)" v-if="majors.length > 1"></Icon>
                        </div>
                    </div>
                    <FormItem>
                        <Button type="dashed" icon="plus" class="plusItem" @click="addRow"></Button>
                    </FormItem>-->
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'college_edit',
        components: {
            //canEditTable
        },
        data () {
            return {
                form: {},
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                p_files: [],
                e_files: [],
                I_index: 0,
                campus_list:[],
                new_campus: {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: '',
                    action: 'create'
                },
                e_campus: {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: '',
                    action: 'create'
                },
                d_campus:[],
                isMath: false,
                e_index: -1,
                isAdd: false,
                isEdit: false,
                id: -1,
                universitys: []
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'college_edit'){
                    this.form = {};
                    this.files = [];
                    this.p_files = [];
                    this.e_files = [];
                    this.I_index = 0;
                    this.campus_list = [];
                    this.new_campus = {
                        name: '',
                        area: '',
                        country_id: 0,
                        province_id: 0,
                        city_id: 0,
                        country: '',
                        province: '',
                        city: '',
                        timezone: '',
                        address: '',
                        postcode: '',
                        phone: '',
                        photo: '',
                        action: 'create'
                    };
                    this.e_campus = {
                        name: '',
                        area: '',
                        country_id: 0,
                        province_id: 0,
                        city_id: 0,
                        country: '',
                        province: '',
                        city: '',
                        timezone: '',
                        address: '',
                        postcode: '',
                        phone: '',
                        photo: '',
                        action: 'create'
                    };
                    this.d_campus = [];
                    this.isMath = false;
                    this.e_index = -1;
                    this.isAdd = false;
                    this.isEdit = false;
                    this.id = -1;
                    this.universitys = [];
                    this.$refs['form'].resetFields();
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData(){
                //this.getUniversity();
                //this.getGeo();
               // this.form = this.$store.state.school.currentRow;
                console.log(this.form)
                //this.files[0].src = this.form.logo_url;
                //this.campus_list = this.form.school_area;
                this.id = window.location.href.split('/').pop();
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/school/' + this.id, //'/api/v1/admin/misc/school/' + this.id,
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        console.log(res.data)
                        //_this.majorList = res.data.data;
                        _this.form = res.data.data;
                        if(!!_this.form.logo_url){
                            _this.files[0] = {};
                            _this.files[0].src = _this.form.logo_url;
                        }
                        _this.campus_list = res.data.data.school_area;
                        for(var i=0;i<_this.campus_list.length;i++){
                            _this.campus_list[i].action = 'update';
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            rander_city(str){
                document.getElementsByTagName('form')[0].setAttribute('onkeypress','return event.keyCode != 13;');
                var link = document.createElement("link");
                link.setAttribute("rel", "stylesheet");
                link.setAttribute("type", "test/css");
                link.setAttribute("href", 'https://forum.chasedream.com/static/chasedream/css/citys.css');

                var script = document.createElement("script");
                script.setAttribute("type", "test/javascript");
                script.setAttribute("src", "https://forum.chasedream.com/static/chasedream/js/citys.js");

                var heads = document.getElementsByTagName("head");
                if(heads.length){
                    heads[0].appendChild(link);
                    heads[0].appendChild(script);
                }else{
                    document.documentElement.appendChild(link);
                    document.documentElement.appendChild(script);
                }
                if(!!str && str === 'edit'){
                    var _this = this;
                    setTimeout(function () {
                        var city_id = [];
                        var city = [];
                        var city_text = [];
                        if(!!_this.e_campus.country_id){
                            city_id.push(_this.e_campus.country_id);
                            city.push(_this.e_campus.country)
                            if(_this.e_campus.country !== '中国' && _this.e_campus.country !== '大陆'){
                                city_text.push(_this.e_campus.country);
                            }
                        }
                        if(!!_this.e_campus.province_id){
                            city_id.push(_this.e_campus.province_id);
                            city.push(_this.e_campus.province);
                            city_text.push(_this.e_campus.province);
                        }
                        if(!!_this.e_campus.city_id){
                            city_id.push(_this.e_campus.city_id);
                            city.push(_this.e_campus.city);
                            city_text.push(_this.e_campus.city);
                        }
                        var city_control = document.getElementById('city_ipt');
                        city_control.setAttribute('data',city_id.join('-'));
                        city_control.setAttribute('data_name',city.join('-'));
                        city_control.innerHTML = city_text.join('-');
                        city_control.nextElementSibling.style.display = 'none';
                    },600)
                }
            },
            getUniversity(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/university',
                    method:'GET',
                    params: {
                        /*page: _this.s_current,
                         page_size: _this.page_size*/
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.universitys = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            add_campus(){
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: '',
                    action: 'create'
                }
                this.e_campus = {};
                this.isAdd = true;
                this.isEdit = false;
                this.rander_city();
            },
            cancel_campus(){
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: '',
                    action: 'create'
                }
                this.e_campus = {};
                this.e_index = -1;
                this.isAdd = false;
                this.isEdit = false;
            },
            handle_campus(){
                /*if(!this.new_campus.name){
                    this.$Notice.error({
                        title: '请填写校区名称！'
                    });
                    return false;
                }*/
                if(!this.new_campus.area){
                    this.$Notice.error({
                        title: '请填写区域！'
                    });
                    return false;
                }
                var city_info = document.getElementById('city_ipt').getAttribute('data').split('-');
                if(city_info.length === 0){//this.new_campus.city
                    this.$Notice.error({
                        title: '请选择城市！'
                    });
                    return false;
                }else {
                    var city_text = document.getElementById('city_ipt').getAttribute('data_name').split('-');
                    this.new_campus.country_id = city_info[0];
                    this.new_campus.country = city_text[0];
                    if(city_info.length === 3){
                        this.new_campus.province_id = city_info[1];
                        this.new_campus.city_id = city_info[2];
                        this.new_campus.province = city_text[1];
                        this.new_campus.city = city_text[2];
                    }else if(city_info.length === 2){
                        this.new_campus.city_id = city_info[1];
                        this.new_campus.city = city_text[1];
                    }
                }
                if(!this.new_campus.timezone){
                    this.$Notice.error({
                        title: '请填写时区！'
                    });
                    return false;
                }
                /*if(!this.new_campus.address){
                    this.$Notice.error({
                        title: '请填写详细地址！'
                    });
                    return false;
                }*/
                /*if(!this.new_campus.postcode){
                    this.$Notice.error({
                        title: '请填写邮编！'
                    });
                    return false;
                }*/
                /*if(!this.new_campus.phone){
                    this.$Notice.error({
                        title: '请填写电话！'
                    });
                    return false;
                }*/
                /*if(!this.new_campus.photo){
                    this.$Notice.error({
                        title: '请上传照片！'
                    });
                    return false;
                }*/

                this.campus_list.push(this.new_campus);
                this.cancel_campus();
            },
            edit_campus(item,index){
                this.e_campus = {};
                this.e_index = index;
                this.e_campus = {
                    name: item.name,
                    area: item.area,
                    country_id: item.country_id,
                    province_id: item.province_id,
                    city_id: item.city_id,
                    country: item.country,
                    province: item.province,
                    city: item.city,
                    timezone: item.timezone,
                    address: item.address,
                    postcode: item.postcode,
                    phone: item.phone,
                    photo: item.photo,
                    id: item.id,
                    action: item.action
                };
                if(!!this.e_campus.photo){
                    this.e_files=[{
                        src: this.e_campus.photo
                    }];
                }else {
                    this.e_files=[];
                }

                this.isEdit = true;
                this.isAdd = false;
                this.rander_city('edit');

            },
            update_campus(){
                /*if(!this.e_campus.name){
                    this.$Notice.error({
                        title: '请填写校区名称！'
                    });
                    return false;
                }*/
                if(!this.e_campus.area){
                    this.$Notice.error({
                        title: '请填写区域！'
                    });
                    return false;
                }
                var city_info = document.getElementById('city_ipt').getAttribute('data').split('-');
                if(city_info.length === 0){//this.new_campus.city
                    this.$Notice.error({
                        title: '请选择城市！'
                    });
                    return false;
                }else {
                    var city_text = document.getElementById('city_ipt').getAttribute('data_name').split('-');
                    this.e_campus.country_id = city_info[0];
                    this.e_campus.country = city_text[0];
                    if(city_info.length === 3){
                        this.e_campus.province_id = city_info[1];
                        this.e_campus.city_id = city_info[2];
                        this.e_campus.province = city_text[1];
                        this.e_campus.city = city_text[2];
                    }else if(city_info.length === 2){
                        this.e_campus.city_id = city_info[1];
                        this.e_campus.city = city_text[1];
                    }
                }
                if(!this.e_campus.timezone){
                    this.$Notice.error({
                        title: '请填写时区！'
                    });
                    return false;
                }
                /*if(!this.e_campus.address){
                    this.$Notice.error({
                        title: '请填写详细地址！'
                    });
                    return false;
                }*/
                /*if(!this.e_campus.postcode){
                    this.$Notice.error({
                        title: '请填写邮编！'
                    });
                    return false;
                }*/
                /*if(!this.e_campus.phone){
                    this.$Notice.error({
                        title: '请填写电话！'
                    });
                    return false;
                }*/
                /*if(!this.e_campus.photo){
                    this.$Notice.error({
                        title: '请上传照片！'
                    });
                    return false;
                }*/

                //this.campus_list.push(this.e_campus);
                console.log(this.e_index)
                this.campus_list[this.e_index] = this.e_campus;

                this.cancel_campus();
            },
            del_campus(index){
                if(this.campus_list[index].id > 0){
                    this.d_campus.push(this.campus_list[index])
                }
                this.campus_list.splice(index, 1);
            },
            addRow (){  // 添加一行活地点
                var item = {
                    english_name: '',
                    display_name: '',
                    field: '',
                    type: 0
                }
                this.majors.push(item);
            },
            delRow (i){  // 删除一行活动地点
                if(i === 0){
                    this.majors.splice(0, 1);
                }else {
                    this.majors.splice(i, 1);
                }
            },
            getGeo (){  //  获取国家列表
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.countrys = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            /*checkImg (file){
                if(!this.form.directory){
                    this.$Notice.error({
                        title: '请先填写文件目录'
                    });
                    return false;
                }
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                //var isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (img.width != img.height || img.width != Math_Width) {
                            _this.$Notice.error({
                                title: '请上传1024*1024的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            _this.uploadImg(file);
                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },*/
            checkImg_p(file){
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            _this.uploadImg(file, 'p');
                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            checkImg_e(file){
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            _this.uploadImg(file, 'e');
                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            checkImg (file,str){
                if(!str && !this.form.directory){
                    this.$Notice.error({
                        title: '请先填写文件目录'
                    });
                    return false;
                }
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                _this.isMath = false;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (!str && (img.width != img.height || img.width != Math_Width)) {
                            _this.$Notice.error({
                                title: '请上传1024*1024的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            if(!!str && str === 'p'){
                                _this.uploadImg(file, str);
                            }else if(!!str && str === 'e') {
                                _this.uploadImg(file, str);
                            }else {
                                _this.uploadImg(file);
                            }

                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            uploadImg (file,str){
                var form = new FormData();
                form.append('file', file);
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school/logo?directory=' + this.form.directory,
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        if(!!str && str === 'p'){
                            console.log(1)
                            _this.new_campus.photo = res.data.data.fullpath;
                            _this.p_files.push({
                                id: res.data.data.aid,
                                src: res.data.data.fullpath
                            })
                            _this.errMsg = '';
                            return;
                        }if(!!str && str === 'e'){
                            _this.e_campus.photo = res.data.data.fullpath;
                            _this.e_files.push({
                                id: res.data.data.aid,
                                src: res.data.data.fullpath
                            })
                            _this.errMsg = '';
                            return;
                        }else {
                            console.log(2)
                            _this.form.logo_url = res.data.data.fullpath;
                            _this.files.push({
                                id: res.data.data.aid,
                                src: res.data.data.fullpath
                            })
                            _this.errMsg = '';
                        }
                    }else{
                        // console.log(res)
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            handleSubmit () {
                var dataIn = {
                    directory: this.form.directory,
                    university_id: this.form.university_id,
                    university_name: this.form.university_name,
                    university_name_cn: this.form.university_name_cn,
                    school_name: this.form.school_name,
                    school_name_cn: this.form.school_name_cn,
                    university_id: this.form.university_id,
                    display_name: this.form.display_name,
                    short: this.form.short,
                    logo_url: this.form.logo_url,
                    keyword: this.form.keyword,
                    website: this.form.website,
                    //area: '',//this.form.area,
                    //country: this.form.country,
                    //majors: this.majors
                    introduction: this.form.introduction,
                    school_area: [],//this.campus_list
                    id: this.form.id,
                };
                //console.log(dataIn);

                if(!dataIn.directory){
                    this.$Notice.error({
                        title: '请填写文件目录！'
                    });
                    return false;
                }
                if(!dataIn.school_name && !dataIn.school_name_cn){
                    this.$Notice.error({
                        title: '学院名称（英）和学院名称（中）至少填写一个！'
                    });
                    return false;
                }
                if(!dataIn.university_name && !dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '大学名称（英）和大学名称（中）至少填写一个！'
                    });
                    return false;
                }
                /*if(!dataIn.school_name_cn){
                    this.$Notice.error({
                        title: '请填写学院名称（中）！'
                    });
                    return false;
                }*/
                /*if(dataIn.university_id < 1){
                    this.$Notice.error({
                        title: '请选择大学！'
                    });
                    return false;
                }*/
                /*if(!dataIn.university_name){
                    this.$Notice.error({
                        title: '请填写大学名称（英）！'
                    });
                    return false;
                }
                if(!dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '请填写大学名称（中）！'
                    });
                    return false;
                }
                if(!dataIn.display_name){
                    this.$Notice.error({
                        title: '请填写显示全称！'
                    });
                    return false;
                }
                if(!dataIn.short){
                    this.$Notice.error({
                        title: '请填写显示简称！'
                    });
                    return false;
                }
                if(!dataIn.logo_url){
                    this.$Notice.error({
                        title: '请上传学校LOGO！'
                    });
                    return false;
                }
                if(!dataIn.keyword){
                    this.$Notice.error({
                        title: '请填写关键词！'
                    });
                    return false;
                }
                if(!dataIn.website){
                    this.$Notice.error({
                        title: '请填写商学院首页！'
                    });
                    return false;
                }*/
                /*if(!dataIn.area){
                    this.$Notice.error({
                        title: '请填写所属地区！'
                    });
                    return false;
                }
                if(!dataIn.country){
                    this.$Notice.error({
                        title: '请填写所属国家！'
                    });
                    return false;
                }*/
                /*if(!dataIn.introduction){
                    this.$Notice.error({
                        title: '请填写学校介绍！'
                    });
                    return false;
                }*/
                for(var i=0;i<this.campus_list.length;i++){
                    var item = {
                        name: this.campus_list[i].name,
                        area: this.campus_list[i].area,
                        country_id: this.campus_list[i].country_id,
                        province_id: this.campus_list[i].province_id,
                        city_id: this.campus_list[i].city_id,
                        country: this.campus_list[i].country,
                        province: this.campus_list[i].province,
                        city: this.campus_list[i].city,
                        timezone: this.campus_list[i].timezone,
                        address: this.campus_list[i].address,
                        postcode: this.campus_list[i].postcode,
                        phone: this.campus_list[i].phone,
                        photo: this.campus_list[i].photo,
                        google_map: '',
                        baidu_map: '',
                        id: this.campus_list[i].id > 0 ? this.campus_list[i].id : null,
                        school_id: this.campus_list[i].school_id,
                        action: this.campus_list[i].action
                    }
                    dataIn.school_area.push(item);
                }
                for(var i=0;i<this.d_campus.length;i++){
                    var item = {
                        name: this.d_campus[i].name,
                        area: this.d_campus[i].area,
                        country_id: this.d_campus[i].country_id,
                        province_id: this.d_campus[i].province_id,
                        city_id: this.d_campus[i].city_id,
                        country: this.d_campus[i].country,
                        province: this.d_campus[i].province,
                        city: this.d_campus[i].city,
                        timezone: this.d_campus[i].timezone,
                        address: this.d_campus[i].address,
                        postcode: this.d_campus[i].postcode,
                        phone: this.d_campus[i].phone,
                        photo: this.d_campus[i].photo,
                        google_map: '',
                        baidu_map: '',
                        id: this.d_campus[i].id,
                        school_id: this.d_campus[i].school_id,
                        action: 'delete'
                    }
                    dataIn.school_area.push(item);
                }

                console.log(dataIn);
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/misc/school',//'/api/v1/admin/event/school',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                       // console.log(res.data)
                        _this.$Notice.success({
                            title: '提交成功！！'
                        });
                        _this.$store.commit('removeTag', 'college_edit');
                        _this.$store.commit('closePage', 'college_edit');
                        _this.$router.push({
                            name: 'college_list'
                        });
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {};
                this.files = [];
                this.p_files = [];
                this.e_files = [];
                this.I_index = 0;
                this.campus_list = [];
                this.new_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: ''
                };
                this.e_campus = {
                    name: '',
                    area: '',
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    country: '',
                    province: '',
                    city: '',
                    timezone: '',
                    address: '',
                    postcode: '',
                    phone: '',
                    photo: ''
                };
                this.d_campus = [];
                this.isMath = false;
                this.e_index = -1;
                this.isAdd = false;
                this.isEdit = false;
                this.id = -1;
                this.universitys = [];
                this.$store.commit('removeTag', 'college_edit');
                this.$router.push({
                    name: 'college_list'
                });
            }
        },
        created () {

        }
    };
</script>