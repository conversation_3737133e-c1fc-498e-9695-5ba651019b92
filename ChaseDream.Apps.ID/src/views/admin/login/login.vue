<style lang="less">
    @import './login.less';
    @import '../../../libs/jigsaw.css';
</style>

<template>
    <div class="login" @keydown.enter="handleSubmit" :style="{backgroundImage: bgImg}">
        <!-- <div class="login-con">
            <Card :bordered="false">
                <p slot="title">
                    <Icon type="log-in"></Icon>
                    欢迎登录
                </p>
                <div class="form-con">
                    <Form ref="loginForm" :model="form" :rules="rules">
                        <FormItem prop="userName">
                            <Input v-model="form.userName" placeholder="请输入用户名">
                                <span slot="prepend">
                                    <Icon :size="16" type="person"></Icon>
                                </span>
                            </Input>
                        </FormItem>
                        <FormItem prop="password">
                            <Input type="password" v-model="form.password" placeholder="请输入密码">
                                <span slot="prepend">
                                    <Icon :size="14" type="locked"></Icon>
                                </span>
                            </Input>
                        </FormItem>
                        <FormItem>
                            <div class="cH">
                                <div id="captcha-box"></div>
                                <div v-if="addYz" class="addYz">正在加载验证码....</div>
                            </div>
                        </FormItem>
                        <p class="l_err_msg">{{errMsg}}</p>
                        <FormItem>
                            <Button @click="handleSubmit" type="primary" long>登录</Button>
                        </FormItem>
                    </Form>
                </div>
            </Card>
        </div> -->
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';
import jigsaws from '@/libs/jigsaw.js';
import initGeetest from '@/libs/gt.js';
export default {
    name: 'console',
    data () {
        return {
            // form: {
            //     userName: '',
            //     password: '',
            //     validate_code: ''
            // },
            // rules: {
            //     userName: [
            //         { required: true, message: '账号不能为空', trigger: 'blur'}
            //     ],
            //     password: [
            //         { required: true, message: '密码不能为空', trigger: 'blur' },
            //         { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur'}
            //     ]
            // },
            // access_token: this.$store.state.user.access_token,
            // bgImg: '',
            // errMsg: '',
            // codeImg: '',
            // isValidate: false,
            // addYz: true,
            // geetest_challenge: '',
            // geetest_validate: '',
            // geetest_seccode: '',
            // islogIn: false

        };
    },
    mounted () {
        // var _this = this;
        // util.ajax({
        //     url:'https://bird.ioliu.cn/v1?url=http://www.bing.com/HPImageArchive.aspx?format=js&idx=0&n=1',
        //     method:'get',
        //     headers: {
        //         'Content-type': 'application/json; charset=UTF-8',
        //     },
        // }).then(function (res) {
        //     _this.bgImg= 'url(https://www.bing.com/' + res.data.images[0].url + ')';
        // }).catch(function (err) {
        //     console.log(err)
        // });
        window.location = window.location
    },
    // methods: {
    //     getLoinInfo (){
    //         var _this = this;
    //         util.ajax({
    //             url:'/api/v1/auth/check_login',
    //             method:'get'
    //         }).then(function (res) {
    //             console.log(res)
    //             if(res.data.msg === 'success'){
    //                 if(res.data.data.uid > 0){
    //                     Cookies.set('user', res.data.data.username);
    //                     Cookies.set('uAccess',0);

    //                     _this.$store.commit('setMenuRspList',false);
    //                     _this.$store.commit('getMenuData',_this.$router);
    //                     var timer;
    //                     clearInterval(timer);
    //                     timer = setInterval(function () {
    //                         if(!!_this.$store.state.app.ismenuLoad){
    //                             window.location.href = window.location.href;
    //                             clearInterval(timer);
    //                         }
    //                     },300);
    //                 }else {
    //                     _this.$router.push({
    //                         name: 'error-404'
    //                     });
    //                 }
    //             }else{
    //                 _this.$router.push({
    //                     name: 'error-404'
    //                 });
    //             }
    //         }).catch(function (err) {
    //             _this.errMsg = err.message;
    //         });
    //     },
    //     geetest(e) {
    //         this.addYz = true;
    //         let _this = this;
    //         this.isValidate = false;
    //         _this.geetest_challenge='';
    //         _this.geetest_validate='';
    //         _this.geetest_seccode='';
    //         util.ajax({
    //             url: "api/v1/auth/geetest/register?t=" + (new Date()).getTime(), // 加随机数防止缓存
    //             method: "get",
    //             dataType: "json"
    //         }).then (function (data) {
    //             //this.xwyz = data;
    //             let removeObj = document.getElementById('captcha-box');    // 获取dom元素
    //             if(removeObj!='underfined'){
    //                 removeObj.innerHTML = ''       // 因为个人当中直接在页面中切换短信或者密码验证，先移除一个，不然会一直 添加
    //             }
    //             // 调用 initGeetest 进行初始化
    //             // 参数1：配置参数
    //             // 参数2：回调，回调的第一个参数验证码对象，之后可以使用它调用相应的接口
    //             initGeetest({
    //                 // 以下 4 个配置参数为必须，不能缺少
    //                 gt: data.data.data.gt,
    //                 challenge: data.data.data.challenge,
    //                 offline: !data.data.data.success, // 表示用户后台检测极验服务器是否宕机
    //                 new_captcha: data.data.data.new_captcha, // 用于宕机时表示是新验证码的宕机
    //                 product: "float", // 产品形式，包括：float，popup
    //                 width: "300px"
    //                 // 更多配置参数说明请参见：http://docs.geetest.com/install/client/web-front/
    //             },function (captchaObj) {
    //                 captchaObj.reset()    // 在切换登录的时候重置，不需要切换的忽略
    //                 captchaObj.appendTo('#captcha-box');     // 插入验证
    //                 _this.addYz = false
    //                 captchaObj.onSuccess(()=>{     // 第一次验证成功，极验自带的回调
    //                     _this.isValidate = true;
    //                     _this.errMsg = '';
    //                     let result = captchaObj.getValidate();
    //                     _this.dis = false;
    //                     _this.geetest_challenge=result.geetest_challenge;     //将第一次验证获取的三个参数保存起来，方便二次验证
    //                     _this.geetest_validate=result.geetest_validate;
    //                     _this.geetest_seccode=result.geetest_seccode;
    //                 })
    //             });
    //         });
    //     },
    //     handleSubmit () {
    //         this.$refs.loginForm.validate((valid) => {
    //             if (valid) {
    //                 if(!this.isValidate){
    //                     this.errMsg = '请点击上面按钮进行验证！';
    //                     return
    //                 }
    //                 var _this = this;
    //                 util.ajax({
    //                     url:'/api/v1/auth/login',
    //                     method:'post',
    //                     data: {
    //                         username: _this.form.userName,
    //                         password: _this.form.password,
    //                         geetest_challenge: _this.geetest_challenge,
    //                         geetest_validate: _this.geetest_validate,
    //                         geetest_seccode: _this.geetest_seccode
    //                     },
    //                 }).then(function (res) {
    //                     if(res.data.msg === 'success'){
    //                         Cookies.set('access_token', res.data.data.access_token);
    //                         Cookies.set('user', _this.form.userName);
    //                         Cookies.set('uAccess',0);
    //                         _this.$store.commit('setAvator', 'https://ss1.bdstatic.com/70cFvXSh_Q1YnxGkpoWK1HF6hhy/it/u=3448484253,3685836170&fm=27&gp=0.jpg');
    //                         _this.$store.commit('setMenuRspList',false);
    //                         _this.$store.commit('getMenuData',_this.$router);
    //                         var timer;
    //                         clearInterval(timer);
    //                         timer = setInterval(function () {
    //                             if(!!_this.$store.state.app.ismenuLoad){
    //                                 window.location.href = window.location.href;
    //                                 clearInterval(timer);
    //                             }
    //                         },300);
    //                     }else{
    //                         _this.errMsg = res.msg;
    //                     }
    //                 }).catch(function (err) {
    //                     console.log(err)
    //                     _this.errMsg = err.msg;
    //                 });
    //             }else {
    //                 this.isValidate = false;
    //                 document.getElementsByClassName('refreshIcon')[0].click();
    //                 document.getElementsByClassName('ivu-poptip')[0].style.display = 'inline-block';
    //             }
    //         });
    //     }
    // }
};
</script>

<style>
    .cH{
        min-height: 44px;   /*//设置一个最小高度，这样就不会出现加载将下面东西顶下来*/
        line-height: 44px;
    }
    #captcha-box .geetest_holder.geetest_wind{
        width: 100% !important
    }

</style>
