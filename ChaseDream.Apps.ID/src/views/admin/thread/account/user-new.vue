<style lang="less">
    @import '../../../../styles/common.less';
    @import '../thread.less';
</style>

<template>
    <div class="user-new">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加发帖帐号
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="90">
                    <FormItem prop="nickname" label="昵称：">
                        <Input v-model="userform.nickname" placeholder="请输入昵称"></Input>
                    </FormItem>
                    <FormItem prop="userName" label="账号：">
                        <Input v-model="userform.userName" placeholder="请输入账号"></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'user_new',
        components: {
            // canEditTable
        },
        data () {
            return {
                userform: {
                    nickname: '',
                    userName: '',
                    password: '',
                    //role: '0'
                },
                rules: {
                    nickname: [
                        { required: true, message: '昵称不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '昵称不能小于3个字符', trigger: 'blur' }
                    ],
                    userName: [
                        { required: true, message: '账号不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '密码不能为空', trigger: 'blur' },
                        { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                    ]
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'user_new'){
                    this.userform.nickname = '';
                    this.userform.userName = '';
                    this.userform.password = '';

                    this.$refs['userForm'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.userForm.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/thread/user',
                            method:'POST',
                            data: {
                                nickname: _this.userform.nickname,
                                username: _this.userform.userName,
                                password: _this.userform.password,
                                //role: _this.userform.role
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'user_new');
                                _this.$store.commit('closePage', 'user_new');
                                _this.$router.push({
                                    name: 'users'
                                });
                            }else{
                                //_this.errMsg = res.msg;
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            //_this.errMsg = err.msg;
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.userform.nickname = '';
                this.userform.userName = '';
                this.userform.password = '';
                //this.userform.role = '';
                this.$store.commit('removeTag', 'user_new');
                this.$router.push({
                    name: 'users'
                });
            }
        }
    };
</script>

<style>

</style>
