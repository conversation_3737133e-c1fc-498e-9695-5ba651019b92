<style lang="less">
@import "../../../../styles/common.less";
@import "../thread.less";
</style>

<template>
  <div class="thread-list">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <div class="tool-bar" v-if="role === 1" style="margin-bottom: 20px;">
            <Button type="success" icon="android-add" @click="pidModal = true"
              >添 加</Button
            >
            <Modal
              v-model="pidModal"
              title="请输入pid"
              @on-ok="addPid"
              @on-cancel="cancel"
            >
              <Input
                v-model="pid"
                placeholder="请输入pid"
                :autofocus="true"
                style="width: 100%"
              ></Input>
            </Modal>
          </div>
          <div class="edittable-con-1">
            <can-edit-table
              refs="plTable"
              @on-delete="handleDel"
              @on-updata="handleUpdata"
              v-model="plRows"
              :columns-list="plColumns"
            ></can-edit-table>
            <div class="page-bar">
              <Page
                :total="pl_total"
                :page-size="page_size"
                :current="pl_current"
                @on-change="getPidlist"
                v-if="pl_showPage"
              ></Page>
            </div>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import tData from "../data/thread_data.js";
import canEditTable from "./components/canEditTable.vue";

export default {
  name: "pid_list",
  components: {
    canEditTable,
  },
  data() {
    return {
      plRows: [],
      tlColumns: [],
      pl_total: 0,
      pl_showPage: false,
      page_size: 10,
      pl_current: 1,
      isAccess: true,
      pidModal: false,
      pid: "",
      role: 0,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "pid_list") {
        //this.$store.commit('removeTag', 'thread_update');
        //this.$store.commit('closePage', 'thread_update');
        this.getData();
      }
    },
  },
  mounted() {
    //this.getData();
  },
  methods: {
    getData() {
      //this.$store.commit('removeTag', 'thread_update');
      this.plColumns = tData.plColumns;
      this.getPidlist(1);
      this.getMy_permission();
    },
    getPidlist(n) {
      this.plRows = [];
      this.pl_total = 0;
      this.pl_current = n;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/pid",
          method: "GET",
          params: {
            page: _this.pl_current,
            page_size: _this.page_size,
            //stick: 0
          },
        })
        .then(function(res) {
          // console.log(res)
          if (res.data.msg === "success") {
            _this.plRows = res.data.data.rows;
            for (var i = 0; i < _this.plRows.length; i++) {
              _this.plRows[i].initHl = _this.plRows[i].highlight;
            }
            _this.pl_total = res.data.data.count;
            if (_this.pl_total > _this.page_size) {
              _this.pl_showPage = true;
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
    },
    handleUpdata() {
      this.getData();
    },
    addPid() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/pid",
          method: "POST",
          data: {
            pid: this.pid, //23517389
          },
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            _this.pidModal = false;
            _this.$Notice.success({
              title: "添加成功！",
            });
            _this.pid = "";
            _this.getPidlist(1);
          } else {
            console.log(res);
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    cancel() {
      this.pid = "";
      this.pidModal = false;
    },
    getMy_permission() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/permission/my_permission",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            if (res.data.data.role.includes(1)) {
              _this.role = 1;
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
  },
  created() {
    this.getData();

    //   获取用户手机号
    /*var getMobile = document.createElement("script");
            getMobile.id = "getMobile_script";
            getMobile.type = "test/javascript";
            getMobile.src = "https://id.chasedream.com/api/v1/auth/user_mobile/" + 1231014 +"?callback=mobileCallback";
            document.getElementsByTagName("head")[0].appendChild(getMobile);
            function mobileCallback(data) {
                if (data.msg == "success") {
                    $('mobile_num').innerHTML ='<em>Mobile</em> +' +  data.data.area_code + ' ' + data.data.mobile;
                } else {
                    console.log(data)
                }
                document.head.removeChild(document.getElementById("getMobile_script"));
            }*/
  },
};
</script>

<style></style>
