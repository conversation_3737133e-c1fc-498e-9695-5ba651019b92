<style lang="less">
    @import '../../../../styles/common.less';
    @import '../thread.less';
</style>

<template>
    <div class="thread-update">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="ios-compose-outline"></Icon>
                    编辑帖子
                </p>
                <Form ref="threadform" :model="threadform" :rules="rules" :label-width="90">
                    <FormItem prop="subject" label="帖子标题：">
                        <Input v-model="threadform.subject" placeholder="请输入帖子标题"></Input>
                    </FormItem>
                    <FormItem prop="content" label="发帖内容：">
                        <Input type="textarea" v-model="threadform.content" :rows="6" placeholder="请输入帖子内容"></Input>
                    </FormItem>
                    <FormItem prop="htmlon" label="">
                        HTML：
                        <RadioGroup v-model="threadform.htmlon">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="fid" label="帖子版块：">
                        <Select v-model="threadform.fid" style="width:200px" @on-change="getType" :label-in-value="true">
                            <!--<Option v-for="item in forum_nav" :value="item.fid" :key="item.index">{{ item.name }}</Option>-->
                            <OptionGroup :label="item.name" v-for="item in forum_nav" :key="item.index">
                                <Option v-for="opt in item.forums" :value="opt.fid" :key="opt.index">{{ opt.name }}</Option>
                            </OptionGroup>
                        </Select>
                        <Select v-model="threadform.typeid" style="width:200px">
                            <Option v-for="item in types" :value="item.typeid" :key="item.index">{{ item.name }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem prop="schedule" label="定时发送：">
                        <Input v-model="threadform.schedule" placeholder="请输入定时发送时间 格式：*************" style="width:400px"></Input>
                    </FormItem>
                    <FormItem prop="uid" label="发帖账号：">
                        <RadioGroup v-model="threadform.uid">
                            <Radio :label="user.id" v-for="user in users" disabled :key="user.index">{{user.nickname}}</Radio>
                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'pid_update',
        components: {
        },
        data () {
            return {
                threadform: {
                    subject: '',
                    content: '',
                    uid: 0,
                    fid: '',
                    forum: '',
                    typeid: '',  //fid 子级的 fid
                    htmlon: 1,   // 0或1，内容是否是html代码
                    schedule: ''
                },
                rules: {
                    subject: [
                        { required: true, message: '标题不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '标题不能小于3个字符', trigger: 'blur' }
                    ],
                    content: [
                        { required: true, message: '内容不能为空', trigger: 'blur' },
                        { type: 'string', min: 6, message: '内容不能小于6个字符', trigger: 'blur' }
                    ],
                    fid: [
                        { required: true, message: '请选择发帖账号', trigger: 'blur' },
                    ],
                    uid: [
                        { required: true, message: '请选择发帖账号', trigger: 'blur', type: 'number'}

                    ]
                    /*,
                    typeid: [
                        { required: true, message: '请选择发帖账号', trigger: 'blur' },
                    ]*/


                },
                users: [],
                forum_nav: [],
                types: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'pid_update'){
                    this.$refs['threadform'].resetFields();
                    this.getData();
                }
            }
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData (){
                this.threadform.subject = '';
                this.threadform.content = '';
                this.threadform.uid = 0;
                this.threadform.fid = '';
                this.threadform.forum = '';
                this.threadform.typeid = '';
                this.threadform.htmlon = 1;
                this.threadform.schedule = '';
                this.users = [];
                this.forum_nav = [];
                this.types = [];

                this.threadform = this.$store.state.thread.currentRow;

                if(!this.threadform.id){
                    this.$router.push({
                        name: 'pid_list'
                    });
                }
                this.threadform.fid = this.threadform.fid + '';
                //this.threadform.typeid = this.threadform.typeid + '';
                this.getUsers();
                this.getForum_nav();

            },
            handleSubmit () {
                this.$refs.threadform.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/thread',
                            method:'PUT',
                            data: {
                                subject: _this.threadform.subject,
                                content: _this.threadform.content,
                                uid: _this.threadform.uid,
                                fid: _this.threadform.fid,
                                forum: _this.threadform.forum,
                                typeid:  _this.threadform.typeid,
                                htmlon: _this.threadform.htmlon,
                                tid: _this.threadform.tid,
                                id: _this.threadform.id,
                                pid: _this.threadform.pid,
                                schedule: _this.threadform.schedule,
                                status: _this.threadform.status,
                                ispid: 1
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'thread_update');
                                _this.$store.commit('closePage', 'thread_update');
                                _this.$router.push({
                                    name: 'pid_list'
                                });
                            }else{
                                _this.errMsg = res.msg;
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.errMsg = err.msg;
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            getUsers (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/user',
                    method:'GET',
                    params: {
                        page: _this.current,
                        pageSize: _this.pageSize
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.users = res.data.data.rows;
                        if(!_this.threadform.uid){
                            for(var i=0;i<_this.users.length;i++){
                                if(_this.users[i].username === _this.threadform.send_username){
                                    _this.threadform.uid = _this.users[i].id;
                                }
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getForum_nav (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/forum_nav',
                    method:'GET',
                    params: {
                        page: _this.current,
                        pageSize: _this.pageSize
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.forum_nav = res.data.data;
                        setTimeout(function () {
                            _this.getType({value: _this.threadform.fid, label:_this.threadform.forum});
                        },300);

                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err.msg)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getType (e){
                this.threadform.forum = e.label;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/forum_nav_sub',
                    method:'GET',
                    params: {
                        fid: e.value
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.types = res.data.data;
                        _this.threadform.typeid = _this.threadform.typeid + '';
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {

                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            closePage () {
                this.threadform.subject = '';
                this.threadform.content = '';
                this.threadform.uid = 0;
                this.threadform.fid = '';
                this.threadform.forum = '';
                this.threadform.typeid = 0;
                this.threadform.htmlon = 1;
                this.threadform.schedule = '';
                this.users = [];
                this.forum_nav = [];
                this.types = [];
                this.$store.commit('removeTag', 'pid_update');
                this.$store.commit('closePage', 'pid_update');
                this.$router.push({
                    name: 'pid_list'
                });
            }
        },
        created () {
            //this.getUsers();
            //this.getForum_nav();
            //this.getData();
        }
    };
</script>