.dragging-tip-enter-active{
    opacity: 1;
    transition: opacity .3s;
}
.dragging-tip-enter, .dragging-tip-leave-to{
    opacity: 0;
    transition: opacity .3s
}
.dragging-tip-con{
    display: block;
    text-align: center;
    width: 100%;
    height: 50px;
}
.dragging-tip-con span{
    font-size: 18px;
}
.record-tip-con{
    display: block;
    width: 100%;
    height: 292px;
    overflow: auto;
}
.record-item{
    box-sizing: content-box;
    display: block;
    overflow: hidden;
    height: 24px;
    line-height: 24px;
    padding: 8px 10px;
    border-bottom: 1px dashed gainsboro;
}
.record-tip-con span{
    font-size: 14px;
}
.edittable-test-con{
    height: 160px;
}
.edittable-table-height-con{
    height: 190px;
}
.edittable-con-1{
    box-sizing: content-box;
    padding: 15px 0 0;
    height: 196px;
}
.edittable-table-get-currentdata-con{
    height: 190px !important;
}
.exportable-table-download-con1{
    padding: 16px 0 16px 20px;
    border-bottom: 1px dashed #c3c3c3;
    margin-bottom: 16px;
}
.exportable-table-download-con2{
    padding-left: 20px;
}
.show-image{
    padding: 20px 0px;
}
.show-image img{
    display: block;
    width: 100%;
    height: auto;
}
.searchable-table{
    &-con1{
        height: 230px !important;
    }
}