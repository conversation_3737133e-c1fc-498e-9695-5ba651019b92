<style lang="less">
    @import '../../../../styles/common.less';
    @import '../thread.less';
</style>

<template>
    <div class="thread-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/thread/thread-post">
                        <Button type="success" icon="ios-paperplane">发 帖</Button>
                    </router-link>
                </div>
                <div class="edittable-con-1">
                    <Tabs value="name1">
                        <TabPane label="置顶列表" name="name1">
                            <can-edit-table refs="dlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="dlRows" :columns-list="tlColumns"></can-edit-table>
                            <div class="page-bar">
                                <Page :total="dl_total" :page-size="page_size" :current="dl_current" @on-change="getdigestlist" v-if="dl_showPage"></Page>
                            </div>
                        </TabPane>
                        <TabPane label="文章列表" name="name2">
                            <can-edit-table refs="tlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="tlRows" :columns-list="tlColumns"></can-edit-table>
                            <div class="page-bar">
                                <Page :total="tl_total" :page-size="page_size" :current="tl_current" @on-change="getthreadlist" v-if="tl_showPage"></Page>
                            </div>
                        </TabPane>
                    </Tabs>
                    <!--<div>
                        <h3 style="margin-bottom: 10px;">置顶列表</h3>
                        <can-edit-table refs="dlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="dlRows" :columns-list="tlColumns"></can-edit-table>
                        <div class="page-bar">
                            <Page :total="tl_total" :page-size="page_size" :current="dl_current" @on-change="getdigestlist" v-if="dl_showPage"></Page>
                        </div>
                    </div>
                    <br>
                    <div>
                        <h3 style="margin-bottom: 10px;">文章列表</h3>
                        <can-edit-table refs="tlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="tlRows" :columns-list="tlColumns"></can-edit-table>
                        <div class="page-bar">
                            <Page :total="total" :page-size="page_size" :current="tl_current" @on-change="getthreadlist" v-if="tl_showPage"></Page>
                        </div>
                    </div>-->
                </div>

            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/thread_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'thread_list',
        components: {
            canEditTable
        },
        data () {
            return {
                tlRows: [],
                dlRows: [],
                tlColumns: [],
                tl_total: 0,
                dl_total: 0,
                tl_showPage: false,
                dl_showPage: false,
                page_size: 10,
                tl_current: 1,
                dl_current: 1,
                isAccess: true,
                keyword: '',
                s_key: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'thread_list'){
                    //this.getthreadlist(1);
                    this.$store.commit('removeTag', 'thread_update');
                    this.$store.commit('closePage', 'thread_update');
                    this.getData();
                }
            }
        },
        mounted () {
            //this.getData();
        },
        methods: {
            getData () {
                //this.$store.commit('removeTag', 'thread_update');
                this.tlColumns = tData.tlColumns;
                this.getthreadlist(1);
                this.getdigestlist(1);
            },
            getthreadlist (n){
                this.tlRows = [];
                this.tl_total = 0;
                this.tl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread',
                    method:'GET',
                    params: {
                        page: _this.tl_current,
                        page_size: _this.page_size,
                        stick: 0
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.tlRows = res.data.data.rows;
                        for(var i=0;i<_this.tlRows.length;i++){
                            _this.tlRows[i].initHl = _this.tlRows[i].highlight;
                        }
                        _this.tl_total = res.data.data.count;
                        if(_this.tl_total > _this.page_size){
                            _this.tl_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getdigestlist (n){
                this.dlRows = [];
                this.dl_total = 0;
                this.dl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread',
                    method:'GET',
                    params: {
                        page: _this.dl_current,
                        page_size: 1000,
                        stick: 1
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.dlRows = res.data.data.rows;
                        for(var i=0;i<_this.dlRows.length;i++){
                            _this.dlRows[i].initHl = _this.dlRows[i].highlight;
                        }
                        /*_this.dl_total = res.data.data.count;
                        if(_this.dl_total > _this.pageSize){
                            _this.dl_showPage = true;
                        }*/
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            handleUpdata (){
                this.getData();
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
