<style lang="less">
@import "../../../../styles/common.less";
@import "../thread.less";
</style>

<template>
  <div class="thread-update">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <p slot="title">
            <Icon type="ios-compose-outline"></Icon>
            编辑帖子
          </p>
          <Modal
            v-model="filemodal"
            title="修改文件名"
            class-name="filemodal"
            width="50px"
            okText="保存"
            @on-ok="filemodalOk"
            @on-cancel="filemodalCancel"
          >
            <Input v-model="fileVal.original"></Input>
          </Modal>
          <Form
            ref="threadform"
            :model="threadform"
            :rules="rules"
            :label-width="90"
          >
            <FormItem prop="subject" label="帖子标题：">
              <Input
                v-model="threadform.subject"
                placeholder="请输入帖子标题"
              ></Input>
            </FormItem>
            <FormItem prop="content" label="发帖内容：">
              <Input
                type="textarea"
                v-model="threadform.content"
                :rows="6"
                placeholder="请输入帖子内容"
              ></Input>
            </FormItem>
            <FormItem prop="htmlon" label="">
              HTML：
              <RadioGroup v-model="threadform.htmlon">
                <Radio :label="1">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem prop="image" label="素材：">
              <div class="material">
                <div v-for="img in material">
                  <img
                    :src="img.fullpath"
                    alt="图片"
                    v-clipboard:copy="img.fullpath"
                    v-clipboard:success="onCopy"
                    v-clipboard:error="onError"
                  />
                  <span
                    v-clipboard:copy="img.fullpath"
                    v-clipboard:success="onCopy"
                    v-clipboard:error="onError"
                    >复制</span
                  >
                </div>
              </div>
              <Button type="ghost" icon="more" @click="moreImg"></Button>
              <Modal v-model="showMore" width="515" class-name="materialModal">
                <p slot="header">
                  <Icon type="images" size="24"></Icon>
                  <span>素材库</span>
                </p>
                <div>
                  <div class="addList">
                    <div class="file-list">
                      <div
                        v-for="(file, index) in files"
                        class="file-list-item"
                      >
                        <img
                          :src="file.src"
                          alt="图片"
                          width="50"
                          v-clipboard:copy="file.src"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError"
                        />
                        <Icon
                          type="android-close"
                          class="del"
                          @click="delImg(file, index)"
                          size="22"
                        ></Icon>
                        <Button
                          type="text"
                          class="btnTitle"
                          size="small"
                          @click="modTitle(file, index, 'files')"
                          >{{ file.original }}</Button
                        >
                      </div>
                      <Upload
                        multiple
                        action="/api/v1/admin/thread/upload"
                        :before-upload="uploadImg"
                        :show-upload-list="true"
                      >
                        <Button type="ghost" icon="plus"></Button>
                      </Upload>
                    </div>
                  </div>
                  <div class="">
                    <div>
                      <span>置顶</span>
                      <span
                        style="float: right;color: #2db7f5;cursor: pointer"
                        class="editImg"
                        v-if="!editImg"
                        @click="editImg = !editImg"
                        >编辑</span
                      >
                      <span
                        style="float:right;color: #2db7f5;cursor: pointer;"
                        class="editImg"
                        v-if="!!editImg"
                        @click="editImg = !editImg"
                        >保存</span
                      >
                    </div>
                    <div class="top_imgs">
                      <div v-for="(item, index) in material">
                        <img
                          :src="item.fullpath"
                          alt="图片"
                          width="50"
                          v-clipboard:copy="item.fullpath"
                          v-clipboard:success="onCopy"
                          v-clipboard:error="onError"
                        />
                        <span
                          class="del"
                          v-if="!!editImg"
                          @click="image_digest(item, 0)"
                          ><Icon type="minus-round"></Icon
                        ></span>
                        <Button
                          type="text"
                          class="btnTitle"
                          size="small"
                          @click="modTitle(item, index, 'material')"
                          >{{ item.original }}</Button
                        >
                      </div>
                    </div>
                  </div>
                  <div class="search">
                    <Input
                      placeholder="文件名"
                      v-model="search"
                      clearable
                      @on-change="searchChange"
                    >
                    </Input>
                  </div>
                  <div class="former_imgs">
                    <div v-for="(item, index) in thread_img">
                      <img
                        :src="item.fullpath"
                        alt="图片"
                        width="50"
                        v-clipboard:copy="item.fullpath"
                        v-clipboard:success="onCopy"
                        v-clipboard:error="onError"
                      />
                      <span
                        class="digest"
                        v-if="!!editImg"
                        @click="image_digest(item, 1)"
                        ><Icon type="plus-round"></Icon
                      ></span>
                      <Button
                        type="text"
                        class="btnTitle"
                        size="small"
                        @click="modTitle(item, index, 'thread_img')"
                        >{{ item.original }}</Button
                      >
                    </div>
                  </div>
                </div>
                <div slot="footer">
                  <Button type="ghost" size="large" @click="showMore = false"
                    >关闭</Button
                  >
                </div>
              </Modal>
            </FormItem>
            <FormItem prop="fid" label="帖子版块：">
              <Select
                v-model="threadform.fid"
                style="width:200px"
                @on-change="getType"
                :label-in-value="true"
              >
                <!--<Option v-for="item in forum_nav" :value="item.fid" :key="item.index">{{ item.name }}</Option>-->
                <OptionGroup
                  :label="item.name"
                  v-for="item in forum_nav"
                  :key="item.index"
                >
                  <Option
                    v-for="opt in item.forums"
                    :value="opt.fid"
                    :key="opt.index"
                    >{{ opt.name }}</Option
                  >
                </OptionGroup>
              </Select>
              <Select v-model="threadform.typeid" style="width:200px">
                <Option
                  v-for="item in types"
                  :value="item.typeid"
                  :key="item.index"
                  >{{ item.name }}</Option
                >
              </Select>
            </FormItem>
            <FormItem prop="schedule" label="定时发送：">
              <Input
                v-model="threadform.schedule"
                placeholder="请输入定时发送时间 格式：*************"
                style="width:400px"
              ></Input>
            </FormItem>
            <FormItem prop="uid" label="发帖账号：">
              <RadioGroup v-model="threadform.uid">
                <Radio
                  :label="user.id"
                  v-for="user in users"
                  disabled
                  :key="user.index"
                  >{{ user.nickname }}</Radio
                >
              </RadioGroup>
            </FormItem>
            <p class="f_err_msg">{{ errMsg }}</p>
            <FormItem>
              <Button @click="handleSubmit" type="primary">提交</Button>
              <Button @click="closePage">取消</Button>
            </FormItem>
          </Form>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>
<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

export default {
  name: "thread_update",
  components: {},
  data() {
    return {
      threadform: {
        subject: "",
        content: "",
        uid: 0,
        fid: "",
        forum: "",
        typeid: "", //fid 子级的 fid
        htmlon: 1, // 0或1，内容是否是html代码
        schedule: "",
      },
      rules: {
        subject: [
          { required: true, message: "标题不能为空", trigger: "blur" },
          {
            type: "string",
            min: 3,
            message: "标题不能小于3个字符",
            trigger: "blur",
          },
        ],
        content: [
          { required: true, message: "内容不能为空", trigger: "blur" },
          {
            type: "string",
            min: 6,
            message: "内容不能小于6个字符",
            trigger: "blur",
          },
        ],
        fid: [{ required: true, message: "请选择发帖账号", trigger: "blur" }],
        uid: [
          {
            required: true,
            message: "请选择发帖账号",
            trigger: "blur",
            type: "number",
          },
        ],
        /*,
                    typeid: [
                        { required: true, message: '请选择发帖账号', trigger: 'blur' },
                    ]*/
      },
      users: [],
      forum_nav: [],
      types: [],
      access_token: this.$store.state.user.access_token,
      errMsg: "",
      isAccess: true,
      showMore: false,
      editImg: false,
      material: [],
      thread_img: [],
      files: [],
      fileVal: "",
      filemodal: false,
      search: "",
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "thread_update") {
        this.$refs["threadform"].resetFields();
        this.getData();
      }
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    searchChange() {
      this.getThread_image();
    },
    filemodalOk() {
      var _this = this;
      var dataIn = {};
      if (!this.fileVal.original) {
        _this.$Notice.error({
          title: "文件名不能为空",
        });
        return;
      }

      dataIn.id = this.fileVal.id;
      dataIn.original = this.fileVal.original;

      util
        .ajax({
          url: "/api/v1/admin/thread/upload",
          method: "PUT",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
          } else {
            _this.errMsg = res.msg;
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.errMsg = err.msg;
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    filemodalCancel() {},
    getThread_image() {
      var _this = this;
      var dataIn = {};

      if (this.search) {
        dataIn.s = this.search;
      }
      util
        .ajax({
          url: "/api/v1/admin/thread/thread_image",
          method: "GET",
          params: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.thread_img = res.data.data.rows;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    moreImg() {
      this.showMore = true;
      this.getThread_image();
      this.getMaterial();
    },
    onCopy(e) {
      this.$Message.success({
        top: 200,
        content: "复制成功！",
      });
    },
    onError() {
      this.$Message.error({
        top: 200,
        content: "复制失败！",
      });
    },
    image_digest(obj, n) {
      //图片置顶/取消置顶
      var dataIn = {};
      dataIn.digest = n;
      dataIn.id = obj.id;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/image_digest",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (n == 1) {
              _this.getMaterial();
              _this.getThread_image();
              _this.$Message.success({
                top: 200,
                content: "置顶成功！",
              });
            } else {
              _this.$Message.success({
                top: 200,
                content: "取消置顶成功！",
              });
            }
            _this.getMaterial();
            _this.getThread_image();
          } else {
            _this.errMsg = res.msg;
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.errMsg = err.msg;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    uploadImg(file) {
      // 上传图片
      var form = new FormData();
      form.append("file", file);
      const _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/upload",
          method: "POST",
          dataType: "JSON",
          contentType: false,
          processData: false,
          data: form,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.$Notice.success({
              title: "上传成功",
            });

            _this.files.push({
              id: res.data.data.aid,
              name: file.name,
              src: res.data.data.fullpath,
              original: res.data.data.original,
            });
          } else {
            _this.$Notice.error({
              title: res.data.errors.message,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
      return false;
    },
    delImg(obj, index) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/upload",
          method: "DELETE",
          data: {
            id: obj.id,
          },
        })
        .then(function(res) {
          console.log(res.data);
          if (res.data.msg === "success") {
            _this.files.splice(index, 1);
          } else {
          }
        })
        .catch(function(err) {
          console.log(err);
        });
    },
    modTitle(obj, index, type) {
      this.filemodal = true;
      this.fileVal = obj;
      this.fileVal.type = type;
      this.fileVal.index = index;
    },
    getMaterial() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/material/digest",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.material = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getData() {
      this.threadform.subject = "";
      this.threadform.content = "";
      this.threadform.uid = 0;
      this.threadform.fid = "";
      this.threadform.forum = "";
      this.threadform.typeid = "";
      this.threadform.htmlon = 1;
      this.threadform.schedule = "";
      this.users = [];
      this.forum_nav = [];
      this.types = [];

      this.threadform = this.$store.state.thread.currentRow;

      if (!this.threadform.id) {
        this.$router.push({
          name: "thread_list",
        });
      }
      this.threadform.fid = this.threadform.fid + "";
      //this.threadform.typeid = this.threadform.typeid + '';
      this.getUsers();
      this.getForum_nav();

      //console.log(this.threadform);
    },
    handleSubmit() {
      this.$refs.threadform.validate((valid) => {
        if (valid) {
          var _this = this;
          util
            .ajax({
              url: "/api/v1/admin/thread",
              method: "PUT",
              data: {
                subject: _this.threadform.subject,
                content: _this.threadform.content,
                uid: _this.threadform.uid,
                fid: _this.threadform.fid,
                forum: _this.threadform.forum,
                typeid: _this.threadform.typeid,
                htmlon: _this.threadform.htmlon,
                tid: _this.threadform.tid,
                id: _this.threadform.id,
                pid: _this.threadform.pid,
                schedule: _this.threadform.schedule,
                status: _this.threadform.status,
              },
            })
            .then(function(res) {
              if (res.data.msg === "success") {
                _this.$store.commit("removeTag", "thread_update");
                _this.$store.commit("closePage", "thread_update");
                _this.$router.push({
                  name: "thread_list",
                });
              } else {
                _this.errMsg = res.msg;
                _this.$Notice.error({
                  title: res.msg,
                });
              }
            })
            .catch(function(err) {
              _this.errMsg = err.msg;
              _this.$Notice.error({
                title: err.msg,
              });
              console.log(err);
            });
        }
      });
    },
    getUsers() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/user",
          method: "GET",
          params: {
            page: _this.current,
            pageSize: _this.pageSize,
          },
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            _this.users = res.data.data.rows;
            if (!_this.threadform.uid) {
              for (var i = 0; i < _this.users.length; i++) {
                if (
                  _this.users[i].username === _this.threadform.send_username
                ) {
                  _this.threadform.uid = _this.users[i].id;
                }
              }
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getForum_nav() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/forum_nav",
          method: "GET",
          params: {
            page: _this.current,
            pageSize: _this.pageSize,
          },
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            _this.forum_nav = res.data.data;
            setTimeout(function() {
              _this.getType({
                value: _this.threadform.fid,
                label: _this.threadform.forum,
              });
            }, 300);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err.msg);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getType(e) {
      this.threadform.forum = e.label;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/forum_nav_sub",
          method: "GET",
          params: {
            fid: e.value,
          },
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            _this.types = res.data.data;
            _this.threadform.typeid = _this.threadform.typeid + "";
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    closePage() {
      this.threadform.subject = "";
      this.threadform.content = "";
      this.threadform.uid = 0;
      this.threadform.fid = "";
      this.threadform.forum = "";
      this.threadform.typeid = 0;
      this.threadform.htmlon = 1;
      this.threadform.schedule = "";
      this.users = [];
      this.forum_nav = [];
      this.types = [];
      this.$store.commit("removeTag", "thread_update");
      this.$store.commit("closePage", "thread_update");
      this.$router.push({
        name: "thread_list",
      });
    },
  },
  created() {
    //this.getUsers();
    //this.getForum_nav();
    //this.getData();
  },
};
</script>
