import util from '@/libs/util.js';
export const tlColumns = [
    /*{
        title: '',
        //key: 'success',
        width: 60,
        align: 'center',
        /!*render: (h, params) => {
            var style = {}
            if(!params.row.success){
                style.color = '#ed3f14';
            }else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size:14
                }
            })
        }*!/
    },*/
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '帖子标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                    target: '_blank'
                }
            }, params.row.subject)
        }
    },
    {
        title: '所在板块',
        align: 'center',
        key: 'forum',
        width: 150,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/forum-' + params.row.fid + '-1.html',
                    target: '_blank'
                }
            }, params.row.forum)
        }
    },
    {
        title: '发帖账号',
        align: 'center',
        width: 120,
        key: 'send_username'
    },
    {
        title: '创建时间',
        align: 'center',
        width: 110,
        key: 'created_at',
        render: (h, params) => {
            return h('span', {}, params.row.created_at.split('T')[0]);
        }
    },
    {
        title: '高亮',
        align: 'center',
        key: 'action',
        width: 120,
        action: ['colorpick']
    },
    {
        title: '精华',
        align: 'center',
        key: 'digest',
        width: 90,
        action: ['iSwich']
    },
    {
        title: '置顶',
        align: 'center',
        key: 'stick',
        width: 100,
        action: ['dropDown']
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['edit']  //,'delete'
    },
];
export const plColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: 'PID',
        key: 'pid',
        width: 100,
        align: 'center',
        render: (h, params) => {
            return h('Tooltip', {
                attrs: {
                    content: params.row.content,
                    placement: 'bottom-start'
                },
                style: {
                    cursor: 'pointer'
                }
            }, params.row.pid)
        }
    },
    {
        title: '帖子标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/forum.php?mod=redirect&goto=findpost&ptid=' + params.row.tid + '&pid=' + params.row.pid,
                    target: '_blank'
                }
            }, params.row.subject)
        }
    },
    {
        title: '所在板块',
        align: 'center',
        key: 'forum',
        width: 150,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/forum-' + params.row.fid + '-1.html',
                    target: '_blank'
                }
            }, params.row.forum)
        }
    },
    {
        title: '发帖账号',
        align: 'center',
        width: 120,
        key: 'send_username'
    },
    {
        title: '创建时间',
        align: 'center',
        width: 110,
        key: 'created_at',
        render: (h, params) => {
            return h('span', {}, params.row.created_at.split('T')[0]);
        }
    },
    {
        title: '高亮',
        align: 'center',
        key: 'action',
        width: 120,
        action: ['colorpick']
    },
    {
        title: '精华',
        align: 'center',
        key: 'digest',
        width: 90,
        action: ['iSwich']
    },
    {
        title: '置顶',
        align: 'center',
        key: 'stick',
        width: 100,
        action: ['dropDown']
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['edit']
    },
];
export const uColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    {
        title: '账号',
        align: 'center',
        key: 'nickname',  //  username
        /*width: 80*/
    },
    /*{
        title: '手机号码',
        align: 'center',
        key: 'mobile'
    },*/
    {
        title: '操作',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['edit', 'delete']
    },
]

const tData = {
    tlColumns: tlColumns,
    plColumns: plColumns,
    uColumns: uColumns
};

export default tData;
