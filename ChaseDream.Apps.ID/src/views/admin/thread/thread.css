.thread-post .f_err_msg {
  color: red;
  margin: -20px 0 20px 90px;
}
.releaseModel .end_date .ivu-form-item-content,
.thread-post .end_date .ivu-form-item-content,
.core-edit .end_date .ivu-form-item-content,
.release_modal .end_date .ivu-form-item-content,
.releaseModel .province .ivu-form-item-content,
.thread-post .province .ivu-form-item-content,
.core-edit .province .ivu-form-item-content,
.release_modal .province .ivu-form-item-content,
.releaseModel .city .ivu-form-item-content,
.thread-post .city .ivu-form-item-content,
.core-edit .city .ivu-form-item-content,
.release_modal .city .ivu-form-item-content {
  margin-left: 0 !important;
}
.releaseModel .end_date .ivu-poptip,
.thread-post .end_date .ivu-poptip,
.core-edit .end_date .ivu-poptip,
.release_modal .end_date .ivu-poptip,
.releaseModel .province .ivu-poptip,
.thread-post .province .ivu-poptip,
.core-edit .province .ivu-poptip,
.release_modal .province .ivu-poptip,
.releaseModel .city .ivu-poptip,
.thread-post .city .ivu-poptip,
.core-edit .city .ivu-poptip,
.release_modal .city .ivu-poptip {
  position: absolute;
  top: -20px;
}
.releaseModel .end_date .ivu-icon-close-circled,
.thread-post .end_date .ivu-icon-close-circled,
.core-edit .end_date .ivu-icon-close-circled,
.release_modal .end_date .ivu-icon-close-circled,
.releaseModel .province .ivu-icon-close-circled,
.thread-post .province .ivu-icon-close-circled,
.core-edit .province .ivu-icon-close-circled,
.release_modal .province .ivu-icon-close-circled,
.releaseModel .city .ivu-icon-close-circled,
.thread-post .city .ivu-icon-close-circled,
.core-edit .city .ivu-icon-close-circled,
.release_modal .city .ivu-icon-close-circled {
  position: absolute;
  right: -40px;
  top: 10px;
  color: #ed3f14;
  cursor: pointer;
}
.releaseModel .end_date .ivu-icon-close-circled:hover,
.thread-post .end_date .ivu-icon-close-circled:hover,
.core-edit .end_date .ivu-icon-close-circled:hover,
.release_modal .end_date .ivu-icon-close-circled:hover,
.releaseModel .province .ivu-icon-close-circled:hover,
.thread-post .province .ivu-icon-close-circled:hover,
.core-edit .province .ivu-icon-close-circled:hover,
.release_modal .province .ivu-icon-close-circled:hover,
.releaseModel .city .ivu-icon-close-circled:hover,
.thread-post .city .ivu-icon-close-circled:hover,
.core-edit .city .ivu-icon-close-circled:hover,
.release_modal .city .ivu-icon-close-circled:hover {
  color: #ed3f14;
}
.releaseModel .ivu-col-span-1,
.thread-post .ivu-col-span-1,
.core-edit .ivu-col-span-1,
.release_modal .ivu-col-span-1 {
  width: 20px;
}
.main .geos .ivu-btn.ivu-btn-dashed,
.releaseModel .geos .ivu-btn.ivu-btn-dashed,
.release_modal .geos .ivu-btn.ivu-btn-dashed {
  border: 1px dashed #dddee1;
  width: 100px;
}
.main .geos .ivu-icon.ivu-icon-checkmark-circled,
.releaseModel .geos .ivu-icon.ivu-icon-checkmark-circled,
.release_modal .geos .ivu-icon.ivu-icon-checkmark-circled {
  position: absolute;
  right: -80px;
  top: 10px;
  color: #19be6b;
  cursor: pointer;
}
.main .geos .ivu-icon.ivu-icon-checkmark-circled:hover,
.releaseModel .geos .ivu-icon.ivu-icon-checkmark-circled:hover,
.release_modal .geos .ivu-icon.ivu-icon-checkmark-circled:hover {
  color: #19be6b;
}
.main .f_err_msg,
.releaseModel .f_err_msg,
.release_modal .f_err_msg {
  color: #ed3f14;
}
.main .ivu-checkbox-wrapper.ivu-checkbox-group-item,
.releaseModel .ivu-checkbox-wrapper.ivu-checkbox-group-item,
.release_modal .ivu-checkbox-wrapper.ivu-checkbox-group-item {
  margin-bottom: 10px;
}
.main .file-list .ivu-icon.ivu-icon-close-circled,
.releaseModel .file-list .ivu-icon.ivu-icon-close-circled,
.release_modal .file-list .ivu-icon.ivu-icon-close-circled {
  margin-left: 10px;
  color: #ed3f14;
}
.pics .ivu-card-dis-hover .ivu-card-body {
  padding-right: 0;
  padding-bottom: 0;
}
.pics .iview-admin-draggable-list {
  display: flex;
  flex-wrap: wrap;
  min-height: 100px;
}
.pics .iview-admin-draggable-list li {
  width: 206px;
  min-height: 150px;
  margin-right: 16px;
  margin-bottom: 16px;
  position: relative;
}
.pics .iview-admin-draggable-list li img {
  vertical-align: top;
  width: 100%;
}
.pics .iview-admin-draggable-list li p {
  margin-top: 10px;
  width: 100%;
  word-wrap: break-word;
  white-space: normal;
  font-size: 12px;
}
.pics .iview-admin-draggable-list li .mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 0px;
  background: rgba(0, 0, 0, 0.5);
  line-height: 30px;
  text-align: right;
  padding-right: 10px ;
  overflow: hidden;
  transition: all 0.2s;
}
.pics .iview-admin-draggable-list li .mask .ivu-icon {
  color: #fff;
  display: inline-block;
}
.pics .iview-admin-draggable-list li .mask .ivu-poptip {
  width: 20px;
  text-align: center;
  display: inline-block;
  margin-left: 10px;
}
.pics .iview-admin-draggable-list li:hover .mask {
  height: 28px;
}
.pics .iview-admin-draggable-list li .ivu-poptip {
  float: right;
  margin-top: -30px;
}
.pics .iview-admin-draggable-list li .ivu-poptip .ivu-icon-android-more-vertical {
  width: 10px;
  text-align: center;
}
.dragTable {
  display: table;
  table-layout: fixed;
  width: 100%;
  border-left: 1px solid #e9eaec;
  background: #fff;
}
.dragTable .tableRow {
  display: table-row;
}
.dragTable .tableRow:hover {
  background: #f5f7fe;
}
.dragTable .tableCell {
  display: table-cell;
  height: 60px;
  font-size: 12px;
  border-right: 1px solid #e9eaec;
  border-bottom: 1px solid #e9eaec;
  vertical-align: middle;
  text-align: center;
  padding-left: 18px;
  padding-right: 18px;
}
.dragTable .tableCell.index {
  width: 60px;
}
.dragTable .tableCell.location {
  width: 90px;
}
.dragTable .tableCell.subject {
  width: auto;
}
.dragTable .tableCell.subject table {
  width: 100%;
}
.dragTable .tableCell.subject table td:nth-of-type(1) span {
  color: #2d8cf0;
}
.dragTable .tableCell.subject table td:nth-of-type(2) {
  padding-left: 35px;
  text-align: left;
  position: relative;
}
.dragTable .tableCell.subject table td:nth-of-type(2) span {
  position: absolute;
  width: 1px;
  height: 60px;
  background: #e9eaec;
  left: 17px;
  top: -13px;
}
.dragTable .tableCell.color {
  width: 100px;
}
.dragTable .tableCell.color .ivu-btn {
  width: 25px;
  height: 25px;
  background: #000;
  padding: 0px;
  border-radius: 5px;
  margin: 0 5px;
}
.dragTable .tableCell.color .ivu-btn.red {
  background-color: #ff0101;
}
.dragTable .tableCell.color .ivu-btn.green {
  background-color: #1ab394;
}
.dragTable .tableCell.color .ivu-btn.blue {
  background-color: #2897c5;
}
.dragTable .tableCell.color .ivu-btn.black {
  background-color: #000;
}
.dragTable .tableCell.color .ivu-btn.on {
  background-image: url("../../../images/release/colour_selected.png");
  background-repeat: no-repeat;
  background-position: bottom right;
  background-size: 140%;
}
.dragTable .tableCell.end_date,
.dragTable .tableCell.push_end_date,
.dragTable .tableCell.new_flag_date {
  width: 140px;
}
.dragTable .tableCell.handle {
  width: 130px;
}
.dragTable .tableCell p {
  width: 100%;
  text-align: left;
  white-space: normal;
  word-wrap: break-word;
}
.dragTable .tableCell .ivu-icon {
  margin: 0 7px;
  cursor: pointer;
}
.headlines-edit .colors .ivu-btn,
.headlines-add .colors .ivu-btn,
.releaseModel .colors .ivu-btn,
.release_modal .colors .ivu-btn {
  width: 25px;
  height: 25px;
  background: #000;
  padding: 0px;
  border-radius: 5px;
  margin: 0 5px;
}
.headlines-edit .colors .ivu-btn.red,
.headlines-add .colors .ivu-btn.red,
.releaseModel .colors .ivu-btn.red,
.release_modal .colors .ivu-btn.red {
  background-color: #ff0101;
}
.headlines-edit .colors .ivu-btn.green,
.headlines-add .colors .ivu-btn.green,
.releaseModel .colors .ivu-btn.green,
.release_modal .colors .ivu-btn.green {
  background-color: #1ab394;
}
.headlines-edit .colors .ivu-btn.blue,
.headlines-add .colors .ivu-btn.blue,
.releaseModel .colors .ivu-btn.blue,
.release_modal .colors .ivu-btn.blue {
  background-color: #2897c5;
}
.headlines-edit .colors .ivu-btn.black,
.headlines-add .colors .ivu-btn.black,
.releaseModel .colors .ivu-btn.black,
.release_modal .colors .ivu-btn.black {
  background-color: #000;
}
.headlines-edit .colors .ivu-btn.on,
.headlines-add .colors .ivu-btn.on,
.releaseModel .colors .ivu-btn.on,
.release_modal .colors .ivu-btn.on {
  background-image: url("../../../images/release/colour_selected.png");
  background-repeat: no-repeat;
  background-position: bottom right;
  background-size: 140%;
}
.ivu-poptip-popper.color_pop .ivu-btn {
  width: 25px;
  height: 25px;
  background: #000;
  padding: 0px;
  border-radius: 5px;
  margin: 0 5px;
  box-shadow: none;
}
.ivu-poptip-popper.color_pop .ivu-btn.red {
  background-color: #ff0101;
}
.ivu-poptip-popper.color_pop .ivu-btn.green {
  background-color: #1ab394;
}
.ivu-poptip-popper.color_pop .ivu-btn.blue {
  background-color: #2897c5;
}
.ivu-poptip-popper.color_pop .ivu-btn.black {
  background-color: #000;
}
.ivu-poptip-popper.color_pop .ivu-btn.on {
  background-image: url("../../../images/release/colour_selected.png");
  background-repeat: no-repeat;
  background-position: bottom right;
  background-size: 140%;
}
.activity-list .iview-admin-draggable-list li,
.headlines-list .iview-admin-draggable-list li {
  padding: 0;
  border: none;
  margin: 0;
}
.activity-edit .province .ivu-form-item-content,
.release_modal .province .ivu-form-item-content,
.activity-edit .city .ivu-form-item-content,
.release_modal .city .ivu-form-item-content {
  margin-left: 0 !important;
}
.file-list div {
  position: relative;
  width: 100px;
  height: 100px;
  border: 1px solid #efefef;
}
.file-list div img {
  width: 100%;
  vertical-align: top;
}
.file-list div .del {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100px;
  line-height: 100%;
  background: rgba(225, 225, 225, 0.8);
  display: none;
}
.file-list div .del .ivu-icon {
  color: #ed3f14;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -12px;
  margin-top: -12px;
  cursor: pointer;
}
.file-list div:hover .del {
  display: block;
}
.file-list .ivu-icon.del {
  position: absolute;
  left: inherit;
  top: -1px;
  width: 15px;
  height: 15px;
  line-height: 15px;
  background: red;
  color: #fff;
  cursor: pointer;
  text-align: center;
  display: block;
}
.file-list .ivu-icon.wrench {
  position: absolute;
  left: inherit;
  right: 0px;
  top: 0px;
  width: 15px;
  height: 15px;
  line-height: 15px;
  cursor: pointer;
  text-align: center;
  display: block;
}
.main .ivu-upload .ivu-btn,
.ivu-modal .ivu-upload .ivu-btn {
  width: 50px;
  height: 50px;
  border: 1px dashed #efefef;
}
.main .common,
.release_modal .common {
  display: flex;
  width: 100%;
}
.main .common strong,
.release_modal .common strong {
  margin-top: 5px;
  min-width: 40px;
}
.main .common span,
.release_modal .common span {
  display: inline-block;
  margin: 5px 10px;
  vertical-align: middle;
  cursor: pointer;
  color: #2d8cf0;
  text-decoration: underline;
  line-height: 16px;
}
.main .plusItem,
.release_modal .plusItem {
  border: 1px dashed #efefef;
}
.main .geos,
.release_modal .geos {
  position: relative;
  /*background: #eaf8f0;*/
  border-radius: 5px;
  margin-bottom: 5px;
  padding-top: 20px;
  border: 1px solid #efefef;
}
.main .geos .delItem,
.release_modal .geos .delItem {
  color: #efefef;
  position: absolute;
  right: 20px;
  top: 20px;
  cursor: pointer;
}
.main .geos:hover .delItem,
.release_modal .geos:hover .delItem {
  color: #ed3f14;
}
.main .material,
.release_modal .material {
  display: inline-block;
  vertical-align: middle;
}
.main .material div,
.release_modal .material div {
  display: inline-block;
  margin-right: 5px;
  position: relative;
  width: 50px;
  height: 50px;
  overflow: hidden;
}
.main .material div img,
.release_modal .material div img {
  width: 100%;
  border: 1px solid #efefef;
  display: block;
  cursor: pointer;
  display: inline-block;
}
.main .material div span,
.release_modal .material div span {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 20px;
  background-color: #000000;
  color: #fff;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  opacity: 0.3;
}
.filemodal {
  z-index: 1001;
}
.search {
  margin-bottom: 10px;
}
.materialModal .ivu-modal-header p {
  text-align: center;
  vertical-align: middle;
}
.materialModal .ivu-modal-header p .ivu-icon {
  font-size: 24px;
  vertical-align: middle;
  margin-right: 5px;
}
.materialModal .ivu-modal-body .addList .file-list {
  margin-bottom: 20px;
}
.materialModal .ivu-modal-body .addList .file-list .file-list-item {
  width: 50px;
  height: 75px;
  display: inline-block;
  vertical-align: middle;
  border: 1px solid #efefef;
  margin-right: 10px;
  overflow: hidden;
}
.materialModal .ivu-modal-body .addList .file-list .file-list-item p {
  height: 100%;
}
.materialModal .ivu-modal-body .addList .file-list .file-list-item p .ivu-icon {
  font-size: 22px;
  margin-left: -8px;
}
.materialModal .ivu-modal-body .addList .file-list .file-list-item img {
  width: 50px;
  height: 50px;
}
.materialModal .ivu-modal-body .addList .file-list .file-list-item .btnTitle {
  width: 50px;
  padding-left: 0px;
  padding-right: 0px;
}
.materialModal .ivu-modal-body .addList .file-list .ivu-upload {
  display: inline-block;
  vertical-align: middle;
  width: 50px;
  height: 50px;
}
.materialModal .ivu-modal-body .former_imgs div {
  width: 50px;
  height: 75px;
  overflow: hidden;
  display: inline-block;
  border: 1px solid #dfdfdf;
  margin: 5px;
  position: relative;
}
.materialModal .ivu-modal-body .former_imgs div img {
  width: 100%;
  height: 50px;
  display: inline-block;
  vertical-align: middle;
}
.materialModal .ivu-modal-body .former_imgs div span.digest {
  position: absolute;
  left: -1px;
  top: -1px;
  width: 13px;
  height: 13px;
  font-size: 13px;
  line-height: 13px;
  color: #fff;
  background: #19be6b;
  border: 1px solid #fff;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  box-sizing: content-box;
}
.materialModal .ivu-modal-body .former_imgs div .btnTitle {
  width: 50px;
  padding-left: 0px;
  padding-right: 0px;
}
.materialModal .ivu-modal-body .top_imgs {
  min-height: 90px;
  background: #f8f8f9;
  margin-bottom: 20px;
  padding: 20px 10px;
}
.materialModal .ivu-modal-body .top_imgs div {
  display: inline-block;
  width: 50px;
  height: 75px;
  overflow: hidden;
  border: 1px solid #efefef;
  position: relative;
  margin-right: 10px;
}
.materialModal .ivu-modal-body .top_imgs div img {
  width: 50px;
  height: 50px;
  display: inline-block;
  vertical-align: middle;
}
.materialModal .ivu-modal-body .top_imgs div span.del {
  position: absolute;
  left: -1px;
  top: -1px;
  width: 13px;
  height: 13px;
  font-size: 13px;
  line-height: 13px;
  color: #fff;
  background: #ed3f14;
  border: 1px solid #fff;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  box-sizing: content-box;
}
.materialModal .ivu-modal-body .top_imgs div .btnTitle {
  width: 50px;
  padding-left: 0px;
  padding-right: 0px;
}
.event-edit .end_date .ivu-form-item-content,
.release_modal .end_date .ivu-form-item-content {
  margin-left: 0 !important;
}
.event-list .o-line {
  background: url("../../../images/release/o-line.png") no-repeat;
  background-size: 100%;
  left: 50%;
  top: 50%;
  margin-left: -17.5px;
  margin-top: -20px;
}
.schools-add .majors .major,
.major-edit .majors .major {
  position: relative;
}
.schools-add .majors .major .delItem,
.major-edit .majors .major .delItem {
  position: absolute;
  right: 20px;
  top: 20px;
  cursor: pointer;
  color: #bbbec4;
}
.schools-add .majors .major:hover .delItem,
.major-edit .majors .major:hover .delItem {
  color: #ed3f14;
}
.ivu-picker-confirm .ivu-btn.ivu-btn-ghost.ivu-btn-small {
  display: none;
}
.pic_btns.ivu-poptip-popper {
  min-width: 100px;
}
.pic_btns.ivu-poptip-popper .btns .ivu-btn {
  display: block;
  margin: 5px 0;
  width: 86px;
  border: none;
  border-radius: 4px;
}
.pic_btns.ivu-poptip-popper .btns .ivu-btn .ivu-icon {
  font-size: 18px;
}
.pic_btns.ivu-poptip-popper .btns .ivu-btn.link {
  color: #3AA1EC;
  background-color: #ebf5fd;
}
.pic_btns.ivu-poptip-popper .btns .ivu-btn.edit {
  color: #4EB985;
  background-color: #edf8f3;
}
.pic_btns.ivu-poptip-popper .btns .ivu-btn.del {
  color: #ed3f14;
  background-color: #bbbec4;
}
.delete.ivu-poptip-popper {
  z-index: 9999;
}
.release_modal input.ivu-input {
  height: 40px;
  border-radius: 0;
  font-size: 14px;
}
.release_modal .ivu-select-single .ivu-select-selection {
  height: 42px;
}
.release_modal .ivu-modal-footer {
  display: none;
}
.release_modal .ivu-card-bordered {
  box-shadow: none;
  border: none;
}
.release_modal .ivu-card-head {
  display: none;
}
.core-list .tool-bar .ivu-input-group:hover .ivu-icon-close-circled {
  right: 50px;
  top: 3px;
  display: block;
  cursor: pointer;
}
.core-list .clTable .ivu-table-row td:first-child {
  /*padding: 0;*/
}
.core-list .clTable .ivu-table-row td:first-child .ivu-table-cell {
  padding: 0;
}
.activity-add .ivu-form-item .ps textarea,
.activity-edit .ivu-form-item .ps textarea {
  background: #EFFAF5;
  border: none;
  box-shadow: none;
}
.releaseModel .ivu-spin-fix {
  /* position: fixed;*/
}
