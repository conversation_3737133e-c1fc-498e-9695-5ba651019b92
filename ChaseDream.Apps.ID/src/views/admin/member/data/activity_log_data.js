export const bColumns = [
    {
        title: 'UID',
        align: 'center',
        key: 'uid',
        width: 150,
        render: (h, params) => {
            if (!!params.row.uid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/space-uid-' + params.row.uid + '.html',
                        target: '_blank'
                    }
                }, params.row.uid)
            } else {
                return h('span', {}, params.row.uid)
            }
        }
    },
    {
        title: 'FID',
        align: 'center',
        key: 'fid',
        width: 150
    },
    {
        title: 'TID',
        align: 'center',
        key: 'tid',
        render: (h, params) => {
            if (!!params.row.tid) {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            } else {
                return h('span', {}, params.row.tid)
            }
        }
    },
    {
        title: '活动',
        align: 'center',
        key: 'action_id',
        width: 150,
        render: (h, params) => {
            var text = '';
            if (params.row.action_id === 0) {
                text = '浏览'
            } else if (params.row.action_id === 1) {
                text = '发帖'
            }
            else if (params.row.action_id === 2) {
                text = '回复'
            } else if (params.row.action_id === 3) {
                text = '编辑'
            }
            return h('span', {}, text)
        }
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 150,
        render: (h, params) => {
            if (!!params.row.created_at) {
                var c_date = new Date(parseInt(params.row.created_at + '000'));
                var y = c_date.getFullYear();
                var M = (c_date.getMonth() + 1) > 9 ? c_date.getMonth() + 1 : '0' + (c_date.getMonth() + 1);
                var d = c_date.getDate() > 9 ? c_date.getDate() : '0' + c_date.getDate();
                var H = c_date.getHours() > 9 ? c_date.getHours() : '0' + c_date.getHours();
                var m = c_date.getMinutes() > 9 ? c_date.getMinutes() : '0' + c_date.getMinutes();
                var s = c_date.getSeconds() > 9 ? c_date.getSeconds() : '0' + c_date.getSeconds();
                return h('span', {}, y + '-' + M + '-' + d + ' ' + H + ':' + m + ':' + s);
            } else {
                return h('span', {}, '')
            }
        }
    }
];
export const s_Columns = [
    {
        title: '#',
        type: '',
        width: 60,
        align: 'center'
    },
    {
        title: 'UID',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['uid']
    },
    {
        title: 'FID',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['fid']
    },
    {
        title: 'TID',
        align: 'left',
        key: 'handle',
        handle: ['tid']
    },
    {
        title: '活动',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['action_id']
    },
    {
        title: '创建时间',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['clear']
    }
]

const tagData = {
    bColumns: bColumns,
    s_Columns: s_Columns
};

export default tagData;
