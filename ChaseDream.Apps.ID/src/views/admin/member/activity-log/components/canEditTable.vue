<style lang="less">
    @import 'editable-table.less';
</style>

<template>
    <div>
        <Table :ref="refs" :columns="columnsList" :data="thisTableData" border></Table>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

const viewButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'eye',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            click: () => {
                vm.$emit('details', currentRow);
            }
        }
    });
};
const editButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'edit',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$router.push('/console/tag/edit/'+currentRow.id)
            }
        }
    });
};
const deleteButton = (vm, h, currentRow, index) => {
    return h('Poptip', {
        props: {
            confirm: true,
            title: '您确定要删除这条数据吗?',
            transfer: true
        },
        on: {
            'on-ok': () => {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/member_activity_log',
                    method:'DELETE',
                    data: {
                        id: currentRow.id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        vm.thisTableData.splice(index, 1);
                        vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                        vm.$emit('on-delete', vm.handleBackdata(vm.thisTableData), index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        }
    }, [
        h('Icon', {
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            props: {
                type: 'trash-a',
                placement: 'top',
                //icon: 'trash-a'
                size: 24
            }
        })
    ]);
};
const uidInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.uid,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'uid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'uid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const fidInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.fid,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'fid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'fid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const tidInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.tid,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'tid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'tid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const action_idInput = (vm, h, currentRow, index) => {
    var typeList =[{
        id: 0,
        name: '浏览'
    },{
        id: 1,
        name: '发帖'
    },{
        id: 2,
        name: '回复'
    },{
        id: 3,
        name: '编辑'
    }];
    return h('Select', {
        props: {
            value: currentRow.action_id, // 获取选择的下拉框的值
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-change': e => {
                vm.$set(vm.thisTableData[0], 'action_id', e);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    },typeList.map((item) => {
        return h('Option', { // 下拉框的值
            props: {
                value: item.id,
                label: item.name
            },
            style:{
                paddingLeft: 0,
                paddingRight: 0
            }
        })
    }));
};
const clearButton = (vm, h, currentRow, index) => {
    return h('span', {
        props: {},
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$set(vm.thisTableData[0], 'uid', '');
                vm.$set(vm.thisTableData[0], 'fid', '');
                vm.$set(vm.thisTableData[0], 'tid', '');
                vm.$set(vm.thisTableData[0], 'action_id', '');
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    }, '清除');
}
export default {
    name: 'canEditTable',
    props: {
        refs: String,
        columnsList: Array,
        value: Array,
        url: String,
        editIncell: {
            type: Boolean,
            default: false
        },
        hoverShow: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            columns: [],
            thisTableData: [],
            edittingStore: []
        };
    },
    created () {
        this.init();
    },
    methods: {
        init () {
            let vm = this;
            let editableCell = this.columnsList.filter(item => {
                if (item.editable) {
                    if (item.editable === true) {
                        return item;
                    }
                }
            });
            let cloneData = JSON.parse(JSON.stringify(this.value));
            let res = [];
            res = cloneData.map((item, index) => {
                let isEditting = false;
                if (this.thisTableData[index]) {
                    if (this.thisTableData[index].editting) {
                        isEditting = true;
                    } else {
                        for (const cell in this.thisTableData[index].edittingCell) {
                            if (this.thisTableData[index].edittingCell[cell] === true) {
                                isEditting = true;
                            }
                        }
                    }
                }
                if (isEditting) {
                    return this.thisTableData[index];
                } else {
                    this.$set(item, 'editting', false);
                    let edittingCell = {};
                    editableCell.forEach(item => {
                        edittingCell[item.key] = false;
                    });
                    this.$set(item, 'edittingCell', edittingCell);
                    return item;
                }
            });
            this.thisTableData = res;
            this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
            this.columnsList.forEach(item => {
                if (item.editable) {
                    item.render = (h, param) => {
                        let currentRow = this.thisTableData[param.index];
                        if (!currentRow.editting) {
                            if (this.editIncell) {
                                return h('Row', {
                                    props: {
                                        type: 'flex',
                                        align: 'middle',
                                        justify: 'center'
                                    }
                                }, [
                                    h('Col', {
                                        props: {
                                            span: '22'
                                        }
                                    }, [
                                        !currentRow.edittingCell[param.column.key] ? h('span', currentRow[item.key]) : cellInput(this, h, param, item)
                                    ]),
                                    h('Col', {
                                        props: {
                                            span: '2'
                                        }
                                    }, [
                                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                                    ])
                                ]);
                            } else {
                                return h('span', currentRow[item.key]);
                            }
                        } else {
                            return h('Input', {
                                props: {
                                    type: 'text',
                                    value: currentRow[item.key]
                                },
                                on: {
                                    'on-change' (event) {
                                        let key = param.column.key;
                                        vm.edittingStore[param.index][key] = event.target.value;
                                    }
                                }
                            });
                        }
                    };
                }
                if (item.handle) {
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.handle.forEach(item => {
                            if (item === 'edit') {
                                children.push(editButton(this, h, currentRowData, param.index));
                            } else if (item === 'delete') {
                                children.push(deleteButton(this, h, currentRowData, param.index));
                            } else if (item === 'view') {
                                children.push(viewButton(this, h, currentRowData, param.index));
                            } else if(item === 'clear'){
                                children.push(clearButton(this, h, currentRowData, param.index));
                            } else if(item === 'uid'){
                                children.push(uidInput(this, h, currentRowData, param.index));
                            }else if(item === 'fid'){
                                children.push(fidInput(this, h, currentRowData, param.index));
                            }else if(item === 'tid'){
                                children.push(tidInput(this, h, currentRowData, param.index));
                            }else if(item === 'action_id'){
                                children.push(action_idInput(this, h, currentRowData, param.index));
                            }
                        });
                        return h('div', children);
                    };
                }
            });
        },
        handleBackdata (data) {
            let clonedData = JSON.parse(JSON.stringify(data));
            clonedData.forEach(item => {
                delete item.editting;
                delete item.edittingCell;
                delete item.saving;
            });
            return clonedData;
        }
    },
    watch: {
        value (data) {
            this.init();
        }
    }
};
</script>
