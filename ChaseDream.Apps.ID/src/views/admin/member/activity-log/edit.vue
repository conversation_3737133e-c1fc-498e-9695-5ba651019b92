<style lang="less">
    @import '../../../../styles/common.less';
    @import '../member_activity_log.less';
</style>

<template>
    <div class="tag-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑标签
                </p>
                <Form>
                    <FormItem v-for="tag,index in tags" :key="tag.index">
                        <Input v-model="tag.name" placeholder="请输标签名称" style="width:300px;margin-right: 20px;"></Input>
                        <Select v-model="property" v-if="!!tag.main" style="width:130px;margin-right: 20px;" placeholder="请选择标签属性...">
                            <Option :value="1">专业方向</Option>
                            <Option :value="2">学校名称</Option>
                            <Option :value="3">帖子类型</Option>
                            <Option :value="4">适用阶段</Option>
                        </Select>
                        <Checkbox v-model="tag.main" :true-value="1" :false-value="0" @on-change="setMain(index)">设为主标签</Checkbox>
                        <Icon type="close-circled" size="18" style="color: #ed3f14;cursor: pointer;margin-left: 20px;" @click="del_row(index)" v-if="tags.length > 1"></Icon>
                    </FormItem>
                    <FormItem>
                        <Button type="dashed" @click="add_row" style="border: 1px dashed #efefef;"><Icon type="ios-plus-empty" size="30"></Icon></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <br><br>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'tag_edit',
        components: {

        },
        data () {
            return {
                tags:[],
                property: 0,
                id: -1,
                synonym_id: '',
                del_tags: [],
                errMsg: '',
                access_token: this.$store.state.user.access_token,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'tag_edit'){
                    this.tags = [];
                    this.property = 0;
                    this.id = -1;
                    this.synonym_id = '';
                    this.del_tags = [];
                    this.errMsg = '';
                    this.getData();
                }
            },
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData (){
                this.id = window.location.href.split('/').pop();
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag/' + this.id,
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.synonym_id = res.data.data.synonym_id
                        var m = {
                            id: res.data.data.id,
                            name: res.data.data.name,
                            main: 1,
                            property: res.data.data.property
                        }
                        _this.property = res.data.data.property;
                        _this.tags.push(m);
                        for(var i=0;i<res.data.data.synonym.length;i++){
                            var t = {
                                id: res.data.data.synonym[i].id,
                                name: res.data.data.synonym[i].name,
                                main: 0,
                                property: res.data.data.synonym[i].property
                            }
                            _this.tags.push(t);
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            add_row (){
                var item = {
                    name: '',
                    main: 0,
                    property: 0
                }
                this.tags.push(item);
            },
            del_row (index){
                if(!!this.tags[index].id){
                    this.del_tags.push(this.tags[index]);
                }
                this.tags.splice(index, 1);

                var isMain = false;
                var _this = this;
                if(!check_main()){
                    this.tags[0].main = 1;
                }
                function check_main() {
                    for(var i=0;i<_this.tags.length;i++){
                        if(_this.tags[i].main === 1){
                            return true;
                        }
                    }
                    return false;
                }

            },
            setMain (index){
                for(var i=0;i<this.tags.length;i++){
                    this.tags[i].main = 0;
                }
                this.tags[index].main = 1;
            },
            handleSubmit () {
                var dataIn = {
                    synonym_id: this.synonym_id,
                    tags: []
                }
                if(this.tags.length > 0 && !!this.property){
                    for(var i=0;i<this.tags.length;i++){
                        if(!!this.tags[i].id){
                            this.tags[i].action = 'update';
                        }else {
                            this.tags[i].action = 'create';
                        }
                        if(this.tags[i].main === 1 && !this.tags[i].name){
                            this.$Notice.error({
                                title: '主标签名称不能为空！'
                            });
                            return false;
                        }
                        this.tags[i].property = this.property;
                        dataIn.tags.push(this.tags[i]);
                    }
                    for(var i=0;i<this.del_tags.length;i++){
                        this.del_tags[i].action = 'delete';
                        dataIn.tags.push(this.del_tags[i]);
                    }
                }else if(this.tags.length === 0){
                    this.$Notice.error({
                        title: '请输入标签！'
                    });
                    return false;
                }else if(!this.property){
                    this.$Notice.error({
                        title: '请选择标签属性！'
                    });
                    return false;
                }


                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'tag_add');
                        _this.$store.commit('closePage', 'tag_add');
                        _this.$router.push({
                            name: 'tag_list'
                        });
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.tags = [];
                this.property = 0;
                this.id = -1;
                this.synonym_id = '';
                this.del_tags = [];
                this.errMsg = '';
                this.$store.commit('removeTag', 'tag_edit');
                this.$store.commit('closePage', 'tag_edit');
                this.$router.push({
                    name: 'tag_list'
                });
            }
        }
    };
</script>

<style>

</style>
