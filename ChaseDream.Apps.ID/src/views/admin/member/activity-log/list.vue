<style lang="less">
    @import '../../../../styles/common.less';
    @import '../member_activity_log.less';
</style>

<template>
    <div class="rename">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <h3>会员活动日志</h3>
                <br>
                <p align="right">共<span style="color:#19be6b"> {{total}} </span>条记录</p>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>
                    <can-edit-table refs="table" @on-delete="handleDel" v-model="Rows" @on-updata="getlist(1)" :columns-list="Columns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import log_Data from '../data/activity_log_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'member_activity_log_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                Columns: [],
                Rows:[],
                s_row:[{
                    uid: '',
                    fid: '',
                    tid: '',
                    action_id: -1
                }],
                s_columns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'member_activity_log_list'){
                    this.isAccess = true;
                    this.Columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.Columns = log_Data.bColumns;
                this.s_columns = log_Data.s_Columns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].uid){
                    s.push('uid=' + this.s_row[0].uid);
                }
                if(!!this.s_row[0].fid){
                    s.push('fid=' + this.s_row[0].fid);
                }
                if(!!this.s_row[0].tid){
                    s.push('tid=' + this.s_row[0].tid);
                }
                if(this.s_row[0].action_id >= 0){
                    s.push('action_id=' + this.s_row[0].action_id);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }


                this.Rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/misc/member_activity_log' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.Rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            set_s_data (obj){
                this.s_row = obj;
            },
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
