<style lang="less">
    @import '../../../../styles/common.less';
    @import '../member_activity_log.less';
</style>

<template>
    <div class="tag-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加标签
                </p>
                <div v-if="tags.length > 0" style="margin-bottom: 20px;width: 80%;">
                    <div v-for="tag,index in tags" :key="tag.index" style="line-height: 24px;border: 1px dashed #efefef;padding: 5px 30px 5px 10px;margin-bottom: 10px;position: relative;cursor: pointer;">
                        <p style="" @click="edit_tag(index)">
                            <strong v-for="t in tag" :key="t.index" v-if="t.main > 0" style="color: #ff9900;margin-right: 10px;">{{t.name}}</strong>
                            <span v-if="t.main === 0" v-for="t in tag" :key="t.index" style="margin-right: 10px;">{{t.name}}</span>
                        </p>
                        <Icon type="close-circled" size="20" style="color: #ed3f14;position: absolute;right: 10px;top: 8px;cursor: pointer;" @click="del_tag(index)"></Icon>
                    </div>

                </div>
                <Form>
                    <FormItem v-for="tag,index in new_tags" :key="tag.index">
                        <Input v-model="tag.name" placeholder="请输标签名称" style="width:300px;margin-right: 20px;"></Input>
                        <Select v-model="property" v-if="!!tag.main" style="width:130px;margin-right: 20px;" placeholder="请选择标签属性...">
                            <Option :value="1">专业方向</Option>
                            <Option :value="2">学校名称</Option>
                            <Option :value="3">帖子类型</Option>
                            <Option :value="4">适用阶段</Option>
                        </Select>
                        <Checkbox v-model="tag.main" :true-value="1" :false-value="0" @on-change="setMain(index)">设为主标签</Checkbox>
                        <Icon type="close-circled" size="18" style="color: #ed3f14;cursor: pointer;margin-left: 20px;" @click="del_row(index)" v-if="new_tags.length > 1"></Icon>
                    </FormItem>
                    <FormItem>
                        <Button type="dashed" @click="add_row" style="border: 1px dashed #efefef;"><Icon type="ios-plus-empty" size="30"></Icon></Button>
                    </FormItem>
                    <FormItem>
                        <Button @click="add_tag" size="small" type="success">保存</Button>
                        <Button @click="clear_tag" size="small">重置</Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <br><br>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'tag_add',
        components: {

        },
        data () {
            return {
                tags: [],
                property: 0,
                new_tags: [
                    {
                        name: '',
                        main: 1,
                        property: 0
                    },
                    {
                        name: '',
                        main: 0,
                        property: 0
                    }
                ],
                edit: false,
                e_index: -1,
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'tag_add'){
                    this.tags = [];
                    this.property = 0;
                    this.new_tags = [
                        {
                            name: '',
                            main: 1,
                            property: 0
                        },
                        {
                            name: '',
                            main: 0,
                            property: 0
                        }
                    ];
                    this.edit = false;
                    this.e_index = -1;
                    this.errMsg = '';
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            add_row (){
                var item = {
                    name: '',
                    main: 0,
                    property: 0
                }
                this.new_tags.push(item);
            },
            del_row (index){
                this.new_tags.splice(index, 1);

                var isMain = false;
                var _this = this;
                if(!check_main()){
                    this.new_tags[0].main = 1;
                }
                function check_main() {
                    for(var i=0;i<_this.new_tags.length;i++){
                        if(_this.new_tags[i].main === 1){
                            return true;
                        }
                    }
                    return false;
                }
            },
            setMain (index){
                for(var i=0;i<this.new_tags.length;i++){
                    this.new_tags[i].main = 0;
                }
                this.new_tags[index].main = 1;
            },
            add_tag (){
                var tag_list = []
                for(var i=0;i<this.new_tags.length;i++){
                    if(this.new_tags[i].main === 1 && !this.new_tags[i].name){
                        this.$Notice.error({
                            title: '主标签名称不能为空！'
                        });
                        return false;
                    }else if(!this.property){
                        this.$Notice.error({
                            title: '请选择标签属性！'
                        });
                        return false;
                    }else if(!!this.new_tags[i].name && !!this.property){
                        this.new_tags[i].property = this.property;
                        tag_list.push(this.new_tags[i])
                    }
                }
                if(!tag_list.length){
                    this.$Notice.error({
                        title: '请输入标签！'
                    });
                    return false;
                }
                if(!this.edit){
                    this.tags.push(tag_list);
                    this.clear_tag();
                }else {
                    this.tags[this.e_index] = tag_list;
                    this.clear_tag();
                    this.edit = false;
                }
            },
            edit_tag (index){
                this.clear_tag();
                this.new_tags = JSON.parse(JSON.stringify(this.tags[index]));
                this.property = this.new_tags[0].property;
                this.edit = true;
                this.e_index = index;
            },
            del_tag (index){
                this.tags.splice(index, 1);
                if(!!this.edit){
                    this.edit = false;
                }
                this.clear_tag();
            },
            clear_tag (){
                this.property = 0;
                this.new_tags = [];
                this.new_tags = [
                    {
                        name: '',
                        main: 1,
                        property: 0
                    },
                    {
                        name: '',
                        main: 0,
                        property: 0
                    }
                ]
                this.edit = false;
            },
            handleSubmit () {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/tag',
                    method:'POST',
                    data: this.tags,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'tag_add');
                        _this.$store.commit('closePage', 'tag_add');
                        _this.$router.push({
                            name: 'tag_list'
                        });
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.tags = [];
                this.property = 0;
                this.new_tags = [
                    {
                        name: '',
                        main: 1,
                        property: 0
                    },
                    {
                        name: '',
                        main: 0,
                        property: 0
                    }
                ];
                this.edit = false;
                this.e_index = -1;
                this.errMsg = '';
                this.$store.commit('removeTag', 'tag_add');
                this.$router.push({
                    name: 'tag_list'
                });
            }
        }
    };
</script>

<style>

</style>
