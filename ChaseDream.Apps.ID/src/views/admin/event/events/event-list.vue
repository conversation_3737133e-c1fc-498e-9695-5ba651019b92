<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="event-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="edittable-con-1">
                    <Tabs value="name1">
                        <TabPane label="当前活动" name="name1">
                            <can-edit-table refs="dlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="elRows" :columns-list="elColumns"></can-edit-table>
                            <Page :total="el_total" :page-size="page_size" :current="el_current" @on-change="getEventlist" v-if="el_showPage"></Page>
                        </TabPane>
                        <TabPane label="已结束活动" name="name2">
                            <can-edit-table refs="tlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="dlRows" :columns-list="delColumns"></can-edit-table>
                            <div class="page-bar">
                                <Page :total="dl_total" :page-size="page_size" :current="dl_current" @on-change="getdellist" v-if="dl_showPage"></Page>
                            </div>
                        </TabPane>
                    </Tabs>
                </div>

            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import eData from '../data/event_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'event_list',
        components: {
            canEditTable
        },
        data () {
            return {
                elRows: [],
                dlRows: [],
                elColumns: [],
                delColumns: [],
                el_total: 0,
                dl_total: 0,
                el_showPage: false,
                dl_showPage: false,
                page_size: 20,
                el_current: 1,
                dl_current: 1,
                isAccess: true,
                keyword: '',
                s_key: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'event_list'){
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.elColumns = eData.elColumns;
                this.delColumns = eData.delColumns;
                this.getEventlist(1);
                this.getdellist(1);
            },
            getEventlist (n){
                this.elRows = [];
                this.el_total = 0;
                this.el_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/calendar',
                    method:'GET',
                    params: {
                        page: _this.el_current,
                        page_size: _this.page_size,
                        status: 0
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.elRows = res.data.data.rows;
                        for(var i=0;i<_this.elRows.length;i++){
                            if(!!_this.elRows[i].event_begin_date){
                                _this.elRows[i].event_begin_date = new Date(parseInt(_this.elRows[i].event_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.elRows[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.elRows[i].event_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.elRows[i].event_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.elRows[i].event_begin_date + '000')).getMinutes());
                            }else {
                                _this.elRows[i].event_begin_date = '';
                            }
                            if(!!_this.elRows[i].event_end_date){
                                _this.elRows[i].event_end_date = new Date(parseInt(_this.elRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.elRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.elRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.elRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.elRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.elRows[i].event_end_date = '';
                            }
                            if(!!_this.elRows[i].push_begin_date){
                                _this.elRows[i].push_begin_date = new Date(parseInt(_this.elRows[i].push_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.elRows[i].push_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.elRows[i].push_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.elRows[i].push_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.elRows[i].push_begin_date + '000')).getMinutes());
                            }else {
                                _this.elRows[i].push_begin_date = '';
                            }
                            if(!!_this.elRows[i].push_end_date){
                                _this.elRows[i].push_end_date = new Date(parseInt(_this.elRows[i].push_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.elRows[i].push_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.elRows[i].push_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.elRows[i].push_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.elRows[i].push_end_date + '000')).getMinutes());
                            }else {
                                _this.elRows[i].push_end_date = '';
                            }
                            if(!!_this.elRows[i].new_flag_date){
                                _this.elRows[i].new_flag_date = new Date(parseInt(_this.elRows[i].new_flag_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.elRows[i].new_flag_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.elRows[i].new_flag_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.elRows[i].new_flag_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.elRows[i].new_flag_date + '000')).getMinutes());
                            }else {
                                _this.elRows[i].push_end_date = '';
                            }

                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }
                        _this.el_total = res.data.data.count;
                        if(_this.el_total > _this.page_size){
                            _this.el_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getdellist (n){
                this.dlRows = [];
                this.dl_total = 0;
                this.dl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/calendar',
                    method:'GET',
                    params: {
                        page: _this.dl_current,
                        page_size: _this.page_size,
                        //type: 1,
                        status: -1
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.dlRows = res.data.data.rows;
                        for(var i=0;i<_this.dlRows.length;i++){
                            if(!!_this.dlRows[i].event_begin_date){
                                _this.dlRows[i].event_begin_date = new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].event_begin_date = '';
                            }
                            if(!!_this.dlRows[i].event_end_date){
                                _this.dlRows[i].event_end_date = new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].event_end_date = '';
                            }
                            if(!!_this.dlRows[i].push_begin_date){
                                _this.dlRows[i].push_begin_date = new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].push_begin_date = '';
                            }
                            if(!!_this.dlRows[i].push_end_date){
                                _this.dlRows[i].push_end_date = new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].push_end_date = '';
                            }

                            if(!!_this.dlRows[i].new_flag_date ){
                                _this.dlRows[i].new_flag_date = new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].new_flag_date = 0;
                            }
                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }
                        _this.dl_total = res.data.data.count;
                        if(_this.dl_total > _this.page_size){
                            _this.dl_showPage = true;
                        }
                    }else{
                        console.log(res.data.msg)
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err.msg)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
                this.getEventlist(1);
                this.getdellist(1);
            },
            handleUpdata (){
                this.getData();
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
