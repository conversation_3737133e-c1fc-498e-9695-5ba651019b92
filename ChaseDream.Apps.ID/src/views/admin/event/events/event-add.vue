<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="headlines-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加 推0
                </p>
                <Form ref="form" :model="form" :rules="rules" :label-width="90">
                    <FormItem prop="subject" label="标题：">
                        <Input v-model="form.subject" placeholder="请输入标题"></Input>
                    </FormItem>
                    <FormItem prop="url" label="URL：">
                        <Input v-model="form.url" placeholder="请输入URL"></Input>
                    </FormItem>
                    <FormItem prop="color" label="颜色：">
                        <RadioGroup v-model="form.color">
                            <Radio label="green">绿色</Radio>
                            <Radio label="blue">蓝色</Radio>
                            <Radio label="red">红色</Radio>
                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'headlines_add',
        components: {

        },
        data () {
            return {
                form: {
                    subject: '',
                    url: '',
                    color: ''
                },
                rules: {
                    subject: [
                        { required: true, message: '标题不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '标题不能小于3个字符', trigger: 'blur' }
                    ],
                    url: [
                        { required: true, message: 'URL不能为空', trigger: 'blur', type: 'string',}
                    ],
                    color: [
                        { required: true, message: '请选择颜色', trigger: 'change', type: 'color'}

                    ]
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'headlines_add'){
                    this.form.subject = '';
                    this.form.url = '';
                    this.form.color = '';
                    this.$refs['form'].resetFields();
                }
            }
        },
        mounted () {

        },
        methods: {
            handleSubmit () {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        var dataIn = {
                            subject: this.form.subject,
                            url: this.form.url,
                            color: this.form.color,
                            type: 3
                        };
                        if(dataIn.subject.length > 120){
                            this.$Notice.warning({
                                title: '活动标题不能大于120个字符'
                            });
                            return false
                        }

                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/event/release',
                            method:'POST',
                            data: dataIn,
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                console.log(res.data)
                                _this.$store.commit('removeTag', 'headlines_add');
                                _this.$store.commit('closePage', 'headlines_add');
                                _this.$router.push({
                                    name: 'headlines_list'
                                });
                            }else{
                                _this.errMsg = res.msg;
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.errMsg = err.msg;
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.form.subject = '';
                this.form.url = '';
                this.form.color = '';
                this.$store.commit('removeTag', 'headlines_add');
                this.$router.push({
                    name: 'headlines_list'
                });
            }
        },
        created () {

        }
    };
</script>