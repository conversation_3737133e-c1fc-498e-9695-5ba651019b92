<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="event-edit" style="width: 100%;">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    编 辑
                </p>
                <Form :label-width="140">
                    <FormItem label="机构：" class="ivu-form-item-required">
                        <Select v-model="form.school_id" placeholder="请选择学校" filterable disabled>
                            <Option :value="item.id" :label="item.display_name" v-for="item in schools" :key="item.index">
                                <span>{{item.display_name}}</span>
                                <span style="float:right;color:#ccc">{{item.country}}</span>
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="WWW URL：" class="ivu-form-item-required">
                        <Input v-model="form.url1" placeholder="请输入WWW地址"></Input>
                    </FormItem>
                    <FormItem label="论坛 URL：" class="ivu-form-item-required">
                        <Input v-model="form.url2" placeholder="请输入Forum地址"></Input>
                    </FormItem>
                    <div class="geos" v-for="item,index in location" :key="item.index" style="padding: 10px 10px 10px 0;border: 1px solid #efefef;border-radius: 5px;">
                        <FormItem label="活动地区：">
                            <div class="hd">
                                <strong style="width: 80px; display: inline-block">城市</strong>
                            </div>
                            <div>
                                <Checkbox v-model="item.checked">
                                    <span style="display: inline-block;width: 50px;">{{item.city_name}}</span>
                                </Checkbox>
                            </div>
                        </FormItem>
                        <FormItem label="专业：" class="ivu-form-item-required">
                            <CheckboxGroup v-model="item.major">
                                <Checkbox :label="major.id" v-for="major in majors" disabled :key="major.index">{{major.name}}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <FormItem label="活动类型：" class="ivu-form-item-required">
                            <CheckboxGroup v-model="item.event_type">
                                <Checkbox :label="type.id" v-for="type in types" disabled :key="type.index">{{type.name}}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <FormItem label="活动标题：" class="ivu-form-item-required">
                            <Input v-model="item.subject" placeholder="请输入活动标题"></Input>
                        </FormItem>
                        <div class="hd" style="padding-left: 140px;">
                            <strong style="width: 275px;display: inline-block">活动开始时间</strong>
                            <strong style="width: 275px;display: inline-block">活动结束时间</strong>
                        </div>
                        <Row>
                            <Col span="9">
                            <FormItem class="begin_data" label="活动时间：">
                                <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="item.begin_date" placeholder="请选择活动开始时间" style="width: 100%" readonly></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="6">
                            <FormItem class="end_date" style="margin-left: 0">
                                <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="item.end_date" placeholder="请选择活动结束时间" style="width: 100%" readonly></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="3" v-if="item.update_date">
                                <Button type="primary" style="margin-left: 20px;background: #fff;color: #2d8cf0;border: 1px solid #2d8cf0;height: 40px;" @click="change_date(index)">更新活动时间</Button>
                            </Col>
                        </Row>
                        <div class="hd" style="padding-left: 140px;">
                            <strong style="width: 275px;display: inline-block">推送开始时间</strong>
                            <strong style="width: 275px;display: inline-block">推送结束时间</strong>
                        </div>
                        <Row>
                            <Col span="9">
                            <FormItem class="begin_data" label="推送时间：">
                                <DatePicker type="datetime" :clearable="false" :readonly="item.fold" :disabled="item.fold" format="yyyy-MM-dd HH:mm" v-model="item.push_begin_date" placeholder="请选择推送开始时间" @on-change="setPushDate(item,'begin')" :options="item.options1" :time-picker-options="{steps: [1, 15, 15]}" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="6">
                            <FormItem class="end_date" style="">
                                <DatePicker type="datetime" :clearable="false" :readonly="item.fold" :disabled="item.fold" format="yyyy-MM-dd HH:mm" v-model="item.push_end_date" placeholder="请选择推送结束时间" @on-change="setPushDate(item,'end')" :options="item.options2" :time-picker-options="{steps: [1, 15, 15]}" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="width: 30px;height: 40px;">

                            </Col>
                            <Col span="3">
                            <Checkbox v-model="item.fold" style="line-height: 30px;">
                                <span style="display: inline-block;width: 50px;">折叠</span>
                            </Checkbox>
                            </Col>
                        </Row>
                        <FormItem label="最新标签下线时间：" class="ivu-form-item-required">
                            <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="item.new_flag_date" placeholder="请选择显示最新的时间" @on-change="setPushDate(item,'flag')" :options="item.options3" style="width: 235px;"></DatePicker>
                        </FormItem>
                        <FormItem label="活动排序：" class="ivu-form-item-required">
                            <RadioGroup v-model="item.position">
                                <Radio :label="1">置顶</Radio>
                                <Radio :label="0">正常</Radio>
                                <Radio :label="-1">沉底</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="上传图片：">
                            <Upload action="/api/v1/admin/event/upload"
                                    :before-upload="uploadImg"
                                    :show-upload-list="true"
                                    v-if="item.files.length === 0"

                            >
                                <Button type="ghost" icon="plus" @click="setIndex(index)"></Button>
                            </Upload>
                            <div class="file-list" v-if="item.files.length > 0">
                                <div v-for="file in item.files">
                                    <img :src="file.src" alt="">
                                    <p class="del">
                                        <Icon type="trash-a" @click="delImg(item)" size="22">删除</Icon>
                                    </p>
                                </div>
                            </div>
                        </FormItem>
                        <Icon type="trash-a" size="24" class="delItem" @click="delRow(index)" v-if="location.length > 1"></Icon>
                    </div>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
                <Modal
                        title="提示"
                        v-model="isPush"
                        class-name="" width="300">
                    <div style="display: flex">
                        <Icon type="information-circled" size="35" style="color: #5cadff"></Icon>
                        <div style="padding-left: 20px;font-size: 14px;line-height: 24px;text-align: center;">
                            <p>核心活动内容新增<strong style="color: #19be6b"> {{newData}} </strong>条新地址信息</p>
                            <p>是否增加到推0内容中</p>
                        </div>
                    </div>
                    <div slot="footer">
                        <Button type="text" size="large" @click="isPush = false">取消</Button>
                        <Button type="info" size="large" @click="pushAll" v-if="!loading">确定</Button>
                        <Button type="info" size="large" v-if="loading">
                            确定
                            <Spin fix>
                                <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
                                <div>加载中...</div>
                            </Spin>
                        </Button>
                    </div>
                </Modal>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'event_edit',
        components: {

        },
        data () {
            return {
                form: {
                    school_id: '',
                    url1: '',
                    url2: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                schools: [],
                location: [],
                majors:[],
                event_id: 0,
                I_index: -1,
                n_index: 0,
                isPush: false,
                newData: 0,
                subject: '',
                school_logo: '',
                isWin: false,
                loading: false,
                core: []
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'event_edit'){
                    this.form = {};
                    this.event_id = 0;
                    this.location = [];
                    this.schools = [];
                    this.majors = [];
                    this.I_index = -1;
                    this.n_index = 0,
                    this.isPush = false;
                    this.newData = 0;
                    this.isWin = false;
                    this.subject = '';
                    this.core = [];

                    this.event_id = window.location.href.split('/').pop();
                    if(this.event_id === 'core-list'){
                        this.isWin = true;
                    }
                    this.getData();
                }
            }
        },
        mounted () {
            this.isWin = false;
            this.event_id = window.location.href.split('/').pop();
            if(this.event_id === 'core-list'){
                this.isWin = true;
            }
            this.getData();
        },
        methods: {
            getData (){
                var rowData = {};
                rowData = this.$store.state.event.currentRow;
                this.form.school_id = rowData.school_id;
                this.form.event_id = rowData.event_id;
                this.form.subject = rowData.subject;
                this.form.url1 = rowData.url1;
                this.form.url2 = rowData.url2;
                this.event_id = this.form.event_id;

                this.getSchool();
                this.getTypes();
                this.getMajor();
                this.getCalendar();
            },
            getSubject (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/core/' + this.event_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.subject = res.data.data.subject;
                        _this.core = res.data.data.event_location;
                        for(var i=0;i<_this.location.length;i++){
                            if(!_this.location[i].subject){
                                _this.location[i].subject = _this.subject;

                            }
                            for(var j=0;j<_this.core.length;j++){
                                if(_this.location[i].lid === _this.core[j].id){
                                    if(_this.location[i].event_begin_date !== _this.core[j].begin_date || _this.location[i].event_end_date !== _this.core[j].end_date){
                                        _this.location[i].update_date = true;
                                    }else {
                                        _this.location[i].update_date = false;
                                    }
                                }
                            }
                        }
                        if(!!res.data.data.event_school && !!res.data.data.event_school.logo_url){
                            _this.school_logo = res.data.data.event_school.logo_url
                        }else {
                            _this.$Notice.error({
                                title: '当前学校LOGO错误，请到学校模块设置当前学校LOGO'
                            });
                        }

                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getCalendar(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/calendar/' + this.event_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success') {
                        _this.locations = res.data.data.locations;
                        if(res.data.data.events.length < 1){
                            _this.getSubject();
                        }
                        if(!_this.form.url1 && res.data.data.events.length > 0){
                            _this.form.url1 = res.data.data.events[0].url1;
                        }
                        if(!_this.form.url2 && res.data.data.events.length > 0){
                            _this.form.url2 = res.data.data.events[0].url2;
                        }
                        for(var i=0;i<res.data.data.events.length;i++){
                            var item = {};
                            item.id = res.data.data.events[i].id;
                            item.lid = res.data.data.events[i].lid;
                    item.country_id = !!res.data.data.events[i].event_location ? res.data.data.events[i].event_location.country_id : '';
                            item.province_id = !!res.data.data.events[i].event_location ? res.data.data.events[i].event_location.province_id : '';
                            item.city_id = !!res.data.data.events[i].event_location ? res.data.data.events[i].event_location.city_id : '';
                            item.city_name = !!res.data.data.events[i].event_geo ? res.data.data.events[i].event_geo.name : '';
                            item.event_school_major = res.data.data.events[i].event_school_major;
                            item.major =[];
                            for(var j=0;j<res.data.data.events[i].event_school_major.length;j++){
                                item.major.push(res.data.data.events[i].event_school_major.school_major_id);
                            }
                            item.event_type = [];
                            for(var j=0;j<res.data.data.events[i].event_type.length;j++){
                                item.event_type.push(res.data.data.events[i].event_type.event_type_id);
                            }
                            item.subject = res.data.data.events[i].subject;
                            item.image = res.data.data.events[i].image;
                            item.event_id = res.data.data.events[i].event_id;
                            item.checked = true;
                            item.position = !!res.data.data.events[i].position ? res.data.data.events[i].position : 0;
                            item.files = [];
                            item.update_date = true;
                            if(!!item.image ){
                                item.files[0] = {};
                                item.files[0].src = item.image;
                            }

                            if(!!res.data.data.events[i].event_begin_date){
                                item.event_begin_date = res.data.data.events[i].event_begin_date;
                                item.begin_date = new Date(parseInt(res.data.data.events[i].event_begin_date + '000'));
                            }else {
                                item.event_begin_date = '';
                                item.begin_date = '';
                            }
                            if(!!res.data.data.events[i].event_end_date){
                                item.event_end_date = res.data.data.events[i].event_end_date
                                item.end_date = new Date(parseInt(res.data.data.events[i].event_end_date + '000'));
                            }else {
                                item.event_end_date = '';
                                item.end_date = '';
                            }

                            if(!!res.data.data.events[i].push_begin_date){
                                item.push_begin_date = new Date(parseInt(res.data.data.events[i].push_begin_date + '000'));
                            }else {
                                item.push_begin_date= new Date();
                            }
                            if(!!res.data.data.events[i].push_end_date){
                                item.push_end_date = new Date(parseInt(res.data.data.events[i].push_end_date + '000'));
                            }else {
                                if(item.push_begin_date.getTime() <= item.begin_date){
                                    if(parseInt(res.data.data.events[i].event_begin_date + '000') + 1000*60*15 >= parseInt(res.data.data.events[i].event_end_date + '000')){
                                        item.push_end_date = item.end_date
                                    }else {
                                        item.push_end_date = new Date(parseInt(res.data.data.events[i].event_begin_date + '000') + 1000*60*15);
                                    }
                                }else {
                                    item.push_end_date = new Date(item.push_begin_date.getTime() + 1000*60*15);
                                }
                            }

                            if(!!res.data.data.events[i].new_flag_date){
                                item.new_flag_date = new Date(parseInt(res.data.data.events[i].new_flag_date + '000'));
                            }else {
                                if(item.push_begin_date.getTime() + 1000*60*60*24*3 < item.push_end_date.getTime()){
                                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*24*3);
                                }else {
                                    item.new_flag_date = item.push_end_date
                                }
                            }

                            if(!!res.data.data.events[i].fold){
                                item.fold = res.data.data.events[i].fold;
                            }else {
                                item.fold = false;
                            }
                            _this.setOptions(item);
                        }

                        setTimeout(function () {
                            if(res.data.data.events.length > 0){
                                _this.getSubject();
                            }
                            if(_this.location.length < 1){
                                _this.pushAll();
                            }else if(_this.locations.length > _this.location.length){
                                _this.newData = _this.locations.length - _this.location.length;
                                _this.isPush = true;
                            }
                        },1300)
                    }else{

                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            setOptions(obj){
                var _this = this;
                setTimeout(function () {
                    obj.options1 = {
                        disabledDate (date) {
                            return date && date.valueOf() > obj.push_end_date.getTime();
                        }
                    }
                    obj.options2 = {
                        disabledDate (date) {
                            return date && date.valueOf() < obj.push_begin_date.getTime()-1000*60*60*24 || date.valueOf() > obj.end_date.getTime()
                        }
                    }
                    obj.options3 = {
                        disabledDate (date) {
                            return date && date.valueOf() > obj.push_end_date.getTime() || date.valueOf() < obj.push_begin_date.getTime() -1000*60*60*24
                        }
                    }
                    _this.location.push(obj);
                    _this.isPush = false;
                },1000)
            },
            getSchool (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.schools = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getMajor (){  //  根据学校id获取专业
                var _this = this;
                util.ajax({
                    url: 'api/v1/admin/event/school/major/' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        var majors = [];
                        for(var i=0;i<res.data.data.length;i++){
                            var item = {};
                            item.id = res.data.data[i].id;
                            item.name = res.data.data[i].short;
                            item.major_id = res.data.data[i].major_id;
                            majors.push(item);
                        }
                        _this.majors = majors;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getTypes (){  // 获取活动类型
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/type',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.types = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getProvinceName (id, obj,n){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo/'+ id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        obj[n].city_name = res.data.data.name;
                        _this.setOptions(obj[n]);
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                        console.log(res.msg)
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            setPushDate (item,str){
                if(str === 'begin'){
                    if(item.push_begin_date.getTime() >= item.end_date.getTime()){
                        this.$Notice.error({
                            title: '推送开始时间不能大于活动结束时间！'
                        });
                        item.push_begin_date = new Date(item.end_date.getTime() - 1000*60*15);
                    }
                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*72);
                    if(item.new_flag_date.getTime() >= item.push_end_date.getTime()){
                        item.new_flag_date = item.push_end_date;
                    }
                    item.options2 = {
                        disabledDate (date) {
                            return date && date.valueOf() < item.push_begin_date.getTime() || date.valueOf() > item.end_date.getTime()
                        }
                    }
                    item.options3 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }
                }
                if(str === 'end'){
                    if(item.push_end_date.getTime() >= item.end_date.getTime()){
                        this.$Notice.error({
                            title: '推送结束时间不能大于活动结束时间！'
                        });
                        item.push_end_date = new Date(item.end_date.getTime() - 1000*60*15);
                    }else if(item.push_end_date.getTime() <= item.push_begin_date.getTime()){
                        this.$Notice.error({
                            title: '推送结束时间不能大于推送开始时间！'
                        });
                        item.push_end_date = new Date(item.push_begin_date.getTime() + 1000*60*15);
                    }
                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*72);
                    if(item.new_flag_date.getTime() > item.push_end_date.getTime()){
                        item.new_flag_date = item.push_end_date
                    }

                    item.options1 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime();
                        }
                    }
                    item.options3 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }
                }
                if(str === 'flag'){
                    if(item.new_flag_date.getTime() >= item.push_end_date.getTime()){
                        this.$Notice.error({
                            title: '最新标签下线时间不能大于推送结束时间！'
                        });
                        item.new_flag_date = item.push_end_date;
                    }
                }
            },
            pushAll(){
                this.loading = true;
                var _this = this;
                var newArr = [];
                for(var i=0;i<this.locations.length;i++){
                    if(isIN(this.locations[i].id)){
                        this.locations[i].subject = this.locations[i].subject ? this.locations[i].subject : this.subject; //this.subject;
                        this.locations[i].location_id = !!this.locations[i].city_id ? this.locations[i].city_id : this.locations[i].province_id;
                        this.locations[i].checked = true;
                        this.locations[i].major = this.locations[i].event_school_major;
                        if(!!this.locations[i].begin_date){
                            this.locations[i].begin_date = new Date(parseInt(this.locations[i].begin_date + '000'));
                        }else {
                            this.locations[i].begin_date = 0;
                        }
                        if(!!this.locations[i].end_date){
                            this.locations[i].end_date = new Date(parseInt(this.locations[i].end_date + '000'));
                        }else {
                            this.locations[i].end_date = 0;
                        }

                        if(!!this.locations[i].push_begin_date){
                            this.locations[i].push_begin_date = new Date(parseInt(this.locations[i].push_begin_date + '000'));
                        }else {
                            this.locations[i].push_begin_date= new Date();
                        }
                        if(!!this.locations[i].push_end_date){
                            this.locations[i].push_end_date = new Date(parseInt(this.locations[i].push_end_date + '000'));
                        }else {
                            if(!!this.locations[i].begin_date){
                                this.locations[i].push_end_date = new Date(this.locations[i].begin_date.getTime() + 1000*60*15);
                            }else {
                                this.locations[i].push_end_date = 0;
                            }
                        }

                        if(!!this.locations[i].new_flag_date){
                            this.locations[i].new_flag_date = new Date(parseInt(this.locations[i].new_flag_date + '000'));
                        }else {
                            if(!!this.locations[i].push_begin_date){
                                this.locations[i].new_flag_date = new Date(this.locations[i].push_begin_date.getTime() + 1000*60*60*24*3);
                            }else {
                                this.locations[i].new_flag_date = 0;
                            }
                        }
                        if(!this.locations[i].position){
                            this.locations[i].position = 0;
                        }
                        this.locations[i].files = [];
                        newArr.push(this.locations[i]);
                    }
                }

                function isIN(id) {
                    for(var j=0;j<_this.location.length;j++){
                        if(id === _this.location[j].lid){
                            return false;
                        }
                    }
                    return true;
                }
                for(var i=0;i<newArr.length;i++){
                    this.getProvinceName(newArr[i].location_id,newArr,i);
                }
            },
            uploadImg (file){
                var imgform = new FormData();
                imgform.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/upload?school_id='+ this.form.school_id +'&type=0',
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: imgform
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        if(_this.I_index !== -1){
                            _this.location[_this.I_index].files.push({
                                id: res.data.data.aid,
                                name: file.name,
                                src: 'https://static.chasedream.com' + res.data.data.fullpath
                            })
                            _this.I_index = -1;
                        }else {
                            _this.files.push({
                                id: res.data.data.aid,
                                name: file.name,
                                src: 'https://static.chasedream.com' + res.data.data.fullpath
                            })
                        }

                    }else{
                        console.log(res)
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            setIndex(i){
                this.I_index = i;
            },
            delImg (obj){
                this.$set(obj,'files',[]);
            },
            handleSubmit () {
                var dataIn = {
                    id: this.id,
                    school_id: this.form.school_id,
                    url1: this.form.url1 ? this.form.url1 : '',
                    url2: this.form.url2 ? this.form.url2 : '',
                };
                if(!dataIn.url1){
                    this.$Notice.error({
                        title: '请填写WWW地址！'
                    });
                    return false;
                }
                if(!dataIn.url2){
                    this.$Notice.error({
                        title: '请填写Forum地址！'
                    });
                    return false;
                }
                for(var i=0;i<this.location.length;i++){
                    if(!!this.location[i].checked && !this.location[i].subject){
                        this.$Notice.error({
                            title: '请填写活动标题！'
                        });
                        return false;
                    }else if(!!this.location[i].checked && (this.location[i].subject.length < 3 || this.location[i].subject.length > 120)){
                        this.$Notice.error({
                            title: '活动标题字数不能小于3或大于120！'
                        });
                        return false;
                    }
                }

                for(var i=0;i<this.location.length;i++){
                    if(!!this.location[i].checked){
                        if(this.location[i].files.length === 0 || !this.location[i].files[0].src){
                            if(!this.school_logo){
                                this.$Notice.error({
                                    title: '当前学校LOGO错误，请到学校模块设置当前学校LOGO'
                                });
                                return false;
                            }
                        }
                        if(!!this.location[i].lid){
                            var item = {
                                school_id: dataIn.school_id,
                                url1: dataIn.url1,
                                url2: dataIn.url2,
                                major_id: 0,
                                id: this.location[i].id,
                                event_id: this.location[i].event_id,
                                subject: this.location[i].subject,
                                event_begin_date: Math.round(this.location[i].begin_date.getTime()/1000),
                                event_end_date: Math.round(this.location[i].end_date.getTime()/1000),
                                fold: !!this.location[i].fold ? 1 : 0,
                                push_begin_date: !this.location[i].fold ? Math.round(this.location[i].push_begin_date.getTime()/1000) : 0,
                                push_end_date: !this.location[i].fold ? Math.round(this.location[i].push_end_date.getTime()/1000) : 0,
                                new_flag_date: Math.round(this.location[i].new_flag_date.getTime()/1000),
                                position: this.location[i].position,
                                image: this.location[i].files[0] ? this.location[i].files[0].src : this.school_logo
                            }
                            this.submitAjax(item,1,'PUT');
                        }else {
                            var item = {
                                school_id: dataIn.school_id,
                                url1: dataIn.url1,
                                url2: dataIn.url2,
                                fold: !!this.location[i].fold ? 1 : 0,
                                event_id: this.location[i].event_id,
                                subject: this.location[i].subject,
                                new_flag_date: Math.round(this.location[i].new_flag_date.getTime()/1000),
                                location: [{
                                    lid: this.location[i].id,
                                    location_id: !!this.location[i].city_id ? this.location[i].city_id : this.location[i].province_id,
                                    event_begin_date: Math.round(this.location[i].begin_date.getTime()/1000),
                                    event_end_date: Math.round(this.location[i].end_date.getTime()/1000),
                                    push_begin_date: !this.location[i].fold ? Math.round(this.location[i].push_begin_date.getTime()/1000) : 0,
                                    push_end_date: !this.location[i].fold ? Math.round(this.location[i].push_end_date.getTime()/1000) : 0,
                                    position: this.location[i].position,
                                    image: this.location[i].files[0] ? this.location[i].files[0].src : this.school_logo
                                }]
                            }
                            this.submitAjax(item,1,'POST');
                        }
                    }
                }
            },
            submitAjax (dataIn,n,method){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/calendar',
                    method:method,
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.n_index++;
                        if(_this.n_index >= _this.location.length){
                            _this.$Notice.success({
                                title: '提交成功！'
                            });
                            _this.$store.commit('removeTag', 'event_edit');
                            _this.$store.commit('closePage', 'event_edit');
                            if(!!_this.isWin){
                                _this.$emit('on-close');
                            }else{
                                _this.$router.push({
                                    name: 'event_list'
                                });
                            }
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            delRow (i){
                if(!!this.location[i].lid){
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/event/calendar',
                        method:'DELETE',
                        data: {
                            id: this.location[i].id,
                            event_id: this.location[i].event_id
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.location.splice(i, 1);
                        }else{

                        }
                    }).catch(function (err) {
                        console.log(err)
                    });
                }else {
                    this.location.splice(i, 1);
                }
            },
            change_date(index){
                for(var i=0;i<this.core.length;i++){
                    if(this.core[i].id === this.location[index].lid){
                        this.location[index].begin_date = new Date(parseInt(this.core[i].begin_date + '000'));
                        this.location[index].end_date = new Date(parseInt(this.core[i].end_date + '000'));
                    }
                }
            },
            closePage () {
                this.form = {};
                this.event_id = 0;
                this.schools = [];
                this.location = [];
                this.majors = [];
                this.I_index = -1;
                this.n_index = 0,
                this.isPush = false;
                this.newData = 0;
                this.core = [];
                if(!!this.isWin){
                    this.$emit('on-close');
                }else {
                    this.$store.commit('removeTag', 'event_edit');
                    this.$router.push({
                        name: 'event_list'
                    });
                }
            }
        },
        created () {

        }
    };
</script>