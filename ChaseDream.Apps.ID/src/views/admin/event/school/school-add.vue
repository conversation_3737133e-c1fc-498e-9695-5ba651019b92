<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="school-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加学校
                </p>
                <Form ref="form" :model="form" :label-width="150">
                    <FormItem label="文件目录：" class="ivu-form-item-required">
                        <Input v-model="form.directory" placeholder="请输入文件目录"></Input>
                    </FormItem>
                    <FormItem label="商学院名称（英）：" class="ivu-form-item-required">
                        <Input v-model="form.school_name" placeholder="请输入商学院名称（英）"></Input>
                    </FormItem>
                    <FormItem label="商学院名称（中）：" class="ivu-form-item-required">
                        <Input v-model="form.school_name_cn" placeholder="请输入商学院名称（中）"></Input>
                    </FormItem>
                    <FormItem label="大学名称（英）：" class="ivu-form-item-required">
                        <Input v-model="form.university_name" placeholder="请输入大学名称（英）"></Input>
                    </FormItem>
                    <FormItem label="大学名称（中）：" class="ivu-form-item-required">
                        <Input v-model="form.university_name_cn" placeholder="请输入大学名称（中）"></Input>
                    </FormItem>
                    <FormItem label="显示全称：" class="ivu-form-item-required">
                        <Input v-model="form.display_name" placeholder="请输入全称"></Input>
                    </FormItem>
                    <FormItem label="显示简称：" class="ivu-form-item-required">
                        <Input v-model="form.short" placeholder="请输入简称"></Input>
                    </FormItem>
                    <FormItem label="LOGO：" class="ivu-form-item-required">
                        <Upload action="/api/v1/admin/event/upload"
                                :before-upload="checkImg"
                                :show-upload-list="true"
                                v-if="files.length === 0"
                        >
                            <Button type="ghost" icon="plus"></Button>
                        </Upload>
                        <div class="file-list" v-if="files.length > 0">
                            <div v-for="file in files">
                                <img :src="file.src" alt="">
                                <p class="del">
                                    <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                </p>
                            </div>
                        </div>
                    </FormItem>
                    <FormItem label="关键词：" class="ivu-form-item-required">
                        <Input v-model="form.keyword" placeholder="请输入关键词"></Input>
                    </FormItem>
                    <FormItem label="商学院首页：" class="ivu-form-item-required">
                        <Input v-model="form.website" placeholder="请输入商学院首页URL"></Input>
                    </FormItem>
                    <FormItem label="所属地区：" class="ivu-form-item-required">
                        <Select v-model="form.area" placeholder="请选择所属地区" style="width: 400px;">
                            <Option value="北美洲">北美洲</Option>
                            <Option value="南美洲">南美洲</Option>
                            <Option value="亚洲">亚洲</Option>
                            <Option value="欧洲">欧洲</Option>
                            <Option value="大洋洲">大洋洲</Option>
                            <Option value="非洲">非洲</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="所属国家：" class="ivu-form-item-required">
                        <Select v-model="form.country" filterable placeholder="请选择所属国家" style="width: 400px;">
                            <Option v-for="country in countrys" :value="country.name" :key="country.index">{{ country.name }}</Option>
                        </Select>
                    </FormItem>

                    <div class="majors">
                        <h3 style="border-bottom: 1px solid #efefef;margin-bottom: 10px;padding-left: 90px;">专业</h3>
                        <div class="major" v-for="major,index in majors" style="position: relative">
                            <FormItem label="专业全称（英）：" class="ivu-form-item-required">
                                <Input v-model="major.fullname_english" placeholder="请输入专业全称（英）" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="专业全称（中）：" class="ivu-form-item-required">
                                <Input v-model="major.fullname_chinese" placeholder="请输入专业全称（中）" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="专业简称：" class="ivu-form-item-required">
                                <Input v-model="major.short" placeholder="请输入专业简称" style="width: 400px;"></Input>
                            </FormItem>
                            <FormItem label="所属分类：" class="ivu-form-item-required">
                                <div v-for="item in majorList" :key="item.index" style="position: relative">
                                    <span style="padding-right: 10px;position: absolute;left: 0;top: 0;">{{item.g}}</span>
                                    <RadioGroup v-model="major.major_id" style="padding-left: 50px;" >
                                        <Radio :label="opt.id" v-for="opt in item.names" :key="opt.index" v-if="!!opt">{{opt.display_name}}</Radio>
                                    </RadioGroup>
                                </div>
                            </FormItem>
                            <FormItem label="" v-if="index !== majors.length-1">
                                <div style="width: 100%;height: 1px;background: #efefef;"></div>
                            </FormItem>
                            <Icon type="trash-a" size="24" class="delItem" style="position: absolute;right: 350px;top: 50%;margin-top: -12px;color: #ed3f14;cursor: pointer" @click="delRow(index)" v-if="majors.length > 1"></Icon>
                        </div>
                    </div>
                    <FormItem>
                        <Button type="dashed" icon="plus" class="plusItem" @click="addRow"></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'school_add',
        components: {

        },
        data () {
            return {
                form: {
                    directory: '',
                    university_name: '',
                    university_name_cn: '',
                    school_name: '',
                    school_name_cn: '',
                    display_name: '',
                    short: '',
                    logo_url: '',
                    keyword: '',
                    website: '',
                    area: '',
                    country: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                countrys: [],
                majors: [
                    {
                        fullname_english: '',
                        fullname_chinese: '',
                        short: '',
                        major_id: 0
                    }
                ],
                majorList: [],
                I_index: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'school_add'){
                    this.form.directory = '';
                    this.university_name = '';
                    this.university_name_cn = '';
                    this.school_name = '';
                    this.school_name_cn = '';
                    this.display_name = '';
                    this.short = '';
                    this.logo_url = '';
                    this.keyword = '';
                    this.website = '';
                    this.area = '';
                    this.country = '';
                    this.majors = [
                        {
                            fullname_english: '',
                            fullname_chinese: '',
                            short: '',
                            major_id: 0
                        }
                    ];

                    this.files = [];
                    this.I_index = 0;
                    this.$refs['form'].resetFields();
                    this.getMajors();
                    this.getGeo();
                }
            }
        },
        mounted () {
            this.getMajors();
            this.getGeo();
        },
        methods: {
            addRow (){  // 添加一行活地点
                var item = {
                    fullname_english: '',
                    fullname_chinese: '',
                    short: '',
                    major_id: 0
                }
                this.majors.push(item);
            },
            delRow (i){  // 删除一行活动地点
                if(i === 0){
                    this.majors.splice(0, 1);
                }else {
                    this.majors.splice(i, 1);
                }
                console.log(i)

            },
            getMajors(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/major',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.majorList = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getGeo (){  //  获取国家列表
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.countrys = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            checkImg (file){
                if(!this.form.directory){
                    this.$Notice.error({
                        title: '请先填写文件目录'
                    });
                    return false;
                }
                var img = new Image();
                var Max_Size = 2000; //2M
                var Math_Width = 1024; //1024px

                var url = window.URL || window.webkitURL;
                img.src = url.createObjectURL(file);//预览加载
                var _this = this;
                var check = function () {
                    // 图片实例化加载时，其宽高为0，只要任何一方大于0，表示图片已经加载完成并返回宽高
                    if (img.width > 0 || img.height > 0) {
                        clearInterval(set);
                        //上传的本地图片支持格式：JPG，JPEG，PNG；单张图片大小不能超过2M，图片的宽高必须相同，且图片像素需大于等于550*550
                        if (file.type.indexOf("image") < 0) {
                            _this.$Notice.error({
                                title: '请选择图片类型的文件！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (file.Size > Max_Size * 1024) {
                            _this.$Notice.error({
                                title: '请上传小于或等于2M的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else if (img.width != img.height || img.width != Math_Width) {
                            _this.$Notice.error({
                                title: '请上传1024*1024的图片！'
                            });
                            _this.isMath = false;
                            return false;
                        }else {
                            _this.isMath = true;
                            _this.uploadImg(file);
                        }
                    }
                }
                var set = setInterval(check, 40);
                if(!this.isMath){
                    return false;
                }
            },
            uploadImg (file){
                var form = new FormData();
                form.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school/logo?directory=' + this.form.directory,
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        _this.form.logo_url = res.data.data.fullpath;
                        _this.files.push({
                            id: res.data.data.aid,
                            src: res.data.data.fullpath
                        })
                        _this.errMsg = '';
                    }else{
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (file){
                this.files = [];
                this.form.image = '';
            },
            handleSubmit () {
                var dataIn = {
                    directory: this.form.directory,
                    university_name: this.form.university_name,
                    university_name_cn: this.form.university_name_cn,
                    school_name: this.form.school_name,
                    school_name_cn: this.form.school_name_cn,
                    display_name: this.form.display_name,
                    short: this.form.short,
                    logo_url: this.form.logo_url,
                    keyword: this.form.keyword,
                    website: this.form.website,
                    area: this.form.area,
                    country: this.form.country,
                    school_area: []
                };

                if(!dataIn.directory){
                    this.$Notice.error({
                        title: '请填写文件目录！'
                    });
                    return false;
                }
                if(!dataIn.university_name){
                    this.$Notice.error({
                        title: '请填写商学院名称（英）！'
                    });
                    return false;
                }
                if(!dataIn.university_name_cn){
                    this.$Notice.error({
                        title: '请填写商学院名称（中）！'
                    });
                    return false;
                }
                if(!dataIn.school_name){
                    this.$Notice.error({
                        title: '请填写大学名称（英）！'
                    });
                    return false;
                }
                if(!dataIn.school_name_cn){
                    this.$Notice.error({
                        title: '请填写大学名称（中）！'
                    });
                    return false;
                }
                if(!dataIn.display_name){
                    this.$Notice.error({
                        title: '请填写显示全称！'
                    });
                    return false;
                }
                if(!dataIn.short){
                    this.$Notice.error({
                        title: '请填写显示简称！'
                    });
                    return false;
                }
                if(!dataIn.logo_url){
                    this.$Notice.error({
                        title: '请上传学校LOGO！'
                    });
                    return false;
                }
                if(!dataIn.keyword){
                    this.$Notice.error({
                        title: '请填写关键词！'
                    });
                    return false;
                }
                if(!dataIn.website){
                    this.$Notice.error({
                        title: '请填写商学院首页！'
                    });
                    return false;
                }
                if(!dataIn.area){
                    this.$Notice.error({
                        title: '请填写所属地区！'
                    });
                    return false;
                }
                if(!dataIn.country){
                    this.$Notice.error({
                        title: '请填写所属国家！'
                    });
                    return false;
                }
                for(var i=0;i<this.majors.length;i++){
                    if(!this.majors[i].fullname_english){
                        this.$Notice.error({
                            title: '请填写专业全称（英）！'
                        });
                        return false;
                    }
                    if(!this.majors[i].fullname_chinese){
                        this.$Notice.error({
                            title: '请填写专业全称（中！'
                        });
                        return false;
                    }
                    if(!this.majors[i].short){
                        this.$Notice.error({
                            title: '请填写专业简称！'
                        });
                        return false;
                    }
                    if(!this.majors[i].major_id){
                        this.$Notice.error({
                            title: '请选择所属分类！'
                        });
                        return false;
                    }
                }
                this.postSchool(dataIn);
            },
            postSchool (dataIn){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        var school_id = res.data.data.id;
                        for(var i=0;i<_this.majors.length;i++){
                            var dataMajor = {};
                            dataMajor.fullname_english = _this.majors[i].fullname_english;
                            dataMajor.fullname_chinese = _this.majors[i].fullname_chinese;
                            dataMajor.short = _this.majors[i].short;
                            dataMajor.major_id = _this.majors[i].major_id;
                            dataMajor.school_id = school_id;

                            _this.postMajor(dataMajor);
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            postMajor (dataIn){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school/major',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.I_index++;
                        if(_this.I_index >= _this.majors.length){
                            _this.$Notice.success({
                                title: '提交成功！！'
                            });
                            _this.$store.commit('removeTag', 'school_add');
                            _this.$store.commit('closePage', 'school_add');
                            _this.$router.push({
                                name: 'schools'
                            });
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.university_name = '';
                this.university_name_cn = '';
                this.school_name = '';
                this.school_name_cn = '';
                this.display_name = '';
                this.short = '';
                this.logo_url = '';
                this.keyword = '';
                this.website = '';
                this.area = '';
                this.country = '';
                this.majors = [
                    {
                        fullname_english: '',
                        fullname_chinese: '',
                        short: '',
                        major_id: 0
                    }
                ];
                this.files = [];
                this.I_index = 0;
                this.$store.commit('removeTag', 'school_add');
                this.$router.push({
                    name: 'schools'
                });
            }
        },
        created () {

        }
    };
</script>