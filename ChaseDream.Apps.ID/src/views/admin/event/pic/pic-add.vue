<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="pic-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加 推2
                </p>
                <Form ref="form" :model="form" :label-width="120">
                    <FormItem prop="subject" label="活动标题：" class="ivu-form-item-required">
                        <Input v-model="form.subject" placeholder="请输入标题"></Input>
                    </FormItem>
                    <FormItem label="推送时间：" class="ivu-form-item-required">
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="form.begin_date" placeholder="请选择活动开始时间" style="width: 250px; display: inline-block;" :time-picker-options="{steps: [1, 15, 15]}"></DatePicker>
                        <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="form.end_date" placeholder="请选择活动结束时间" style="width: 250px;display: inline-block" :time-picker-options="{steps: [1, 15, 15]}"></DatePicker>
                    </FormItem>
                    <FormItem prop="url1" label="WWW URL：" class="ivu-form-item-required">
                        <Input v-model="form.url1" placeholder="请输入URL"></Input>
                    </FormItem>
                    <FormItem prop="image" label="上传图片：" class="ivu-form-item-required">
                        <Upload action="/api/v1/admin/event/upload"
                                :before-upload="uploadImg"
                                :show-upload-list="true"
                                v-if="files.length === 0"
                        >
                            <Button type="ghost" icon="plus"></Button>
                        </Upload>
                        <div class="file-list" v-if="files.length > 0">
                            <div v-for="file in files">
                                <img :src="file.src" alt="">
                                <p class="del">
                                    <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                </p>
                            </div>
                        </div>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary" v-if="!isDisabled">提交</Button>
                        <Button type="primary" disabled v-if="!!isDisabled">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'pic_add',
        components: {
        },
        data () {
            return {
                form: {
                    subject: '',
                    url1: '',
                    image: 0,
                    begin_date: '',
                    end_date: ''
                },
                files: [],
                local: '',
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                isDisabled: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'pic_edit'){
                    this.form.subject = '';
                    this.form.url1 = '';
                    this.form.image = 0;
                    this.form.begin_date = new Date();
                    this.form.end_date = new Date(new Date().getTime() + 1000*60*15);
                    this.files = [];
                    this.local = '';
                    this.isDisabled = false;
                    this.$refs['form'].resetFields();
                }
            }
        },
        mounted () {
            this.form.begin_date = new Date();
            this.form.end_date = new Date(new Date().getTime() + 1000*60*15);
        },
        methods: {
            handleSubmit () {
                var dataIn = {
                    subject: this.form.subject,
                    url1: this.form.url1,
                    image: this.form.image,
                    event_begin_date: !!this.form.begin_date ? Math.round(this.form.begin_date .getTime()/1000) : 0,
                    event_end_date: !!this.form.end_date  ? Math.round(this.form.end_date.getTime()/1000) : 0,
                    push_begin_date: !!this.form.begin_date ? Math.round(this.form.begin_date .getTime()/1000) : 0,
                    push_end_date: !!this.form.end_date  ? Math.round(this.form.end_date.getTime()/1000) : 0,
                    event_id: 0,
                    level: 1,
                    type: 2
                };
                //console.log(dataIn);
                if(!dataIn.subject){
                    this.$Notice.error({
                        title: '请填写活动标题！'
                    });
                    return false;
                }else if(dataIn.subject.length < 3 || dataIn.subject.length > 120){
                    this.$Notice.error({
                        title: '活动标题字数不能小于3或大于120！'
                    });
                    return false;
                }
                if(!dataIn.event_begin_date){
                    this.$Notice.error({
                        title: '请选择活动开始时间！'
                    });
                    return false;
                }
                if(!dataIn.event_end_date){
                    this.$Notice.error({
                        title: '请选择活动结束时间！'
                    });
                    return false;
                }
                if(!dataIn.url1){
                    this.$Notice.error({
                        title: '请填写WWW地址！'
                    });
                    return false;
                }
                if(!dataIn.image){
                    this.$Notice.error({
                        title: '请上传活动图片！'
                    });
                    return false;
                }
                this.isDisabled = true;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'pic_edit');
                        _this.$store.commit('closePage', 'pic_edit');
                        _this.$router.push({
                            name: 'pics'
                        });
                    }else{
                        _this.isDisabled = false;
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.isDisabled = false;
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            uploadImg (file){
                var form = new FormData();
                form.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/upload/push2',
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        _this.form.image = 'https://static.chasedream.com' + res.data.data.fullpath;
                        _this.files.push({
                            id: res.data.data.aid,
                            src: 'https://static.chasedream.com' + res.data.data.fullpath
                        })
                        _this.errMsg = '';
                    }else{
                         console.log(res)
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (file){
                this.files = [];
                this.form.image = '';
            },
            closePage () {
                this.form.subject = '';
                this.form.url1 = '';
                this.form.image = '';
                this.form.begin_date = new Date();
                this.form.end_date = new Date(new Date().getTime() + 1000*60*15);
                this.local = '';
                this.files = [];
                this.isDisabled = false;
                this.$store.commit('removeTag', 'pic_edit');
                this.$router.push({
                    name: 'pics'
                });
            }
        },
        created () {

        }
    };
</script>