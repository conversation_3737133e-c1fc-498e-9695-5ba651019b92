<style lang="less">
    @import '../../../../styles/common.less';
    @import './draggable-list/draggable-list.less';
    @import '../event.less';
</style>

<template>
    <div class="pics">
        <Card>
            <div class="tool-bar">
                <router-link to="/console/event/pic-add">
                    <Button type="success" icon="plus">添 加</Button>
                    <br>
                </router-link>
            </div>
            <div class="edittable-con-1">
                <Tabs value="name1">
                    <TabPane label="当前活动" name="name1">
                        <Card dis-hover style="margin-bottom: 10px;">
                            <p slot="title">
                                <Icon type="images"></Icon>
                                LEVEL 1
                            </p>
                            <div style="">
                                <ul id="level_1" class="iview-admin-draggable-list">
                                    <li v-for="(item, index) in level.level1" :key="index" class="notwrap level_2-item" :data-index="index">
                                        <img :src="item.image" :alt="item.subject">
                                        <p>{{item.begin_date}} <br> {{item.end_date}}</p>
                                        <Poptip placement="bottom-end" v-model="item.showBtns" :transfer="true" popper-class="pic_btns" width="120">
                                            <Icon type="android-more-vertical" size="20"></Icon>
                                            <div class="btns" slot="content">
                                                <a :href="item.url1" target="_blank">
                                                    <Button type="ghost" class="link" icon="link" @click="item.showBtns = false">链接</Button>
                                                </a>
                                                <Button type="ghost" icon="edit" class="edit" @click="item.showBtns = false;editImg(item)">编辑</Button>
                                                <Poptip
                                                        confirm
                                                        :transfer="true"
                                                        placement="top-end"
                                                        popper-class="delete"
                                                        title="您确定要删除这条数据吗?"
                                                        @on-ok="delImg(item.id,index)"
                                                >
                                                    <Button type="ghost" class="del" icon="trash-a">删除</Button>
                                                </Poptip>

                                            </div>
                                        </Poptip>
                                        <span style="position: absolute;right: 0;top: 0;background: #c90505;color: #fff;font-size: 14px;width: 54px;height: 22px;line-height: 22px;text-align: center;font-weight: bold;" v-if="item.isplan">未开始</span>
                                    </li>
                                </ul>
                            </div>
                        </Card>
                        <Card dis-hover style="margin-bottom: 10px;">
                            <p slot="title">
                                <Icon type="images"></Icon>
                                LEVEL 2
                            </p>
                            <div style="">
                                <ul id="level_2" class="iview-admin-draggable-list">
                                    <li v-for="(item, index) in level.level2" :key="index" class="notwrap level_2-item" :data-index="index">
                                        <img :src="item.image" :alt="item.subject">
                                        <p>{{item.begin_date}} <br> {{item.end_date}}</p>
                                        <Poptip placement="bottom-end" v-model="item.showBtns" :transfer="true" popper-class="pic_btns" width="120">
                                            <Icon type="android-more-vertical" size="20"></Icon>
                                            <div class="btns" slot="content">
                                                <a :href="item.url1" target="_blank">
                                                    <Button type="ghost" class="link" icon="link" @click="item.showBtns = false">链接</Button>
                                                </a>
                                                <Button type="ghost" icon="edit" class="edit" @click="item.showBtns = false;editImg(item)">编辑</Button>
                                                <Poptip
                                                        confirm
                                                        :transfer="true"
                                                        placement="top-end"
                                                        popper-class="delete"
                                                        title="您确定要删除这条数据吗?"
                                                        @on-ok="delImg(item.id,index)"
                                                >
                                                    <Button type="ghost" class="del" icon="trash-a">删除</Button>
                                                </Poptip>

                                            </div>
                                        </Poptip>
                                        <span style="position: absolute;right: 0;top: 0;background: #c90505;color: #fff;font-size: 14px;width: 54px;height: 22px;line-height: 22px;text-align: center;font-weight: bold;" v-if="item.isplan">未开始</span>
                                    </li>
                                </ul>
                            </div>
                        </Card>
                        <Card dis-hover style="margin-bottom: 10px;">
                            <p slot="title">
                                <Icon type="images"></Icon>
                                LEVEL 3
                            </p>
                            <div style="">
                                <ul id="level_3" class="iview-admin-draggable-list">
                                    <li v-for="(item, index) in level.level3" :key="index" class="notwrap level_3-item" :data-index="index">
                                        <img :src="item.image" :alt="item.subject">
                                        <p>{{item.begin_date}} <br> {{item.end_date}}</p>
                                        <Poptip placement="bottom-end" v-model="item.showBtns" :transfer="true" popper-class="pic_btns" width="120">
                                            <Icon type="android-more-vertical" size="20"></Icon>
                                            <div class="btns" slot="content">
                                                <a :href="item.url1" target="_blank">
                                                    <Button type="ghost" class="link" icon="link" @click="item.showBtns = false">链接</Button>
                                                </a>
                                                <Button type="ghost" icon="edit" class="edit" @click="item.showBtns = false;editImg(item)">编辑</Button>
                                                <Poptip
                                                        confirm
                                                        :transfer="true"
                                                        placement="top-end"
                                                        popper-class="delete"
                                                        title="您确定要删除这条数据吗?"
                                                        @on-ok="delImg(item.id,index)"
                                                >
                                                    <Button type="ghost" class="del" icon="trash-a">删除</Button>
                                                </Poptip>

                                            </div>
                                        </Poptip>
                                        <span style="position: absolute;right: 0;top: 0;background: #c90505;color: #fff;font-size: 14px;width: 54px;height: 22px;line-height: 22px;text-align: center;font-weight: bold;" v-if="item.isplan">未开始</span>
                                    </li>
                                </ul>
                            </div>
                        </Card>
                        <Spin size="large" fix v-if="spinShow">
                            <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
                            <div>加载中...</div>
                        </Spin>
                    </TabPane>
                    <TabPane label="已结束活动" name="name2">
                        <can-edit-table refs="tlTable" v-model="plRows" :columns-list="plColumns"></can-edit-table>
                        <div class="page-bar">
                            <Page :total="pl_total" :page-size="page_size" :current="pl_current" @on-change="getDelDate" v-if="pl_showPage"></Page>
                        </div>
                    </TabPane>
                </Tabs>
            </div>
        </Card>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import eData from '../data/event_data.js';
    import canEditTable from './components/canEditTable.vue';
    import Sortable from 'sortablejs';

    export default {
        name: 'pics',
        components: {

            canEditTable
        },
        data () {
            return {
                images: [],
                level: {
                    level1: [],
                    level2: [],
                    level3: []
                },
                spinShow: false,
                local: '',
                plColumns: [],
                plRows: [],
                pl_total: 0,
                pl_showPage: false,
                page_size: 20,
                pl_current: 1,
                showBtns: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'pics'){
                    this.getData();
                    this.getDelDate();
                }
            }
        },
        mounted () {
            this.plColumns = eData.plColumns;
            this.getData();
            this.getDelDate(1);
            if(window.location.href.split('http://localhost/').length > 0){
                this.local = 'http://localhost:7004';
            }else {
                this.local = '';
            }
            document.body.ondrop = function (event) {
                event.preventDefault();
                event.stopPropagation();
            };
            let vm = this;
            let level_1 = document.getElementById('level_1');
            Sortable.create(level_1, {
                group: {
                    name: 'list',
                    pull: true
                },
                animation: 120,
                ghostClass: 'placeholder-style',
                fallbackClass: 'iview-admin-cloned-item',
                onEnd (evt){
                    vm.chage(evt,vm);
                }
            });
            let level_2 = document.getElementById('level_2');
            Sortable.create(level_2, {
                group: {
                    name: 'list',
                    pull: true
                },
                animation: 120,
                ghostClass: 'placeholder-style',
                fallbackClass: 'iview-admin-cloned-item',
                onEnd (evt){
                    vm.chage(evt,vm);
                }
            });
            let level_3 = document.getElementById('level_3');
            Sortable.create(level_3, {
                group: {
                    name: 'list',
                    pull: true
                },
                animation: 120,
                ghostClass: 'placeholder-style',
                fallbackClass: 'iview-admin-cloned-item',
                onEnd (evt){
                    vm.chage(evt,vm);
                }
            });
        },
        methods: {
            getData () {
                this.spinShow = true;
                this.images = [];
                this.level = {};
                var _this = this;
                util.ajax({
                    url:'api/v1/admin/event/release/push2_coming_soon',//'/event/release',
                    method:'GET',
                    params: {
                        status: 0
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.images = res.data.data;
                        _this.setDate (_this.images.level1);
                        _this.setDate (_this.images.level2);
                        _this.setDate (_this.images.level3);
                        _this.setData();
                    }else{
                        console.log(res)
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            setDate (obj){
                for(var i=0;i<obj.length;i++){
                    obj[i].begin_date = new Date(parseInt(obj[i].event_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(obj[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(obj[i].event_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(obj[i].event_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(obj[i].event_begin_date + '000')).getMinutes());
                    obj[i].end_date = new Date(parseInt(obj[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(obj[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(obj[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(obj[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(obj[i].event_end_date + '000')).getMinutes());

                    obj.showBtns = false;
                    this.$set(obj, 'showBtns', false);
                    obj[i].isplan = false;
                    if(parseInt(obj[i].event_begin_date + '000') > new Date().getTime()){
                        this.$set(obj[i], 'isplan', true);
                    }
                }
                function add0(n) {
                    if(n < 10){
                        return '0' + n;
                    }else {
                        return n;
                    }
                }
            },
            getDelDate (n){
                this.plRows = [];
                this.pl_total = 0;
                this.pl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'GET',
                    params: {
                        page: _this.pl_current,
                        page_size: _this.page_size,
                        type: 2,
                        status: -1
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.plRows = res.data.data.rows;
                        for(var i=0;i<_this.plRows.length;i++){
                            if(!!_this.plRows[i].event_end_date){
                                _this.plRows[i].end_date = new Date(parseInt(_this.plRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.plRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.plRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.plRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.plRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.plRows[i].end_date = '';
                            }
                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }
                        _this.pl_total = res.data.data.count;
                        if(_this.pl_total > _this.page_size){
                            _this.pl_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getImg (id,str,index){
                var _this = this;
                util.ajax({
                    url:'/event/image/'+ id,
                    method:'GET',
                    params: {
                    },
                }).then(function (res) {
                    if(res.status === 200) {
                        if(str === '1'){
                            _this.images.level1[index].src = res.data;
                        }else if(str === '2'){
                            _this.images.level2[index].src = res.data;
                        }else if(str === '3'){
                            _this.images.level3[index].src = res.data;
                        }

                    }else{
                        console.log(res)
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleChange (data) {
                var dataIn = {
                    type: 2,
                    data: data
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/change_order',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.getData();

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            setData(){
                var vm = this;
                setTimeout(function () {
                    vm.level = vm.images;
                    vm.spinShow = false;
                    console.log(vm.images)
                },100);

            },
            editImg(item){
                this.showBtns = false;
                this.$store.commit('geteventRow', item);
                this.$router.push({
                    name: 'pic_edit'
                });
            },
            delImg(id){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'DELETE',
                    data: {
                        id: id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Message.success('删除成功！');
                        _this.getData();
                        _this.getDelDate();
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            chage (evt,vm){
                var data = [
                    {
                        level: 1,
                        ids: []
                    },
                    {
                        level: 2,
                        ids: []
                    },
                    {
                        level: 3,
                        ids: []
                    },
                ];
                for(var i=0;i<vm.level.level1.length;i++){
                    data[0].ids.push(vm.level.level1[i].id);
                }
                for(var i=0;i<vm.level.level2.length;i++){
                    data[1].ids.push(vm.level.level2[i].id);
                }
                for(var i=0;i<vm.level.level3.length;i++){
                    data[2].ids.push(vm.level.level3[i].id);
                }

                if(evt.from.id === 'level_1'){
                    data[0].ids.splice(evt.oldIndex, 1);
                    if(evt.to.id === 'level_1'){
                        data[0].ids.splice(evt.newIndex, 0, vm.level.level1[evt.item.getAttribute('data-index')].id);
                    }else if(evt.to.id === 'level_2'){
                        data[1].ids.splice(evt.newIndex, 0, vm.level.level1[evt.item.getAttribute('data-index')].id);
                    }else if(evt.to.id === 'level_3'){
                        data[2].ids.splice(evt.newIndex, 0, vm.level.level1[evt.item.getAttribute('data-index')].id);
                    }
                }else if(evt.from.id === 'level_2'){
                    data[1].ids.splice(evt.oldIndex, 1);
                    if(evt.to.id === 'level_1'){
                        data[0].ids.splice(evt.newIndex, 0, vm.level.level2[evt.item.getAttribute('data-index')].id);
                    }else if(evt.to.id === 'level_2'){
                        data[1].ids.splice(evt.newIndex, 0, vm.level.level2[evt.item.getAttribute('data-index')].id);
                    }else if(evt.to.id === 'level_3'){
                        data[2].ids.splice(evt.newIndex, 0, vm.level.level2[evt.item.getAttribute('data-index')].id);
                    }
                }else if(evt.from.id === 'level_3'){
                    data[2].ids.splice(evt.oldIndex, 1);
                    if(evt.to.id === 'level_1'){
                        data[0].ids.splice(evt.newIndex, 0, vm.level.level3[evt.item.getAttribute('data-index')].id);
                    }else if(evt.to.id === 'level_2'){
                        data[1].ids.splice(evt.newIndex, 0, vm.level.level3[evt.item.getAttribute('data-index')].id);
                    }else if(evt.to.id === 'level_3'){
                        data[2].ids.splice(evt.newIndex, 0, vm.level.level3[evt.item.getAttribute('data-index')].id);
                    }
                }
                this.handleChange(data);
            }
        },
        created () {

        }
    };
</script>

<style lang="less">

</style>
