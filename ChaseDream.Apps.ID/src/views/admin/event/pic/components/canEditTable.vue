<style lang="less">
    @import './editable-table.less';
</style>

<template>
    <div>
        <Table :ref="refs" :columns="columnsList" :data="thisTableData" border></Table>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

const viewButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'eye',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            click: () => {
                vm.$emit('details', currentRow);
            }
        }
    });
};
const editButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'edit',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                let cr = currentRow;
                vm.$store.commit('geteventRow', cr);
                vm.$router.push({
                    name: 'pic_edit'
                });
            }
        }
    });
};
const deleteButton = (vm, h, currentRow, index) => {
    return h('Poptip', {
        props: {
            confirm: true,
            title: '您确定要删除这条数据吗?',
            transfer: true
        },
        on: {
            'on-ok': () => {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'DELETE',
                    data: {
                        id: currentRow.id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        vm.thisTableData.splice(index, 1);
                        vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                        vm.$emit('on-delete', vm.handleBackdata(vm.thisTableData), index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        }
    }, [
        h('Icon', {
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            props: {
                type: 'trash-a',
                placement: 'top',
                size: 24
            }
        })
    ]);
};
const colorpick = (vm, h, currentRow, index) => {
    var colors = [{
        val: 0,
        color: '#000',
        title: '黑'
    },{
        val: 1,
        color: '#ff0101',
        title: '红'
    },{
        val: 2,
        color: '#2897c5',
        title: '蓝'
    },{
        val: 3,
        color: 'orange',
        title: '橙'
    },{
        val: 4,
        color: '#9f00fb',
        title: '紫'
    },{
        val: 5,
        color: '#1ab394',
        title: '绿'
    },{
        val: 6,
        color: 'red',
        title: '红字黄底'
    },{
        val: 7,
        color: '#fcf301',
        title: '黄字红底'
    },{
        val: 8,
        color: '#ff0101',
        title: '红字灰底'
    }];
    var isChange = false;
    var isShow = false;
    currentRow.hlc = {}
    for(var i=0;i<colors.length;i++){
        if(currentRow.highlight === colors[i].val){
            currentRow.hlc = colors[i]
        }
    }
    if(!!currentRow.editable){
        return h('div',[
            h('Poptip', {
                props: {
                    trigger: 'click',
                    placement: 'bottom',
                },
                on: {
                    'on-ok': () => {

                    },
                    'on-cancel': () => {

                    },
                    'on-popper-hide': (e) => {

                    }

                }
            }, [
                h('a', {
                    style: {
                        width: '20px',
                        height: '20px',
                        background: currentRow.hlc.color,
                        display: 'inline-block',
                        verticalAlign: 'middle'
                    },
                    attrs: {
                        title: currentRow.hlc.title
                    },
                    props: {
                        title: currentRow.hlc.title
                    }
                }),
                h('div', {
                    slot: 'content',
                    style: {
                        width: '90px',
                        margin: '0 auto',
                        overflow: 'hidden'
                    },
                }, [
                    h('div',{
                        style: {
                            width: '90px',
                            height: '90px',
                            marginBottom: '10px'
                        },
                    },colors.map(item => {
                        return h('a', {
                            style: {
                                width: '30px',
                                height: '30px',
                                background: item.color,
                                cursor: 'pointer',
                                float: 'left'
                            },
                            attrs: {
                                title: item.title
                            },
                            props: {
                                title: item.title
                            },
                            on: {
                                click: () => {
                                    currentRow.highlight = item.val;
                                }
                            }
                        })
                    }))
                ])
            ]),
            h('Icon', {
                style: {
                    margin: '0 8px 0 15px',
                    color: '#19be6b',
                    cursor: 'pointer',
                    verticalAlign: 'middle'
                },
                props: {
                    type: 'checkmark-round',
                    placement: 'top',
                    size: 18
                },
                on: {
                    'click': () => {
                        var lh = currentRow.highlight;
                        util.ajax({
                            url:'/api/v1/admin/thread/highlight_digest',
                            method:'POST',
                            data: {
                                uid: currentRow.uid,
                                fid: currentRow.fid,
                                tid: currentRow.tid,
                                id: currentRow.id,
                                opt:  'highlight',
                                highlight_color: currentRow.highlight,
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                currentRow.highlight = 0;
                                currentRow.highlight = lh;
                                currentRow.editable = false;
                                vm.$Notice.success({
                                    title: '设置成功！'
                                });
                                vm.$emit('on-updata');
                            }else{
                                vm.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            vm.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });

                    }
                }
            }),
            h('Icon', {
                style: {
                    color: '#ed3f14',
                    cursor: 'pointer',
                    verticalAlign: 'middle'
                },
                props: {
                    type: 'close-round',
                    placement: 'top',
                    size: 18
                },
                on: {
                    'click': () => {
                        currentRow.highlight = currentRow.initHl
                        currentRow.editable = false;
                    }
                }
            }),
        ])
    }else {
        return h('div',[
            h('Poptip', {
                props: {
                    trigger: 'click',
                    placement: 'bottom',
                },
                on: {
                    'on-ok': () => {

                    },
                    'on-cancel': () => {

                    },
                    'on-popper-hide': (e) => {

                    }

                }
            }, [
                h('a', {
                    style: {
                        width: '20px',
                        height: '20px',
                        background: currentRow.hlc.color,
                        display: 'inline-block'
                    },
                    attrs: {
                        title: currentRow.hlc.title
                    },
                    props: {
                        title: currentRow.hlc.title
                    }
                }),
                h('div', {
                    slot: 'content',
                    style: {
                        width: '90px',
                        margin: '0 auto',
                        overflow: 'hidden'
                    },
                }, [
                    h('div',{
                        style: {
                            width: '90px',
                            height: '90px',
                            marginBottom: '10px'
                        },
                    },colors.map(item => {
                        return h('a', {
                            style: {
                                width: '30px',
                                height: '30px',
                                background: item.color,
                                cursor: 'pointer',
                                float: 'left'
                            },
                            attrs: {
                                title: item.title
                            },
                            props: {
                                title: item.title
                            },
                            on: {
                                click: () => {
                                    currentRow.highlight = item.val;
                                    currentRow.editable = true;
                                }
                            }
                        })
                    })),
                ])

            ])
        ])
    }

};
const iSwich = (vm, h, currentRow, index) => {
    return h('i-switch', {
        props: {
            value: !!currentRow.digest ? true : false
        },
        nativeOn: {
            click: () => {
                if(!currentRow.digest){
                    currentRow.digest = 1;
                }else {
                    currentRow.digest = 0;
                }
                util.ajax({
                    url:'/api/v1/admin/thread/highlight_digest',
                    method:'POST',
                    data: {
                        uid: currentRow.uid,
                        fid: currentRow.fid,
                        tid: currentRow.tid,
                        id: currentRow.id,
                        opt:  'digest',
                        digest: currentRow.digest,
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        vm.$Notice.success({
                            title: '设置成功！'
                        });
                    }else{
                        vm.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    vm.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            }
        }
    })
};
const dropDown = (vm, h, currentRow, index) => {
    const items = [
        {
            val: 0,
            text: '无'
        },
        {
            val: 1,
            text: '本版置顶'
        },
        {
            val: 2,
            text: '分类置顶'
        },
        {
            val: 3,
            text: '全局置顶'
        },
        {
            val: 4,
            text: '活动置顶'
        }
    ];

    currentRow.text = items[currentRow.stick].text
    return h('Dropdown',
        [
        h('a',currentRow.text),
        h('DropdownMenu',{
            slot: 'list'
        },items.map(item => {
            return h('DropdownItem', {
                nativeOn:{
                    click: (name) => {
                        console.log(item)
                        util.ajax({
                            url:'/api/v1/admin/thread/stick',
                            method:'POST',
                            data: {
                                uid: currentRow.uid,
                                fid: currentRow.fid,
                                tid: currentRow.tid,
                                id: currentRow.id,
                                stick: item.val,
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                vm.$Notice.success({
                                    title: '设置成功！'
                                });
                                currentRow.stick = item.val;
                                currentRow.text = item.text;
                                vm.$emit('on-updata');

                            }else{
                                vm.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            vm.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                }
            },item.text)
        }))
    ])
};
const incellEditBtn = (vm, h, param) => {
    if (vm.hoverShow) {
        return h('div', {
            'class': {
                'show-edit-btn': vm.hoverShow
            }
        }, [
            h('Button', {
                props: {
                    type: 'text',
                    icon: 'edit'
                },
                on: {
                    click: (event) => {
                        vm.edittingStore[param.index].edittingCell[param.column.key] = true;
                        vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
                    }
                }
            })
        ]);
    } else {
        return h('Button', {
            props: {
                type: 'text',
                icon: 'edit'
            },
            on: {
                click: (event) => {
                    vm.edittingStore[param.index].edittingCell[param.column.key] = true;
                    vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
                }
            }
        });
    }
};


export default {
    name: 'canEditTable',
    props: {
        refs: String,
        columnsList: Array,
        value: Array,
        url: String,
        editIncell: {
            type: Boolean,
            default: false
        },
        hoverShow: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            columns: [],
            thisTableData: [],
            edittingStore: []
        };
    },
    created () {
        this.init();
    },
    methods: {
        init () {
            let vm = this;
            let editableCell = this.columnsList.filter(item => {
                if (item.editable) {
                    if (item.editable === true) {
                        return item;
                    }
                }
            });
            let cloneData = JSON.parse(JSON.stringify(this.value));
            let res = [];
            res = cloneData.map((item, index) => {
                let isEditting = false;
                if (this.thisTableData[index]) {
                    if (this.thisTableData[index].editting) {
                        isEditting = true;
                    } else {
                        for (const cell in this.thisTableData[index].edittingCell) {
                            if (this.thisTableData[index].edittingCell[cell] === true) {
                                isEditting = true;
                            }
                        }
                    }
                }
                if (isEditting) {
                    return this.thisTableData[index];
                } else {
                    this.$set(item, 'editting', false);
                    let edittingCell = {};
                    editableCell.forEach(item => {
                        edittingCell[item.key] = false;
                    });
                    this.$set(item, 'edittingCell', edittingCell);
                    return item;
                }
            });
            this.thisTableData = res;
            this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
            this.columnsList.forEach(item => {
                if (item.editable) {
                    item.render = (h, param) => {
                        let currentRow = this.thisTableData[param.index];
                        if (!currentRow.editting) {
                            if (this.editIncell) {
                                return h('Row', {
                                    props: {
                                        type: 'flex',
                                        align: 'middle',
                                        justify: 'center'
                                    }
                                }, [
                                    h('Col', {
                                        props: {
                                            span: '22'
                                        }
                                    }, [
                                        !currentRow.edittingCell[param.column.key] ? h('span', currentRow[item.key]) : cellInput(this, h, param, item)
                                    ]),
                                    h('Col', {
                                        props: {
                                            span: '2'
                                        }
                                    }, [
                                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                                    ])
                                ]);
                            } else {
                                return h('span', currentRow[item.key]);
                            }
                        } else {
                            return h('Input', {
                                props: {
                                    type: 'text',
                                    value: currentRow[item.key]
                                },
                                on: {
                                    'on-change' (event) {
                                        let key = param.column.key;
                                        vm.edittingStore[param.index][key] = event.target.value;
                                    }
                                }
                            });
                        }
                    };
                }
                if (item.handle) {
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.handle.forEach(item => {
                            if (item === 'edit') {
                                children.push(editButton(this, h, currentRowData, param.index));
                            } else if (item === 'delete') {
                                children.push(deleteButton(this, h, currentRowData, param.index));
                            } else if (item === 'view') {
                                children.push(viewButton(this, h, currentRowData, param.index));
                            }
                        });
                        return h('div', children);
                    };
                }
                if(item.action){
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.action.forEach(item => {
                            if (item === 'colorpick') {
                                children.push(colorpick(this, h, currentRowData, param.index));
                            }else if(item === 'iSwich'){
                                children.push(iSwich(this, h, currentRowData, param.index));
                            }else if(item === 'dropDown'){
                                children.push(dropDown(this, h, currentRowData, param.index));
                            }
                        });
                        return h('div', children);
                    };
                }
            });
        },
        handleBackdata (data) {
            let clonedData = JSON.parse(JSON.stringify(data));
            clonedData.forEach(item => {
                delete item.editting;
                delete item.edittingCell;
                delete item.saving;
            });
            return clonedData;
        }
    },
    watch: {
        value (data) {
            this.init();
        }
    }
};
</script>
