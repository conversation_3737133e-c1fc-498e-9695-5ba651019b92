<style lang="less">
    @import '../../../../../styles/common.less';
    @import 'draggable-list.less';
</style>

<template>
    <div>
        <Card>
            <Card dis-hover style="margin-bottom: 10px;">
                <p slot="title">
                    <Icon type="ios-list-outline"></Icon>
                    LEVEL 1
                </p>
                <div style="">
                    <ul id="level_1" class="iview-admin-draggable-list">
                        <li v-for="(item, index) in level.level1" :key="index" class="notwrap level_2-item" :data-index="index">
                            <h3>{{ item.subject }}</h3>
                            <img :src="item.src" :alt="item.subject">
                        </li>
                    </ul>
                </div>
            </Card>
            <Card dis-hover style="margin-bottom: 10px;">
                <p slot="title">
                    <Icon type="ios-list"></Icon>
                    LEVEL 2
                </p>
                <div style="">
                    <ul id="level_2" class="iview-admin-draggable-list">
                        <li v-for="(item, index) in level.level2" :key="index" class="notwrap level_2-item" :data-index="index">
                            <h3>{{ item.subject }}</h3>
                            <img :src="item.src" :alt="item.subject">
                        </li>
                    </ul>
                </div>
            </Card>
            <Card dis-hover style="margin-bottom: 10px;">
                <p slot="title">
                    <Icon type="ios-list"></Icon>
                    LEVEL 3
                </p>
                <div style="">
                    <ul id="level_3" class="iview-admin-draggable-list">
                        <li v-for="(item, index) in level.level3" :key="index" class="notwrap level_3-item" :data-index="index">
                            <h3>{{ item.subject }}</h3>
                            <img :src="item.src" :alt="item.subject">
                            </li>
                    </ul>
                </div>
            </Card>
        </Card>
    </div>
</template>

<script>
import Sortable from 'sortablejs';
export default {
    name: 'draggable-list',
    data () {
        return {
            level: {
                level1: [],
                level2: [],
                level3: []
            }
        };
    },
    mounted () {
        document.body.ondrop = function (event) {
            event.preventDefault();
            event.stopPropagation();
        };
        let vm = this;
        let level_1 = document.getElementById('level_1');
        Sortable.create(level_1, {
            group: {
                name: 'list',
                pull: true
            },
            animation: 120,
            ghostClass: 'placeholder-style',
            fallbackClass: 'iview-admin-cloned-item',
            onChange (evt){
                console.log(evt)
                vm.change(evt,vm);

            }
        });
        let level_2 = document.getElementById('level_2');
        Sortable.create(level_2, {
            group: {
                name: 'list',
                pull: true
            },
            animation: 120,
            ghostClass: 'placeholder-style',
            fallbackClass: 'iview-admin-cloned-item',
            onChange (evt){
                console.log(evt)
                vm.change(evt,vm);
            }
        });
        let level_3 = document.getElementById('level_3');
        Sortable.create(level_3, {
            group: {
                name: 'list',
                pull: true
            },
            animation: 120,
            ghostClass: 'placeholder-style',
            fallbackClass: 'iview-admin-cloned-item',
            onChange (evt){
                console.log(evt)
                vm.change(evt,vm);
            }
        });

    },
    methods: {
        setData(){
            var vm = this;
            console.log(this.$parent.images);
            this.level = {};
            this.level = this.$parent.images;
        },
        change (evt,vm){
            var data = [
                {
                    level: 1,
                    ids: []
                },
                {
                    level: 2,
                    ids: []
                },
                {
                    level: 3,
                    ids: []
                },
            ];
            for(var i=0;i<vm.level.level1.length;i++){
                data[0].ids.push(vm.level.level1[i].id);
            }
            for(var i=0;i<vm.level.level2.length;i++){
                data[1].ids.push(vm.level.level2[i].id);
            }
            for(var i=0;i<vm.level.level3.length;i++){
                data[2].ids.push(vm.level.level3[i].id);
            }

            if(evt.from.id === 'level_1'){
                data[0].ids.splice(evt.oldIndex, 1);
                if(evt.to.id === 'level_1'){
                    data[0].ids.splice(evt.newIndex, 0, vm.level.level1[evt.item.getAttribute('data-index')].id);
                }else if(evt.to.id === 'level_2'){
                    data[1].ids.splice(evt.newIndex, 0, vm.level.level1[evt.item.getAttribute('data-index')].id);
                }else if(evt.to.id === 'level_3'){
                    data[2].ids.splice(evt.newIndex, 0, vm.level.level1[evt.item.getAttribute('data-index')].id);
                }
            }else if(evt.from.id === 'level_2'){
                data[1].ids.splice(evt.oldIndex, 1);
                if(evt.to.id === 'level_1'){
                    data[0].ids.splice(evt.newIndex, 0, vm.level.level2[evt.item.getAttribute('data-index')].id);
                }else if(evt.to.id === 'level_2'){
                    data[1].ids.splice(evt.newIndex, 0, vm.level.level2[evt.item.getAttribute('data-index')].id);
                }else if(evt.to.id === 'level_3'){
                    data[2].ids.splice(evt.newIndex, 0, vm.level.level2[evt.item.getAttribute('data-index')].id);
                }
            }else if(evt.from.id === 'level_3'){
                data[2].ids.splice(evt.oldIndex, 1);
                if(evt.to.id === 'level_1'){
                    data[0].ids.splice(evt.newIndex, 0, vm.level.level3[evt.item.getAttribute('data-index')].id);
                }else if(evt.to.id === 'level_2'){
                    data[1].ids.splice(evt.newIndex, 0, vm.level.level3[evt.item.getAttribute('data-index')].id);
                }else if(evt.to.id === 'level_3'){
                    data[2].ids.splice(evt.newIndex, 0, vm.level.level3[evt.item.getAttribute('data-index')].id);
                }
            }

            vm.$emit('on-change', data);
        }
    }
};
</script>
