<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="eUser-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑发帖账号
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="80">
                    <FormItem prop="nickname" label="昵称：">
                        <Input v-model="userform.nickname" placeholder="请输入昵称"></Input>
                    </FormItem>
                    <FormItem prop="username" label="用户名：">
                        <Input v-model="userform.username" readonly></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem prop="password" label="用户类型：">
                        <RadioGroup v-model="userform.type">
                            <Radio :label="1">论坛用户</Radio>
                            <Radio :label="2">WWW用户</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

export default {
    name: 'eUser_edit',
    components: {

    },
    data () {
        return {
            userform: {
                nickname: '',
                username: '',
                password: '',
                type: 1
            },
            rules: {
                nickname: [
                    { required: true, message: '昵称不能为空', trigger: 'blur' },
                    { type: 'string', min: 2, message: '昵称不能小于2个字符', trigger: 'blur' }
                ],
                username: [
                    { required: true, message: '账号不能为空', trigger: 'blur' },
                    { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                ]
            },
            access_token: this.$store.state.user.access_token,
            isAccess: true
        };
    },
    computed: {

    },
    watch: {
        '$route' (to) {
           if(to.name === 'eUser_edit'){
               this.userform = {}
               this.userform = this.$store.state.event.currentRow;
               if(!this.userform.id){
                   this.$router.push({
                       name: 'eUser_list'
                   });
               }
           }
        },
    },
    mounted () {
        if(parseInt(Cookies.get('uAccess')) === 1){
            this.isAccess = false
        }

        this.userform = {}
        this.userform = this.$store.state.event.currentRow;
        if(!this.userform.id){
            this.$router.push({
                name: 'eUser_list'
            });
        }
    },
    methods: {
        handleSubmit () {
            this.$refs.userForm.validate((valid) => {
                if (valid) {
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/event/user',
                        method:'PUT',
                        data: {
                            nickname: _this.userform.nickname,
                            username: _this.userform.username,
                            id: _this.userform.id,
                            password: _this.userform.password,
                            type: _this.userform.type
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.$store.commit('removeTag', 'eUser_edit');
                            _this.$store.commit('closePage', 'eUser_edit');
                            _this.$router.push({
                                name: 'eUser_list'
                            });
                        }else{

                        }
                    }).catch(function (err) {
                        console.log(err)
                    });
                }
            });
        },
        closePage () {
            this.userform = {}
            this.$store.commit('removeTag', 'eUser_edit');
            this.$store.commit('closePage', 'eUser_edit');
            this.$router.push({
                name: 'eUser_list'
            });
        }

    }
};
</script>

<style>

</style>
