<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="eUser-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/event/eUser-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="uTable" @on-delete="handleDel" v-model="uRows" :columns-list="uColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getUserslist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/event_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'eUser_list',
        components: {
            canEditTable
        },
        data () {
            return {
                uRows: [],
                uColumns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true,
                keyword: '',
                s_key: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'eUser_list'){
                    this.getUserslist(1);
                }
            }
        },
        mounted () {
            this.getUserslist(1);
        },
        methods: {
            getData () {
                this.uColumns = tData.uColumns;
            },
            getUserslist (n){
                this.uRows = [];
                this.total = 0;
                this.current = n;
                var keys = '';
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/user',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.uRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
