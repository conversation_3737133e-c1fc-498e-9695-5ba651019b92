<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="eUser-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加发帖账号
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="90">
                    <FormItem prop="nickname" label="昵称：">
                        <Input v-model="userform.nickname" placeholder="请输入昵称"></Input>
                    </FormItem>
                    <FormItem prop="userName" label="账号：">
                        <Input v-model="userform.userName" placeholder="请输入账号"></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem prop="password" label="用户类型：">
                        <RadioGroup v-model="userform.type">
                            <Radio :label="1">论坛用户</Radio>
                            <Radio :label="2">WWW用户</Radio>
                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'eUser_add',
        components: {

        },
        data () {
            return {
                userform: {
                    nickname: '',
                    userName: '',
                    password: '',
                    type: 1
                },
                rules: {
                    nickname: [
                        { required: true, message: '昵称不能为空', trigger: 'blur' },
                        { type: 'string', min: 2, message: '昵称不能小于2个字符', trigger: 'blur' }
                    ],
                    userName: [
                        { required: true, message: '账号不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '密码不能为空', trigger: 'blur' },
                        { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                    ]
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'eUser_add'){
                    this.userform.nickname = '';
                    this.userform.userName = '';
                    this.userform.password = '';
                    this.userform.type = 1;

                    this.$refs['userForm'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.userForm.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/event/user',
                            method:'POST',
                            data: {
                                nickname: _this.userform.nickname,
                                username: _this.userform.userName,
                                password: _this.userform.password,
                                type: _this.userform.type
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'eUser_add');
                                _this.$store.commit('closePage', 'eUser_add');
                                _this.$router.push({
                                    name: 'eUser_list'
                                });
                            }else{
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.userform.nickname = '';
                this.userform.userName = '';
                this.userform.password = '';
                this.userform.type = 1;
                this.$store.commit('removeTag', 'eUser_add');
                this.$router.push({
                    name: 'eUser_list'
                });
            }
        }
    };
</script>

<style>

</style>
