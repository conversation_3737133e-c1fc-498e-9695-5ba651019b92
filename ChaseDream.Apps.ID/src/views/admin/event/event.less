.releaseModel, .core-add, .core-edit,.release_modal{
  .end_date,.province,.city{
    .ivu-form-item-content{
      margin-left: 0 !important;
    }
    .ivu-poptip{
      position: absolute;
      top: -20px;
    }
    .ivu-icon-close-circled{
      position: absolute;
      right: -40px;
      top: 10px;
      color: #ed3f14; ///dddccc
      cursor: pointer;
      &:hover{
        color: #ed3f14;
      }

    }
  }
  .ivu-col-span-1{
    width: 20px;
  }
}
.main, .releaseModel,.release_modal{
  .geos{
    .ivu-btn.ivu-btn-dashed{
      border:1px dashed #dddee1;
      width: 100px;
    }
    .ivu-icon.ivu-icon-checkmark-circled{
      position: absolute;
      right: -80px;
      top: 10px;
      color: #19be6b; ///dddccc
      cursor: pointer;
      &:hover{
        color: #19be6b;
      }
    }
  }
  .f_err_msg{
    color: #ed3f14;
  }

  .ivu-checkbox-wrapper.ivu-checkbox-group-item{
    margin-bottom: 10px;
  }
  .file-list{
    .ivu-icon.ivu-icon-close-circled{
       margin-left: 10px;
      color: #ed3f14;
    }
  }
}

.pics{
  .ivu-card-dis-hover{
    .ivu-card-body{
      padding-right: 0;
      padding-bottom: 0;
    }
  }
  .iview-admin-draggable-list{
    display: flex;
    flex-wrap: wrap;
    min-height: 100px;

    li{
      width: 206px;
      min-height: 150px;
      margin-right: 16px;
      margin-bottom: 16px;
      position: relative;

      img{
        vertical-align: top;
        width: 100%;
      }
      p{
        margin-top: 10px;
        width: 100%;
        word-wrap: break-word;
        white-space: normal;
        font-size: 12px;
      }
      .mask{
        position: absolute;
        left:0;
        top:0;
        width: 100%;
        height: 0px;
        background: rgba(0,0,0,0.5);
        line-height: 30px;
        text-align: right;
        padding-right: 10px ;
        overflow: hidden;
        transition: all .2s;

        .ivu-icon{
          color: #fff;
          display: inline-block;
        }
        .ivu-poptip{
          width: 20px;
          text-align: center;
          display: inline-block;
          margin-left: 10px;
        }
      }
      &:hover{
        .mask{
          height: 28px;
        }
      }
      .ivu-poptip{
        float: right;
        margin-top: -30px;
        .ivu-icon-android-more-vertical{
          width: 10px;
          text-align: center;
        }
      }
    }
  }
}
.dragTable{
  display: table;
  table-layout: fixed;
  width: 100%;
  border-left: 1px solid #e9eaec;
  background: #fff;

  .tableRow{
    display: table-row;
    &:hover{
      background: #f5f7fe;
    }
  }

  .tableCell{
    display: table-cell;
    height: 60px;
    font-size: 12px;
    border-right: 1px solid #e9eaec;
    border-bottom: 1px solid #e9eaec;
    vertical-align: middle;
    text-align: center;
    padding-left: 18px;
    padding-right: 18px;
    &.index{
      width: 60px;
    }
    &.location{
      width:90px;
    }
    &.subject{
      width: auto;

      table{
        width: 100%;
        td:nth-of-type(1){
          span{
            color: #2d8cf0;
          }
        }
        td:nth-of-type(2){
          padding-left: 35px;
          text-align: left;
          position: relative;

          span{
            position: absolute;
            width: 1px;
            height: 60px;
            background: #e9eaec;
            left: 17px;
            top: -13px;
          }

        }
      }
    }
    &.color{
      width:100px;
      .ivu-btn{
        width: 25px;
        height: 25px;
        background: #000;
        padding: 0px;
        border-radius: 5px;
        margin: 0 5px;

        &.red{
          background-color: #ff0101;
        }
        &.green{
          background-color: #1ab394;
        }
        &.blue{
          background-color: #2897c5;
        }
        &.black{
          background-color: #000;
        }
        &.on{
          background-image: url("../../../images/release/colour_selected.png");
          background-repeat: no-repeat;
          background-position: bottom right;
          background-size: 140%;
        }
      }
    }
    &.end_date,&.push_end_date,&.new_flag_date{
      width: 140px;
    }
    &.handle{
      width: 130px;
    }
    p{
      width:100%;
      text-align: left;
      white-space: normal;
      word-wrap: break-word;
    }
    .ivu-icon{
      margin:0 7px;
      cursor: pointer;
    }
  }
}
.headlines-edit,.headlines-add,.releaseModel,.release_modal{
  .colors{
    .ivu-btn{
      width: 25px;
      height: 25px;
      background: #000;
      padding: 0px;
      border-radius: 5px;
      margin: 0 5px;

      &.red{
        background-color: #ff0101;
      }
      &.green{
        background-color: #1ab394;
      }
      &.blue{
        background-color: #2897c5;
      }
      &.black{
        background-color: #000;
      }
      &.on{
        background-image: url("../../../images/release/colour_selected.png");
        background-repeat: no-repeat;
        background-position: bottom right;
        background-size: 140%;
      }
    }
  }
}
.ivu-poptip-popper.color_pop {
  .ivu-btn {
    width: 25px;
    height: 25px;
    background: #000;
    padding: 0px;
    border-radius: 5px;
    margin: 0 5px;
    box-shadow: none;

    &.red {
      background-color: #ff0101;
    }
    &.green {
      background-color: #1ab394;
    }
    &.blue {
      background-color: #2897c5;
    }
    &.black {
      background-color: #000;
    }
    &.on {
      background-image: url("../../../images/release/colour_selected.png");
      background-repeat: no-repeat;
      background-position: bottom right;
      background-size: 140%;
    }
  }
}
.activity-list, .headlines-list{
  .iview-admin-draggable-list li{
    padding: 0;
    border: none;
    margin:0;

  }
}
.activity-edit,.release_modal{
  .province,.city{
    .ivu-form-item-content{
      margin-left: 0 !important;
    }
  }
}
.file-list{
  div{
    position: relative;
    width: 100px;
    height:100px;
    border: 1px solid #efefef;
    img{
      width: 100%;
      vertical-align: top;
    }
    .del{
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100px;
      line-height: 100%;
      background: rgba(225,225,225,0.8);
      display: none;
      .ivu-icon{
        color: #ed3f14;
        position: absolute;
        left:50%;
        top: 50%;
        margin-left: -12px;
        margin-top: -12px;
        cursor: pointer;
      }
    }
    &:hover{
      .del{
        display: block;
      }
    }
  }
  .ivu-icon.del{
    position: absolute;
    left: inherit;
    right: -1px;
    top: -1px;
    width: 15px;
    height: 15px;
    line-height: 15px;
    background: red;
    color: #fff;
    cursor: pointer;
    text-align: center;
    display: block;
  }
}
.main,.ivu-modal{
  .ivu-upload{
    .ivu-btn{
      width: 50px;
      height: 50px;
      border: 1px dashed #efefef;
    }
  }
}
.main,.release_modal{
  .common{
    display: flex;
    width: 100%;

    strong{
      margin-top: 5px;
      min-width: 40px;
    }
    span{
      display: inline-block;
      margin: 5px 10px;
      vertical-align: middle;
      cursor: pointer;
      color: #2d8cf0;
      text-decoration: underline;
      line-height: 16px;
    }
  }
  .plusItem{
    border: 1px dashed #efefef;
  }
  .geos{
    position: relative;
    /*background: #eaf8f0;*/
    border-radius: 5px;
    margin-bottom: 5px;
    padding-top: 20px;
    border: 1px solid #efefef;
    .delItem{
      color: #efefef;
      position: absolute;
      right:20px;
      top: 20px;
      cursor: pointer;
    }
    &:hover{
      .delItem{
        color: #ed3f14;
      }
    }
  }
.material{
  display: inline-block;
  vertical-align: middle;
  div{
    display: inline-block;
    margin-right: 5px;
    position: relative;
    width: 50px;
    height: 50px;
    overflow: hidden;
    img{
      width: 100%;
      border: 1px solid #efefef;
      display: block;
      cursor: pointer;
      display: inline-block;
    }
    span{
      position: absolute;
      left: 0;
      bottom:0;
      width: 100%;
      height: 20px;
      background-color: rgb(0,0,0,0.3);
      color: #fff;
      line-height: 20px;
      text-align: center;
      cursor: pointer;
      opacity: 0.3;
    }
  }
}

}
.materialModal{
  .ivu-modal-header{
    p{
      text-align: center;
      vertical-align: middle;
      .ivu-icon{
        font-size: 24px;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
  .ivu-modal-body{
    .addList{
      .file-list{
        margin-bottom: 20px;

        .file-list-item{
          width: 50px;
          height: 50px;
          display: inline-block;
          vertical-align: middle;
          border: 1px solid rgb(239, 239, 239);
          margin-right: 10px;

          p{
            height: 100%;

            .ivu-icon{
              font-size: 22px;
              margin-left: -8px;
            }
          }
        }
        .ivu-upload{
          display: inline-block;
          vertical-align: middle;
          width: 50px;
          height: 50px;
        }
      }
    }
    .former_imgs{
      div{
        width: 50px;
        height: 50px;
        overflow: hidden;
        display: inline-block;
        border: 1px solid rgb(223, 223, 223);
        margin: 5px;
        position: relative;
        img{
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
        span{
          position: absolute;
          left: -1px;
          top: -1px;
          width: 13px;
          height: 13px;
          font-size: 13px;
          line-height: 13px;
          color: #fff;
          background: #19be6b;
          border: 1px solid #fff;
          cursor: pointer;
          text-align: center;
          vertical-align: middle;
          box-sizing: content-box;
        }
      }
    }
    .top_imgs{
      min-height: 90px;
      background: #f8f8f9;
      margin-bottom: 20px;
      padding: 20px 10px;
      div{
        display: inline-block;
        width: 50px;
        height: 50px;
        overflow: hidden;
        border: 1px solid rgb(239, 239, 239);
        position: relative;
        margin-right: 10px;

        img{
          width: 100%;
          display: inline-block;
          vertical-align: middle;
        }
        span{
          position: absolute;
          left: -1px;
          top: -1px;
          width: 13px;
          height: 13px;
          font-size: 13px;
          line-height: 13px;
          color: #fff;
          background: #ed3f14;
          border: 1px solid #fff;
          cursor: pointer;
          text-align: center;
          vertical-align: middle;
          box-sizing: content-box;
        }
      }
    }
  }
}
.event-edit,.release_modal{
  .end_date{
    .ivu-form-item-content{
      margin-left: 0 !important;
    }
  }
}
.event-list{
  .o-line{
    background: url("../../../images/release/o-line.png") no-repeat;
    background-size: 100%;
    left: 50%;
    top: 50%;
    margin-left: -17.5px;
    margin-top: -20px;
  }
}
.schools-add,.major-edit{
  .majors{
    .major{
      position: relative;

      .delItem{
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        color: #bbbec4;

      }
      &:hover{
        .delItem{
          color: #ed3f14;
        }
      }
    }
  }
}

.ivu-picker-confirm{
  .ivu-btn.ivu-btn-ghost.ivu-btn-small{
    display: none;
  }
}
.pic_btns.ivu-poptip-popper{
  min-width: 100px;
  .btns{

    .ivu-btn{
      display: block;
      margin:5px 0;
      width: 86px;
      border: none;
      border-radius: 4px;
      .ivu-icon{
        font-size: 18px;
      }
      &.link{
        color: #3AA1EC;
        background-color: #ebf5fd;
      }
      &.edit{
        color: #4EB985;
        background-color: #edf8f3;
      }
      &.del{
        color: #ed3f14;
        background-color: #bbbec4;
      }
    }
  }
}
.delete.ivu-poptip-popper{
  z-index: 9999;
}
.release_modal {
  input.ivu-input{
    height: 40px;
    border-radius: 0;
    font-size: 14px;
  }
  .ivu-select-single .ivu-select-selection{
    height: 42px;
  }
  .ivu-modal-footer{
    display: none;
  }
  .ivu-card-bordered{
    box-shadow: none;
    border: none;
  }
  .ivu-card-head{
    display: none;
  }
}
.core-list{
  .tool-bar{
    .ivu-input-group:hover{
      .ivu-icon-close-circled{
        right: 50px;
        top: 3px;
        display: block;
        cursor: pointer;
      }
    }
  }
  .clTable{
    .ivu-table-row td:first-child{
      /*padding: 0;*/

      .ivu-table-cell{
        padding: 0;
      }
    }
  }
}
.activity-add,.activity-edit{
  .ivu-form-item .ps{
    textarea{
      background: #EFFAF5;
      border: none;
      box-shadow: none;
    }
  }
}
.releaseModel{
  .ivu-spin-fix {
   /* position: fixed;*/
  }
}

