<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="headlines-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/event/headlines-add">
                        <Button type="success" icon="plus">添 加</Button>
                        <br>
                    </router-link>
                </div>
                <div class="edittable-con-1">
                    <Tabs value="name1">
                        <TabPane label="当前活动" name="name1">
                            <can-edit-table refs="dlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="nlRows" :columns-list="hlColumns"></can-edit-table>

                            <div style="margin-top: -60px;position: relative;">
                                <ul id="draglist" class="iview-admin-draggable-list">
                                    <li v-for="(item, index) in hlRows" :key="index" class="notwrap drag-item" :data-index="index">
                                        <div class="dragTable">
                                            <div class="tableRow">
                                                <div class="tableCell index">{{index + 1}}</div>
                                                <div class="tableCell subject">
                                                    <p><a :href="item.url1" target="_blank" :style="{color: item.color}">{{item.subject}}</a></p>
                                                </div>
                                                <div class="tableCell color">
                                                    <Poptip popper-class="color_pop" placement="top" width="170" :transfer="true" v-model="item.visible">
                                                        <Button type="ghost" class="red" v-if="item.color === 'red'"></Button>
                                                        <Button type="ghost" class="green" v-if="item.color === 'green'"></Button>
                                                        <Button type="ghost" class="blue" v-if="item.color === 'blue'"></Button>
                                                        <Button type="ghost" class="black" v-if="item.color === 'black'"></Button>
                                                        <div class="colors" slot="content">
                                                            <Button :class="c.color === item.color ? c.color + ' on' : c.color" v-for="c in colors" :key="c.index" @click="item.visible = false;changeColor(item,c.color,index)"></Button>
                                                        </div>
                                                    </Poptip>
                                                </div>
                                                <div class="tableCell end_date">{{item.end_date}}</div>
                                                <div class="tableCell handle">
                                                    <Icon type="edit" size="24" color="#2d8cf0" @click="editRow(item)"></Icon>
                                                    <Poptip
                                                            confirm
                                                            :transfer="true"
                                                            placement="top-end"
                                                            title="您确定要删除这条数据吗?"
                                                            @on-ok="delRow(item.id,index)"
                                                    >
                                                        <Icon type="trash-a" size="24" color="#ed3f14"></Icon>
                                                    </Poptip>

                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            <div class="page-bar">
                                <Page :total="hl_total" :page-size="page_size" :current="hl_current" @on-change="getheadlineslist" v-if="hl_showPage"></Page>
                            </div>
                        </TabPane>
                        <TabPane label="已结束活动" name="name2">
                            <can-edit-table refs="tlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="dlRows" :columns-list="dlColumns"></can-edit-table>
                            <div class="page-bar">
                                <Page :total="dl_total" :page-size="page_size" :current="dl_current" @on-change="getdellist" v-if="dl_showPage"></Page>
                            </div>
                        </TabPane>
                    </Tabs>
                </div>

            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import eData from '../data/event_data.js';
    import canEditTable from './components/canEditTable.vue';
    import Sortable from 'sortablejs';

    export default {
        name: 'headlines_list',
        components: {
            canEditTable
        },
        data () {
            return {
                hlRows: [],
                dlRows: [],
                nlRows: [],
                hlColumns: [],
                dlColumns: [],
                hl_total: 0,
                dl_total: 0,
                hl_showPage: false,
                dl_showPage: false,
                page_size: 20,
                hl_current: 1,
                dl_current: 1,
                isAccess: true,
                keyword: '',
                s_key: '',
                colors: [
                    {
                        color: 'red'
                    },
                    {
                        color: 'green'
                    },
                    {
                        color: 'blue'
                    },
                    {
                        color: 'black'
                    }
                ],
                visible: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'headlines_list'){
                    this.$store.commit('removeTag', 'headlines_update');
                    this.$store.commit('closePage', 'headlines_update');
                    this.getData();
                }
            }
        },
        mounted () {
            document.body.ondrop = function (event) {
                event.preventDefault();
                event.stopPropagation();
            };
            let vm = this;
            let draglist = document.getElementById('draglist');
            Sortable.create(draglist, {
                group: {
                    name: 'list',
                    pull: true
                },
                animation: 120,
                ghostClass: 'placeholder-style',
                fallbackClass: 'iview-admin-cloned-item',
                onEnd (evt){
                    var data = {
                        type: 3,
                        ids: []
                    };
                    for(var i=0;i<vm.hlRows.length;i++){
                        data.ids.push(vm.hlRows[i].id);
                    }
                    data.ids.splice(evt.oldIndex, 1);
                    data.ids.splice(evt.newIndex, 0, vm.hlRows[evt.item.getAttribute('data-index')].id);

                    vm.handleChange(data);

                }
            });
        },
        methods: {
            getData () {
                this.hlColumns = eData.hlColumns;
                this.dlColumns = eData.dhlColumns;
                this.hlRows = [];
                this.dlRows = [];
                this.getheadlineslist(1);
                this.getdellist(1);
            },
            getheadlineslist (n){
                this.hlRows = [];
                this.hl_total = 0;
                this.hl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'GET',
                    params: {
                        page: _this.hl_current,
                        page_size: _this.page_size,
                        type: 3,
                        status: 0
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.hlRows = res.data.data.rows;
                        for(var i=0;i<_this.hlRows.length;i++){
                            if(!!_this.hlRows[i].event_end_date){
                                _this.hlRows[i].end_date = new Date(parseInt(_this.hlRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.hlRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.hlRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.hlRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.hlRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.hlRows[i].end_date = '';
                            }
                            if(!_this.hlRows[i].color){
                                _this.hlRows[i].color = 'black';
                            }
                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }
                        _this.hl_total = res.data.data.count;
                        if(_this.hl_total > _this.page_size){
                            _this.hl_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getdellist (n){
                this.dlRows = [];
                this.dl_total = 0;
                this.dl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'GET',
                    params: {
                        page: _this.dl_current,
                        page_size: _this.page_size,
                        type: 3,
                        status: -1
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.dlRows = res.data.data.rows;
                        for(var i=0;i<_this.dlRows.length;i++){
                            if(!!_this.dlRows[i].event_end_date){
                                _this.dlRows[i].end_date = new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].end_date = '';
                            }
                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }
                        _this.dl_total = res.data.data.count;
                        if(_this.dl_total > _this.page_size){
                            _this.dl_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleChange (dataIn) {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/change_order',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.hlRows = [];
                        _this.getheadlineslist(1);

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            editRow (item){
                this.$store.commit('geteventRow', item);
                this.$router.push({
                    name: 'headlines_edit'
                });
            },
            changeColor(item,c,i){
                var color = c;
                if(c === 'black'){
                    color = '';
                }
                var dataIn = {
                    id: item.id,
                    subject: item.subject,
                    url1: item.url1,
                    type: 3,
                    event_end_date: item.event_end_date,
                    color: color
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        item.visible = false;
                        _this.$Message.success('修改成功！');
                        item.color = c;
                    }else{
                        console.log(res.msg);
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });



            },
            delRow (id,index){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'DELETE',
                    data: {
                        id: id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.hlRows.splice(index, 1);
                        _this.handleUpdata();
                        _this.handleDel(_this.hlRows[index],index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            handleUpdata (){
                this.getData();
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
