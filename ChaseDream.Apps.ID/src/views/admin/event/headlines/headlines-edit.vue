<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="headlines-edit" style="width: 100%;">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="edit"></Icon>
                    编辑 推3
                </p>
                <Form ref="form" :model="form" :label-width="120">
                    <FormItem prop="subject" label="标题：" class="ivu-form-item-required">
                        <Input v-model="form.subject" placeholder="请输入标题"></Input>
                    </FormItem>
                    <FormItem prop="event_end_date" class="ivu-form-item-required" label="推送 时间：">
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="form.event_begin_date" placeholder="请选择活动开始时间" style="width: 250px; display: inline-block;" :time-picker-options="{steps: [1, 15, 15]}"></DatePicker>
                        <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="form.event_end_date" placeholder="请选择活动结束时间" style="width: 250px;display: inline-block" :time-picker-options="{steps: [1, 15, 15]}"></DatePicker>
                    </FormItem>
                    <FormItem prop="url1" label="WWW：" class="ivu-form-item-required">
                        <Input v-model="form.url1" placeholder="请输入URL"></Input>
                    </FormItem>
                    <FormItem prop="color" label="颜色：" class="ivu-form-item-required colors">
                        <Button :class="c.color === form.color ? c.color + ' on' : c.color" v-for="c in colors" :key="c.index" @click="changeColor(c.color)"></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary" v-if="!isDisabled">提交</Button>
                        <Button type="primary" v-if="!!isDisabled">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'headlines_edit',
        components: {

        },
        data () {
            return {
                form: {
                    subject: '',
                    url1: '',
                    color: '',
                    event_begin_date: '',
                    event_end_date: '',
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                colors: [
                    {
                        color: 'red'
                    },
                    {
                        color: 'green'
                    },
                    {
                        color: 'blue'
                    },
                    {
                        color: 'black'
                    }
                ],
                isWin: false,
                isDisabled: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'headlines_edit'){
                    this.form.subject = '';
                    this.form.url1 = '';
                    this.form.color = '';
                    this.form.event_begin_date = '';
                    this.form.event_end_date = '';
                    this.s_e_date = [0,0];
                    this.isWin = false;
                    this.isDisabled = false;
                    this.$refs['form'].resetFields();
                    if(window.location.href.split('/').pop() === 'core-list'){
                        this.isWin = true;
                    }
                    this.getData();
                }
            }
        },
        mounted () {
            this.isWin = false;
            if(window.location.href.split('/').pop() === 'core-list'){
                this.isWin = true;
            }
            this.getData();
        },
        methods: {
            getData (){
                this.form = this.$store.state.event.currentRow;
                if(!this.form.event_begin_date){
                    this.form.event_begin_date = 0;
                }else {
                    this.form.event_begin_date = new Date(parseInt(this.form.event_begin_date + '000'));
                }
                if(!this.form.event_end_date){
                    this.form.event_end_date = 0;
                }else {
                    this.form.event_end_date = new Date(parseInt(this.form.event_end_date + '000'));
                }

                this.s_e_date = [this.form.event_begin_date,this.form.event_end_date];
            },
            getS_E_date(e){

            },
            changeColor(c){
                this.form.color = c;
            },
            handleSubmit () {
                var dataIn = {
                    subject: this.form.subject,
                    url1: this.form.url1,
                    color: this.form.color === 'black' ? '' : this.form.color,
                    event_begin_date: !!this.form.event_begin_date ? Math.round(this.form.event_begin_date.getTime()/1000) : 0,
                    event_end_date: !!this.form.event_end_date ? Math.round(this.form.event_end_date.getTime()/1000) : 0,
                    push_begin_date: !!this.form.event_begin_date ? Math.round(this.form.event_begin_date.getTime()/1000) : 0,
                    push_end_date: !!this.form.event_end_date ? Math.round(this.form.event_end_date.getTime()/1000) : 0,
                    type: 3
                };
                if(!this.form.event_id){
                    dataIn.event_id = this.form.id;

                }else{
                    dataIn.id = this.form.id;
                }

                if(!dataIn.subject){
                    this.$Notice.error({
                        title: '请填写活动标题！'
                    });
                    return false;
                }else if(dataIn.subject.length < 3 || dataIn.subject.length > 120){
                    this.$Notice.error({
                        title: '活动标题字数不能小于3或大于120！'
                    });
                    return false;
                }
                if(!dataIn.event_begin_date){
                    this.$Notice.error({
                        title: '请选择活动开始时间！'
                    });
                    return false;
                }
                if(!dataIn.event_end_date){
                    this.$Notice.error({
                        title: '请选择活动结束时间！'
                    });
                    return false;
                }
                if(!dataIn.url1){
                    this.$Notice.error({
                        title: '请填写WWW地址！'
                    });
                    return false;
                }
                if(!dataIn.color && dataIn.color !== ''){
                    this.$Notice.error({
                        title: '请选择颜色！'
                    });
                    return false;
                }
                this.isDisabled = true;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method: !this.form.event_id ? 'POST' : 'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '提交成功！！'
                        });
                        if(!!_this.isWin){
                            _this.$emit('on-close');
                        }else {
                            _this.$store.commit('removeTag', 'headlines_edit');
                            _this.$store.commit('closePage', 'headlines_edit');
                            _this.$router.push({
                                name: 'headlines_list'
                            });
                        }
                    }else{
                        _this.isDisabled = false;
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.isDisabled = false;
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {};
                if(!!this.isWin){
                    this.$emit('on-close');
                }else {
                    this.$store.commit('removeTag', 'headlines_edit');
                    this.$router.push({
                        name: 'headlines_list'
                    });
                }
            }
        },
        created () {

        }
    };
</script>