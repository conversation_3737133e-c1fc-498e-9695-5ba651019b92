<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="headlines-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加 推3
                </p>
                <Form ref="form" :model="form" :label-width="120">
                    <FormItem prop="subject" label="标题：" class="ivu-form-item-required">
                        <Input v-model="form.subject" placeholder="请输入标题"></Input>
                    </FormItem>
                    <FormItem prop="event_end_date" class="ivu-form-item-required" label="推送时间：">
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="form.begin_date" placeholder="请选择活动开始时间" style="width: 250px; display: inline-block;" :time-picker-options="{steps: [1, 15, 15]}"></DatePicker>
                        <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="form.end_date" placeholder="请选择活动结束时间" style="width: 250px;display: inline-block" :time-picker-options="{steps: [1, 15, 15]}"></DatePicker>
                    </FormItem>
                    <FormItem prop="url1" label="WWW URL：" class="ivu-form-item-required">
                        <Input v-model="form.url1" placeholder="请输入WWW URL"></Input>
                    </FormItem>
                    <FormItem prop="color" label="颜色：" class="ivu-form-item-required colors">
                        <RadioGroup v-model="form.color">
                            <Button :class="c.color === form.color ? c.color + ' on' : c.color" v-for="c in colors" :key="c.index" @click="changeColor(c.color)"></Button>
                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary" v-if="!isDisabled">提交</Button>
                        <Button type="primary" disabled v-if="!!isDisabled">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'headlines_add',
        components: {

        },
        data () {
            return {
                form: {
                    subject: '',
                    url1: '',
                    color: 0,
                    begin_date: '',
                    end_date: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                colors: [
                    {
                        color: 'red'
                    },
                    {
                        color: 'green'
                    },
                    {
                        color: 'blue'
                    },
                    {
                        color: 'black'
                    }
                ],
                isDisabled: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'headlines_add'){
                    this.form.subject = '';
                    this.form.url1 = '';
                    this.form.color = 0;
                    this.form.begin_date = new Date();
                    this.form.end_date = new Date(new Date().getTime() + 1000*60*15);
                    this.isDisabled = false;
                    this.$refs['form'].resetFields();
                }
            }
        },
        mounted () {
            this.form.begin_date = new Date();
            this.form.end_date = new Date(new Date().getTime() + 1000*60*15);
        },
        methods: {
            changeColor(c){
                this.form.color = c;
            },
            handleSubmit () {
                var dataIn = {
                    subject: this.form.subject,
                    url1: this.form.url1,
                    color: this.form.color === 'black' ? '' : this.form.color,
                    event_begin_date: (!!this.s_e_date && !!this.s_e_date[0]) ? Math.round(this.s_e_date[0].getTime()/1000) : 0,
                    event_end_date: (!!this.s_e_date && !!this.s_e_date[1]) ? Math.round(this.s_e_date[1].getTime()/1000) : 0,
                    push_begin_date: (!!this.s_e_date && !!this.s_e_date[0]) ? Math.round(this.s_e_date[0].getTime()/1000) : 0,
                    push_end_date: (!!this.s_e_date && !!this.s_e_date[1]) ? Math.round(this.s_e_date[1].getTime()/1000) : 0,
                    event_id: 0,
                    type: 3
                };
                if(!dataIn.subject){
                    this.$Notice.error({
                        title: '请填写活动标题！'
                    });
                    return false;
                }else if(dataIn.subject.length < 3 || dataIn.subject.length > 120){
                    this.$Notice.error({
                        title: '活动标题字数不能小于3或大于120！'
                    });
                    return false;
                }
                if(!dataIn.url1){
                    this.$Notice.error({
                        title: '请填写WWW地址！'
                    });
                    return false;
                }
                if(!dataIn.color && dataIn.color !== ''){
                    this.$Notice.error({
                        title: '请选择颜色！'
                    });
                    return false;
                }
                this.isDisabled = true;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'headlines_add');
                        _this.$store.commit('closePage', 'headlines_add');
                        _this.$router.push({
                            name: 'headlines_list'
                        });
                    }else{
                        _this.isDisabled = false;
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.isDisabled = false;
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form.subject = '';
                this.form.url1 = '';
                this.form.color = 0;
                this.form.begin_date = new Date();
                this.form.end_date = new Date(new Date().getTime() + 1000*60*15);
                this.isDisabled = false;
                this.$store.commit('removeTag', 'headlines_add');
                this.$router.push({
                    name: 'headlines_list'
                });
            }
        },
        created () {

        }
    };
</script>