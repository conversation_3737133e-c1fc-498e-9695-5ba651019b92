import util from '@/libs/util.js';
export const elColumns = [
    {
        title: '日期',
        align: 'center',
        width: 90,
        key: 'time',
        render: (h, params) => {
            var weeks = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
            var months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            var month = months[params.row.event_begin_date.split('-')[1] - 1];
            var day = params.row.event_begin_date.split('-')[2].split(' ')[0];
            var week = weeks[new Date(params.row.event_begin_date.split(' ')[0].split('-').join('/')).getDay()];
            return h('div', {
                style: {
                    padding: '15px 0'
                }
            }, [
                h('div', {
                    style: {
                        position: 'relative',
                        height: '45px',
                        width: '50px'
                    }
                }, [
                    h('span', {
                        style: {
                            position: 'absolute',
                            left: '8px',
                            top: 0,
                            fontSize: '10px',
                            fontWeight: 'bold',
                            color: '#D93434'
                        }
                    }, month),
                    h('span', {
                        attrs: {
                            class: 'o-line'
                        },
                        style: {
                            position: 'absolute',
                            width: '35px',
                            height: '40px',
                        }
                    }),
                    h('span', {
                        style: {
                            position: 'absolute',
                            right: 0,
                            bottom: 0,
                            fontSize: '20px',
                            fontWeight: 'bold',
                            color: '#D93434'
                        }
                    }, day)
                ]),
                h('span', {}, week)
            ]);
        }
    },
    {
        title: 'logo',
        align: 'center',
        width: 100,
        key: 'image',
        render: (h, params) => {

            return h('div', {
                style: {
                    width: '60px',
                    height: '60px'
                }
            }, [
                h('img', {
                    attrs: {
                        src: params.row.image
                    },
                    style: {
                        width: '100%',
                        verticalAlign: 'top'
                    }
                })
            ]);
        }
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('span', {
            }, params.row.subject)
        }
    },
    {
        title: '展示/开始结束时间',
        align: 'center',
        width: 140,
        key: 'push_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.push_begin_date),
                h('p', {}, params.row.push_end_date)
            ])
        }
    },
    {
        title: '活动/开始结束时间',
        align: 'center',
        width: 140,
        key: 'event_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.event_begin_date),
                h('p', {}, params.row.event_end_date)
            ])
        }
    },
    {
        title: '最新标签时间',
        align: 'center',
        width: 140,
        key: 'new_flag_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];
export const alColumns = [
    {
        title: '地点',
        align: 'center',
        width: 90,
        key: 'location',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.location),
                h('p', {}, params.row.event_begin_date)
            ])
        }
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
    },
    {
        title: '展示/开始结束时间',
        align: 'center',
        width: 140,
        key: 'push_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.push_begin_date),
                h('p', {}, params.row.push_end_date)
            ])
        }
    },
    {
        title: '活动/开始结束时间',
        align: 'center',
        width: 140,
        key: 'event_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.begin_date),
                h('p', {}, params.row.end_date)
            ])
        }
    },
    {
        title: '最新标签时间',
        align: 'center',
        width: 140,
        key: 'new_flag_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];
export const hlColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'id',
        align: 'center'
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: params.row.url1,
                    target: '_blank'
                },
                style: {
                    color: params.row.color
                }

            }, params.row.subject)
        }
    },
    {
        title: '颜色',
        align: 'center',
        width: 100,
        key: 'action',
        action: ['colorpick']
    },
    {
        title: '结束时间',
        align: 'center',
        width: 150,
        key: 'end_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    },
];
export const plColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'id',
        align: 'center'
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: params.row.url1,
                    target: '_blank'
                }
            }, params.row.subject)
        }
    },
    {
        title: '结束时间',
        align: 'center',
        width: 150,
        key: 'end_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit']
    }
];
export const uColumns = [
    {
        title: 'ID',
        width: 80,
        key: 'id',
        align: 'center'
    },
    {
        title: '账号',
        align: 'center',
        key: 'nickname',
    },
    {
        title: '用户类型',
        align: 'center',
        width: 150,
        key: 'type',
        render: (h, params) => {
            return h('span', {}, params.row.type === 1 ? '论坛用户' : 'WWW用户');
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['edit', 'delete']
    },
]

export const clColumns = [
    {
        title: '',
        key: 'id',
        width: 70,
        align: 'center',
        class: 'index_col',
        render: (h, params) => {
            var bg = false;
            if (params.row.stick > 0) {
                var e_d = 0;
                if (params.row.event_location.length > 0) {
                    if (params.row.event_location.length === 1) {
                        e_d = params.row.event_location[0].end_date
                    } else {
                        var d_arr = [];
                        for (var i = 0; i < params.row.event_location.length; i++) {
                            d_arr.push(params.row.event_location[i].end_date)
                        }
                        d_arr.sort();
                        e_d = d_arr.pop();
                    }
                    if (parseInt(e_d + '000') < new Date().getTime()) {
                        bg = true;
                    }
                }
            }
            return h('div', {
                style: {
                    background: !!bg ? '#FDEFF0' : 'none',
                    width: '100%',
                    height: '80px',
                    lineHeight: '80px'
                }
            }, params.row.id)
        }
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject'
    },
    {
        title: '学校',
        align: 'center',
        key: 'event_school',
        width: 130,
        render: (h, params) => {
            return h('span', {}, params.row.event_school && params.row.event_school.display_name)
        }
    },
    {
        title: '发布',
        align: 'center',
        width: 95,
        key: 'publish',
        action: ['publish']
    },
    {
        title: '推送',
        align: 'center',
        width: 120,
        key: 'release',
        action: ['release']
    },
    {
        title: '高亮/置顶',
        align: 'center',
        key: 'action',
        width: 110,
        action: ['colorpick', 'dropDown']
    },
    {
        title: '精华',
        align: 'center',
        key: 'digest',
        width: 80,
        action: ['iSwich']
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];
export const dclColumns = [
    {
        title: '',
        key: 'id',
        width: 70,
        align: 'center'
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
    },
    {
        title: '学校',
        align: 'center',
        key: 'event_school',
        width: 130,
        render: (h, params) => {
            return h('span', {}, !!params.row.event_school ? params.row.event_school.display_name : '')
        }
    },
    {
        title: '发布',
        align: 'center',
        width: 95,
        key: 'publish',
        action: ['publish']
    },
    {
        title: '推送',
        align: 'center',
        width: 120,
        key: 'release',
        action: ['release']
    },
    {
        title: '高亮/置顶',
        align: 'center',
        key: 'action',
        width: 110,
        action: ['colorpick', 'dropDown']
    },
    {
        title: '精华',
        align: 'center',
        key: 'digest',
        width: 80,
        action: ['iSwich']
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete'] //,
    }
];
export const delColumns = [
    {
        title: '日期',
        align: 'center',
        width: 90,
        key: 'time',
        render: (h, params) => {
            var weeks = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
            var months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            var month = months[params.row.event_begin_date.split('-')[1] - 1];
            var day = params.row.event_begin_date.split('-')[2].split(' ')[0];
            var week = weeks[new Date(params.row.event_begin_date.split(' ')[0].split('-').join('/')).getDay()];
            return h('div', {
                style: {
                    padding: '15px 0'
                }
            }, [
                h('div', {
                    style: {
                        position: 'relative',
                        height: '45px',
                        width: '50px'
                    }
                }, [
                    h('span', {
                        style: {
                            position: 'absolute',
                            left: '8px',
                            top: 0,
                            fontSize: '10px',
                            fontWeight: 'bold',
                            color: '#D93434'
                        }
                    }, month),
                    h('span', {
                        attrs: {
                            class: 'o-line'
                        },
                        style: {
                            position: 'absolute',
                            width: '35px',
                            height: '40px',
                        }
                    }),
                    h('span', {
                        style: {
                            position: 'absolute',
                            right: 0,
                            bottom: 0,
                            fontSize: '20px',
                            fontWeight: 'bold',
                            color: '#D93434'
                        }
                    }, day)
                ]),
                h('span', {}, week)
            ]);
        }
    },
    {
        title: 'logo',
        align: 'center',
        width: 100,
        key: 'image',
        render: (h, params) => {

            return h('div', {
                style: {
                    width: '60px',
                    height: '60px'
                }
            }, [
                h('img', {
                    attrs: {
                        src: params.row.image
                    },
                    style: {
                        width: '100%',
                        verticalAlign: 'top'
                    }
                })
            ]);
        }
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('span', {
            }, params.row.subject)
        }
    },
    {
        title: '展示/开始结束时间',
        align: 'center',
        width: 140,
        key: 'push_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.push_begin_date),
                h('p', {}, params.row.push_end_date)
            ])
        }
    },
    {
        title: '活动/开始结束时间',
        align: 'center',
        width: 140,
        key: 'event_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.event_begin_date),
                h('p', {}, params.row.event_end_date)
            ])
        }
    },
    {
        title: '最新标签时间',
        align: 'center',
        width: 140,
        key: 'new_flag_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['delete']
    }
];
export const dlColumns = [
    {
        title: '地点',
        align: 'center',
        width: 120,
        key: 'event_geo',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {
                    style: {
                        color: '#1f85f4'
                    }
                }, params.row.event_geo ? params.row.event_geo.name : ''),
                h('p', {}, params.row.m_date ? params.row.m_date : '')
            ])
        }
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject'
    },
    {
        title: '展示/开始结束时间',
        align: 'center',
        width: 140,
        key: 'push_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.push_begin_date),
                h('p', {}, params.row.push_end_date)
            ])
        }
    },
    {
        title: '活动/开始结束时间',
        align: 'center',
        width: 140,
        key: 'event_date',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.begin_date),
                h('p', {}, params.row.end_date)
            ])
        }
    },
    {
        title: '最新标签时间',
        align: 'center',
        width: 140,
        key: 'new_flag_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit']
    }
];
export const dhlColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'id',
        align: 'center'
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: params.row.url1,
                    target: '_blank'
                },
                style: {
                    color: params.row.color
                }
            }, params.row.subject)
        }
    },
    {
        title: '颜色',
        align: 'center',
        width: 100,
        key: 'color',
        render: (h, params) => {
            var color = '#000';
            if (params.row.color === 'green') {
                color = '#1ab394';
            } else if (params.row.color === 'blue') {
                color = '#2897c5';
            } else if (params.row.color === 'red') {
                color = '#ff0101';
            } else {
                color = '#000';
            }
            return h('span', {
                style: {
                    background: color,
                    display: 'inline-block',
                    width: '25px',
                    height: '25px',
                    borderRadius: '5px'
                }
            });
        }
    },
    {
        title: '结束时间',
        align: 'center',
        width: 150,
        key: 'end_date'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit']
    }
];
export const sColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: '显示全称/显示简称',
        align: 'left',
        key: 'name',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.school_name),
                h('p', {}, params.row.university_name)
            ])
        }
    },
    {
        title: '官网',
        align: 'center',
        width: 100,
        key: 'website',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: params.row.website,
                    target: '_blank'
                }
            }, '官网')
        }
    },
    {
        title: '地区/国家',
        align: 'center',
        width: 150,
        key: 'location',
        render: (h, params) => {
            return h('div', {
            }, [
                h('p', {}, params.row.area),
                h('p', {}, params.row.country)
            ])
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'edit_m']
    }
];

const tData = {
    elColumns: elColumns,
    alColumns: alColumns,
    hlColumns: hlColumns,
    plColumns: plColumns,
    uColumns: uColumns,
    clColumns: clColumns,
    dclColumns: dclColumns,
    delColumns: delColumns,
    dlColumns: dlColumns,
    dhlColumns: dhlColumns,
    sColumns: sColumns
};

export default tData;
