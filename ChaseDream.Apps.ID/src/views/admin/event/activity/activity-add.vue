<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="activity-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加活动
                </p>
                <Form ref="form" :model="form" :label-width="150">
                    <FormItem label="HTML标签内容：" class="ivu-form-item-required">
                        <Input v-model="form.subject" type="textarea" :autosize="{minRows: 5,maxRows: 8}" placeholder="请输入活动内容"></Input>
                    </FormItem>
                    <div class="hd" style="padding-left: 150px;">
                        <strong style="width: 283px;display: inline-block">活动开始时间</strong>
                        <strong style="width: 235px;display: inline-block">活动结束时间</strong>
                    </div>
                    <FormItem prop="begin_date" class="begin_data" label="活动时间：">
                        <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="form.event_begin_date" placeholder="请选择活动开始时间" :time-picker-options="{steps: [1, 15, 15]}" @on-change="setPushDate(form,'e_begin')" style="width: 250px; display: inline-block;" :options="options1"></DatePicker>
                        <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                        <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="form.event_end_date" placeholder="请选择活动结束时间" :time-picker-options="{steps: [1, 15, 15]}" @on-change="setPushDate(form,'e_end')" :options="options2" style="width: 250px;display: inline-block"></DatePicker>
                    </FormItem>
                    <div class="hd" style="padding-left: 150px;">
                        <strong style="width: 283px;display: inline-block">推送开始时间</strong>
                        <strong style="width: 235px;display: inline-block">推送结束时间</strong>
                    </div>
                    <FormItem prop="begin_date" class="begin_data" label="推送时间：">
                        <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="form.push_begin_date" placeholder="请选择推送开始时间" :time-picker-options="{steps: [1, 15, 15]}" @on-change="setPushDate(form,'begin')" :options="options3" style="width: 250px;display: inline-block"></DatePicker>
                        <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                        <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="form.push_end_date" placeholder="请选择推送结束时间" @on-change="setPushDate(form,'end')" :options="options4" style="width: 250px;display: inline-block"></DatePicker>
                    </FormItem>
                    <FormItem prop="new_flag_date" label="最新标签下线时间：" class="ivu-form-item-required">
                        <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="form.new_flag_date" placeholder="请选择显示最新的时间" :time-picker-options="{steps: [1, 15, 15]}" @on-change="setPushDate(form,'flag')" :options="options5" style="width: 250px;" ></DatePicker>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary" v-if="!isDisabled">提交</Button>
                        <Button type="primary" v-if="!!isDisabled">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                    <FormItem label="HTML标签示例：">
                        <Input v-model="content" type="textarea" class="ps" readonly :autosize="{minRows: 3,maxRows: 8}" ></Input>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'activity_add',
        components: {

        },
        data () {
            return {
                form: {
                    subject: '<tr><td colspan="2" style="padding: 5px 0;"><i></i><a href="www URL|||forum URL " target="_blank">标题</a></td></tr>',
                    event_begin_date: '',
                    event_end_date: '',
                    push_begin_date: new Date(),
                    push_end_date: '',
                    new_flag_date: new Date(new Date().getTime() + 1000*60*60*72)
                },
                content:
                "<tr><td colspan=\"2\" style=\"padding: 5px 0;\"><i></i><a href=\"https://www.chasedream.com/show.aspx?id=31217&amp;cid=11|||https://forum.chasedream.com/thread-1374459-1-1.html\" target=\"_blank\">UCR商学院ABLE未来管理领袖体验营线上项目<img src=\"图片地址（图片请放到a标签内）\"></a></td></tr>",
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                options1: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options2: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options3: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options4: {
                    disabledDate (date) {
                        return date && date.valueOf() < new Date().getTime()-1000*60*60*24;
                    }
                },
                options5: {
                    disabledDate (date) {
                        return date && date.valueOf() < new Date().getTime() -1000*60*60*24
                    }
                },
                isDisabled: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'activity_add'){
                    this.form.subject = '<tr><td colspan="2" style="padding: 5px 0;"><i></i><a href="www URL|||forum URL " target="_blank">标题</a></td></tr>';
                    this.form.event_begin_date = '';
                    this.form.event_end_date = '';
                    this.form.push_begin_date = new Date();
                    this.form.push_end_date = '';
                    this.form.new_flag_date = new Date(new Date().getTime() + 1000*60*60*72);
                    this.isDisabled = false;
                    this.$refs['form'].resetFields();
                }
            }
        },
        mounted () {

        },
        methods: {
            setPushDate (item,str){
                if(str === 'e_begin'){
                    if(!!item.event_end_date && item.event_begin_date.getTime() > item.event_end_date.getTime()){
                        this.$Notice.error({
                            title: '活动结束时间不能大于活动开始时间！'
                        });
                        item.event_begin_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                    }else if(!!item.event_end_date && item.event_begin_date.getTime() +1000*60*15 >= item.event_end_date.getTime()){
                        item.push_end_date = item.event_end_date
                    }else if(!item.push_end_date){
                        item.push_end_date = new Date(item.event_begin_date.getTime() + 1000*60*15);
                    }
                    this.options2 = {
                        disabledDate (date) {
                            return date && date.valueOf() < item.event_begin_date.getTime()-1000*60*60*24
                        }
                    }
                }
                if(str === 'e_end'){
                    if(!!item.event_begin_date && item.event_begin_date.getTime() > item.event_end_date.getTime()){
                        this.$Notice.error({
                            title: '活动结束时间不能小于活动开始时间！'
                        });
                        item.event_end_date = new Date(item.event_begin_date.getTime() + 1000*60*15);
                    }else {
                        if(!!item.push_end_date && item.push_end_date.getTime() > item.event_end_date.getTime()){
                            item.push_end_date = item.event_end_date;
                        }else{
                            //item.push_end_date = new Date(item.event_begin_date.getTime() + 1000*60*15);
                        }
                        if(item.push_begin_date.getTime() > item.event_end_date.getTime()){
                            item.push_begin_date = new Date(item.event_end_date.getTime() - 1000*60*15);

                        }
                    }
                    this.options1 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.event_end_date.getTime()
                        }
                    }
                    if(!!item.push_end_date){
                        this.options3 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() > item.event_end_date.getTime()
                            }
                        }
                        this.options5 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }else {
                        this.options3 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.event_end_date.getTime()
                            }
                        }
                        this.options5 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.event_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }

                    this.options4 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.event_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }



                }
                if(str === 'begin'){
                    if(!!item.event_end_date && item.push_begin_date.getTime() > item.event_end_date.getTime()){
                        this.$Notice.error({
                            title: '推送开始时间不能大于活动结束时间！'
                        });
                        item.push_begin_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                    }
                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*72);
                    if(item.new_flag_date.getTime() >= item.push_end_date.getTime()){
                        item.new_flag_date = item.push_end_date;
                    }
                    if(!!item.event_end_date){
                        this.options4 = {
                            disabledDate (date) {
                                return date && date.valueOf() < item.push_begin_date.getTime() || date.valueOf() > item.event_end_date.getTime()
                            }
                        }
                    }else {
                        this.options4 = {
                            disabledDate (date) {
                                return date && date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }
                    if(!!item.push_end_date){
                        this.options5 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }else {
                        this.options5 = {
                            disabledDate (date) {
                                return date && date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }
                }
                if(str === 'end'){
                    if(!!item.event_end_date && item.push_end_date.getTime() > item.event_end_date.getTime()){
                        this.$Notice.error({
                            title: '推送结束时间不能大于活动结束时间！'
                        });
                        item.push_end_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                    }else if(item.push_end_date.getTime() < item.push_begin_date.getTime()){
                        this.$Notice.error({
                            title: '推送结束时间不能大于推送开始时间！'
                        });
                        item.push_end_date = new Date(item.push_begin_date.getTime() + 1000*60*15);
                    }
                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*72);
                    if(item.new_flag_date.getTime() > item.push_end_date.getTime()){
                        item.new_flag_date = item.push_end_date
                    }

                    this.options3 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime();
                        }
                    }
                    this.options5 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }
                }
                if(str === 'flag'){
                    if(!!item.push_end_date && item.new_flag_date.getTime() >= item.push_end_date.getTime()){
                        this.$Notice.error({
                            title: '最新标签下线时间不能大于推送结束时间！'
                        });
                        item.new_flag_date = item.push_end_date;
                    }
                }
            },
            handleSubmit () {
                var dataIn = {
                    subject: this.form.subject,
                    event_begin_date: !!this.form.event_begin_date ? Math.round(this.form.event_begin_date.getTime()/1000) : 0,
                    event_end_date: !!this.form.event_end_date ? Math.round(this.form.event_end_date.getTime()/1000) : 0,
                    push_begin_date: !!this.form.push_begin_date ? Math.round(this.form.push_begin_date.getTime()/1000) : 0,
                    push_end_date: !!this.form.push_end_date ? Math.round(this.form.push_end_date.getTime()/1000) : 0,
                    new_flag_date: !!this.form.new_flag_date ? Math.round(this.form.new_flag_date.getTime()/1000) : 0,
                    type: 1,
                    html: 1
                };
                this.isDisabled = true;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.$store.commit('removeTag', 'activity_add');
                        _this.$store.commit('closePage', 'activity_add');
                        _this.$router.push({
                            name: 'activity_list'
                        });
                    }else{
                        _this.isDisabled = false;
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.isDisabled = false;
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form.subject = '';
                this.form.event_begin_date = '';
                this.form.event_end_date = '';
                this.form.push_begin_date = new Date();
                this.form.push_end_date = '';
                this.form.new_flag_date = new Date(new Date().getTime() + 1000*60*60*72);
                this.isDisabled = false;
                this.$store.commit('removeTag', 'activity_add');
                this.$router.push({
                    name: 'activity_list'
                });
            }
        },
        created () {

        }
    };
</script>