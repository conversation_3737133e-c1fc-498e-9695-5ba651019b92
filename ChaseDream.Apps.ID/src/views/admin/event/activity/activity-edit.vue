<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="activity-edit" style="width: 100%;">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="edit"></Icon>
                    编辑 推1
                </p>
                <Form ref="form" :model="form" :label-width="150">
                    <FormItem prop="html" label="通栏：" class="ivu-form-item-required" v-if="!form.html">
                        <RadioGroup v-model="form.html" @on-change="setHtml">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="url" label="WWW URL：" class="ivu-form-item-required" v-if="!form.html">
                        <Input v-model="form.url1" placeholder="请输入WWW地址"></Input>
                    </FormItem>
                    <FormItem prop="url2" label="论坛 URL：" class="ivu-form-item-required" v-if="!form.html">
                        <Input v-model="form.url2" placeholder="请输入Forum地址"></Input>
                    </FormItem>
                    <div class="geos" v-for="item,index in location" :key="item.index" style="padding: 10px 10px 10px 0;border: 1px solid #efefef;border-radius: 5px;">
                        <FormItem prop="locations" label="活动地区：" v-if="!form.html">
                            <div class="hd">
                                <strong style="width: 80px; display: inline-block">城市</strong>
                            </div>
                            <div>
                                <Checkbox v-model="item.checked">
                                    <span style="display: inline-block;width: 50px;">{{item.city_name}}</span>
                                </Checkbox>
                            </div>
                        </FormItem>
                        <FormItem prop="subject" label="活动标题：" v-if="!form.html" class="ivu-form-item-required">
                            <Input v-model="item.subject" placeholder="请输入活动标题"></Input>
                        </FormItem>
                        <FormItem prop="content" label="活动内容：" v-if="!!form.html" class="ivu-form-item-required" style="width: 95%">
                            <Input v-model="item.subject" type="textarea" :autosize="{minRows: 5,maxRows: 8}" placeholder="请输入活动内容"></Input>
                        </FormItem>
                        <div class="hd" style="padding-left: 150px;">
                            <strong style="width: 283px;display: inline-block">活动开始时间</strong>
                            <strong style="width: 235px;display: inline-block">活动结束时间</strong>
                        </div>
                        <FormItem prop="begin_date" class="begin_data" label="活动时间：">
                            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="item.begin_date" placeholder="请选择活动开始时间" style="width: 250px; display: inline-block;" readonly></DatePicker>
                            <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="item.end_date" placeholder="请选择活动结束时间" style="width: 250px;display: inline-block" readonly></DatePicker>
                            <Button type="primary" style="margin-left: 20px;background: #fff;color: #2d8cf0;border: 1px solid #2d8cf0;height: 40px;" @click="change_date(index)" v-if="item.update_date">更新活动时间</Button>
                        </FormItem>
                        <div class="hd" style="padding-left: 150px;">
                            <strong style="width: 283px;display: inline-block">推送开始时间</strong>
                            <strong style="width: 235px;display: inline-block">推送结束时间</strong>
                        </div>
                        <FormItem prop="begin_date" class="begin_data" label="推送时间：">
                            <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="item.push_begin_date" @on-change="setPushDate(item,'begin')" :options="item.options1" :time-picker-options="{steps: [1, 15, 15]}" placeholder="请选择推送开始时间" style="width: 250px;display: inline-block"></DatePicker>
                            <span style="display: inline-block;width: 30px;text-align: center"> - </span>
                            <DatePicker type="datetime" :clearable="false" format="yyyy-MM-dd HH:mm" v-model="item.push_end_date" placeholder="请选择推送结束时间" @on-change="setPushDate(item,'end')" :options="item.options2" :time-picker-options="{steps: [1, 15, 15]}" style="width: 250px;display: inline-block"></DatePicker>
                        </FormItem>
                        <FormItem prop="new_flag_date" label="最新标签下线时间：" class="ivu-form-item-required">
                            <DatePicker type="datetime"  :clearable="false" @on-change="setPushDate(item,'flag')" :options="item.options3" format="yyyy-MM-dd HH:mm" v-model="item.new_flag_date" placeholder="请选择显示最新的时间" style="width: 220px;" ></DatePicker>
                        </FormItem>
                        <Icon type="trash-a" size="24" class="delItem" @click="delRow(index)" v-if="location.length > 1"></Icon>
                    </div>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem prop="html" label="通栏：" class="ivu-form-item-required" v-if="!!form.html">
                        <RadioGroup v-model="form.html" @on-change="setHtml">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                    <FormItem label="HTML标签示例：" v-if="!!form.html">
                        <Input v-model="content" type="textarea" class="ps" readonly :autosize="{minRows: 3,maxRows: 8}" ></Input>
                    </FormItem>
                </Form>
                <Modal
                        title="提示"
                        v-model="isPush"
                        class-name="" width="300">
                    <div style="display: flex">
                        <Icon type="information-circled" size="35" style="color: #5cadff"></Icon>
                        <div style="padding-left: 20px;font-size: 14px;line-height: 24px;text-align: center;">
                            <p>核心活动内容新增<strong style="color: #19be6b"> {{newData}} </strong>条新地址信息</p>
                            <p>是否增加到推1内容中</p>
                        </div>
                    </div>
                    <div slot="footer">
                        <Button type="text" size="large" @click="isPush = false">取消</Button>
                        <Button type="info" size="large" @click="pushAll" v-if="!loading">确定</Button>
                        <Button type="info" size="large" v-if="loading">
                            确定
                            <Spin fix>
                            <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
                            <div>加载中...</div>
                        </Spin>
                        </Button>
                    </div>
                </Modal>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'activity_edit',
        components: {
            //canEditTable
        },
        data () {
            return {
                form: {},
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                schools: [],
                location: [],
                locations: [],
                majors:[],
                id: 0,
                options1: {
                    disabledDate (date) {
                        return date && date.valueOf() < Date.now() - 86400000;
                    }
                },
                I_index: 0,
                isPush: false,
                newData: 0,
                isWin: false,
                core:[],
                loading: false,
                content:
                '<tr><td colspan="2" style="padding: 5px 0;"><i></i><a href="https://www.chasedream.com/show.aspx?id=31217&amp;cid=11|||https://forum.chasedream.com/thread-1374459-1-1.html" target="_blank">UCR商学院ABLE未来管理领袖体验营线上项目<img src="图片地址（图片请放到a标签内）"></a></td></tr>',
                options1: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options2: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options3: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options4: {
                    disabledDate (date) {
                        return false;
                    }
                },
                options5: {
                    disabledDate (date) {
                        return false;
                    }
                },
                isDisabled: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'activity_edit'){
                    this.form = {};
                    this.id = 1;
                    this.location = [];
                    this.schools = [];
                    this.majors = [];
                    this.I_index = 0;
                    this.isPush = false;
                    this.newData = 0;
                    this.isWin = false;
                    this.core = [];
                    this.isDisabled = false;
                    this.id = window.location.href.split('/').pop();
                    if(this.id === 'core-list'){
                        this.isWin = true;
                    }
                    this.getData();
                }
            }
        },
        mounted () {
            this.isWin = false;
            this.id = window.location.href.split('/').pop();
            if(this.id === 'core-list'){
                this.isWin = true;
            }
            this.getData();
        },
        methods: {
            getData (){
                this.form_s = {};
                this.form_s = this.$store.state.event.currentRow;
                if(!this.form_s.html){
                    this.form_s.html = 0;
                }else {
                    this.form_s.html = 1;
                }
                this.form = JSON.parse(JSON.stringify(this.form_s));

                this.event_id = this.form.event_id;
                if(!!this.event_id){
                    this.getRelease();
                }else{
                    this.location[0] = {};
                    this.location[0] = this.form;
                }
                if(!!this.form.push_begin_date){
                    this.form.push_begin_date = new Date(this.form.push_begin_date).getTime();
                }else {

                }
                if(!!this.form.push_end_date){
                    this.form.push_end_date = new Date(this.form.push_end_date).getTime();
                }else {

                }
                if(!!this.form.new_flag_date){
                    this.form.new_flag_date = new Date(this.form.new_flag_date).getTime();
                }else {

                }

                this.setDates();
            },
            getRelease(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release/' + this.event_id + '/1',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        if(!_this.form.url1 && res.data.data.events.length > 0){
                            _this.form.url1 = res.data.data.events[0].url1;
                        }
                        if(!_this.form.url2 && res.data.data.events.length > 0){
                            _this.form.url2 = res.data.data.events[0].url2;
                        }
                        _this.locations = res.data.data.locations;
                        if(!!res.data.data.events[0] && !!res.data.data.events[0].html){
                            _this.form.html = 1;
                        }else {
                            _this.form.html = 0;
                        }

                        for(var i=0;i<res.data.data.events.length;i++){
                            var item = {};
                            item.id = res.data.data.events[i].id;
                            item.lid = res.data.data.events[i].lid;
                            item.country_id = res.data.data.events[i].country_id;
                            item.province_id = res.data.data.events[i].province_id;
                            item.location_id = res.data.data.events[i].location_id;
                            item.city_name = !!res.data.data.events[i].event_geo ? res.data.data.events[i].event_geo.name : '';
                            item.checked = true;
                            item.subject = res.data.data.events[i].subject;
                            item.event_id = res.data.data.events[i].event_id;
                            if(!!res.data.data.events[i].event_begin_date){
                                item.event_begin_date = res.data.data.events[i].event_begin_date;
                                item.begin_date = new Date(parseInt(res.data.data.events[i].event_begin_date + '000'));
                            }else {
                                item.event_begin_date = 0;
                                item.begin_date = 0;
                            }
                            if(!!res.data.data.events[i].event_end_date){
                                item.event_end_date = res.data.data.events[i].event_end_date;
                                item.end_date = new Date(parseInt(res.data.data.events[i].event_end_date + '000'));
                            }else {
                                item.end_date = 0;
                            }

                            if(!!res.data.data.events[i].push_begin_date){
                                item.push_begin_date = new Date(parseInt(res.data.data.events[i].push_begin_date + '000'));
                            }else {
                                item.push_begin_date= item.begin_date;
                            }
                            if(!!res.data.data.events[i].push_end_date){
                                item.push_end_date = new Date(parseInt(res.data.data.events[i].push_end_date + '000'));
                            }else {
                                if(!!item.push_begin_date){
                                    item.push_end_date = new Date(res.data.data.events[i].push_begin_date.getTime() + 1000*60*15);
                                }else {
                                    item.push_end_date = 0;
                                }
                            }

                            if(!!res.data.data.events[i].new_flag_date){
                                item.new_flag_date = new Date(parseInt(res.data.data.events[i].new_flag_date + '000'));
                            }else {
                                if(!!item.push_begin_date){
                                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*24*3);
                                }else {
                                    item.new_flag_date = 0
                                }
                            }

                            setOptions(item);
                        }
                        function setOptions(obj){
                            setTimeout(function () {
                                obj.options1 = {
                                    disabledDate (date) {
                                        return date && date.valueOf() > obj.push_end_date.getTime();
                                    }
                                }
                                obj.options2 = {
                                    disabledDate (date) {
                                        return date && date.valueOf() < obj.push_begin_date.getTime()-1000*60*60*24 || date.valueOf() > obj.end_date.getTime()
                                    }
                                }
                                obj.options3 = {
                                    disabledDate (date) {
                                        return date && date.valueOf() > obj.push_end_date.getTime() || date.valueOf() < obj.push_begin_date.getTime() -1000*60*60*24
                                    }
                                }
                                _this.location.push(obj);
                            },1000)
                        }

                        setTimeout(function () {
                            _this.core = res.data.data.locations;
                            for(var i=0;i<_this.location.length;i++){
                                for(var j=0;j<_this.core.length;j++){
                                    if(_this.location[i].lid === _this.core[j].id){
                                        if(_this.location[i].event_begin_date !== _this.core[j].begin_date || _this.location[i].event_end_date !== _this.core[j].end_date){
                                            _this.$set(_this.location[i],'update_date',true);
                                        }else {
                                            _this.$set(_this.location[i],'update_date',false);
                                        }
                                    }
                                }
                            }

                            if(_this.location.length < 1){
                                _this.pushAll();
                            }else if(_this.locations.length > _this.location.length){
                                _this.newData = _this.locations.length - _this.location.length;
                                _this.isPush = true;
                            }
                        },1300)
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            setDates(){
                if(!!this.form.event_begin_date){
                    this.form.event_begin_date = new Date(parseInt(this.form.event_begin_date + '000'));
                }else {
                    this.form.event_begin_date = '';
                }
                if(!!this.form.event_end_date){
                    this.form.event_end_date = new Date(parseInt(this.form.event_end_date + '000'));
                }else {
                    this.form.event_end_date = '';
                }
                if(!!this.form.push_begin_date){
                    this.form.push_begin_date = new Date(this.form.push_begin_date);
                }else {
                    this.form.push_begin_date = new Date();
                }
                if(!!this.form.push_end_date){
                    this.form.push_end_date = new Date(this.form.push_end_date);
                }else {
                    this.form.push_end_date = '';
                }
                if(!!this.form.new_flag_date){
                    this.form.new_flag_date = new Date(this.form.new_flag_date);
                }else {
                    this.form.new_flag_date = new Date(new Date().getTime() + 1000*60*60*72);
                }

                var _this = this;
                this.options1 = {
                    disabledDate (date) {
                        if(!!_this.form.event_end_date){
                            return date && date.valueOf() > _this.form.event_end_date.getTime();
                        }else{
                            return false;
                        }
                    }
                }
                this.options2 = {
                    disabledDate (date) {
                        if(!!_this.form.event_begin_date){
                            return date && date.valueOf() < _this.form.event_begin_date.getTime();
                        }else {
                            return false;
                        }
                    }
                }
                this.options3 = {
                    disabledDate (date) {
                        if(!!_this.form.push_end_date){
                            return date && date.valueOf() > _this.form.push_end_date.getTime();
                        }else {
                            return false
                        }

                    }
                }
                this.options4 = {
                    disabledDate (date) {
                        if(!!_this.form.event_end_date){
                            return date && date.valueOf() < _this.form.push_begin_date.getTime() -1000*60*60*24 || date.valueOf() > _this.form.event_end_date.getTime();
                        }else {
                            return date && date.valueOf() < _this.form.push_begin_date.getTime() -1000*60*60*24;
                        }

                    }
                }
                this.options5 = {
                    disabledDate (date) {
                        if(!!_this.form.push_end_date){
                            return date && date.valueOf() > _this.form.push_end_date.getTime() || date.valueOf() < _this.form.push_begin_date.getTime();
                        }else {
                            return date && date.valueOf() < _this.form.push_begin_date.getTime();
                        }

                    }
                }
            },
            setPushDate (item,str,n){
                if(str === 'e_begin'){
                    if(!!item.event_end_date && item.event_begin_date.getTime() > item.event_end_date.getTime()){
                        this.$Notice.error({
                            title: '活动结束时间不能大于活动开始时间！'
                        });
                        item.event_begin_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                    }else if(!!item.event_end_date && item.event_begin_date.getTime() +1000*60*15 >= item.event_end_date.getTime()){
                        item.push_end_date = item.event_end_date
                    }else if(!item.push_end_date){
                        item.push_end_date = new Date(item.event_begin_date.getTime() + 1000*60*15);
                    }
                    this.options2 = {
                        disabledDate (date) {
                            return date && date.valueOf() < item.event_begin_date.getTime()
                        }
                    }
                }
                if(str === 'e_end'){
                    if(!!item.event_begin_date && item.event_begin_date.getTime() > item.event_end_date.getTime()){
                        this.$Notice.error({
                            title: '活动结束时间不能小于活动开始时间！'
                        });
                        item.event_end_date = new Date(item.event_begin_date.getTime() + 1000*60*15);
                    }else {
                        if(!!item.push_end_date && item.push_end_date.getTime() > item.event_end_date.getTime()){
                            item.push_end_date = item.event_end_date;
                        }else{
                        }
                        if(item.push_begin_date.getTime() > item.event_end_date.getTime()){
                            item.push_begin_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                        }
                    }
                    this.options1 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.event_end_date.getTime()
                        }
                    }
                    if(!!item.push_end_date){
                        this.options3 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() > item.event_end_date.getTime()
                            }
                        }
                        this.options5 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }else {
                        this.options3 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.event_end_date.getTime()
                            }
                        }
                        this.options5 = {
                            disabledDate (date) {
                                return date && date.valueOf() > item.event_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                            }
                        }
                    }

                    this.options4 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.event_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }



                }
                if(str === 'begin'){
                    if(!!n){
                        if(item.push_begin_date.getTime() >= item.event_end_date.getTime()){
                            this.$Notice.error({
                                title: '推送开始时间不能大于活动结束时间！'
                            });
                            item.push_begin_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                        }
                    }else {
                        if(item.push_begin_date.getTime() >= item.end_date.getTime()){
                            this.$Notice.error({
                                title: '推送开始时间不能大于活动结束时间！'
                            });
                            item.push_begin_date = new Date(item.end_date.getTime() - 1000*60*15);
                        }
                    }

                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*72);
                    if(item.new_flag_date.getTime() >= item.push_end_date.getTime()){
                        item.new_flag_date = item.push_end_date;
                    }
                    if(!!n){
                        item.options2 = {
                            disabledDate (date) {
                                return date && date.valueOf() < item.push_begin_date.getTime() || date.valueOf() > item.event_end_date.getTime();
                            }
                        }
                    }else {
                        item.options2 = {
                            disabledDate (date) {
                                return date && date.valueOf() < item.push_begin_date.getTime() || date.valueOf() > item.end_date.getTime()
                            }
                        }
                    }

                    item.options3 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }
                }
                if(str === 'end'){
                    if(!!n){
                        if(item.push_end_date.getTime() >= item.event_end_date.getTime()){
                            this.$Notice.error({
                                title: '推送结束时间不能大于活动结束时间！'
                            });
                            item.push_end_date = new Date(item.event_end_date.getTime() - 1000*60*15);
                        }else if(item.push_end_date.getTime() <= item.push_begin_date.getTime()){
                            this.$Notice.error({
                                title: '推送结束时间不能大于推送开始时间！'
                            });
                            item.push_end_date = new Date(item.push_begin_date.getTime() + 1000*60*15);
                        }
                    }else {
                        if(item.push_end_date.getTime() >= item.end_date.getTime()){
                            this.$Notice.error({
                                title: '推送结束时间不能大于活动结束时间！'
                            });
                            item.push_end_date = new Date(item.end_date.getTime() - 1000*60*15);
                        }else if(item.push_end_date.getTime() <= item.push_begin_date.getTime()){
                            this.$Notice.error({
                                title: '推送结束时间不能大于推送开始时间！'
                            });
                            item.push_end_date = new Date(item.push_begin_date.getTime() + 1000*60*15);
                        }
                    }


                    item.new_flag_date = new Date(item.push_begin_date.getTime() + 1000*60*60*72);
                    if(item.new_flag_date.getTime() > item.push_end_date.getTime()){
                        item.new_flag_date = item.push_end_date
                    }
                    item.options1 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime();
                        }
                    }
                    item.options3 = {
                        disabledDate (date) {
                            return date && date.valueOf() > item.push_end_date.getTime() || date.valueOf() < item.push_begin_date.getTime()
                        }
                    }
                }
                if(str === 'flag'){
                    if(item.new_flag_date.getTime() >= item.push_end_date.getTime()){
                        this.$Notice.error({
                            title: '最新标签下线时间不能大于推送结束时间！'
                        });
                        item.new_flag_date = item.push_end_date;
                    }
                }
            },
            pushAll(){
                this.loading = true;
                var _this = this;
                var newArr = [];
                for(var i=0;i<this.locations.length;i++){
                    if(isIN(this.locations[i].id)){
                        this.locations[i].subject = this.form.subject;
                        this.locations[i].location_id = !!this.locations[i].city_id ? this.locations[i].city_id : this.locations[i].province_id;
                        this.locations[i].checked = true;
                        if(!!this.locations[i].begin_date){
                            this.locations[i].begin_date = new Date(parseInt(this.locations[i].begin_date + '000'));
                        }else {
                            this.locations[i].begin_date = 0;
                        }
                        if(!!this.locations[i].end_date){
                            this.locations[i].end_date = new Date(parseInt(this.locations[i].end_date + '000'));
                        }else {
                            this.locations[i].end_date = 0;
                        }

                        if(!!this.locations[i].push_begin_date){
                            this.locations[i].push_begin_date = new Date(parseInt(this.locations[i].push_begin_date + '000'));
                        }else {
                            this.locations[i].push_begin_date= new Date();
                        }
                        if(!!this.locations[i].push_end_date){
                            this.locations[i].push_end_date = new Date(parseInt(this.locations[i].push_end_date + '000'));
                        }else {
                            if(!!this.locations[i].begin_date){
                                this.locations[i].push_end_date = new Date(this.locations[i].begin_date.getTime() + 1000*60*15);
                            }else {
                                this.locations[i].push_end_date = 0;
                            }
                        }

                        if(!!this.locations[i].new_flag_date){
                            this.locations[i].new_flag_date = new Date(parseInt(this.locations[i].new_flag_date + '000'));
                        }else {
                            if(!!this.locations[i].push_begin_date){
                                this.locations[i].new_flag_date = new Date(this.locations[i].push_begin_date.getTime() + 1000*60*60*24*3);
                            }else {
                                this.locations[i].new_flag_date = 0;
                            }
                        }
                        newArr.push(this.locations[i]);
                    }
                }
                function isIN(id) {
                    for(var j=0;j<_this.location.length;j++){
                        if(id === _this.location[j].lid){
                            return false;
                        }
                    }
                    return true;
                }
                for(var i=0;i<newArr.length;i++){
                    this.getProvinceName(newArr[i].location_id,newArr,i);
                }
            },
            getSchool (i){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.schools = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getProvinceName (id, obj, n){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo/'+ id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        obj[n].city_name = res.data.data.name;
                        obj.options1 = {
                            disabledDate (date) {
                                return date && date.valueOf() > obj.push_end_date.getTime();
                            }
                        }
                        obj.options2 = {
                            disabledDate (date) {
                                return date && date.valueOf() < obj.push_begin_date.getTime() || date.valueOf() > obj.end_date.getTime()
                            }
                        }
                        obj.options3 = {
                            disabledDate (date) {
                                return date && date.valueOf() > obj.push_end_date.getTime() || date.valueOf() < obj.push_begin_date.getTime()
                            }
                        }
                        if(n == obj.length -1){
                            _this.location = [..._this.location,...obj]
                            _this.isPush = false;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                        console.log(res.msg)
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            setIndex(i){
                this.I_index = i;
            },
            setHtml (e){
                this.errMsg = '';
                this.$set(this.form,'html',e);
            },
            handleSubmit () {
                if(!this.form.html){
                    var dataIn = {
                        subject: this.form.subject,
                        url1: this.form.url1,
                        url2: this.form.url2
                    };
                    if(!dataIn.subject){
                        this.errMsg = '请填写活动标题！';
                        return false;
                    }
                    if(!dataIn.url1){
                        this.errMsg = '请填写WWW地址！';
                        return false;
                    }
                    if(!dataIn.url2){
                        this.errMsg = '请填写Forum地址！';
                        return false;
                    }
                    for(var i=0;i<this.location.length;i++){
                        if(!this.location[i].subject || this.location[i].subject.length < 3 || this.location[i].subject.length > 120){
                            this.$Notice.error({
                                title: '活动标题字数不能小于3或大于120！'
                            });
                            return false;
                        }
                        if(!!this.location[i].lid){
                            var item = {
                                url1: dataIn.url1,
                                url2: dataIn.url2,
                                html: this.form.html,
                                major_id: 0,
                                id: this.location[i].id,
                                event_id: this.location[i].event_id,
                                subject: this.location[i].subject,
                                event_begin_date: Math.round(this.location[i].begin_date.getTime()/1000),
                                event_end_date: Math.round(this.location[i].end_date.getTime()/1000),
                                push_begin_date: Math.round(this.location[i].push_begin_date.getTime()/1000),
                                push_end_date: Math.round(this.location[i].push_end_date.getTime()/1000),
                                new_flag_date: Math.round(this.location[i].new_flag_date.getTime()/1000),
                                type: 1
                            }
                            this.subminAjax(item,1,'PUT');
                        }else {
                            var item = {
                                url1: dataIn.url1,
                                url2: dataIn.url2,
                                html: this.form.html,
                                event_id: this.location[i].event_id,
                                subject: this.location[i].subject,
                                new_flag_date: Math.round(this.location[i].new_flag_date.getTime()/1000),
                                type: 1,
                                location: [{
                                    lid: this.location[i].id,
                                    location_id: !!this.location[i].city_id ? this.location[i].city_id : this.location[i].province_id,
                                    event_begin_date: Math.round(this.location[i].begin_date.getTime()/1000),
                                    event_end_date: Math.round(this.location[i].end_date.getTime()/1000),
                                    push_begin_date: Math.round(this.location[i].push_begin_date.getTime()/1000),
                                    push_end_date: Math.round(this.location[i].push_end_date.getTime()/1000),
                                }]
                            }
                            this.subminAjax(item,1,'POST');
                        }

                    }
                }else {
                    for(var i=0;i<this.location.length;i++){
                        if(!this.location[i].subject){
                            this.$Notice.error({
                                title: '活动内容不能为空！'
                            });
                            return false;
                        }
                        if(!this.location[i].new_flag_date){
                            this.$Notice.error({
                                title: '请选择最新标签下线时间！'
                            });
                            return false;
                        }
                        if(!!this.location[i].lid || (!!this.location[i].id && !this.event_id)){
                            var item = {
                                html: this.form.html,
                                id: this.location[i].id,
                                subject: this.location[i].subject,

                                event_begin_date: this.location[i].begin_date ? Math.round(this.location[i].begin_date.getTime()/1000) : 0,
                                event_end_date: this.location[i].end_date ? Math.round(this.location[i].end_date.getTime()/1000) : 0,
                                push_begin_date: this.location[i].push_begin_date ? Math.round(this.location[i].push_begin_date.getTime()/1000) : 0,
                                push_end_date: this.location[i].push_end_date ? Math.round(this.location[i].push_end_date.getTime()/1000) : 0,
                                new_flag_date: this.location[i].new_flag_date ? Math.round(this.location[i].new_flag_date.getTime()/1000) : 0,
                                type: 1
                            }
                            this.subminAjax(item,1,'PUT');
                        }else {
                            var item = {
                                html: this.form.html,
                                subject: this.location[i].subject,
                                type: 1,
                                event_id: this.location[i].event_id,
                                lid: this.location[i].id,
                                location_id: !!this.location[i].city_id ? this.location[i].city_id : this.location[i].province_id,
                                event_begin_date: !!this.location[i].begin_date ? Math.round(this.location[i].begin_date.getTime()/1000) : 0,
                                event_end_date: !!this.location[i].end_date ? Math.round(this.location[i].end_date.getTime()/1000) : 0,
                                push_begin_date: !!this.location[i].push_begin_date ? Math.round(this.location[i].push_begin_date.getTime()/1000) : 0,
                                push_end_date: !!this.location[i].push_end_date ? Math.round(this.location[i].push_end_date.getTime()/1000) : 0,
                                new_flag_date: !!this.location[i].new_flag_date ? Math.round(this.location[i].new_flag_date.getTime()/1000) : 0
                            }
                            this.subminAjax(item,1,'POST');
                        }
                    }
                }
            },
            subminAjax (dataIn,n,method){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method: method,
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(n){
                            _this.I_index++;
                            if(_this.I_index >= _this.location.length){
                                _this.$Notice.success({
                                    title: '提交成功！'
                                });
                                _this.$store.commit('removeTag', 'activity_edit');
                                _this.$store.commit('closePage', 'activity_edit');
                                if(!!_this.isWin){
                                    _this.$emit('on-close');
                                }else{
                                    _this.$router.push({
                                        name: 'activity_list'
                                    });
                                }
                            }
                        }else {
                            _this.$Notice.success({
                                title: '提交成功！'
                            });
                            _this.$store.commit('removeTag', 'activity_edit');
                            _this.$store.commit('closePage', 'activity_edit');
                            if(!!_this.isWin){
                                _this.$emit('on-close');
                            }else{
                                _this.$router.push({
                                    name: 'activity_list'
                                });
                            }
                        }

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            delRow (i){
                if(!!this.location[i].lid){
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/event/release',
                        method:'DELETE',
                        data: {
                            id: this.location[i].id,
                            event_id: this.location[i].event_id
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.location.splice(i, 1);

                        }else{

                        }
                    }).catch(function (err) {
                        console.log(err)
                    });
                }else {
                    this.location.splice(i, 1);
                }
            },
            change_date(index){
                for(var i=0;i<this.core.length;i++){
                    if(this.core[i].id === this.location[index].lid){
                        this.location[index].begin_date = new Date(parseInt(this.core[i].begin_date + '000'));
                        this.location[index].end_date = new Date(parseInt(this.core[i].end_date + '000'));
                    }
                }
            },
            closePage () {
                this.form = {};
                this.country_id = 0;
                this.province_id = 0;
                this.city_id = 0;
                this.I_index = 0;
                this.isPush = false;
                this.newData = 0;
                this.core = [];
                this.$store.commit('removeTag', 'activity_edit');
                if(!!this.isWin){
                    this.$emit('on-close');
                }else{
                    this.$router.push({
                        name: 'activity_list'
                    });
                }
            }
        },
        created () {

        }
    };
</script>