<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="activity-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar" style="position: relative;">
                    <router-link to="/console/event/activity-add">
                        <Button type="success" icon="plus">添 加</Button>
                        <br>
                    </router-link>
                    <p style="position: absolute;right: 0;top: 0;width: 300px;text-align: right;color: #a6b2bf">
                        <span>web显示条数 （{{dataNubs}}）</span>
                        <Input v-model="showNub" size="small" placeholder="条数" style="width: 50px;"></Input>
                        <Button type="text" @click="setNubs" style="color: #2d8cf0;">更改</Button>
                    </p>
                    <p style="position: absolute;right: 300px;top: 10px;width: 300px;text-align: right;">
                        <span>广告显示位置：</span>
                        <RadioGroup v-model="push1_www_position" @on-change="set_www_position">
                            <Radio label="top" style="font-size: 14px;">上</Radio>
                            <Radio label="bottom" style="font-size: 14px;">下</Radio>
                        </RadioGroup>
                    </p>
                    <p style="position: absolute;right: 530px;top: 0;width: 150px;text-align: right;">
                        <span>本周热门：</span>
                        <Select v-model="push1_hot_num" @on-change="set_hot_show" style="width:50px;font-size: 14px;text-align: center;">
                            <Option v-for="item in list_num" :value="item.val" :key="item.index" style="font-size: 14px;">{{ item.label }}</Option>
                        </Select>
                    </p>
                </div>
                <div class="edittable-con-1">
                    <Tabs value="name1">
                        <TabPane label="当前活动" name="name1">
                            <can-edit-table refs="dlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="nlRows" :columns-list="alColumns"></can-edit-table>
                            <div style="margin-top: -60px;position: relative;">
                                <ul id="draglist" class="iview-admin-draggable-list">
                                    <li v-for="(item, index) in alRows" :key="index" class="notwrap drag-item" :data-index="index">
                                        <div class="dragTable">
                                            <div class="tableRow">
                                                <div class="tableCell location" v-if="!item.html">
                                                    <span style="color: #1f85f4;">{{!!item.event_geo ? item.event_geo.name : ''}}</span>
                                                    <br>
                                                    {{item.m_date}}
                                                </div>
                                                <div class="tableCell subject" v-if="!item.html">
                                                    <p>{{item.subject}}</p>
                                                </div>
                                                <div class="tableCell subject" v-if="item.html">

                                                    <table>
                                                        <tr v-html="item.subject"></tr>
                                                    </table>
                                                </div>
                                                <div class="tableCell push_end_date">
                                                    {{item.push_begin_date}}
                                                    <br>
                                                    {{item.push_end_date}}
                                                </div>
                                                <div class="tableCell end_date">
                                                    {{item.begin_date}}
                                                    <br>
                                                    {{item.end_date}}
                                                </div>
                                                <div class="tableCell new_flag_date">
                                                    {{item.new_flag_date}}
                                                </div>
                                                <div class="tableCell handle">
                                                    <Icon type="edit" size="24" color="#2d8cf0" @click="editRow(item)"></Icon>
                                                    <Poptip
                                                            confirm
                                                            :transfer="true"
                                                            placement="top-end"
                                                            title="您确定要删除这条数据吗?"
                                                            @on-ok="delRow(item.id,index)"
                                                            >
                                                        <Icon type="trash-a" size="24" color="#ed3f14"></Icon>
                                                    </Poptip>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <Page :total="al_total" :page-size="page_size" :current="al_current" @on-change="getactivitylist" v-if="al_showPage"></Page>
                        </TabPane>
                        <TabPane label="已结束活动" name="name2">
                            <can-edit-table refs="tlTable" @on-delete="handleDel" @on-updata="handleUpdata" v-model="dlRows" :columns-list="dlColumns"></can-edit-table>
                            <div class="page-bar">
                                <Page :total="dl_total" :page-size="page_size" :current="dl_current" @on-change="getdellist" v-if="dl_showPage"></Page>
                            </div>
                        </TabPane>
                    </Tabs>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import eData from '../data/event_data.js';
    import canEditTable from './components/canEditTable.vue';
    import Sortable from 'sortablejs';

    export default {
        name: 'activity_list',
        components: {
            canEditTable
        },
        data () {
            return {
                alRows: [],
                dlRows: [],
                nlRows: [],
                alColumns: [],
                dlColumns: [],
                al_total: 0,
                dl_total: 0,
                al_showPage: false,
                dl_showPage: false,
                page_size: 20,
                al_current: 1,
                dl_current: 1,
                isAccess: true,
                keyword: '',
                s_key: '',
                dataNubs: 0,
                showNub: '',
                push1_www_position: '',
                push1_hot_num: 0,
                list_num: [
                    {
                        val: 0,
                        label: 0
                    },
                    {
                        val: 1,
                        label: 1
                    },
                    {
                        val: 2,
                        label: 2
                    },
                    {
                        val: 3,
                        label: 3
                    },
                    {
                        val: 4,
                        label: 4
                    },
                    {
                        val: 5,
                        label: 5
                    },
                    {
                        val: 6,
                        label: 6
                    },
                    {
                        val: 7,
                        label: 7
                    },
                    {
                        val: 8,
                        label: 8
                    },
                    {
                        val: 9,
                        label: 9
                    },
                    {
                        val: 10,
                        label: 10
                    },
                ]
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'activity_list'){
                    this.$store.commit('removeTag', 'activity_edit');
                    this.$store.commit('closePage', 'activity_edit');
                    this.getData();
                }
            }
        },
        mounted () {
            document.body.ondrop = function (event) {
                event.preventDefault();
                event.stopPropagation();
            };
            let vm = this;
            let draglist = document.getElementById('draglist');
            Sortable.create(draglist, {
                group: {
                    name: 'list',
                    pull: true
                },
                animation: 120,
                ghostClass: 'placeholder-style',
                fallbackClass: 'iview-admin-cloned-item',
                onEnd (evt){
                    var data = {
                        type: 1,
                        ids: []
                    };
                    for(var i=0;i<vm.alRows.length;i++){
                        data.ids.push(vm.alRows[i].id);
                    }
                    data.ids.splice(evt.oldIndex, 1);
                    data.ids.splice(evt.newIndex, 0, vm.alRows[evt.item.getAttribute('data-index')].id);

                    vm.handleChange(data);

                }
            });
        },
        methods: {
            getData () {
                this.alColumns = eData.alColumns;
                this.dlColumns = eData.dlColumns;
                this.alRows = [];
                this.dlRows = [];
                this.push1_www_position = '';
                this.push1_hot_show = 0;
                this.getactivitylist(1);
                this.getdellist(1);
                this.getEventsCount();
                this.get_www_position();
                this.get_hot_show();
            },
            getactivitylist (n){
                this.alRows = [];
                this.al_total = 0;
                this.al_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'GET',
                    params: {
                        page: _this.al_current,
                        page_size: _this.page_size,
                        type: 1,
                        status: 0
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.alRows = res.data.data.rows;
                        for(var i=0;i<_this.alRows.length;i++){
                            if(!!_this.alRows[i].event_begin_date){
                                _this.alRows[i].begin_date = new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getMinutes());
                            }else {
                                _this.alRows[i].begin_date = '';
                            }
                            if(!!_this.alRows[i].event_end_date){
                                _this.alRows[i].end_date = new Date(parseInt(_this.alRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.alRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.alRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.alRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.alRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.alRows[i].end_date = '';
                            }
                            if(!!_this.alRows[i].push_begin_date){
                                _this.alRows[i].push_begin_date = new Date(parseInt(_this.alRows[i].push_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.alRows[i].push_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.alRows[i].push_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.alRows[i].push_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.alRows[i].push_begin_date + '000')).getMinutes());
                            }else {
                                _this.alRows[i].push_begin_date = '';
                            }
                            if(!!_this.alRows[i].push_end_date){
                                _this.alRows[i].push_end_date = new Date(parseInt(_this.alRows[i].push_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.alRows[i].push_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.alRows[i].push_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.alRows[i].push_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.alRows[i].push_end_date + '000')).getMinutes());
                            }else {
                                _this.alRows[i].push_end_date = '';
                            }
                            if(!!_this.alRows[i].new_flag_date){
                                _this.alRows[i].new_flag_date = new Date(parseInt(_this.alRows[i].new_flag_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.alRows[i].new_flag_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.alRows[i].new_flag_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.alRows[i].new_flag_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.alRows[i].new_flag_date + '000')).getMinutes());
                            }else {
                                _this.alRows[i].new_flag_date = '';
                            }
                            if(!!_this.alRows[i].event_begin_date){
                                _this.alRows[i].m_date = add0(new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.alRows[i].event_begin_date + '000')).getDate());
                            }else {
                                _this.alRows[i].m_date = '';
                            }
                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }

                        _this.al_total = res.data.data.count;
                        if(_this.al_total > _this.page_size){
                            _this.al_showPage = true;
                        }
                    }else{
                        console.log(res.msg)
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err.msg)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getdellist (n){
                this.dlRows = [];
                this.dl_total = 0;
                this.dl_current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'GET',
                    params: {
                        page: _this.dl_current,
                        page_size: _this.page_size,
                        type: 1,
                        status: -1
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.dlRows = res.data.data.rows;
                        for(var i=0;i<_this.dlRows.length;i++){
                            if(!!_this.dlRows[i].event_begin_date){
                                _this.dlRows[i].begin_date = new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].begin_date = '';
                            }
                            if(!!_this.dlRows[i].event_end_date){
                                _this.dlRows[i].end_date = new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].event_end_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].end_date = '';
                            }
                            if(!!_this.dlRows[i].push_begin_date){
                                _this.dlRows[i].push_begin_date = new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].push_begin_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].begin_date = '';
                            }
                            if(!!_this.dlRows[i].push_end_date){
                                _this.dlRows[i].push_end_date = new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].push_end_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].end_date = '';
                            }
                            if(!!_this.dlRows[i].new_flag_date){
                                _this.dlRows[i].new_flag_date = new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getFullYear() + '-' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getDate()) + ' ' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getHours()) + ':' + add0(new Date(parseInt(_this.dlRows[i].new_flag_date + '000')).getMinutes());
                            }else {
                                _this.dlRows[i].end_date = '';
                            }
                            if(!!_this.dlRows[i].event_begin_date){
                                _this.dlRows[i].m_date = add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getMonth() + 1) + '-' + add0(new Date(parseInt(_this.dlRows[i].event_begin_date + '000')).getDate());
                            }else {
                                _this.dlRows[i].m_date = '';
                            }
                        }
                        function add0(n) {
                            if(n < 10){
                                return '0' + n;
                            }else {
                                return n;
                            }
                        }
                        _this.dl_total = res.data.data.count;
                        if(_this.dl_total > _this.page_size){
                            _this.dl_showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleChange (dataIn) {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/change_order',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.alRows = [];
                        _this.getactivitylist(1);

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            editRow (item){
                this.$store.commit('geteventRow', item)
                this.$router.push('/console/event/activity-edit/'+item.id);
            },
            delRow (id,index){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release',
                    method:'DELETE',
                    data: {
                        id: id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.alRows.splice(index, 1);
                        _this.handleUpdata();
                        _this.handleDel(_this.alRows[index],index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
                this.getdellist(1);
            },
            handleUpdata (){
                this.getData();
            },
            getLocation(id,index,str){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo/'+ id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(str === 'del'){
                            _this.$set(_this.dlRows[index], 'location', res.data.data.name);
                        }else {
                            _this.$set(_this.alRows[index], 'location', res.data.data.name);
                        }

                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getEventsCount (){
                var _this = this;
                util.ajax({
                    url:'/event/release?status=0',
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.dataNubs = res.data.data.events_count;
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            setNubs (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release/push1_setting',
                    method:'POST',
                    data: {
                        push1_count: this.showNub
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.dataNubs = _this.showNub;
                        _this.showNub = '';

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            get_www_position(){
                var _this = this;
                util.ajax({
                    url:'/event/release/push1_www_position',
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.push1_www_position = res.data.data.push1_www_position;
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            set_www_position(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release/push1_www_position',
                    method:'POST',
                    data: {
                        push1_www_position: this.push1_www_position
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                       _this.get_www_position();
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                    _this.get_www_position();
                });
            },
            get_hot_show(){
                var _this = this;
                util.ajax({
                    url:'/event/release/www_hot',
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.push1_hot_num = parseInt(res.data.data.www_hot);
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            set_hot_show (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/release/www_hot',
                    method:'POST',
                    data: {
                        www_hot: this.push1_hot_num
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){

                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                        _this.get_www_position();
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                    _this.get_www_position();
                });
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
