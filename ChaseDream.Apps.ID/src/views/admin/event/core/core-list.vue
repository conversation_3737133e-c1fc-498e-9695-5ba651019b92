<style lang="less">
@import "../../../../styles/common.less";
@import "../event.less";
</style>

<template>
  <div class="core-list">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <div class="tool-bar" style="position: relative;margin-bottom: 20px;">
            <router-link to="/console/event/core-add">
              <Button type="success" icon="plus">添 加</Button>
            </router-link>
            <Input
              v-model="keyword"
              @on-enter="searchCore()"
              icon="close-circled"
              placeholder="请输入标题"
              @on-click="
                keyword = '';
                getCorelist(1);
                getdellist(1);
              "
              style="width:400px;margin-bottom: 10px;position: absolute;right: 0;top:0;margin-top: 0;"
            >
              <Button
                slot="append"
                icon="search"
                style="color: #fff;font-size: 20px;background-color: #2d8cf0;font-weight: bold;"
                @click="searchCore()"
              ></Button>
              <Icon type="close-circled"></Icon>
            </Input>
          </div>
          <div class="edittable-con-1">
            <Tabs v-model="tabname" @on-click="changeTab">
              <TabPane label="当前活动" name="name1">
                <can-edit-table
                  refs="clTable"
                  @on-delete="handleDel"
                  @on-updata="handleUpdata"
                  @on-publish="publish"
                  @on-release="toRelease"
                  v-model="clRows"
                  :columns-list="clColumns"
                  class="clTable"
                ></can-edit-table>
                <div class="page-bar">
                  <Page
                    :total="cl_total"
                    :page-size="page_size"
                    :current="cl_current"
                    @on-change="getCorelist"
                    v-if="cl_showPage"
                  ></Page>
                </div>
              </TabPane>
              <TabPane label="已结束活动" name="name2">
                <can-edit-table
                  refs="clTable"
                  @on-delete="handleDel"
                  @on-updata="handleUpdata"
                  v-model="dlRows"
                  :columns-list="dclColumns"
                ></can-edit-table>
                <div class="page-bar">
                  <Page
                    :total="dl_total"
                    :page-size="page_size"
                    :current="dl_current"
                    @on-change="getdellist"
                    v-if="dl_showPage"
                  ></Page>
                </div>
              </TabPane>
            </Tabs>
          </div>
        </Card>
      </Col>
    </Row>
    <Modal
      v-model="release"
      :mask-closable="false"
      :title="m_title"
      @on-ok="releaseCore"
      @on-cancel="release = false"
      width="1000"
      class-name="releaseModel"
    >
      <div>
        <Form ref="form" :model="form" :label-width="140">
          <FormItem
            prop="subject"
            label="活动标题："
            v-if="
              m_title === '发布到论坛' ||
                m_title === '发布到WWW' ||
                m_title === '推 2' ||
                m_title === '推 3'
            "
            class="ivu-form-item-required"
          >
            <Input
              v-model="form.subject"
              :maxlength="max_l"
              @on-change="check_subject_num(form.subject)"
              placeholder="请输入活动标题"
            ></Input>
            还可输入<strong> {{ subject_num }} </strong>个字符
          </FormItem>
          <Row v-if="m_title === '推 2' || m_title === '推 3'">
            <Col span="8">
              <FormItem
                prop="begin_date"
                label="推送时间："
                class="ivu-form-item-required"
              >
                <DatePicker
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  v-model="form.begin_date"
                  placeholder="请选择活动开始时间"
                  :time-picker-options="{ steps: [1, 15, 15] }"
                  style="width: 100%"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col span="1" align="center" style="line-height: 40px;">
              -
            </Col>
            <Col span="8">
              <FormItem
                prop="end_date"
                class="end_date"
                style="margin-right: 90px;"
              >
                <DatePicker
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  v-model="form.end_date"
                  placeholder="请选择活动结束时间"
                  :time-picker-options="{ steps: [1, 15, 15] }"
                  style="width: 100%"
                ></DatePicker>
              </FormItem>
            </Col>
          </Row>
          <FormItem
            prop="content"
            label="活动内容："
            v-if="m_title === '发布到论坛' || m_title === '发布到WWW'"
            class="ivu-form-item-required"
          >
            <Input
              v-model="form.content"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 8 }"
              placeholder="请输入活动内容"
            ></Input>
          </FormItem>
          <FormItem
            prop="school_id"
            label="机构："
            v-if="m_title === '推 0'"
            class="ivu-form-item-required"
          >
            <Select
              v-model="form.school_id"
              placeholder="请选择学校"
              filterable
            >
              <Option
                :value="item.id"
                :label="item.display_name"
                v-for="item in schools"
                :key="item.index"
              >
                <span>{{ item.display_name }}</span>
                <span style="float:right;color:#ccc">{{ item.country }}</span>
              </Option>
            </Select>
          </FormItem>
          <FormItem
            prop="fid"
            label="帖子版块："
            v-if="m_title === '发布到论坛'"
            class="ivu-form-item-required"
          >
            <Select
              v-model="form.fid"
              style="width:200px"
              @on-change="getType"
              :label-in-value="true"
            >
              <OptionGroup
                :label="item.name"
                v-for="item in forum_nav"
                :key="item.index"
              >
                <Option
                  v-for="opt in item.forums"
                  :value="opt.fid"
                  :key="opt.index"
                  >{{ opt.name }}</Option
                >
              </OptionGroup>
            </Select>
            <Select v-model="form.typeid" style="width:200px">
              <Option
                v-for="item in f_types"
                :value="item.typeid"
                :key="item.index"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem
            prop="uid"
            label="发帖账号："
            v-if="m_title === '发布到论坛' || m_title === '发布到WWW'"
            class="ivu-form-item-required"
          >
            <RadioGroup v-model="form.uid" @on-change="errMsg = ''">
              <Radio :label="user.id" v-for="user in users" :key="user.index">{{
                user.nickname
              }}</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem
            prop="content"
            label="活动内容："
            v-if="m_title === '推 1' && !!form.html"
            class="ivu-form-item-required"
          >
            <Input
              v-model="form.h_content"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 8 }"
              placeholder="请输入活动内容"
            ></Input>
          </FormItem>
          <FormItem
            prop="html"
            label="通栏："
            v-if="m_title === '推 1'"
            class="ivu-form-item-required"
          >
            <RadioGroup v-model="form.html" @on-change="setHtml">
              <Radio :label="1">是</Radio>
              <Radio :label="0">否</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem
            prop="url1"
            label="WWW URL："
            v-if="
              m_title === '推 0' ||
                (m_title === '推 1' && !form.html) ||
                m_title === '推 2' ||
                m_title === '推 3'
            "
            class="ivu-form-item-required"
          >
            <Input v-model="form.url1" placeholder="请输入WWW地址"></Input>
          </FormItem>
          <FormItem
            prop="url2"
            label="论坛 URL："
            v-if="m_title === '推 0' || (m_title === '推 1' && !form.html)"
            class="ivu-form-item-required"
          >
            <Input v-model="form.url2" placeholder="请输入Forum地址"></Input>
          </FormItem>
          <div
            class="geos"
            v-for="(item, index) in location"
            :key="item.index"
            v-if="m_title === '推 0' || (m_title === '推 1' && !form.html)"
            style="padding: 10px 10px 10px 0;border: 1px solid #efefef;border-radius: 5px;"
          >
            <FormItem prop="locations" label="活动地区：">
              <div class="hd">
                <strong style="width: 80px; display: inline-block">城市</strong>
              </div>
              <div>
                <Checkbox v-model="item.checked">
                  <span style="display: inline-block;width: 50px;">{{
                    item.city_name
                  }}</span>
                </Checkbox>
              </div>
            </FormItem>
            <FormItem
              prop="major_id"
              label="专业："
              v-if="m_title !== '推 1'"
              class="ivu-form-item-required"
            >
              <CheckboxGroup v-model="item.major">
                <Checkbox
                  :label="major.id"
                  v-for="major in majors"
                  disabled
                  :key="major.index"
                  >{{ major.name }}</Checkbox
                >
              </CheckboxGroup>
            </FormItem>
            <FormItem
              prop="event_type"
              label="活动类型："
              v-if="m_title !== '推 1'"
              class="ivu-form-item-required"
            >
              <CheckboxGroup v-model="item.event_type">
                <Checkbox
                  :label="type.id"
                  v-for="type in types"
                  disabled
                  :key="type.index"
                  >{{ type.name }}</Checkbox
                >
              </CheckboxGroup>
            </FormItem>
            <FormItem
              prop="subject"
              label="活动标题："
              class="ivu-form-item-required"
            >
              <Input
                v-model="item.subject"
                placeholder="请输入活动标题"
              ></Input>
            </FormItem>
            <div class="hd" style="padding-left: 140px;">
              <strong style="width: 235px;display: inline-block"
                >活动开始时间</strong
              >
              <strong style="width: 235px;display: inline-block"
                >活动结束时间</strong
              >
            </div>
            <Row>
              <Col span="9">
                <FormItem
                  prop="begin_date"
                  class="begin_data"
                  label="活动时间："
                >
                  <DatePicker
                    type="datetime"
                    format="yyyy-MM-dd HH:mm"
                    v-model="item.begin_date"
                    placeholder="请选择活动开始时间"
                    style="width: 100%"
                    readonly
                  ></DatePicker>
                </FormItem>
              </Col>
              <Col span="1" align="center" style="line-height: 40px;">
                -
              </Col>
              <Col span="6">
                <FormItem prop="end_date" class="end_date" style="">
                  <DatePicker
                    type="datetime"
                    format="yyyy-MM-dd HH:mm"
                    v-model="item.end_date"
                    placeholder="请选择活动结束时间"
                    style="width: 100%"
                    readonly
                  ></DatePicker>
                </FormItem>
              </Col>
            </Row>
            <div class="hd" style="padding-left: 140px;">
              <strong style="width: 235px;display: inline-block"
                >推送开始时间</strong
              >
              <strong style="width: 235px;display: inline-block"
                >推送结束时间</strong
              >
            </div>
            <Row>
              <Col span="9">
                <FormItem
                  prop="begin_date"
                  class="begin_data"
                  label="推送时间："
                >
                  <DatePicker
                    type="datetime"
                    :clearable="false"
                    :readonly="item.fold"
                    :disabled="item.fold"
                    format="yyyy-MM-dd HH:mm"
                    v-model="item.push_begin_date"
                    placeholder="请选择推送开始时间"
                    :time-picker-options="{ steps: [1, 15, 15] }"
                    @on-change="setPushDate(item, 'begin')"
                    :options="item.options1"
                    style="width: 100%"
                  ></DatePicker>
                </FormItem>
              </Col>
              <Col span="1" align="center" style="line-height: 40px;">
                -
              </Col>
              <Col span="6">
                <FormItem prop="end_date" class="end_date" style="">
                  <DatePicker
                    type="datetime"
                    :clearable="false"
                    :readonly="item.fold"
                    :disabled="item.fold"
                    format="yyyy-MM-dd HH:mm"
                    v-model="item.push_end_date"
                    placeholder="请选择推送结束时间"
                    :time-picker-options="{ steps: [1, 15, 15] }"
                    @on-change="setPushDate(item, 'end')"
                    :options="item.options2"
                    style="width: 100%"
                  ></DatePicker>
                </FormItem>
              </Col>
              <Col span="1" align="center" style="width: 30px;height: 40px;">
              </Col>
              <Col span="2" v-if="m_title === '推 0'">
                <Checkbox v-model="item.fold" style="line-height: 30px;">
                  <span style="display: inline-block;width: 50px;">折叠</span>
                </Checkbox>
              </Col>
            </Row>

            <FormItem
              prop="new_flag_date"
              label="最新标签下线时间："
              class="ivu-form-item-required"
            >
              <DatePicker
                type="datetime"
                :clearable="false"
                format="yyyy-MM-dd HH:mm"
                v-model="item.new_flag_date"
                placeholder="请选择显示最新的时间"
                @on-change="setPushDate(item, 'flag')"
                :options="item.options3"
                :time-picker-options="{ steps: [1, 15, 15] }"
                style="width: 220px;"
              ></DatePicker>
            </FormItem>
            <FormItem
              prop="image_0"
              label="上传图片："
              v-if="m_title !== '推 1'"
            >
              <Upload
                action="/api/v1/admin/event/upload"
                :before-upload="uploadImg"
                :show-upload-list="true"
                v-if="item.files.length === 0"
              >
                <Button
                  type="ghost"
                  icon="plus"
                  @click="setIndex(index)"
                ></Button>
              </Upload>
              <div class="file-list" v-if="item.files.length > 0">
                <div v-for="file in item.files">
                  <img :src="file.src" alt="" />
                  <p class="del">
                    <Icon type="trash-a" @click="delImg(index)" size="22"
                      >删除</Icon
                    >
                  </p>
                </div>
              </div>
            </FormItem>
            <Icon
              type="trash-a"
              size="24"
              class="delItem"
              @click="delLocation(index)"
              v-if="location.length > 1"
            ></Icon>
          </div>
          <FormItem
            prop="image"
            label="上传图片："
            v-if="m_title === '推 2'"
            class="ivu-form-item-required"
          >
            <Upload
              action="/api/v1/admin/event/upload"
              :before-upload="uploadImg"
              :show-upload-list="true"
              v-if="files.length === 0"
            >
              <Button type="ghost" icon="plus"></Button>
            </Upload>
            <div class="file-list" v-if="files.length > 0">
              <div v-for="file in files">
                <img :src="file.src" alt="" />
                <p class="del">
                  <Icon type="trash-a" @click="delImg(-1)" size="22">删除</Icon>
                </p>
              </div>
            </div>
          </FormItem>
          <FormItem
            prop="color"
            label="颜色："
            v-if="m_title === '推 3'"
            class="ivu-form-item-required colors"
          >
            <Button
              :class="c.color === form.color ? c.color + ' on' : c.color"
              v-for="c in colors"
              :key="c.index"
              @click="changeColor(c.color)"
            ></Button>
          </FormItem>
          <p class="f_err_msg">{{ errMsg }}</p>
        </Form>
      </div>
      <div slot="footer">
        <span style="position: relative;display: inline-block;">
          <Button @click="handleSubmit" type="primary">提交</Button>
          <Spin size="large" fix v-if="isSpin"></Spin>
        </span>
        <Button @click="release = false">取消</Button>
      </div>
    </Modal>
    <Modal
      :title="m_title"
      v-model="isPush"
      :mask-closable="false"
      @on-cancel="
        m_title = '';
        isPush = false;
      "
      class-name="release_modal"
      width="1000"
    >
      <div style="display: flex;width: 100%;">
        <release0
          v-if="m_title === '推 0'"
          @on-close="
            m_title = '';
            isPush = false;
            getData;
          "
        ></release0>
        <release1
          v-if="m_title === '推 1'"
          @on-close="
            m_title = '';
            isPush = false;
            getData;
          "
        ></release1>
        <release2
          v-if="m_title === '推 2'"
          @on-close="
            m_title = '';
            isPush = false;
            getData;
          "
        ></release2>
        <release3
          v-if="m_title === '推 3'"
          @on-close="
            m_title = '';
            isPush = false;
            getData;
          "
        ></release3>
      </div>
      <div slot="footer" style="display: none;"></div>
    </Modal>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import eData from "../data/event_data.js";
import canEditTable from "./components/canEditTable.vue";
import release0 from "../events/event-edit.vue";
import release1 from "../activity/activity-edit.vue";
import release2 from "../pic/pic-edit.vue";
import release3 from "../headlines/headlines-edit.vue";

export default {
  name: "core_list",
  components: {
    canEditTable,
    release0,
    release1,
    release2,
    release3,
  },
  data() {
    return {
      clRows: [],
      dlRows: [],
      clColumns: [],
      cl_total: 0,
      dl_total: 0,
      cl_showPage: false,
      dl_showPage: false,
      page_size: 35,
      cl_current: 1,
      dl_current: 1,
      dclColumns: [],
      isAccess: true,
      keyword: "",
      s_key: "",
      release: false,
      form: {
        school_id: 0,
        subject: "",
        content: "",
        location: [],
      },
      schools: [],
      majors: [],
      countrys: [],
      provinces: [],
      location: [],
      geos: [],
      users: [],
      forum_nav: [],
      f_types: [],
      types: [],
      event_id: 0,
      errMsg: "",
      m_title: "",
      locations: [],
      files: [],
      local: "",
      I_index: -1,
      push_start_date: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      push_end_date: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      colors: [
        {
          color: "red",
        },
        {
          color: "green",
        },
        {
          color: "blue",
        },
        {
          color: "black",
        },
      ],
      school_logo: "",
      isPush: false,
      tabname: "name1",
      isSpin: false,
      subject_num: 120,
      max_l: 120,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "core_list") {
        this.subject_num = 120;
        this.max_l = 120;
        this.$store.commit("removeTag", "core_edit");
        this.$store.commit("closePage", "core_edit");
        this.getData();
      }
    },
  },
  mounted() {},
  methods: {
    getData() {
      this.clColumns = eData.clColumns;
      this.dclColumns = eData.dclColumns;
      this.getCorelist(1);
      this.getdellist(1);
      if (window.location.href.split("http://localhost/").length > 0) {
        this.local = "http://localhost:7004";
      } else {
        this.local = "";
      }
    },
    getCorelist(n) {
      this.clRows = [];
      this.cl_total = 0;
      this.cl_current = n;
      var kw = "";
      if (this.keyword != "") {
        kw = "?subject=" + this.keyword;
      }
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event" + kw,
          method: "GET",
          params: {
            page: _this.cl_current,
            page_size: _this.page_size,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.clRows = res.data.data.rows;

            _this.cl_total = res.data.data.count;
            if (_this.cl_total > _this.page_size) {
              _this.cl_showPage = true;
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getdellist(n) {
      this.dlRows = [];
      this.dl_total = 0;
      this.dl_current = n;
      var kw = "";
      if (this.keyword != "") {
        kw = "?subject=" + this.keyword;
      }
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event" + kw,
          method: "GET",
          params: {
            page: _this.dl_current,
            page_size: _this.page_size,
            status: -1,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.dlRows = res.data.data.rows;
            _this.dl_total = res.data.data.count;

            if (_this.dl_total > _this.page_size) {
              _this.dl_showPage = true;
            }
            if (_this.dl_current === 1) {
              setTimeout(function() {
                for (var i = 0; i < _this.dlRows.length; i++) {
                  _this.getTopItem(_this.dlRows[i]);
                }

                for (var i = _this.dlRows.length - 1; i > -1; i--) {
                  if (!!_this.dlRows[i].bg) {
                    _this.clRows.push(_this.dlRows[i]);
                    _this.dlRows.splice(i, 1);
                  }
                }
              }, 600);
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getTopItem(obj, i) {
      obj.bg = false;
      if (obj.stick > 0) {
        var e_d = 0;
        if (obj.event_location.length > 0) {
          if (obj.event_location.length === 1) {
            e_d = obj.event_location[0].end_date;
          } else {
            var d_arr = [];
            for (var i = 0; i < obj.event_location.length; i++) {
              d_arr.push(obj.event_location[i].end_date);
            }
            d_arr.sort();
            e_d = d_arr.pop();
          }
          if (parseInt(e_d + "000") < new Date().getTime()) {
            obj.bg = true;
          }
        }
      }
    },
    changeTab(e) {
      if (e === "name1") {
        this.getCorelist(1);
        this.getdellist(1);
      } else {
        this.getdellist(1);
      }
    },
    releaseCore() {},
    publish(val, index, str) {
      this.$refs["form"].resetFields();
      this.formReset();
      this.release = true;
      if (str === "forum") {
        this.m_title = "发布到论坛";
      } else if (str === "www") {
        this.m_title = "发布到WWW";
      }
      this.form = Object.assign({}, val);
      this.form.begin_date = new Date(parseInt(this.form.begin_date + "000"));
      this.form.event_end_date = new Date(
        parseInt(this.form.event_end_date + "000")
      );
      this.event_id = this.form.event_location[0].event_id;

      if (str === "forum") {
        this.check_subject_num(this.form.subject);
      }
      this.getGeo();
      this.getUsers();
      this.getForum_nav();
    },
    handleSubmit() {
      if (this.m_title === "发布到论坛") {
        this.publish_to_forum();
      } else if (this.m_title === "发布到WWW") {
        this.publish_to_www();
      } else if (this.m_title === "推 0") {
        this.release_to_0();
      } else {
        this.release_to_other(this.m_title);
      }
    },
    publish_to_www() {
      //发布到WWW
      var dataIn = {
        uid: this.form.uid,
        title: this.form.subject,
        content: this.form.content,
        event_id: this.event_id,
      };
      if (!dataIn.title) {
        this.$Notice.error({
          title: "活动标题不能为空！",
        });
        return;
      }
      if (!dataIn.content) {
        this.$Notice.error({
          title: "活动内容不能为空！",
        });
        return;
      }
      if (!dataIn.uid) {
        this.$Notice.error({
          title: "请选择发帖账号！",
        });
        return;
      }

      this.isSpin = true;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/publish_to_www",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          _this.isSpin = false;
          if (res.data.msg === "success") {
            _this.release = false;
            _this.formReset();
            _this.$Notice.success({
              title: "发布成功！",
            });
            _this.getCorelist(1);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.isSpin = false;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    publish_to_forum() {
      // 发布到论坛
      var dataIn = {
        uid: this.form.uid,
        event_id: this.event_id,
        subject: this.form.subject,
        content: this.form.content,
        fid: this.form.fid,
        typeid: this.form.typeid,
      };
      if (!dataIn.subject) {
        this.$Notice.error({
          title: "活动标题不能为空！",
        });
        return;
      }

      if (this.CountChineseCharacters(dataIn.subject) > 120) {
        this.$Notice.error({
          title: "标题字符数不能大于120个！",
        });
        return;
      }
      if (!dataIn.content) {
        this.$Notice.error({
          title: "活动内容不能为空！",
        });
        return;
      }
      if (!dataIn.fid) {
        this.$Notice.error({
          title: "请选择帖子版块！",
        });
        return;
      }
      if (!dataIn.uid) {
        this.$Notice.error({
          title: "请选择发帖账号！",
        });
        return;
      }
      if (!dataIn.typeid) {
        dataIn.typeid = 0;
      }
      var _this = this;
      _this.isSpin = true;
      util
        .ajax({
          url: "/api/v1/admin/event/publish_to_forum",
          method: "POST",
          data: dataIn,
        })
        .then(function(res) {
          _this.isSpin = false;
          if (res.data.msg === "success") {
            _this.release = false;
            _this.formReset();
            _this.$Notice.success({
              title: "发布成功！",
            });
            _this.getCorelist(1);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.isSpin = false;
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    toRelease(val, index, str) {
      var row = val;
      var url = "";
      if (str === "2") {
        url = "/api/v1/admin/event/release/" + val.id + "/2";
      } else if (str === "3") {
        url = "/api/v1/admin/event/release/" + val.id + "/3";
      }
      if (str === "0" || str === "1") {
        row.event_id = row.id;
        row.url1 = row.www_url
          ? "https://www.chasedream.com/show.aspx?id=" + row.www_url + "&cid=11"
          : "";
        row.url2 = row.forum_url
          ? "https://forum.chasedream.com/thread-" + row.forum_url + "-1-1.html"
          : "";
        this.$store.commit("geteventRow", row);
        this.isPush = true;
        if (str === "0") {
          this.m_title = "推 0";
        } else if (str === "1") {
          this.m_title = "推 1";
        }
        return false;
      } else if (str === "2" || str === "3") {
        var _this = this;
        util
          .ajax({
            url: url,
            method: "GET",
            params: {},
          })
          .then(function(res) {
            if (res.data.msg === "success") {
              if (res.data.data.length < 1) {
                var row_data = {
                  subject: row.subject,
                  id: row.id,
                  url1: !!row.www_url
                    ? "https://www.chasedream.com/show.aspx?id=" +
                      row.www_url +
                      "&cid=11"
                    : "",
                  url2: !!row.forum_url
                    ? "https://forum.chasedream.com/thread-" +
                      row.forum_url +
                      "-1-1.html"
                    : "",
                  event_begin_date: Math.round(new Date().getTime() / 1000),
                };
                if (row.event_location.length > 1) {
                  var begin_times = [];
                  lastTime();
                  begin_times.sort(function(a, b) {
                    return b - a;
                  });
                  var end_time =
                    parseInt(begin_times[0] + "000") + 1000 * 60 * 15;
                  if (end_time <= new Date().getTime()) {
                    end_time = new Date().getTime() + 1000 * 60 * 15;
                  } else {
                    end_time = end_time;
                  }

                  row_data.event_end_date = Math.round(end_time / 1000);
                } else {
                  row_data.event_end_date = row.event_location[0].begin_date;
                }
                function lastTime() {
                  for (var num = 0; num < row.event_location.length; num++) {
                    begin_times.push(row.event_location[num].begin_date);
                  }
                }

                if (str === "2") {
                  row_data.image = "";
                }
                if (str === "3") {
                  row_data.color = "";
                }

                _this.$store.commit("geteventRow", row_data);
                _this.isPush = true;
                if (str === "2") {
                  _this.m_title = "推 2";
                } else if (str === "3") {
                  _this.m_title = "推 3";
                }
              } else {
                var row_data = res.data.data[0];
                _this.$store.commit("geteventRow", row_data);
                _this.isPush = true;
                if (str === "2") {
                  _this.m_title = "推 2";
                } else if (str === "3") {
                  _this.m_title = "推 3";
                }
              }
            } else {
              _this.$Notice.error({
                title: res.msg,
              });
            }
          })
          .catch(function(err) {
            console.log(err);
            _this.$Notice.error({
              title: err.msg,
            });
          });
      }
      return false;

      var _this = this;
      util
        .ajax({
          url: url,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (res.data.events.length <= 0) {
              _this.form = _this.d_form;
            }
            return false;
            _this.form = res.data.data;
            _this.getSchoolLogo();
            _this.event_id = res.data.data.event_location[0].event_id;
            _this.getMajor();
            for (var i = 0; i < res.data.data.event_location.length; i++) {
              var item = {};
              item.id = res.data.data.event_location[i].id;
              item.country_id = res.data.data.event_location[i].country_id;
              item.province_id = res.data.data.event_location[i].province_id;
              item.city_id = res.data.data.event_location[i].city_id;
              item.app_join_event =
                res.data.data.event_location[i].app_join_event;
              item.event_major = res.data.data.event_location[i].event_major;
              item.event_school_major =
                res.data.data.event_location[i].event_school_major;
              item.major = res.data.data.event_location[i].event_school_major;
              item.event_type = res.data.data.event_location[i].event_type;
              item.image = res.data.data.event_location[i].image;
              item.files = [];
              if (!!item.image) {
                item.files[0] = {};
                item.files[0].src = item.image;
              }
              if (!!res.data.data.event_location[i].begin_date) {
                item.begin_date = new Date(
                  parseInt(res.data.data.event_location[i].begin_date + "000")
                );
              } else {
                item.begin_date = "";
              }
              if (!!res.data.data.event_location[i].end_date) {
                item.end_date = new Date(
                  parseInt(res.data.data.event_location[i].end_date + "000")
                );
              } else {
                item.end_date = "";
              }

              if (!!res.data.data.event_location[i].push_begin_date) {
                item.push_begin_date = new Date(
                  parseInt(
                    res.data.data.event_location[i].push_begin_date + "000"
                  )
                );
              } else {
                item.push_begin_date = new Date();
              }
              if (!!res.data.data.event_location[i].push_end_date) {
                item.push_end_date = new Date(
                  parseInt(
                    res.data.data.event_location[i].push_end_date + "000"
                  )
                );
              } else {
                if (item.push_begin_date.getTime() <= item.begin_date) {
                  if (
                    parseInt(
                      res.data.data.event_location[i].begin_date + "000"
                    ) +
                      1000 * 60 * 15 >=
                    parseInt(res.data.data.event_location[i].end_date + "000")
                  ) {
                    item.push_end_date = item.end_date;
                  } else {
                    item.push_end_date = new Date(
                      parseInt(
                        res.data.data.event_location[i].begin_date + "000"
                      ) +
                        1000 * 60 * 15
                    );
                  }
                } else {
                  item.push_end_date = new Date(
                    item.push_begin_date.getTime() + 1000 * 60 * 15
                  );
                }
              }

              if (!!res.data.data.event_location[i].new_flag_date) {
                item.new_flag_date = new Date(
                  parseInt(
                    res.data.data.event_location[i].new_flag_date + "000"
                  )
                );
              } else {
                if (
                  item.push_begin_date.getTime() + 1000 * 60 * 60 * 24 * 3 <
                  item.push_end_date.getTime()
                ) {
                  item.new_flag_date = new Date(
                    item.push_begin_date.getTime() + 1000 * 60 * 60 * 24 * 3
                  );
                } else {
                  item.new_flag_date = item.push_end_date;
                }
              }
              if (!!res.data.data.event_location[i].fold) {
                item.fold = res.data.data.event_location[i].fold;
              } else {
                item.fold = false;
              }

              if (str !== "3" && str !== "2") {
                if (!!item.city_id) {
                  _this.getProvinceName(
                    item.city_id,
                    item,
                    !!res.data.data.event_location[i].new_flag_date
                  );
                } else {
                  _this.getProvinceName(
                    item.province_id,
                    item,
                    !!res.data.data.event_location[i].new_flag_date
                  );
                }
              }
            }

            if (!!_this.form.www_url) {
              _this.form.url1 =
                "https://www.chasedream.com/show.aspx?id=" +
                _this.form.www_url +
                "&cid=11";
            } else {
              _this.form.url1 = "";
            }
            if (!!_this.form.forum_url) {
              _this.form.url2 =
                "https://forum.chasedream.com/thread-" +
                _this.form.forum_url +
                "-1-1.html";
            } else {
              _this.form.url2 = "";
            }

            _this.$set(
              _this.form,
              "begin_date",
              !!res.data.data.event_location[0].begin_date
                ? new Date(
                    parseInt(res.data.data.event_location[0].begin_date + "000")
                  )
                : ""
            );
            _this.$set(
              _this.form,
              "end_date",
              !!res.data.data.event_location[0].end_date
                ? new Date(
                    parseInt(res.data.data.event_location[0].end_date + "000")
                  )
                : ""
            );

            if (str === "2") {
              _this.$set(_this.form, "begin_date", new Date());
              if (_this.form.event_location.length > 1) {
                var begin_times = [];
                lastTime();
                begin_times.sort(function(a, b) {
                  return b - a;
                });
                var end_time =
                  parseInt(begin_times[0] + "000") + 1000 * 60 * 15;
                if (end_time <= new Date().getTime()) {
                  end_time = new Date().getTime() + 1000 * 60 * 15;
                } else {
                  end_time = end_time;
                }
                _this.$set(_this.form, "end_date", new Date(end_time));
              } else {
                _this.$set(
                  _this.form,
                  "end_date",
                  new Date(
                    parseInt(_this.form.event_location[0].begin_date + "000") +
                      1000 * 60 * 15
                  )
                );
              }
              function lastTime() {
                for (
                  var num = 0;
                  num < _this.form.event_location.length;
                  num++
                ) {
                  begin_times.push(_this.form.event_location[num].begin_date);
                }
              }
              if (!!_this.form.image) {
                _this.files[0].src = _this.form.image;
              }
            }
            if (str === "3") {
              _this.$set(_this.form, "begin_date", new Date());
              if (_this.form.event_location.length > 1) {
                var begin_times = [];
                lastTime();
                begin_times.sort(function(a, b) {
                  return b - a;
                });
                var end_time =
                  parseInt(begin_times[0] + "000") + 1000 * 60 * 15;
                if (end_time <= new Date().getTime()) {
                  end_time = new Date().getTime() + 1000 * 60 * 15;
                } else {
                  end_time = end_time;
                }
                _this.$set(_this.form, "end_date", new Date(end_time));
              } else {
                _this.$set(
                  _this.form,
                  "end_date",
                  new Date(
                    parseInt(_this.form.event_location[0].begin_date + "000") +
                      1000 * 60 * 15
                  )
                );
              }
              function lastTime() {
                for (
                  var num = 0;
                  num < _this.form.event_location.length;
                  num++
                ) {
                  begin_times.push(_this.form.event_location[num].begin_date);
                }
              }
              if (_this.form.color == "") {
                _this.$set(_this.form, "color", "black");
              }
            }
            _this.$set(_this.form, "html", 0);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    get_data_0() {
      var rowData = this.clRows[index];
      this.form.school_id = rowData.school_id;
      this.form.event_id = rowData.event_id;
      this.form.subject = rowData.subject;
      this.form.url1 = rowData.url1;
      this.form.url2 = rowData.url2;
      this.getSchool();
      this.getTypes();
      this.getMajor();
      this.getCalendar();
      this.getSubject();
    },
    getCalendar() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/calendar/" + this.form.event_id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.locations = res.data.data.locations;
            for (var i = 0; i < res.data.data.events.length; i++) {
              var item = {};
              item.id = res.data.data.events[i].id;
              item.lid = res.data.data.events[i].lid;
              item.country_id =
                res.data.data.events[i].event_location.country_id;
              item.province_id =
                res.data.data.events[i].event_location.province_id;
              item.city_id = res.data.data.events[i].event_location.city_id;
              item.city_name = res.data.data.events[i].event_geo.name;
              item.event_school_major =
                res.data.data.events[i].event_school_major;
              item.major = res.data.data.locations[i].event_school_major;
              item.event_type = res.data.data.locations[i].event_type;
              item.subject = res.data.data.events[i].subject;
              item.image = res.data.data.events[i].image;
              item.event_id = res.data.data.events[i].event_id;
              item.checked = true;
              item.files = [];
              if (!!item.image) {
                item.files[0] = {};
                item.files[0].src = item.image;
              }
              if (!!res.data.data.events[i].event_begin_date) {
                item.begin_date = new Date(
                  parseInt(res.data.data.events[i].event_begin_date + "000")
                );
              } else {
                item.begin_date = "";
              }
              if (!!res.data.data.events[i].event_end_date) {
                item.end_date = new Date(
                  parseInt(res.data.data.events[i].event_end_date + "000")
                );
              } else {
                item.end_date = "";
              }

              if (!!res.data.data.events[i].push_begin_date) {
                item.push_begin_date = new Date(
                  parseInt(res.data.data.events[i].push_begin_date + "000")
                );
              } else {
                item.push_begin_date = new Date();
              }
              if (!!res.data.data.events[i].push_end_date) {
                item.push_end_date = new Date(
                  parseInt(res.data.data.events[i].push_end_date + "000")
                );
              } else {
                if (item.push_begin_date.getTime() <= item.begin_date) {
                  if (
                    parseInt(res.data.data.events[i].event_begin_date + "000") +
                      1000 * 60 * 15 >=
                    parseInt(res.data.data.events[i].event_end_date + "000")
                  ) {
                    item.push_end_date = item.end_date;
                  } else {
                    item.push_end_date = new Date(
                      parseInt(
                        res.data.data.events[i].event_begin_date + "000"
                      ) +
                        1000 * 60 * 15
                    );
                  }
                } else {
                  item.push_end_date = new Date(
                    item.push_begin_date.getTime() + 1000 * 60 * 15
                  );
                }
              }

              if (!!res.data.data.events[i].new_flag_date) {
                item.new_flag_date = new Date(
                  parseInt(res.data.data.events[i].new_flag_date + "000")
                );
              } else {
                if (
                  item.push_begin_date.getTime() + 1000 * 60 * 60 * 24 * 3 <
                  item.push_end_date.getTime()
                ) {
                  item.new_flag_date = new Date(
                    item.push_begin_date.getTime() + 1000 * 60 * 60 * 24 * 3
                  );
                } else {
                  item.new_flag_date = item.push_end_date;
                }
              }

              if (!!res.data.data.events[i].fold) {
                item.fold = res.data.data.events[i].fold;
              } else {
                item.fold = false;
              }

              _this.setOptions(item);
            }
            setTimeout(function() {
              if (_this.locations.length > _this.location.length) {
                _this.newData = _this.locations.length - _this.location.length;
                _this.isPush = true;
              }
            }, 1300);
          } else {
            console.log(res.msg);
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    setOptions(obj) {
      var _this = this;
      setTimeout(function() {
        obj.options1 = {
          disabledDate(date) {
            return date && date.valueOf() > obj.push_end_date.getTime();
          },
        };
        obj.options2 = {
          disabledDate(date) {
            return (
              (date &&
                date.valueOf() <
                  obj.push_begin_date.getTime() - 1000 * 60 * 60 * 24) ||
              date.valueOf() > obj.end_date.getTime()
            );
          },
        };
        obj.options3 = {
          disabledDate(date) {
            return (
              (date && date.valueOf() > obj.push_end_date.getTime()) ||
              date.valueOf() <
                obj.push_begin_date.getTime() - 1000 * 60 * 60 * 24
            );
          },
        };
        _this.location.push(obj);
        _this.isPush = false;
      }, 1000);
    },
    release_to_0() {
      //推0
      if (!this.form.school_id) {
        this.$Notice.error({
          title: "请选择学校！",
        });

        return false;
      }
      if (!this.form.url1) {
        this.$Notice.error({
          title: "请填写WWW地址！",
        });
        return false;
      }
      if (!this.form.url2) {
        this.$Notice.error({
          title: "请填写Forum地址！",
        });
        return false;
      }

      for (var i = 0; i < this.location.length; i++) {
        if (!this.location[i].subject) {
          this.$Notice.error({
            title: "请填写活动标题！",
          });
          return false;
        }
        if (!this.location[i].new_flag_date) {
          this.$Notice.error({
            title: "请选择最新标签下线时间！",
          });
          return false;
        }
        if (!this.location[i].files[0] && !this.school_logo) {
          this.$Notice.error({
            title: "当前学校LOGO错误，请到学校模块设置当前学校LOGO",
          });
          return false;
        }
      }
      var num = 0;
      for (var i = 0; i < this.location.length; i++) {
        if (!!this.location[i].checked) {
          var dataIn = {
            event_id: this.event_id,
            school_id: this.form.school_id,
            url1: this.form.url1,
            url2: this.form.url2,
            subject: this.location[i].subject,
            new_flag_date: Math.round(
              this.location[i].new_flag_date.getTime() / 1000
            ),
            location: [],
          };
          var item = {
            lid: this.location[i].id,
            location_id: this.location[i].city_id
              ? this.location[i].city_id
              : this.location[i].province_id,
            event_begin_date: !!this.location[i].begin_date
              ? Math.round(this.location[i].begin_date.getTime() / 1000)
              : "",
            event_end_date: this.location[i].end_date
              ? Math.round(this.location[i].end_date.getTime() / 1000)
              : "",
            fold: this.location[i].fold,

            image: this.location[i].files[0]
              ? this.location[i].files[0].src
              : this.school_logo,
          };
          if (!!this.location[i].fold) {
            item.push_begin_date = 0;
            item.push_end_date = 0;
          } else {
            if (!!this.location[i].push_begin_date) {
              item.push_begin_date = Math.round(
                this.location[i].push_begin_date.getTime() / 1000
              );
            } else {
              item.push_begin_date = "";
            }
            if (!!this.location[i].push_end_date) {
              item.push_end_date = Math.round(
                this.location[i].push_end_date.getTime() / 1000
              );
            } else {
              item.push_end_date = "";
            }
          }
          dataIn.location.push(item);

          var _this = this;
          util
            .ajax({
              url: "/api/v1/admin/event/calendar",
              method: "POST",
              data: dataIn,
            })
            .then(function(res) {
              if (res.data.msg === "success") {
                num++;
                if (num === _this.location.length) {
                  _this.release = false;
                  _this.formReset();
                  _this.$Notice.success({
                    title: "推送成功！",
                  });
                }
              } else {
                _this.$Notice.error({
                  title: res.msg,
                });
              }
            })
            .catch(function(err) {
              _this.$Notice.error({
                title: err.msg,
              });
              console.log(err);
            });
        }
      }
    },
    release_to_other(n) {
      //推1/推2/推3
      n = parseInt(n.split("推 ")[1]);
      var dataIn = {
        event_id: this.event_id,
        subject: this.form.subject,
        type: n,
      };
      if (n === 1) {
        // 推1
        if (!this.form.html) {
          dataIn.url1 = this.form.url1;
          dataIn.url2 = this.form.url2;
          dataIn.location = [];
          dataIn.html = this.form.html;

          if (!dataIn.url1) {
            this.$Notice.error({
              title: "请填写WWW地址！",
            });
            return false;
          }
          if (!dataIn.url2) {
            this.$Notice.error({
              title: "请填写Forum地址！",
            });
            return false;
          }
          for (var i = 0; i < this.location.length; i++) {
            if (!!this.location[i].checked) {
              if (!this.location[i].subject) {
                this.$Notice.error({
                  title: "请填写活动标题！",
                });
                return false;
              }
              if (!this.location[i].new_flag_date) {
                this.$Notice.error({
                  title: "请选择最新标签下线时间！",
                });
                return false;
              }
            }
          }
          var l = 0;
          for (var i = 0; i < this.location.length; i++) {
            if (!!this.location[i].checked) {
              l++;
              var postData = {
                event_id: this.event_id,
                type: n,
                url1: this.form.url1,
                url2: this.form.url2,
                subject: this.location[i].subject,
                new_flag_date: Math.round(
                  this.location[i].new_flag_date.getTime() / 1000
                ),
                location: [],
              };

              var item = {
                lid: this.location[i].id,
                location_id: this.location[i].city_id
                  ? this.location[i].city_id
                  : this.location[i].province_id,
                event_begin_date: !!this.location[i].begin_date
                  ? Math.round(this.location[i].begin_date.getTime() / 1000)
                  : "",
                event_end_date: this.location[i].end_date
                  ? Math.round(this.location[i].end_date.getTime() / 1000)
                  : "",
                fold: this.location[i].fold,
              };
              if (!!this.location[i].fold) {
                item.push_begin_date = 0;
                item.push_end_date = 0;
              } else {
                if (!!this.location[i].push_begin_date) {
                  item.push_begin_date = Math.round(
                    this.location[i].push_begin_date.getTime() / 1000
                  );
                } else {
                  item.push_begin_date = "";
                }
                if (!!this.location[i].push_end_date) {
                  item.push_end_date = Math.round(
                    this.location[i].push_end_date.getTime() / 1000
                  );
                } else {
                  item.push_end_date = "";
                }
              }

              postData.location.push(item);

              this.ajax_release(postData, 1, i);
            }
          }
          if (l === 0) {
            this.$Notice.error({
              title: "请选择活动地区！",
            });
            return false;
          }
        } else {
          dataIn.subject = this.form.h_content;
          dataIn.html = this.form.html;
          if (!dataIn.subject) {
            this.$Notice.error({
              title: "活动内容不能为空！",
            });
            return false;
          }
          this.ajax_release(dataIn);
        }
      }
      if (n === 2) {
        // 推2
        dataIn.url1 = this.form.url1;
        if (!dataIn.subject) {
          this.$Notice.error({
            title: "请填写活动标题！",
          });
          return false;
        }

        if (!this.form.begin_date) {
          this.$Notice.error({
            title: "请选择推送开始时间！",
          });
          return false;
        }
        if (!this.form.end_date) {
          this.$Notice.error({
            title: "请选择推送结束时间！",
          });
          return false;
        }

        dataIn.event_begin_date = Math.round(
          this.form.begin_date.getTime() / 1000
        );
        dataIn.event_end_date = Math.round(this.form.end_date.getTime() / 1000);
        dataIn.push_begin_date = Math.round(
          this.form.begin_date.getTime() / 1000
        );
        dataIn.push_end_date = Math.round(this.form.end_date.getTime() / 1000);
        dataIn.image = this.files[0] ? this.files[0].src : "";
        dataIn.level = 1;

        if (!dataIn.url1) {
          this.$Notice.error({
            title: "请填写WWW地址！",
          });
          return false;
        }
        if (!dataIn.image) {
          this.$Notice.error({
            title: "请上传活动图片！",
          });
          return false;
        }
        this.ajax_release(dataIn);
      }
      if (n === 3) {
        // 推3
        dataIn.url1 = this.form.url1;
        if (!dataIn.subject) {
          this.$Notice.error({
            title: "请填写活动标题！",
          });
          return false;
        }
        dataIn.event_begin_date = Math.round(
          this.form.begin_date.getTime() / 1000
        );
        dataIn.event_end_date = Math.round(this.form.end_date.getTime() / 1000);
        dataIn.push_begin_date = Math.round(
          this.form.begin_date.getTime() / 1000
        );
        dataIn.push_end_date = Math.round(this.form.end_date.getTime() / 1000);
        if (!dataIn.url1) {
          this.$Notice.error({
            title: "请填写WWW地址！",
          });
          return false;
        }
        dataIn.color = this.form.color;
        if (!dataIn.color) {
          this.$Notice.error({
            title: "请选择颜色！",
          });
          return false;
        }
        if (dataIn.color === "black") {
          dataIn.color = "";
        }

        this.ajax_release(dataIn);
      }
    },
    ajax_release(dataIn, n, i) {
      this.errMsg = "";

      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/release",
          method: "POST",
          async: false,
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (n === 1) {
              if (i === _this.location.length - 1) {
                _this.release = false;
                _this.formReset();
                _this.$Notice.success({
                  title: "推送成功！",
                });
              }
            } else {
              _this.release = false;
              _this.formReset();
              _this.$Notice.success({
                title: "推送成功！",
              });
            }
          } else {
            console.log(res.data.msg);
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err.msg);
        });
    },
    delLocation(i) {
      if (!!this.location[i].lid) {
        var _this = this;
        util
          .ajax({
            url: "/api/v1/admin/event/calendar",
            method: "DELETE",
            data: {
              id: this.location[i].id,
              event_id: this.location[i].event_id,
            },
          })
          .then(function(res) {
            if (res.data.msg === "success") {
              _this.location.splice(i, 1);
            } else {
            }
          })
          .catch(function(err) {
            console.log(err);
          });
      } else {
        this.location.splice(i, 1);
      }
    },
    addRow() {
      var item = {
        country_id: "",
        province_id: "",
        provinces: [],
      };
      this.geos.push(item);
    },
    delRow(i, str) {
      if (str === "geos") {
        this.geos.splice(i, 1);
      } else {
        var _this = this;
        util
          .ajax({
            url: "/api/v1/admin/event/location",
            method: "DELETE",
            data: {
              id: this.location[i].id,
            },
          })
          .then(function(res) {
            if (res.data.msg === "success") {
              _this.location.splice(i, 1);
            } else {
            }
          })
          .catch(function(err) {
            console.log(err);
          });
      }
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
      this.getCorelist(1);
      this.getdellist(1);
    },
    handleUpdata() {
      this.getData();
    },
    getSchool(i) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/school",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.schools = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getMajor() {
      //  根据学校id获取专业
      var _this = this;
      util
        .ajax({
          url: "api/v1/admin/event/school/major/" + this.form.school_id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            var majors = [];
            for (var i = 0; i < res.data.data.length; i++) {
              var item = {};
              item.id = res.data.data[i].id;
              item.name = res.data.data[i].short;
              item.major_id = res.data.data[i].major_id;
              majors.push(item);
            }
            _this.majors = majors;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getGeo(pid) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/geo",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.countrys = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getProvince(pid, i, str) {
      if (!!str) {
        this.form.location[i].provinces = [];
        this.form.location[i].province_id = 0;
      }
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/geo",
          method: "GET",
          params: {
            parent_id: pid,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.provinces = res.data.data;
            _this.form.location[i].provinces = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    getUsers() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/user",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            for (var i = 0; i < res.data.data.rows.length; i++) {
              if (_this.m_title === "发布到论坛") {
                if (res.data.data.rows[i].type === 1) {
                  _this.users.push(res.data.data.rows[i]);
                }
              } else if (_this.m_title === "发布到WWW") {
                if (res.data.data.rows[i].type === 2) {
                  _this.users.push(res.data.data.rows[i]);
                }
              }
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getForum_nav() {
      // 获取论坛一级版块
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/forum_nav",
          method: "GET",
          params: {
            page: _this.current,
            pageSize: _this.pageSize,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.forum_nav = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    getType(e) {
      // 获取论坛二级版块
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/thread/forum_nav_sub",
          method: "GET",
          params: {
            fid: e.value,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.f_types = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getTypes() {
      // 获取活动类型
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/type",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.types = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getProvinceName(id, obj, n) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/geo/" + id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            obj.city_name = res.data.data.name;
            obj.checked = true;
            obj.files = [];
            obj.subject = _this.form.subject;

            function add0(str) {
              if (str.length > 1) {
                return str;
              } else {
                return "0" + str;
              }
            }
            obj.options1 = {
              disabledDate(date) {
                return date && date.valueOf() > obj.push_end_date.getTime();
              },
            };
            obj.options2 = {
              disabledDate(date) {
                return (
                  (date &&
                    date.valueOf() <
                      obj.push_begin_date.getTime() - 1000 * 60 * 60 * 24) ||
                  date.valueOf() > obj.end_date.getTime()
                );
              },
            };
            obj.options3 = {
              disabledDate(date) {
                return (
                  (date && date.valueOf() > obj.push_end_date.getTime()) ||
                  date.valueOf() <
                    obj.push_begin_date.getTime() - 1000 * 60 * 60 * 24
                );
              },
            };

            _this.location.push(obj);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
            console.log(res.msg);
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    setPushDate(item, str) {
      if (str === "begin") {
        if (item.push_begin_date.getTime() >= item.end_date.getTime()) {
          this.$Notice.error({
            title: "推送开始时间不能大于活动结束时间！",
          });
          item.push_begin_date = new Date(
            item.end_date.getTime() - 1000 * 60 * 15
          );
        }
        item.options2 = {
          disabledDate(date) {
            return (
              (date && date.valueOf() < item.push_begin_date.getTime()) ||
              date.valueOf() > item.end_date.getTime()
            );
          },
        };
        item.options3 = {
          disabledDate(date) {
            return (
              (date && date.valueOf() > item.push_end_date.getTime()) ||
              date.valueOf() < item.push_begin_date.getTime()
            );
          },
        };
      }
      if (str === "end") {
        if (item.push_end_date.getTime() >= item.end_date.getTime()) {
          this.$Notice.error({
            title: "推送结束时间不能大于活动结束时间！",
          });
          item.push_end_date = new Date(
            item.end_date.getTime() - 1000 * 60 * 15
          );
        } else if (
          item.push_end_date.getTime() <= item.push_begin_date.getTime()
        ) {
          this.$Notice.error({
            title: "推送结束时间不能大于推送开始时间！",
          });
          item.push_end_date = new Date(
            item.push_begin_date.getTime() + 1000 * 60 * 15
          );
        }
        if (item.new_flag_date.getTime() > item.push_end_date.getTime()) {
          item.new_flag_date = item.push_end_date;
        }
        item.options1 = {
          disabledDate(date) {
            return date && date.valueOf() > item.push_end_date.getTime();
          },
        };
        item.options3 = {
          disabledDate(date) {
            return (
              (date && date.valueOf() > item.push_end_date.getTime()) ||
              date.valueOf() < item.push_begin_date.getTime()
            );
          },
        };
      }
      if (str === "flag") {
        if (item.new_flag_date.getTime() >= item.push_end_date.getTime()) {
          this.$Notice.error({
            title: "最新标签下线时间不能大于推送结束时间！",
          });
          item.new_flag_date = item.push_end_date;
        }
      }
    },
    getSchoolLogo() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/event/school/logo/" + this.form.school_id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (res.data.data.logo_url.split("1x1.png").length > 1) {
              _this.$Notice.error({
                title: "当前学校LOGO错误，请到学校模块设置当前学校LOGO",
              });
            } else {
              _this.school_logo = res.data.data.logo_url;
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
            console.log(res.msg);
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    uploadImg(file) {
      var form = new FormData();
      form.append("file", file);
      const _this = this;
      util
        .ajax({
          url:
            "/api/v1/admin/event/upload?school_id=" +
            this.form.school_id +
            "&type=0",
          method: "POST",
          dataType: "JSON",
          contentType: false,
          processData: false,
          data: form,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.$Notice.success({
              title: "上传成功",
            });
            if (_this.I_index !== -1) {
              _this.location[_this.I_index].files.push({
                id: res.data.data.aid,
                name: file.name,
                src: "https://static.chasedream.com" + res.data.data.fullpath,
              });
              _this.I_index = -1;
            } else {
              _this.files.push({
                id: res.data.data.aid,
                name: file.name,
                src: "https://static.chasedream.com" + res.data.data.fullpath,
              });
            }
          } else {
            console.log(res);
            _this.$Notice.error({
              title: res.data.errors.message,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
      return false;
    },
    setIndex(i) {
      this.I_index = i;
    },
    delImg(i) {
      if (i !== -1) {
        this.$set(this.location[i], "files", []);
      } else {
        this.files = [];
      }
    },
    setHtml(e) {
      this.errMsg = "";
      this.$set(this.form, "html", e);
    },
    changeColor(c) {
      this.$set(this.form, "color", c);
    },
    searchCore() {
      if (this.keyword === " ") {
        this.keyword = "";
      }
      if (this.tabname === "name1") {
        this.getCorelist(1);
      } else {
        this.getdellist(1);
      }
    },
    pushAll() {
      var _this = this;
      var newArr = [];
      for (var i = 0; i < this.locations.length; i++) {
        if (isIN(this.locations[i].id)) {
          this.locations[i].subject = this.subject;
          this.locations[i].location_id = !!this.locations[i].city_id
            ? this.locations[i].city_id
            : this.locations[i].province_id;
          this.locations[i].checked = true;
          this.locations[i].major = this.locations[i].event_school_major;
          if (!!this.locations[i].begin_date) {
            this.locations[i].begin_date = new Date(
              parseInt(this.locations[i].begin_date + "000")
            );
          } else {
            this.locations[i].begin_date = 0;
          }
          if (!!this.locations[i].end_date) {
            this.locations[i].end_date = new Date(
              parseInt(this.locations[i].end_date + "000")
            );
          } else {
            this.locations[i].end_date = 0;
          }

          if (!!this.locations[i].push_begin_date) {
            this.locations[i].push_begin_date = new Date(
              parseInt(this.locations[i].push_begin_date + "000")
            );
          } else {
            this.locations[i].push_begin_date = this.locations[i].begin_date;
          }
          if (!!this.locations[i].push_end_date) {
            this.locations[i].push_end_date = new Date(
              parseInt(this.locations[i].push_end_date + "000")
            );
          } else {
            if (!!this.locations[i].push_begin_date) {
              this.locations[i].push_end_date = new Date(
                this.locations[i].push_begin_date.getTime() + 1000 * 60 * 15
              );
            } else {
              this.locations[i].push_end_date = 0;
            }
          }

          if (!!this.locations[i].new_flag_date) {
            this.locations[i].new_flag_date = new Date(
              parseInt(this.locations[i].new_flag_date + "000")
            );
          } else {
            if (!!this.locations[i].push_begin_date) {
              this.locations[i].new_flag_date = new Date(
                this.locations[i].push_begin_date.getTime() +
                  1000 * 60 * 60 * 24 * 3
              );
            } else {
              this.locations[i].new_flag_date = 0;
            }
          }
          this.locations[i].files = [];
          newArr.push(this.locations[i]);
        }
      }

      function isIN(id) {
        for (var j = 0; j < _this.location.length; j++) {
          if (id === _this.location[j].lid) {
            return false;
          }
        }
        return true;
      }
      for (var i = 0; i < newArr.length; i++) {
        this.getProvinceName(newArr[i].location_id, newArr, i);
      }
    },
    check_subject_num(e) {
      this.strLenCalc(this.form.subject, this.subject_num, 120);
    },
    strLenCalc(obj, checklen, maxlen) {
      var charset = "utf-8";
      var v = obj,
        charlen = 0,
        maxlen = !maxlen ? 200 : maxlen,
        curlen = maxlen,
        len = this.strlen(v);
      for (var i = 0; i < v.length; i++) {
        if (v.charCodeAt(i) < 0 || v.charCodeAt(i) > 255) {
          curlen -= charset == "utf-8" ? 2 : 1;
        }
      }
      if (curlen >= len) {
        this.subject_num = curlen - len;
      } else {
        this.form.subject = this.mb_cutstr(v, maxlen, 0);
        this.max_l = this.form.subject.length + this.subject_num;
      }
    },
    strlen(str) {
      return str.indexOf("\n") != -1
        ? str.replace(/\r?\n/g, "_").length
        : str.length; //BROWSER.ie &&
    },
    mb_cutstr(str, maxlen, dot) {
      var charset = "utf-8";
      var len = 0;
      var ret = "";
      var dot = !dot ? "..." : dot;
      maxlen = maxlen - dot.length;
      for (var i = 0; i < str.length; i++) {
        len +=
          str.charCodeAt(i) < 0 || str.charCodeAt(i) > 255
            ? charset == "utf-8"
              ? 3
              : 2
            : 1;
        if (len > maxlen) {
          ret += dot;
          break;
        }
        ret += str.substr(i, 1);
      }
      return ret;
    },
    CountChineseCharacters(text) {
      //限制标题字符数
      var Words = text;
      var W = new Object();
      var Result = new Array();
      var iNumwords = 0;
      var sNumwords = 0;
      var sTotal = 0;
      var iTotal = 0;
      var eTotal = 0;
      var otherTotal = 0;
      var bTotal = 0;
      var inum = 0;
      for (var i = 0; i < Words.length; i++) {
        var c = Words.charAt(i);
        if (c.match(/[\u4e00-\u9fa5]/)) {
          if (isNaN(W[c])) {
            iNumwords++;
            W[c] = 1;
          }
          iTotal++;
        }
      }
      for (var i = 0; i < Words.length; i++) {
        var c = Words.charAt(i);
        if (c.match(/[^\x00-\xff]/)) {
          if (isNaN(W[c])) {
            sNumwords++;
          }
          sTotal++;
        } else {
          eTotal++;
        }
        if (c.match(/[0-9]/)) {
          inum++;
        }
      }
      return iTotal * 2 + (sTotal - iTotal) * 2 + eTotal;
    },
    formReset() {
      this.form = {
        school_id: 0,
        subject: "",
        content: "",
        location: [],
      };
      this.schools = [];
      this.majors = [];
      this.countrys = [];
      this.provinces = [];
      this.location = [];
      this.geos = [];
      this.users = [];
      this.forum_nav = [];
      this.types = [];
      this.event_id = 0;
      this.errMsg = "";
      this.m_title = "";
      this.locations = [];
      this.files = [];
      this.subject_num = 120;
      this.max_l = 120;
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style></style>
