<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="core-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加核心活动
                </p>
                <Form ref="form" :model="form" :label-width="90">
                    <FormItem prop="school_id" label="机构：" class="ivu-form-item-required">
                        <Select v-model="form.school_id" placeholder="请选择学校" filterable @on-change="checkSchool">
                            <Option :value="item.id" :label="item.display_name + ' （'+ item.keyword +'）'" v-for="item in schools" :key="item.index">
                                <span>{{item.display_name}}</span>
                                <span style="float:right;color:#ccc">{{item.country}}</span>
                            </Option>
                        </Select>
                        <div class="common" v-if="useSchools.length > 0">
                            <strong>常用：</strong>
                            <p>
                                <span v-for="item in useSchools" :key="item.index" @click="checkSchool(item)">{{item.name}}</span>
                            </p>
                        </div>
                    </FormItem>
                    <FormItem prop="image" label="素材：">
                        <div class="material">
                            <div v-for="img in material" >
                                <img :src="local + img.fullpath" alt="图片" v-clipboard:copy="'https://static.chasedream.com' + img.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                <span v-clipboard:copy="local + img.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">复制</span>
                            </div>
                        </div>
                        <Button type="ghost" icon="more" @click="moreImg"></Button>
                        <Modal v-model="showMore" width="515" class-name="materialModal">
                            <p slot="header">
                                <Icon type="images" size="24"></Icon>
                                <span>素材库</span>
                            </p>
                            <div>
                                <div class="addList">
                                    <div class="file-list">
                                        <div v-for="file,index in files" class="file-list-item">
                                            <img :src="file.src" alt="图片" width="50" v-clipboard:copy=" file.src" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                            <Icon type="android-close" class="del" @click="delImg(file,index)" size="22"></Icon>
                                        </div>
                                        <Upload multiple action="/api/v1/admin/event/upload"
                                                :before-upload="uploadImg"
                                                :show-upload-list="true"
                                        >
                                            <Button type="ghost" icon="plus"></Button>
                                        </Upload>
                                    </div>
                                </div>
                                <div class="">
                                    <div>
                                        <span>置顶</span>
                                        <span style="float: right;color: #2db7f5;cursor: pointer" class="editImg" v-if="!editImg" @click="editImg = !editImg">编辑</span>
                                        <span style="float:right;color: #2db7f5;cursor: pointer;" class="editImg" v-if="!!editImg" @click="editImg = !editImg">保存</span>
                                    </div>
                                    <div class="top_imgs">
                                        <div v-for="item in material">
                                            <img :src="local + item.fullpath" alt="图片" width="50" v-clipboard:copy="'https://static.chasedream.com' + item.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                            <span v-if="!!editImg" @click="image_digest(item, 0);"><Icon type="minus-round"></Icon></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="former_imgs">
                                    <div v-for="item in event_img">
                                        <img :src="local + item.fullpath" alt="图片" width="50" v-clipboard:copy="'https://static.chasedream.com' + item.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                        <span v-if="!!editImg" @click="image_digest(item, 1);"><Icon type="plus-round"></Icon></span>
                                    </div>
                                </div>
                            </div>
                            <div slot="footer">
                                <Button type="ghost" size="large" @click="showMore = false">关闭</Button>
                            </div>
                        </Modal>
                    </FormItem>
                    <FormItem prop="subject" label="活动标题：" class="ivu-form-item-required">
                        <Input v-model="form.subject" placeholder="请输入活动标题"></Input>
                    </FormItem>
                    <FormItem prop="content" label="活动内容：" class="ivu-form-item-required">
                        <Input v-model="form.content" type="textarea" :autosize="{minRows: 5,maxRows: 8}" placeholder="请输入活动内容"></Input>
                    </FormItem>
                    <div class="geos" v-for="geo,index in form.location" :key="geo.index">
                        <Row>
                            <Col span="6">
                            <FormItem prop="country_id" label="地点：" class="ivu-form-item-required">
                                <Select v-model="geo.country_id" placeholder="请选择国家" @on-change="getProvince(geo.country_id,index)">
                                    <Option v-for="item in countrys" :value="item.id" :key="item.index">{{ item.name }}</Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="4">
                            <FormItem prop="province_id" class="province" style="">
                                <Select v-model="geo.province_id" placeholder="请选择地区" @on-change="getProvince(geo.province_id,index,'city')">
                                    <Option v-for="item in geo.provinces" :value="item.id" :key="item.index">{{ item.name }}</Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="4">
                            <FormItem prop="city_id" class="city" style="margin-right: 30px;">
                                <Select v-model="geo.city_id" placeholder="请选择城市">
                                    <Option v-for="item in geo.citys" :value="item.id" :key="item.index">{{ item.name }}</Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="1">
                            </Col>
                            <Col span="4">
                            <FormItem prop="" class="city" style="margin-right: 30px;">
                                <Checkbox v-model="geo.app_join_event">app 参加</Checkbox>
                            </FormItem>
                            </Col>
                            <Col span="24" v-if="useCitys.length > 0">
                            <div class="common" style="margin: -20px 0 20px 90px;">
                                <strong>常用：</strong>
                                <p>
                                    <span v-for="item in useCitys" :key="item.index" @click="checkCity(item,geo,index)">{{item.name}}</span>
                                </p>
                            </div>
                            </Col>
                        </Row>
                        <FormItem prop="major_id" label="专业：" class="ivu-form-item-required">
                            <CheckboxGroup v-model="geo.major">
                                <Checkbox :label="item.id" v-for="item in majors" :key="item.index">{{item.name}}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <FormItem prop="event_type" label="活动类型：" class="ivu-form-item-required">
                            <CheckboxGroup v-model="geo.event_type">
                                <Checkbox :label="item.id" v-for="item in types" :key="item.index">{{item.name}}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <Row>
                            <Col span="7">
                            <FormItem prop="begin_date" class="begin_data" label="时间：">
                                <DatePicker type="datetime" @on-ok="setEndDate(geo,'begin')" v-model="geo.begin_date" placeholder="请选择开始时间" :time-picker-options="{steps: [1, 15, 15]}" format="yyyy-MM-dd HH:mm" :confirm="true" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="6">
                            <FormItem prop="end_date" class="end_date" style="">
                                <DatePicker type="datetime" v-model="geo.end_date" @on-ok="setEndDate(geo,'end')" placeholder="请选择结束时间" :time-picker-options="{steps: [1, 15, 15]}" format="yyyy-MM-dd HH:mm" :options="geo.options" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                        </Row>
                        <Icon type="trash-a" size="24" class="delItem" @click="delRow(index)" v-if="form.location.length > 1"></Icon>
                    </div>
                    <FormItem>
                        <Button type="dashed" icon="plus" class="plusItem" @click="addRow"></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary" v-if="!isDisabled">提交</Button>
                        <Button type="primary" disabled v-if="!!isDisabled">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'core_add',
        components: {

        },
        data () {
            return {
                form: {
                    subject: '',
                    content: '',
                    school_id:'',
                    location: [{
                        country_id: '',
                        province_id: '',
                        city_id: '',
                        begin_date: '',
                        end_date: '',
                        provinces: [],
                        citys: [],
                        event_type: [],
                        major: [],
                        school_major: [],
                        options: {
                            disabledDate (date) {
                                return false;
                            }
                        }
                    }]
                },
                schools: [],
                useSchools: [],
                majors:[],
                countrys:[],
                provinces:[],
                citys: [],
                useCitys: [
                    {
                        name: '在线',
                        country_id: 4134,
                        province_id: 4135,
                        city_id: ''
                    },
                    {
                        name: '北京',
                        country_id: 1,
                        province_id: 2,
                        city_id: ''
                    },
                    {
                        name: '上海',
                        country_id: 1,
                        province_id: 111,
                        city_id: ''
                    },
                    {
                        name: '深圳',
                        country_id: 1,
                        province_id: 265,
                        city_id: 268
                    },
                    {
                        name: '广州',
                        country_id: 1,
                        province_id: 265,
                        city_id: 266
                    },
                    {
                        name: '南京',
                        country_id: 1,
                        province_id: 130,
                        city_id: 131
                    },
                    {
                        name: '香港',
                        country_id: 1,
                        province_id: 503,
                        city_id: ''
                    },
                    {
                        name: '天津',
                        country_id: 1,
                        province_id: 19,
                        city_id: ''
                    }
                ],
                types: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                school_major: [],
                material: [],
                event_img: [],
                showMore: false,
                local: 'https://static.chasedream.com',
                editImg:false,
                isDisabled: false,
                subject_num: 120,
                max_l: 120
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'core_add'){
                    this.form.subject = '';
                    this.form.content = '';
                    this.form.school_id = '';
                    this.form.location = [{
                        country_id: '',
                        province_id: '',
                        city_id: '',
                        provinces: [],
                        citys: [],
                        event_type: [],
                        major: [],
                        school_major: [],
                        app_join_event: 0
                    }]
                    this.schools = [];
                    this.majors = [];
                    this.countrys = [];
                    this.types = [];
                    this.material = [];
                    this.isDisabled = false;
                    this.subject_num = 120;
                    this.$refs['form'].resetFields();
                    this.getSchool();
                    this.getGeo();
                    this.getTypes();
                }
            }
        },
        mounted () {
            this.getSchool();
            this.getGeo();
            this.getTypes();
        },
        methods: {
            addRow (){  // 添加一行活地点
                var item = {
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    provinces: [],
                    citys: [],
                    app_join_event: 0,
                    begin_date: '',
                    end_date: '',
                    event_type: [],
                    major: [],
                    school_major: [],
                    options: {
                        disabledDate (date) {
                            return false;
                        }
                    }
                }
                this.form.location.push(item);
            },
            delRow (i){  // 删除一行活动地点
                if(i === 0){
                    this.form.location.splice(0, 1);
                }else {
                    this.form.location.splice(i, 1);
                }
                console.log(i)

            },
            getSchool (){  // 获取学校列表
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.schools = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            checkSchool (obj){
                this.getMajor();
                this.getMaterial();
            },
            setEndDate (obj,str){
                if(str === 'begin'){
                    if(!!obj.begin_date){
                        if(obj.begin_date.getDate() !== new Date(obj.begin_date.getTime() + 1000*60*120).getDate()){
                            obj.end_date = new Date(obj.begin_date.getFullYear() + '-' + (obj.begin_date.getMonth()+1) + '-' + obj.begin_date.getDate() + ' 23:59');
                        }else{
                            obj.end_date = new Date(obj.begin_date.getTime() + 1000*60*60); //1000*60*120
                        }
                        obj.options = {
                            disabledDate (date) {
                                return date && date.valueOf() < obj.begin_date.getTime()-1000*60*60*24
                            }
                        }
                    }
                }else {
                    if(!!obj.begin_date && !!obj.end_date){
                        if(obj.begin_date.getTime() >= obj.end_date.getTime()){
                            this.$Notice.error({
                                title: '结束时间不能小于开始时间！'
                            });
                            obj.end_date = new Date(obj.begin_date.getTime() + 1000*60*60); // 1000*60*120
                        }
                    }
                }
            },
            uploadImg (file){ // 上传图片
                var form = new FormData();
                form.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/upload?school_id='+ this.form.school_id +'&type=0',
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });

                        _this.files.push({
                            id: res.data.data.aid,
                            name: file.name,
                            src: 'https://static.chasedream.com' + res.data.data.fullpath//'/event/image/'+ res.data.data.aid
                        })
                    }else{
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (obj,index){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/upload',
                    method:'DELETE',
                    data: {
                        id: obj.id
                    },
                }).then(function (res) {
                    console.log(res.data);
                    if(res.data.msg === 'success'){
                        _this.files.splice(index, 1);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            getMajor (){  //  根据学校id获取专业
                var _this = this;
                util.ajax({
                    url: 'api/v1/admin/event/school/major/' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        var majors = [];
                        for(var i=0;i<res.data.data.length;i++){
                            var item = {};
                            item.id = res.data.data[i].id;
                            item.name = res.data.data[i].short;
                            item.major_id = res.data.data[i].major_id;
                            majors.push(item);
                        }
                        _this.majors = majors;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getGeo (pid){  //  获取国家列表
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.countrys = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getProvince (pid,i,str){  // 根据国家或省获取下一级区域列表
                if(!pid){
                    return false;
                }
                if(str === 'city'){
                    this.form.location[i].citys = [];
                    this.form.location[i].city_id = 0;
                }else {
                    this.form.location[i].provinces = [];
                    this.form.location[i].province_id = 0;
                }

                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    params: {
                        parent_id: pid
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        if(str === 'city'){
                            _this.form.location[i].citys = res.data.data;
                            if(!!_this.city_id){
                                _this.form.location[i].city_id = _this.city_id;
                            }
                        }else {
                            _this.form.location[i].provinces = res.data.data;
                            if(!!_this.province_id){
                                _this.form.location[i].province_id = _this.province_id;
                                _this.getProvince (_this.province_id,i,'city');
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            checkCity(obj, geo, index){
                geo.country_id = obj.country_id;
                this.getProvince (obj.country_id,index,'province');
                this.province_id = obj.province_id;
                this.city_id = obj.city_id;
            },
            getTypes (){  // 获取活动类型
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/type',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.types = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {  //  提交数据
                if(!this.form.school_id){
                    this.$Notice.warning({
                        title: '请选择学校'
                    });
                    return false;
                }
                if(!this.form.subject){
                    this.$Notice.warning({
                        title: '请输入活动标题'
                    });
                    return false
                }else if(this.form.subject.length < 3){
                    this.$Notice.warning({
                        title: '活动标题不能小于3个字符'
                    });
                    return false
                }else if(this.form.subject.length > 120){
                    this.$Notice.warning({
                        title: '活动标题不能大于120个字符'
                    });
                    return false
                }
                if(!this.form.content){
                    this.$Notice.warning({
                        title: '请输入活动内容'
                    });
                    return false
                }
                for(var i=0;i<this.form.location.length;i++){
                    if(!this.form.location[i].country_id){
                        this.$Notice.warning({
                            title: '请选择国家'
                        });
                        return false
                    }
                    if(!this.form.location[i].province_id){
                        this.$Notice.warning({
                            title: '请选择地区'
                        });
                        return false
                    }
                    if(this.form.location[i].major.length === 0){
                        this.$Notice.warning({
                            title: '请选择专业'
                        });
                        return false
                    }
                    if(this.form.location[i].event_type.length === 0){
                        this.$Notice.warning({
                            title: '请选择活动类型'
                        });
                        return false
                    }
                }

                var dataIn = {
                    subject: this.form.subject,
                    content: this.form.content,
                    school_id: this.form.school_id,
                    location: []
                };
                for(var i=0;i<this.form.location.length;i++){
                    var item = {
                        app_join_event: !!this.form.location[i].app_join_event ? 1 : 0,
                        country_id: this.form.location[i].country_id,
                        province_id: this.form.location[i].province_id,
                        city_id: this.form.location[i].city_id,
                        event_type: this.form.location[i].event_type,
                        school_major: this.form.location[i].major,
                        major: this.getSchool_major(this.form.location[i].major),
                        begin_date: !!this.form.location[i].begin_date ? Math.round(this.form.location[i].begin_date.getTime()/1000) : '',
                        end_date: !!this.form.location[i].end_date ? Math.round(this.form.location[i].end_date.getTime()/1000): ''
                    }
                    dataIn.location.push(item);
                }
                this.isDisabled = true;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'core_add');
                        _this.$store.commit('closePage', 'core_add');
                        _this.$router.push({
                            name: 'core_list'
                        });
                    }else{
                        _this.isDisabled = false;
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.isDisabled = false;
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            check_subject_num (e){
                this.strLenCalc(this.form.subject,this.subject_num,120)
            },
            strLenCalc(obj, checklen, maxlen) {
                var charset = 'utf-8';
                var v = obj, charlen = 0, maxlen = !maxlen ? 200 : maxlen, curlen = maxlen, len = this.strlen(v);
                for(var i = 0; i < v.length; i++) {
                    if(v.charCodeAt(i) < 0 || v.charCodeAt(i) > 255) {
                        curlen -= charset == 'utf-8' ? 2 : 1;
                    }
                }
                if(curlen >= len) {
                    this.subject_num = curlen - len;
                } else {
                    this.form.subject = this.mb_cutstr(v, maxlen, 0);
                    this.max_l = this.form.subject.length + this.subject_num;
                }
            },
            strlen(str) {
                return (str.indexOf('\n') != -1) ? str.replace(/\r?\n/g, '_').length : str.length;//BROWSER.ie &&
            },
            mb_cutstr(str, maxlen, dot) {
                var charset = 'utf-8';
                var len = 0;
                var ret = '';
                var dot = !dot ? '...' : dot;
                maxlen = maxlen - dot.length;
                for(var i = 0; i < str.length; i++) {
                    len += str.charCodeAt(i) < 0 || str.charCodeAt(i) > 255 ? (charset == 'utf-8' ? 3 : 2) : 1;
                    if(len > maxlen) {
                        ret += dot;
                        break;
                    }
                    ret += str.substr(i, 1);
                }
                return ret;
            },
            distinct(arr) {
                return arr.sort().reduce((init, current) => {
                    if(init.length === 0 || init[init.length-1] !== current) {
                        init.push(current);
                    }
                    return init;
                }, []);
            },
            getSchool_major(arr){
                var school_major = [];
                for(var i=0;i<arr.length;i++){
                    for(var j=0;j<this.majors.length;j++){
                        if(this.majors[j].id === arr[i]){
                            school_major.push(this.majors[j].major_id);
                        }
                    }
                }
                return school_major
            },
            getMaterial(){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/material/digest?school_id=' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.material = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getEvent_image (){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/event_image?school_id=' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.event_img = res.data.data.rows;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            moreImg (){
                if(!!this.form.school_id){
                    this.showMore = true;
                    this.getEvent_image();
                }else {
                    this.$Notice.warning({
                        title: '请选学校！'
                    });
                }
            },
            onCopy (e){
                this.$Message.success({
                    top: 200,
                    content: '复制成功！'
                });
            },
            onError (){
                this.$Message.error({
                    top: 200,
                    content: '复制失败！'
                });
            },
            image_digest(obj,n){  //图片置顶/取消置顶
                var dataIn = {};
                dataIn.digest = n;
                dataIn.id = obj.id;
                dataIn.school_id = obj.school_id;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/image_digest',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(n == 1){
                            _this.getMaterial();
                            _this.getEvent_image();
                            _this.$Message.success({
                                top: 200,
                                content: '置顶成功！'
                            });

                        }else {
                            _this.$Message.success({
                                top: 200,
                                content: '取消置顶成功！'
                            });
                        }
                        _this.getMaterial();
                        _this.getEvent_image();
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form.subject = '';
                this.form.begin_date = '';
                this.form.end_date = '';
                this.form.content = '';
                this.form.school_id = '';
                this.form.major_id = '';
                this.form.event_type = [];
                this.form.location = [{
                    country_id: '',
                    province_id: '',
                    provinces: []
                }]
                this.schools = [];
                this.majors = [];
                this.countrys = [];
                this.types = [];
                this.material = [];
                this.isDisabled = false;
                this.subject_num = 120;
                this.$store.commit('removeTag', 'core_add');
                this.$router.push({
                    name: 'core_list'
                });
            }
        },
        created () {

        }
    };
</script>