<style lang="less">
    @import '../../../../styles/common.less';
    @import '../event.less';
</style>

<template>
    <div class="core-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    编辑核心活动
                </p>
                <Form ref="form" :model="form" :label-width="90">
                    <FormItem prop="school_id" label="机构：" class="ivu-form-item-required">
                        <Select v-model="form.school_id" placeholder="请选择学校" filterable @on-change="checkSchool">
                            <Option :value="item.id" :label="item.display_name +' （'+ item.keyword +'）'" v-for="item in schools" :key="item.index">
                                <span>{{item.display_name}}</span>
                                <span style="float:right;color:#ccc">{{item.country}}</span>
                            </Option>
                        </Select>
                        <div class="common" v-if="useSchools.length > 0">
                            <strong>常用：</strong>
                            <p>
                                <span v-for="item in useSchools" :key="item.index" @click="checkSchool(item)">{{item.name}}</span>
                            </p>
                        </div>
                    </FormItem>
                    <FormItem prop="image" label="素材：">
                        <div class="material">
                            <div v-for="img in material">
                                <img :src="local + img.fullpath" alt="图片" v-clipboard:copy="local + img.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                <span v-clipboard:copy="local + img.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">复制</span>
                            </div>
                        </div>
                        <Button type="ghost" icon="more" @click="moreImg"></Button>
                        <Modal v-model="showMore" width="515" class-name="materialModal">
                            <p slot="header">
                                <Icon type="images" size="24"></Icon>
                                <span>素材库</span>
                            </p>
                            <div>
                                <div class="addList">
                                    <div class="file-list">
                                        <div v-for="file,index in files" class="file-list-item">
                                            <img :src="file.src" alt="图片" width="50" v-clipboard:copy=" file.src" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                            <Icon type="android-close" class="del" @click="delImg(file,index)" size="22"></Icon>
                                        </div>
                                        <Upload multiple action="/api/v1/admin/event/upload"
                                                :before-upload="uploadImg"
                                                :show-upload-list="true"
                                        >
                                            <Button type="ghost" icon="plus"></Button>
                                        </Upload>
                                    </div>
                                </div>
                                <div>
                                    <div>
                                        <span>置顶</span>
                                        <span style="float: right;color: #2db7f5;cursor: pointer" class="editImg" v-if="!editImg" @click="editImg = !editImg">编辑</span>
                                        <span style="float:right;color: #2db7f5;cursor: pointer;" class="editImg" v-if="!!editImg" @click="editImg = !editImg">保存</span>
                                    </div>
                                    <div class="top_imgs">
                                        <div v-for="item in material">
                                            <img :src="local + item.fullpath" alt="图片" width="50" v-clipboard:copy="local + item.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                        </div>
                                    </div>
                                </div>
                                <div class="former_imgs">
                                    <div v-for="item in event_img">
                                        <img :src="local + item.fullpath" alt="图片" width="50" v-clipboard:copy="local + item.fullpath" v-clipboard:success="onCopy" v-clipboard:error="onError">
                                        <span v-if="!!editImg" @click="image_digest(1);"><Icon type="plus-round"></Icon></span>
                                    </div>
                                </div>
                            </div>
                            <div slot="footer">
                                <Button type="ghost" size="large" @click="showMore = false">关闭</Button>
                            </div>
                        </Modal>
                    </FormItem>
                    <FormItem prop="subject" label="活动标题：" class="ivu-form-item-required">
                        <Input v-model="form.subject" placeholder="请输入活动标题"></Input>
                    </FormItem>
                    <FormItem prop="content" label="活动内容：" class="ivu-form-item-required">
                        <Input v-model="form.content" type="textarea" :autosize="{minRows: 5,maxRows: 8}" placeholder="请输入活动内容"></Input>
                    </FormItem>
                    <div class="geos" v-for="geo,index in form.location" :key="geo.index">
                        <Row>
                            <Col span="6">
                            <FormItem prop="country_id" label="地点：" class="ivu-form-item-required">
                                <Select v-model="geo.country_id" placeholder="请选择国家" @on-change="getProvince(geo.country_id,geo,'province')">
                                    <Option v-for="item in countrys" :value="item.id" :key="item.index">{{ item.name }}</Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="4">
                            <FormItem prop="province_id" class="province" style="">
                                <Select v-model="geo.province_id" placeholder="请选择地区" @on-change="getProvince(geo.province_id,geo,'city')">
                                    <Option v-for="item in geo.provinces" :value="item.id" :key="item.index">{{ item.name }}</Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="4">
                            <FormItem prop="city_id" class="city" style="margin-right: 30px;">
                                <Select v-model="geo.city_id" placeholder="请选择城市">
                                    <Option v-for="item in geo.citys" :value="item.id" :key="item.index">{{ item.name }}</Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="1">
                            </Col>
                            <Col span="4">
                            <FormItem prop="" class="city" style="margin-right: 30px;">
                                <Checkbox v-model="geo.app_join_event">app 参加</Checkbox>
                            </FormItem>
                            </Col>
                            <Col span="24" v-if="useCitys.length > 0">
                            <div class="common" style="margin: -20px 0 20px 90px;">
                                <strong>常用：</strong>
                                <p>
                                    <span v-for="item in useCitys" :key="item.index" @click="checkCity(item,geo,index)">{{item.name}}</span>
                                </p>
                            </div>
                            </Col>
                        </Row>
                        <FormItem prop="major_id" label="专业：" class="ivu-form-item-required">
                            <CheckboxGroup v-model="geo.event_school_major" @on-change="setMajors"><!--major-->
                                <Checkbox :label="item.id" v-for="item in majors" :key="item.index">{{item.name}}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <FormItem prop="event_type" label="活动类型：" class="ivu-form-item-required">
                            <CheckboxGroup v-model="geo.event_type" @on-change="setMajors">
                                <Checkbox :label="item.id" v-for="item in types" :key="item.index">{{item.name}}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        <Row>
                            <Col span="7">
                            <FormItem prop="begin_date" class="begin_data" label="时间：">
                                <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="geo.begin_date" @on-ok="setEndDate(geo,'begin')" :time-picker-options="{steps: [1, 15, 15]}" placeholder="请选择开始时间" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col span="1" align="center" style="line-height: 40px;">
                            -
                            </Col>
                            <Col span="6">
                            <FormItem prop="end_date" class="end_date" style="">
                                <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" v-model="geo.end_date" @on-ok="setEndDate(geo,'end')" :time-picker-options="{steps: [1, 15, 15]}" placeholder="请选择结束时间" :options="geo.options" style="width: 100%"></DatePicker>
                            </FormItem>
                            </Col>
                        </Row>
                        <Icon type="trash-a" size="24" class="delItem" @click="delRow(index)" v-if="form.location.length > 1"></Icon>
                    </div>
                    <FormItem>
                        <Button type="dashed" icon="plus" class="plusItem" @click="addRow"></Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary" v-if="!isDisabled">提交</Button>
                        <Button type="primary" disabled v-if="!!isDisabled">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'core_edit',
        components: {

        },
        data () {
            return {
                form: {
                    subject: '',
                    content: '',
                    school_id:'',
                    location: [{
                        country_id: '',
                        province_id: '',
                        city_id: '',
                        begin_date: '',
                        end_date: '',
                        provinces: [],
                        citys: [],
                        event_type: [],
                        major: [],
                        event_school_major: [],
                        school_major: [],
                        options: {
                            disabledDate (date) {
                                return false;
                            }
                        }
                    }]
                },
                schools: [],
                useSchools: [],
                majors:[],
                countrys:[],
                provinces:[],
                citys: [],
                useCitys: [
                    {
                        name: '在线',
                        country_id: 4134,
                        province_id: 4135,
                        city_id: ''
                    },
                    {
                        name: '北京',
                        country_id: 1,
                        province_id: 2,
                        city_id: ''
                    },
                    {
                        name: '上海',
                        country_id: 1,
                        province_id: 111,
                        city_id: ''
                    },
                    {
                        name: '深圳',
                        country_id: 1,
                        province_id: 265,
                        city_id: 268
                    },
                    {
                        name: '广州',
                        country_id: 1,
                        province_id: 265,
                        city_id: 266
                    },
                    {
                        name: '南京',
                        country_id: 1,
                        province_id: 130,
                        city_id: 131
                    },
                    {
                        name: '香港',
                        country_id: 1,
                        province_id: 503,
                        city_id: ''
                    },
                    {
                        name: '天津',
                        country_id: 1,
                        province_id: 19,
                        city_id: ''
                    }
                ],
                types: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                files: [],
                school_major: [],
                material: [],
                event_img: [],
                showMore: false,
                local: 'https://static.chasedream.com',
                editImg:false,
                id: 0,
                num: 0,
                timer: null,
                isDisabled: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'core_edit'){
                    this.form = {
                        subject: '',
                        content: '',
                        school_id:'',
                        location: [{
                            country_id: '',
                            province_id: '',
                            city_id: '',
                            begin_date: '',
                            end_date: '',
                            provinces: [],
                            citys: [],
                            event_type: [],
                            major: [],
                            event_school_major: [],
                            school_major: [],
                            options: {
                                disabledDate (date) {
                                    return false;
                                }
                            }
                        }]
                    };
                    this.schools = [];
                    this.useSchools = [];
                    this.majors = [];
                    this.countrys = [];
                    this.province = [];
                    this.geos = [];
                    this.location = [];
                    this.types = [];
                    this.school_major = [];
                    this.material = [];
                    this.local = 'https://static.chasedream.com';
                    this.id = 0;
                    this.num = 0;
                    this.isDisabled = false;

                    this.$refs['form'].resetFields();
                    this.getSchool();
                    this.getTypes();
                    this.getGeo();
                    this.getData();
                }
            }
        },
        mounted () {
            this.getSchool();
            this.getTypes();
            this.getGeo();
            this.getData();
        },
        methods: {
            addRow (){
                var item = {
                    event_id: this.id,
                    country_id: 0,
                    province_id: 0,
                    city_id: 0,
                    provinces: [],
                    citys: [],
                    app_join_event: 0,
                    begin_date: '',
                    end_date: '',
                    event_type: [],
                    event_school_major: [],
                    major: [],
                    school_major: [],
                    options: {
                        disabledDate (date) {
                            return false;
                        }
                    }
                }
                this.$set(this.form.location,this.form.location.length,item);
            },
            delRow (i,str){
                if(!this.form.location[i].id){
                    this.form.location.splice(i, 1);
                }else {
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/event/location',
                        method:'DELETE',
                        data: {
                            id: this.form.location[i].id
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.form.location.splice(i, 1);

                        }else{

                        }
                    }).catch(function (err) {
                        console.log(err)
                    });
                }
            },
            saveRow (i){
                if(!this.geos[i].country_id){
                    this.$Notice.error({
                        title: '请选择国家！'
                    });
                    return false;
                }
                if(!this.geos[i].province_id){
                    this.$Notice.error({
                        title: '请选择地区！'
                    });
                    return false;
                }

                var dataIn = {
                    event_id: this.event_id,
                    country_id: this.geos[i].country_id,
                    province_id: this.geos[i].province_id,
                    city_id: this.geos[i].city_id,
                    begin_date: Math.round(this.geos[i].begin_date.getTime()/1000),
                    end_date: Math.round(this.geos[i].end_date.getTime()/1000)
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/location',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '添加成功！'
                        });
                        dataIn.provinces = _this.geos[i].provinces;
                        dataIn.citys = _this.geos[i].citys;
                        _this.location.push(dataIn);

                        var len = _this.location.length-1;
                        _this.location[len].begin_date = new Date(parseInt(_this.location[len].begin_date + '000'));
                        _this.location[len].end_date = new Date(parseInt(_this.location[len].end_date + '000'));

                        _this.geos.splice(i, 1);
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            getData (){
                this.id = window.location.href.split('/').pop();
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/core/' + this.id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.form.school_id = res.data.data.school_id;
                        _this.form.subject = res.data.data.subject;
                        _this.form.content = res.data.data.content;
                        _this.form.location = res.data.data.event_location
                        _this.checkSchool();
                        for(var i=0;i<_this.form.location.length;i++){
                            _this.form.location[i].begin_date = new Date(parseInt(_this.form.location[i].begin_date + '000'));
                            _this.form.location[i].end_date = new Date(parseInt(_this.form.location[i].end_date + '000'));
                            if(_this.form.location[i].app_join_event === 0){
                                _this.form.location[i].app_join_event = false;
                            }else {
                                _this.form.location[i].app_join_event = true;
                            }

                            _this.form.location[i].major = [];
                            for(var j=0;j<_this.form.location[i].event_school_major.length;j++){
                                _this.form.location[i].major.push(_this.form.location[i].event_school_major[j]);
                            }
                            _this.province_id = _this.form.location[i].province_id;
                            _this.city_id = _this.form.location[i].city_id;
                            _this.form.location[i].province_id_c = _this.form.location[i].province_id;
                            _this.form.location[i].city_id_c = _this.form.location[i].city_id;
                            _this.form.location[i].options = {
                                disabledDate (date) {
                                    return false;
                                }
                            }
                            _this.getProvince (_this.form.location[i].country_id,_this.form.location[i],'province');
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.ms
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getSchool (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/school',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.schools = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            checkSchool (obj){
                this.getMajor();
                this.getMaterial();
            },
            setEndDate (obj,str){
                if(str === 'begin'){
                    if(!!obj.begin_date){
                        if(obj.begin_date.getDate() !== new Date(obj.begin_date.getTime() + 1000*60*120).getDate()){
                            obj.end_date = new Date(obj.begin_date.getFullYear() + '-' + (obj.begin_date.getMonth()+1) + '-' + obj.begin_date.getDate() + ' 23:59');
                        }else{
                            obj.end_date = new Date(obj.begin_date.getTime() + 1000*60*60);
                        }
                        obj.options = {
                            disabledDate (date) {
                                return date && date.valueOf() < obj.begin_date.getTime()-1000*60*60*24
                            }
                        }
                    }
                }else {
                    if(!!obj.begin_date && !!obj.end_date){
                        if(obj.begin_date.getTime() >= obj.end_date.getTime()){
                            this.$Notice.error({
                                title: '结束时间不能小于开始时间！'
                            });
                            obj.end_date = new Date(obj.begin_date.getTime() + 1000*60*60);
                            this.$set(obj,'begin_date',new Date(obj.begin_date.getTime() + 1000*60*60))
                        }
                    }
                }
            },
            uploadImg (file){ // 上传图片
                var form = new FormData();
                form.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/upload?school_id='+ this.form.school_id +'&type=0',
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });

                        _this.files.push({
                            id: res.data.data.aid,
                            name: file.name,
                            src: 'https://static.chasedream.com' + res.data.data.fullpath
                        })
                    }else{
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (obj,index){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/upload',
                    method:'DELETE',
                    data: {
                        id: obj.id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.files.splice(index, 1);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            getMajor (){  //  根据学校id获取专业
                if(!this.form.school_id){
                   return false;
                }
                var _this = this;
                util.ajax({
                    url: 'api/v1/admin/event/school/major/' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        var majors = [];
                        for(var i=0;i<res.data.data.length;i++){
                            var item = {};
                            item.id = res.data.data[i].id;
                            item.name = res.data.data[i].short;
                            item.major_id = res.data.data[i].major_id;
                            majors.push(item);
                        }

                        _this.majors = majors;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getGeo (pid){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.countrys = res.data.data;

                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getProvince (pid,item,str){
                if(!pid){
                    return false;
                }
                if(str === 'city'){
                    item.citys = [];
                    item.city_id = 0;
                }else {
                    item.provinces = [];
                    item.province_id = 0;
                    item.citys = [];
                    item.city_id = 0;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo',
                    method:'GET',
                    async: false,
                    params: {
                        parent_id: pid
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        if(str === 'city'){
                            item.citys = res.data.data;
                            if(!!item.city_id_c){
                                item.city_id = item.city_id_c;
                            }
                        }else {
                            item.provinces = res.data.data;
                            if(!!item.province_id_c){
                                item.province_id = item.province_id_c;
                                if(!!item.city_id_c){
                                    _this.getProvince (item.province_id_c,item,'city');
                                }
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            checkCity(obj, geo, index){
                geo.country_id = obj.country_id;
                geo.province_id_c = obj.province_id;
                geo.city_id_c = obj.city_id;
                this.getProvince (obj.country_id,geo,'province');
            },
            getGeoName (id,i,str){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/geo/'+ id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        if(str === 'city'){
                            _this.location[i].citys.push(res.data.data);
                        }else {
                            _this.location[i].provinces.push(res.data.data);
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getTypes (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/type',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.types = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {
                if(!this.form.school_id){
                    this.$Notice.warning({
                        title: '请选择学校'
                    });
                    return false;
                }
                if(!this.form.subject){
                    this.$Notice.warning({
                        title: '请输入活动标题'
                    });
                    return false
                }else if(this.form.subject.length < 3){
                    this.$Notice.warning({
                        title: '活动标题不能小于3个字符'
                    });
                    return false
                }else if(this.form.subject.length > 120){
                    this.$Notice.warning({
                        title: '活动标题不能大于120个字符'
                    });
                    return false
                }
                if(!this.form.content){
                    this.$Notice.warning({
                        title: '请输入活动内容'
                    });
                    return false
                }
                for(var i=0;i<this.form.location.length;i++){
                    if(!this.form.location[i].country_id){
                        this.$Notice.warning({
                            title: '请选择国家'
                        });
                        return false
                    }
                    if(!this.form.location[i].province_id){
                        this.$Notice.warning({
                            title: '请选择地区'
                        });
                        return false
                    }
                    if(this.form.location[i].event_school_major.length === 0){
                        this.$Notice.warning({
                            title: '请选择专业'
                        });
                        return false
                    }
                    if(this.form.location[i].event_type.length === 0){
                        this.$Notice.warning({
                            title: '请选择活动类型'
                        });
                        return false
                    }
                }

                this.isDisabled = true;
                this.submitEvent();
                for(var i=0;i<this.form.location.length;i++){
                    this.submitLocation(this.form.location[i]);
                }
                var _this = this;

                this.timer = setInterval(function () {
                    if(_this.num === _this.form.location.length + 1){
                        clearInterval(_this.timer);
                        _this.$Notice.success({
                            title: "提交成功！"
                        });
                        setTimeout(function () {
                            _this.$store.commit('removeTag', 'core_edit');
                            _this.$store.commit('closePage', 'core_edit');
                            _this.$router.push({
                                name: 'core_list'
                            });
                        },1000);
                    }else {
                        _this.isDisabled = false;
                    }
                },100);

                return false;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'core_edit');
                        _this.$store.commit('closePage', 'core_edit');
                        _this.$router.push({
                            name: 'core_list'
                        });
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            submitEvent (){
                var dataIn = {
                    id: this.id,
                    subject: this.form.subject,
                    content: this.form.content,
                    school_id: this.form.school_id
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event',
                    method:'PUT',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.num++;
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                        clearInterval(_this.timer);
                        _this.num = 0;
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                    clearInterval(_this.timer);
                    _this.num = 0;
                });
            },
            submitLocation (obj){
                var dataIn = {
                    country_id: obj.country_id,
                    province_id: obj.province_id,
                    city_id: obj.city_id,
                    app_join_event: !obj.app_join_event ? 0 : 1,
                    school_major: obj.event_school_major,
                    major: this.getSchool_major(obj.event_school_major),
                    event_id: obj.event_id,
                    event_type: obj.event_type,
                    begin_date: !!obj.begin_date ? Math.round(obj.begin_date.getTime()/1000) : '',
                    end_date: !!obj.end_date ? Math.round(obj.end_date.getTime()/1000): ''
                }
                var _this = this;
                var method = 'POST'
                if(!!obj.id){
                    dataIn.id = obj.id;
                    method = 'PUT'
                }
                util.ajax({
                    url:'/api/v1/admin/event/location',
                    method: method,
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.num++;
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                        clearInterval(_this.timer);
                        _this.num = 0;
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    clearInterval(_this.timer);
                    _this.num = 0;
                    console.log(err)
                });
            },
            getSchool_major(arr){
                var school_major = [];
                for(var i=0;i<arr.length;i++){
                    for(var j=0;j<this.majors.length;j++){
                        if(this.majors[j].id === arr[i]){
                            school_major.push(this.majors[j].major_id);
                        }
                    }
                }
                return school_major
            },
            getMaterial(){
                if(!this.form.school_id){
                    return false;
                }
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/material/digest?school_id=' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.material = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getEvent_image (){
                var _this = this;
                util.ajax({
                    url: '/api/v1/admin/event/event_image?school_id=' + this.form.school_id,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success') {
                        _this.event_img = res.data.data.rows;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            moreImg (){
                if(!!this.form.school_id){
                    this.showMore = true;
                    this.getEvent_image();
                }else {
                    this.$Notice.warning({
                        title: '请选学校！'
                    });
                }
            },
            onCopy (e){
                this.$Message.success({
                    top: 200,
                    content: '复制成功！'
                });
            },
            onError (){
                this.$Message.error({
                    top: 200,
                    content: '复制失败！'
                });
            },
            image_digest(obj,n){  //图片置顶/取消置顶
                var dataIn = {};
                dataIn.digest = n;
                dataIn.id = obj.id;
                dataIn.school_id = obj.school_id;

                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/event/image_digest',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(n == 1){
                            _this.$Message.success({
                                top: 200,
                                content: '置顶成功！'
                            });

                        }else {
                            _this.$Message.success({
                                top: 200,
                                content: '取消置顶成功！'
                            });
                        }
                        _this.getMaterial();
                        _this.getEvent_image();
                    }else{
                        _this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            setMajors (index){

            },
            closePage () {
                this.form = {
                    subject: '',
                    content: '',
                    school_id:'',
                    location: [{
                        country_id: '',
                        province_id: '',
                        city_id: '',
                        begin_date: '',
                        end_date: '',
                        provinces: [],
                        citys: [],
                        event_type: [],
                        major: [],
                        event_school_major: [],
                        school_major: [],
                        options: {
                            disabledDate (date) {
                                return false;
                            }
                        }
                    }]
                };
                this.schools = [];
                this.useSchools = [];
                this.majors = [];
                this.countrys = [];
                this.province = [];
                this.geos = [];
                this.location = [];
                this.types = [];
                this.school_major = [];
                this.material = [];
                this.local = 'https://static.chasedream.com';
                this.id = 0;
                this.num = 0;
                this.isDisabled = false;
                this.$store.commit('removeTag', 'core_edit');
                this.$router.push({
                    name: 'core_list'
                });
            }
        },
        created () {

        }
    };
</script>