export const logColumns = [
    {
        //title: '',
        key: 'success',
        width: 50,
        align: 'center',
        render: (h, params) => {
            var style = {}
            if (!params.row.success) {
                style.color = '#ed3f14';
            } else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size: 14
                }
            })
        }
    },
    /*{
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },*/
    /*{
        title: '区号',
        align: 'center',
        key: 'area_code',
        width: 80
    },*/
    {
        title: '区号/手机号码',
        align: 'center',
        key: 'mobile',
        width: 140,
        render: (h, params) => {
            return h('span', {}, params.row.area_code + '-' + params.row.mobile)
        }
    },
    {
        title: '验证码',
        align: 'center',
        key: 'captcha',
        width: 80,
        render: (h, params) => {
            var style = {}
            var font = '';
            style.cursor = 'pointer';
            if (!params.row.used) {
                style.color = '#19be6b';
                font = '未使用';
            } else {
                style.color = '#ccc';
                font = '已使用';
            }
            return h('div', {}, [
                h('Tooltip', {
                    props: {
                        content: font,
                        placement: 'right'
                    }
                }, [
                    h('Icon', {

                        style: style,
                        props: {
                            type: 'ios-barcode',
                            size: 22
                        }
                    })
                ]),
                h('p', {}, params.row.captcha)
            ])
        }
    },
    /*{
        title: '是否验证',
        align: 'center',
        key: 'used',
        width: 80,
        render: (h, params) => {
            var style = {}
            var font = '';
            style.cursor = 'pointer';
            if(!params.row.used){
                style.color = '#19be6b';
                font = '未使用';
            }else {
                style.color = '#ccc';
                font = '已使用';
            }
            return h('Tooltip',{
                props: {
                    content: font,
                    placement: 'right'
                }
            },[
                h('Icon', {

                    style: style,
                    props: {
                        type: 'ios-analytics',
                        size:18
                    }
                })
            ])
        }
    },*/
    {
        title: '特征码',
        align: 'center',
        key: 'token'
    },
    {
        title: '类型',
        align: 'center',
        key: 'type',
        width: 145
    },
    {
        title: 'IP',
        align: 'center',
        width: 130,
        key: 'ip'
    },
    /*{
        title: '阿里云码',
        align: 'center',
        key: 'aliyun_code',
        width: 120
    },*/
    {
        title: '平台',
        align: 'center',
        key: 'platform',
        width: 90
    },
    /*{
        title: '平台返回信息',
        align: 'center',
        key: 'platform_response',
        width: 120
    },*/
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 100,
        render: (h, params) => {
            var hs = new Date(parseInt(params.row.timestamp + '000')).getHours();
            var m = new Date(parseInt(params.row.timestamp + '000')).getMinutes();
            var s = new Date(parseInt(params.row.timestamp + '000')).getSeconds();
            hs = hs.toString().length > 1 ? hs : ('0' + hs);
            m = m.toString().length > 1 ? m : ('0' + m);
            s = s.toString().length > 1 ? s : ('0' + s);
            return h('span', {
            }, params.row.created_at + ' ' + hs + ':' + m + ':' + s)
        }
    },
    {
        title: '查看详情',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['view']
    }
];
export const blColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    {
        title: '区号',
        align: 'center',
        key: 'area_code',
        width: 80
        /*editable: true*/
    },
    {
        title: '手机号码',
        align: 'center',
        key: 'mobile'
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['delete']
    },
];
export const mColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    {
        title: '用户名',
        align: 'left',
        key: 'member',
        width: 150,
        render: (h, params) => {
            return h('span', {}, params.row.member.username);
        }
    },
    /*{
        title: '区号',
        align: 'center',
        key: 'area_code',
        width: 80
    },*/
    {
        title: '手机号码',
        align: 'left',
        key: 'mobile'
    },
    /*{
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['delete']
    },*/
];
export const uColumns = [
    {
        title: '用户名',
        key: 'username',
        width: 150,
        align: 'center',
        /*render: (h, params) => {
            return h('span', {},params.row.member.username)
        }*/
    },
    {
        title: '区号',
        align: 'center',
        key: 'area_code',
        width: 80,
        render: (h, params) => {
            return h('span', {}, !!params.row.mobile ? params.row.mobile.area_code : '')
        }
    },
    {
        title: '手机号码',
        align: 'center',
        key: 'mobile',
        width: 160,
        render: (h, params) => {
            //return h('span', {},!!params.row.mobile ? params.row.mobile.area_code + '-' +params.row.mobile.mobile : '')
            return h('span', {}, !!params.row.mobile ? params.row.mobile.mobile : '')
        }
    },
    /*{
        title: '微信',
        align: 'center',
        key: 'wechat',
        width: 120
    },*/
    {
        title: '特征码',
        align: 'left',
        key: 'useragent',
        render: (h, params) => {
            return h('span', {}, !!params.row.mobile_log ? params.row.mobile_log.useragent : '')
        }
    },
    {
        title: '注册IP',
        align: 'center',
        width: 150,
        key: 'regip',
        /*render: (h, params) => {
            return h('span', {},params.row.member.regip)
        }*/
    },
    {
        title: '注册时间',
        align: 'center',
        key: 'regdate',
        width: 150,
        render: (h, params) => {
            var y = new Date(parseInt(params.row.regdate + '000')).getFullYear();
            var M = new Date(parseInt(params.row.regdate + '000')).getMonth() + 1;
            var d = new Date(parseInt(params.row.regdate + '000')).getDate();
            var hs = new Date(parseInt(params.row.regdate + '000')).getHours();
            var m = new Date(parseInt(params.row.regdate + '000')).getMinutes();
            var s = new Date(parseInt(params.row.regdate + '000')).getSeconds();
            M = M.toString().length > 1 ? M : ('0' + M);
            d = d.toString().length > 1 ? d : ('0' + d);
            hs = hs.toString().length > 1 ? hs : ('0' + hs);
            m = m.toString().length > 1 ? m : ('0' + m);
            s = s.toString().length > 1 ? s : ('0' + s);
            return h('span', {
            }, y + '-' + M + '-' + d + ' ' + hs + ':' + m + ':' + s)
        }
    },
    /*{
        title: '查看详情',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['view']
    },*/
];
export const usColumns = [
    {
        title: '用户名',
        key: 'handle',
        width: 150,
        align: 'center',
        handle: ['username']
    },
    {
        title: '区号',
        align: 'center',
        key: 'handle',
        width: 80,
        handle: ['area_code']
    },
    {
        title: '手机号码',
        align: 'center',
        key: 'handle',
        width: 160,
        handle: ['mobile']
    },
    {
        title: '特征码',
        align: 'left',
        key: 'handle',
        handle: ['useragent']
    },
    {
        title: '注册IP',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['regip']
    },
    {
        title: '注册时间',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['clear']
    }
];
const smsData = {
    logColumns: logColumns,
    blColumns: blColumns,
    mColumns: mColumns,
    uColumns: uColumns,
    usColumns: usColumns
};

export default smsData;
