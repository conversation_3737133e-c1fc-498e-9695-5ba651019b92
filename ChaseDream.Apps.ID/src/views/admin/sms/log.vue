<style lang="less">
    @import '../../../styles/common.less';
    @import './sms.less';
</style>

<template>
    <div class="log sms-log">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <div style="width: 420px;display: inline-block;vertical-align: bottom;">
                        <Input v-model="s_val" style="width: 400px;" @on-enter="searchLogs('enter')" placeholder="按关键词搜索">
                        <Select v-model="search_type" slot="prepend" style="width: 80px" placeholder="请选择查询类型">
                            <Option value="area_code">区号</Option>
                            <Option value="mobile">手机号</Option>
                            <Option value="ip">IP</Option>
                        </Select>
                        <Button slot="append" icon="ios-search" type="primary" @click="searchLogs()"></Button>
                        </Input>
                    </div>
                    <RadioGroup v-model="keyword" type="button" size="large" @on-change="getLogs(1)">
                        <Radio label="">全部</Radio>
                        <Radio label="1">成功</Radio>
                        <Radio label="0">失败</Radio>
                    </RadioGroup>
                </div>

                <div class="edittable-con-1">
                    <can-edit-table refs="logTable" @details="showDetails" v-model="logRows" :columns-list="logColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getLogs" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="日志详情" class="log_details">
            <p>
                <strong>ID</strong>
                <span>{{log_details.id}}</span>
            </p>
            <p>
                <strong>区号</strong>
                <span>{{log_details.area_code}}</span>
            </p>
            <p>
                <strong>手机号</strong>
                <span>{{log_details.mobile}}</span>
            </p>
            <!--<p>
                <strong>biz_id</strong>
                <span>{{log_details.aliyun_biz_id}}</span>
            </p>
            <p>
                <strong>阿里云码</strong>
                <span>{{log_details.aliyun_code}}</span>
            </p>
            <p>
                <strong>阿里云信息</strong>
                <span>{{log_details.aliyun_message}}</span>
            </p>
            <p>
                <strong>阿里云请求ID</strong>
                <span>{{log_details.aliyun_request_id}}</span>
            </p>-->
            <p>
                <strong>IP地址</strong>
                <span>{{log_details.ip}}</span>
            </p>
            <p>
                <strong>平台</strong>
                <span>{{log_details.platform}}</span>
            </p>
            <p>
                <strong>平台返回信息</strong>
                <span>
                    <code v-html="log_details.b_platform_response" style="opacity: 0"></code>
                    <textarea disabled>{{log_details.platform_response}}</textarea>
                </span>
            </p>
            <p>
                <strong>验证码</strong>
                <span>{{log_details.captcha}}</span>
            </p>
            <p>
                <strong>状态</strong>
                <span>{{log_details.success}}</span>s
            </p>
            <p>
                <strong>特征码</strong>
                <span>{{log_details.token}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span>{{log_details.created_at}}</span>
            </p>

            <!--<p>
                <strong>时间戳</strong>
                <span>{{log_details.timestamp}}</span>
            </p>
            <p>
                <strong>更新时间</strong>
                <span>{{log_details.updated_at}}</span>
            </p>-->
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import smsData from './data/sms_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'log',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                logColumns: [],
                logRows: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                log_details: {},
                details: false,
                keyword: '',
                search_type: 'mobile',
                s_val: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'log'){
                    this.total = 0;
                    this.showPage = false;
                    this.current = 1;
                    this.keyword = '';
                    this.search_type = 'area_code';
                    this.s_val = '';
                    this.getLogs(1);
                }
            }
        },
        mounted () {
            this.getLogs(1);
        },
        methods: {
            getData () {
                this.logColumns = smsData.logColumns;
            },
            getLogs (n){
                this.current = n;

                //this.keyword = '';
                var keys = '';
                if(this.keyword != ''){
                    keys = '?success=' + this.keyword;
                }
                var search = '';
                if(this.s_val !== ''){
                    //if(this.search_type === 'area_code'){
                        if(this.keyword != ''){
                            search = '&' + this.search_type + '=' + this.s_val;
                        }else {
                            search = '?' + this.search_type + '=' + this.s_val;
                        }

                    //}
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/sms/log' + keys + search,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.logRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                        /*for(var i=0;i<_this.logRows.length;i++){
                            if(!!_this.logRows[i].success){
                                _this.logRows[i].success = '<span class="ivu-badge-dot">1</span>'
                            }else {
                                _this.logRows[i].success = '<span class="ivu-badge-dot">2</span>'
                            }

                        }*/
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            searchLogs (str){
                /*if(this.s_val === ''){
                    return false
                }
                if(!!str){
                    if(str === 'enter'){

                    }
                }*/

                this.getLogs(1);

            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            showDetails (row){
                //console.log(row)
                var b_str = row.platform_response.replace(/\{/g,'\{\<br\>\&nbsp\;\&nbsp\;').replace(/\}/g,'\<br\>\}').replace(/\,/g,'\,<br>\&nbsp\;\&nbsp\;').replace(/\[/g,'\[\<br\>\<br\>').replace(/\]/g,'\<br\>\]');
                var str = JSON.stringify(JSON.parse(row.platform_response),null,4);
                this.log_details = row;
                this.log_details.platform_response = str;
                this.log_details.b_platform_response = b_str;
                this.details = true;
            },
            close (){
                this.log_details = {};
                this.details = false;
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
