<style lang="less">
    @import '../../../styles/common.less';
    @import './sms.less';
</style>

<template>
    <div class="isp">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <br>
                <br>

                <Form ref="ispForm" :model="ispform" :rules="rules" :label-width="100">
                    <FormItem prop="userName" label="国内：">
                        <RadioGroup v-model="ispform.local">
                            <Radio label="aliyun">阿里云</Radio>
                            <Radio label="send_cloud">SendCloud</Radio>
                            <Radio label="azure" disabled>Azure</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="password" label="海外：">
                        <RadioGroup v-model="ispform.oversea">
                            <Radio label="aliyun">阿里云</Radio>
                            <Radio label="send_cloud">SendCloud</Radio>
                            <Radio label="twilio">Twilio</Radio>

                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="getIsp(1)" type="primary">保存</Button>
                        <!--<Button @click="closePage">取消</Button>-->
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>

    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'isp',
        components: {

        },
        data () {
            return {
                ispform: {
                    local: '',
                    oversea: '',
                    id: 0
                },
                rules: {
                    userName: [
                        { required: true, message: '国内为必选项', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '海外为必选项', trigger: 'blur' }
                    ]
                },
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'isp'){
                    this.getIsp();
                }
            }
        },
        mounted () {
            this.getIsp();
        },
        methods: {
            getData () {
               // this.ispColumns = smsData.ispColumns;
            },
            getIsp (n){
                var type = 'GET';
                var dataIn = null;
                if(n === 1){
                    var type = 'PUT';
                    if(!this.ispform.local || !this.ispform.oversea){
                        this.errMsg = '国内海外都为必选项'
                    }
                    dataIn = {};
                    dataIn.local = this.ispform.local;
                    dataIn.oversea = this.ispform.oversea;
                    dataIn.id = this.ispform.id;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/isp',
                    method:type,
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(n !== 1){
                            _this.ispform.local = res.data.data.local;
                            _this.ispform.oversea = res.data.data.oversea;
                            _this.ispform.id = res.data.data.id;
                        }else {
                            if(res.data.data[0] === 1){
                                _this.$Notice.success({
                                    title: '提交成功！'
                                });
                            }else {
                                _this.$Notice.error({
                                    title: '提交失败！'
                                });
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
