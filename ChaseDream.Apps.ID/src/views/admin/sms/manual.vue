<style lang="less">
@import "../../../styles/common.less";
@import "./sms.less";
</style>

<template>
  <div class="manual">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <br />
          <br />

          <Form
            ref="manualform"
            :model="manualform"
            :rules="rules"
            :label-width="100"
          >
            <FormItem prop="usernames" label="用户名：">
              <Input
                v-model="manualform.usernames"
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入用户名"
                style="width:400px"
              ></Input>
            </FormItem>
            <FormItem prop="type" label="类型：">
              <RadioGroup v-model="manualform.type" @on-change="getTypeInfo">
                <Radio label="00">Steven和老大</Radio>
                <Radio label="01">运营部门</Radio>
                <Radio label="02">MBA部门</Radio>
                <Radio label="03">Master部门</Radio>
                <Radio label="04">IT测试</Radio>
                <Radio label="05">客户账号</Radio>
                <Radio label="06">GMAT部门</Radio>
                <Radio label="90">顶帖</Radio>
              </RadioGroup>
              <!--<Select v-model="manualform.type" style="width:400px">
                            <Option value="00" label="Steven和老大">Steven和老大</Option>
                            <Option value="01" label="运营部门">运营部门</Option>
                            <Option value="02" label="MBA部门">MBA部门</Option>
                            <Option value="03" label="Master部门">Master部门</Option>
                            <Option value="04" label="IT测试">IT测试</Option>
                            <Option value="05" label="客户账号">客户账号</Option>
                            <Option value="06" label="GMAT部门">GMAT部门</Option>
                        </Select>-->
            </FormItem>
            <p class="f_err_msg">{{ errMsg }}</p>
            <FormItem>
              <Button @click="getManual(1)" type="primary">提交</Button>
              <!--<Button @click="closePage">取消</Button>-->
            </FormItem>
          </Form>
          <div class="edittable-con-1" v-if="!!showTb">
            <can-edit-table
              refs="mTable"
              v-model="mRows"
              :columns-list="mColumns"
            ></can-edit-table>
          </div>
          <div class="page-bar" v-if="!!showTb">
            <Page
              :total="total"
              :page-size="page_size"
              :current="current"
              @on-change="getlist"
              v-if="showPage"
            ></Page>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import smsData from "./data/sms_data.js";
import canEditTable from "./components/canEditTable.vue";

export default {
  name: "manual",
  components: {
    canEditTable,
  },
  data() {
    return {
      manualform: {
        usernames: "",
        type: "",
      },
      rules: {
        usernames: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择类型", trigger: "change" }],
      },
      errMsg: "",
      isAccess: true,
      mRows: [],
      mColumns: [],
      showPage: false,
      showTb: false,
      total: 0,
      page_size: 20,
      current: 1,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "manual") {
        //this.getIsp();
        this.showPage = false;
        this.showTb = false;
        this.total = 0;
        this.current = 1;
      }
    },
  },
  mounted() {
    // this.getManual();
    this.getData();
  },
  methods: {
    getData() {
      this.mColumns = smsData.mColumns;
    },
    getManual(n) {
      this.$refs.manualform.validate((valid) => {
        if (valid) {
          var type = "GET";
          var dataIn = null;
          if (n === 1) {
            var type = "post";
            dataIn = {};
            dataIn.usernames = this.manualform.usernames;
            dataIn.type = this.manualform.type;
          }
          var _this = this;
          util
            .ajax({
              url: "/api/v1/admin/sms/binding_mobile/manual",
              method: type,
              data: dataIn,
            })
            .then(function(res) {
              if (res.data.msg === "success") {
                console.log(res.data);
                _this.$Notice.success({
                  title: "提交成功！",
                });
                /*if(n !== 1){
                                    //_this.manualform.usernames = res.data.data.usernames;
                                    //_this.manualform.type = res.data.data.type;
                                }else {
                                    if(res.data.data[0] === 1){
                                        _this.$Notice.success({
                                            title: '提交成功！'
                                        });
                                    }else {
                                        _this.$Notice.error({
                                            title: '提交失败！'
                                        });
                                    }
                                }*/
              } else {
                _this.$Notice.error({
                  title: res.data.msg,
                });
              }
            })
            .catch(function(err) {
              console.log(err);
              _this.$Notice.error({
                title: err.msg,
              });
            });
        }
      });
    },
    getlist(n) {
      this.current = n;
      this.getTypeInfo(this.manualform.type);
    },
    getTypeInfo(e) {
      // console.log(e);
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/sms/binding_mobile/manual?type=" + e,
          params: {
            page: _this.current,
            page_size: _this.page_size,
          },
          method: "GET",
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.showTb = true;
            _this.mRows = res.data.data.rows;
            _this.total = res.data.data.count;

            _this.showPage = _this.total > _this.page_size;
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
  },
  created() {
    //this.getData();
  },
};
</script>

<style></style>
