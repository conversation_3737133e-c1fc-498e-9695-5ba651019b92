<style lang="less">
    @import '../../../styles/common.less';
    @import './sms.less';
</style>

<template>
    <div class="users">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div style="position: relative;margin-bottom: 20px;height: 30px;">
                    <!--<Input v-model="s_val" style="width: 400px;" @on-enter="searchUs('enter')" placeholder="按关键词搜索">
                    <Select v-model="search_type" slot="prepend" style="width: 80px" placeholder="请选择查询类型">
                        <Option value="area_code">区号</Option>
                        <Option value="mobile">手机号</Option>
                        <Option value="regip">注册IP</Option>
                        <Option value="useragent">特征码</Option>
                    </Select>
                    <Button slot="append" icon="ios-search" type="primary" @click="searchUs()"></Button>
                    </Input>-->
                    <span style="position: absolute;top: 20px;right: 0;" v-if="count > 0">用户数：<strong style="color: #19be6b">{{count}}</strong></span>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getusers(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>
                    <can-edit-table refs="uTable" @details="showDetails" v-model="uRows" :columns-list="uColumns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getusers" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <!--<Modal v-moddetailails" title="日志详情" class="log_details">
            <p>
                <strong>ID</strong>
                <span>{{u_details.id}}</span>
            </p>
            <p>
                <strong>区号</strong>
                <span>{{u_details.area_code}}</span>
            </p>
            <p>
                <strong>手机号</strong>
                <span>{{u_details.mobile}}</span>
            </p>
            &lt;!&ndash;<p>
                <strong>biz_id</strong>
                <span>{{log_details.aliyun_biz_id}}</span>
            </p>
            <p>
                <strong>阿里云码</strong>
                <span>{{log_details.aliyun_code}}</span>
            </p>
            <p>
                <strong>阿里云信息</strong>
                <span>{{log_details.aliyun_message}}</span>
            </p>
            <p>
                <strong>阿里云请求ID</strong>
                <span>{{log_details.aliyun_request_id}}</span>
            </p>&ndash;&gt;
            <p>
                <strong>IP地址</strong>
                <span>{{u_details.ip}}</span>
            </p>
            <p>
                <strong>平台</strong>
                <span>{{u_details.platform}}</span>
            </p>
            <p>
                <strong>平台返回信息</strong>
                <span>
                    <code v-html="u_details.b_platform_response" style="opacity: 0"></code>
                    <textarea disabled>{{u_details.platform_response}}</textarea>
                </span>
            </p>
            <p>
                <strong>验证码</strong>
                <span>{{u_details.captcha}}</span>
            </p>
            <p>
                <strong>状态</strong>
                <span>{{u_details.success}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span>{{u_details.created_at}}</span>
            </p>

            &lt;!&ndash;<p>
                <strong>时间戳</strong>
                <span>{{log_details.timestamp}}</span>
            </p>
            <p>
                <strong>更新时间</strong>
                <span>{{log_details.updated_at}}</span>
            </p>&ndash;&gt;
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>-->
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import smsData from './data/sms_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'smsuser',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                uColumns: [],
                uRows: [],
                s_row: [{
                    username: '',
                    area_code: '',
                    mobile: '',
                    useragent: '',
                    regip: ''
                }],
                s_columns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                u_details: {},
                details: false,
                search_type: 'mobile',
                s_val: '',
                count: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'log'){
                    this.total = 0;
                    this.showPage = false;
                    this.current = 1;
                    this.search_type = 'area_code';
                    this.s_val = '';
                    this.getusers(1);
                }
            }
        },
        mounted () {
            this.getusers(1);
        },
        methods: {
            getData () {
                this.uColumns = smsData.uColumns;
                this.s_columns = smsData.usColumns;
            },
            getusers (n){
                this.current = n;

                var s = [];
                var search = '';
                if(!!this.s_row[0].username){
                    s.push('username=' + this.s_row[0].username);
                }
                if(!!this.s_row[0].area_code){
                    s.push('area_code=' + this.s_row[0].area_code);
                }
                if(!!this.s_row[0].mobile){
                    s.push('mobile=' + this.s_row[0].mobile);
                }
                if(!!this.s_row[0].useragent){
                    s.push('useragent=' + this.s_row[0].useragent);
                }
                if(!!this.s_row[0].regip){
                    s.push('regip=' + this.s_row[0].regip);
                }

                if(s.length > 0){
                    search ='?' + s.join('&')
                }
                /*if(this.s_val !== ''){
                    search = '?' + this.search_type + '=' + this.s_val;
                }*/
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/sms/user' + search,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.uRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        _this.count = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                        /*for(var i=0;i<_this.logRows.length;i++){
                         if(!!_this.logRows[i].success){
                         _this.logRows[i].success = '<span class="ivu-badge-dot">1</span>'
                         }else {
                         _this.logRows[i].success = '<span class="ivu-badge-dot">2</span>'
                         }

                         }*/
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            searchUs (str){
                this.getusers(1);
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            showDetails (row){
                //console.log(row)
                var b_str = row.platform_response.replace(/\{/g,'\{\<br\>\&nbsp\;\&nbsp\;').replace(/\}/g,'\<br\>\}').replace(/\,/g,'\,<br>\&nbsp\;\&nbsp\;').replace(/\[/g,'\[\<br\>\<br\>').replace(/\]/g,'\<br\>\]');
                var str = JSON.stringify(JSON.parse(row.platform_response),null,4);
                this.u_details = row;
                this.u_details.platform_response = str;
                this.u_details.b_platform_response = b_str;
                this.details = true;
            },
            close (){
                this.u_details = {};
                this.details = false;
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
