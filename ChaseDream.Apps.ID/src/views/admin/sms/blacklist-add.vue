<style lang="less">
    @import '../../../styles/common.less';
    @import '../../user/add/user-add.less';
</style>

<template>
    <div class="blacklist-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="person-add"></Icon>
                    添加黑名单
                </p>
                <Form ref="blackForm" :model="blackform" :rules="rules" :label-width="100">
                    <FormItem prop="area_code" label="区号：">
                        <!--<Input v-model="blackform.area_code" placeholder="请输入区号"></Input>-->
                        <Select v-model="blackform.area_code" style="width:400px">
                            <Option :value="code.area_code" :label="code.area_code" v-for="code in codes" :key="code.index">
                                <span>{{code.name}}</span>
                                <span style="float:right;color:#ccc">{{code.area_code}}</span>
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem prop="mobile" label="手机号码：">
                        <Input v-model="blackform.mobile" placeholder="请输入手机号码" style="width: 400px;"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../../area_code';

    export default {
        name: 'blacklist_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                blackform: {
                    area_code: 86,
                    mobile: ''
                },
                rules: {
                    area_code: [
                        /*{ required: true, message: '区不能为空', trigger: 'blur' },*/
                       /* { type: 'string', min: 3, message: '手机号码不能小于3个字符', trigger: 'blur' }*/
                    ],
                    mobile: [
                        { required: true, message: '手机号码不能为空', trigger: 'blur' },
                        /*{ type: 'string', min: 11, message: '手机号码只能为数字且不能小于11个字符', trigger: 'blur' }*/
                    ]
                },
                errMsg: '',
                isAccess: true,
                codes: []
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'blacklist_add'){
                    this.blackform.area_code = 86;
                    this.blackform.mobile = '';
                    this.errMsg = '';
                }
            }
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.codes = area.area_code;
        },
        methods: {
            handleSubmit () {
                this.$refs.blackForm.validate((valid) => {
                    if (valid) {
                        if(!this.blackform.mobile){
                            this.errMsg = '手机号码不能为空'
                            return false
                        }
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/sms/blacklist',
                            method:'POST',
                            data: {
                                area_code: _this.blackform.area_code,
                                mobile: _this.blackform.mobile
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'blacklist_add');
                                _this.$store.commit('closePage', 'blacklist_add');
                                _this.$router.push({
                                    name: 'blacklist'
                                });
                            }else{
                               // _this.errMsg = res.msg;
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.$Notice.error({
                                title: err.msg
                            });
                            //console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.blackform.area_code = 86;
                this.blackform.mobile = '';
                this.errMsg = '';
                this.$store.commit('removeTag', 'blacklist_add');
                this.$router.push({
                    name: 'blacklist'
                });
            }
        }
    };
</script>

<style>

</style>
