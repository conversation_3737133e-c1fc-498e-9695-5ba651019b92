export const editInlineColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    /*{
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },*/
    {
        title: '姓名',
        align: 'center',
        key: 'realname',
        width: 120
    },
    {
        title: '用户名',
        align: 'center',
        key: 'username',
    },
    /*{
        title: '密码',
        align: 'center',
        key: 'password',
        width: 150,
        editable: true
    },*/
    {
        title: '角色',
        align: 'center',
        key: 'role',
        width: 150,
        render: (h, params) => {
            var role_names = []
            for (var i = 0; i < params.row.role.length; i++) {
                role_names.push(params.row.role[i].name)
            }
            return h('span', {}, role_names.join('、'))
        }
    },
    {
        title: '部门',
        align: 'center',
        key: 'department',
        width: 150,
        render: (h, params) => {
            var text = '';
            if (params.row.department === 1) {
                text = 'MBA';
            } else if (params.row.department === 2) {
                text = 'Master';
            } else if (params.row.department === 3) {
                text = '网站';
            } else if (params.row.department === 4) {
                text = '开发测试';
            } else if (params.row.department === 5) {
                text = 'mba'
            } else if (params.row.department === 6) {
                text = '总办'
            } else if (params.row.department === 7) {
                text = 'Agent Sim'
            } else if (params.row.department === 8) {
                text = 'Agent FB'
            } else if (params.row.department === 9) {
                text = 'Agent CL'
            } else if (params.row.department === 10) {
                text = 'GMAT'
            } else if (params.row.department === 11) {
                text = 'YY'
            } else if (params.row.department === 12) {
                text = '老王'
            }

            return h('span', {}, text)
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 190,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];

export const editInlineData = [
    {
        username: 'Aresn',
        password: '1',
        role: '管理员'
    },
    {
        username: 'Lison',
        password: '1',
        role: '成员'
    },
    {
        username: 'lisa',
        password: '123',
        role: '成员'
    }
];


const tableData = {
    editInlineColumns: editInlineColumns,
    editInlineData: editInlineData
};

export default tableData;
