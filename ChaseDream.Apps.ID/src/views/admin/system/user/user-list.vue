<style lang="less">
    @import '../../../../styles/common.less';
    @import 'user-list.less';
</style>

<template>
    <div class="user-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/system/user-add">
                        <Button type="success" icon="person-add">添加用户</Button>
                    </router-link>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table2" @on-delete="handleDel" v-model="editInlineData" :columns-list="editInlineColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getUsers" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import canEditTable from './components/canEditTable.vue';
    import tableData from './data/table_data.js';
    import util from '@/libs/util.js';

    export default {
        name: 'user_index',
        components: {
            canEditTable
        },
        data () {
            return {
                editInlineColumns: [],
                editInlineData: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true
            };
        },
        computed: {
            /*avatorPath () {
             //return localStorage.avatorImgPath;
             }*/
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getUsers(1);
        },
        methods: {
            getData () {
                this.editInlineColumns = tableData.editInlineColumns;
            },
            getUsers (n){
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/users',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.editInlineData = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            handleNetConnect (state) {
                this.breakConnect = state;
            },
            handleLowSpeed (state) {
                this.lowNetSpeed = state;
            },
            getCurrentData () {
                this.showCurrentTableData = true;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            handleCellChange (val, index, key) {
                this.$Message.success('修改了第 ' + (index + 1) + ' 行列名为 ' + key + ' 的数据');
            },
            handleChange (val, index) {
                this.$Message.success('修改了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
