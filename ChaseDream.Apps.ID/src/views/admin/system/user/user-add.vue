<style lang="less">
    @import '../../../../styles/common.less';
    @import 'user-add.less';
</style>

<template>
    <div class="user-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="person-add"></Icon>
                    添加用户
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="90">
                    <FormItem prop="userName" label="用户名：">
                        <Input v-model="userform.userName" placeholder="请输入用户名"></Input>
                    </FormItem>
                    <FormItem prop="role" label="角色：" required>
                        <CheckboxGroup v-model="userform.role">
                            <Checkbox :label="role.id" v-for="role in roleData" :key="role.index">{{role.name}}</Checkbox>
                        </CheckboxGroup>
                    </FormItem>
                    <FormItem prop="realname" label="姓名：" required>
                        <Input v-model="userform.realname" placeholder="请输入姓名"></Input>
                    </FormItem>
                    <FormItem prop="department" label="部门：" required>
                        <RadioGroup v-model="userform.department">
                            <Radio :label="department.id" v-for="department in departments" :key="department.index">{{department.name}}</Radio>
                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'user_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                userform: {
                    userName: '',
                    role: [],
                    realname: '',
                    department: 0
                },
                rules: {
                    userName: [
                        { required: true, message: '账号不能为空', trigger: 'blur' },
                        { type: 'string', min: 1, message: '账号不能小于1个字符', trigger: 'blur' }
                    ],
                    realname: [
                        { required: true, message: '姓名不能为空', trigger: 'blur' },
                        { type: 'string', min: 1, message: '姓名不能小于1个字符', trigger: 'blur' }
                    ],
                    /*password: [
                        { required: true, message: '密码不能为空', trigger: 'blur' },
                        { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                    ]*/
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                roleData: [],
                departments:[
                    /*{
                        id: 1,
                        name: 'MBA'
                    },
                    {
                        id: 2,
                        name: 'Master'
                    },
                    {
                        id: 3,
                        name: '网站'
                    },
                    {
                        id: 4,
                        name: '开发测试'
                    }*/
                ]
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'user_add'){
                    this.$refs.userForm.resetFields();
                    this.userform.userName = '';
                    this.userform.realname = '';
                    this.userform.department = 0;
                    this.userform.role = [];
                    this.departments = [];
                    this.getRole();
                    this.get_departments();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getRole();
            this.get_departments();
        },
        methods: {
            getRole() {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/permission/role',
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.roleData = res.data.data;
                        //_this.userform.role = 2;
                        _this.userform.role = [];
                    }else{
                        console.log(err)
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            get_departments (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/user/departments',
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                       _this.departments = res.data.data;
                    }else{
                        console.log(err)
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            handleSubmit () {
                this.$refs.userForm.validate((valid) => {
                    if (valid) {
                        if(this.userform.role.length === 0){
                            this.$Notice.error({
                                title: '请选择至少1个角色！'
                            });
                            return false
                        }
                        if(this.userform.department <= 0){
                            this.$Notice.error({
                                title: '请选择部门！'
                            });
                            return false
                        }
                        if(this.userform.department <= 0){
                            this.$Notice.error({
                                title: '请选择部门！'
                            });
                            return false
                        }

                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/user',
                            method:'POST',
                            data: {
                                username: _this.userform.userName,
                                realname: _this.userform.realname,
                                role: _this.userform.role,
                                department: _this.userform.department
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'user_add');
                                _this.$store.commit('closePage', 'user_add');
                                _this.$refs.userForm.resetFields();
                                _this.$router.push({
                                    name: 'user_list'
                                });
                            }else{
                                //_this.errMsg = res.msg;
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            //_this.errMsg = err.msg;
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.userform.userName = '';
                this.userform.realname = '';
                this.userform.department = 0;
                this.userform.role = [];
                this.$store.commit('removeTag', 'user_add');
                this.$refs.userForm.resetFields();
                this.$router.push({
                    name: 'user_list'
                });
            }
        }
    };
</script>

<style>

</style>
