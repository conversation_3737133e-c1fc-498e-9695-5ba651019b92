<style lang="less">
    @import '../../../../styles/common.less';
    @import 'menu.less';
</style>

<template>
    <div class="menu-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-add"></Icon>
                    添加菜单
                </p>
                <Form ref="menuform" :model="menuform" :rules="rules" :label-width="100">
                    <FormItem prop="pid" label="上级菜单：">
                        <!--<Input v-model="menuform.pid" placeholder="选择上级菜单"></Input>-->
                        <i-select v-model="menuform.pid" style="width:200px" placeholder="选择上级菜单">
                            <i-option v-for="item in pmenus" :value="item.id" :key="item.index">{{ item.text }}</i-option>
                        </i-select>
                    </FormItem>
                    <FormItem prop="text" label="菜单名称：">
                        <Input v-model="menuform.text" placeholder="请输入菜单名称"></Input>
                    </FormItem>
                    <FormItem prop="name" label="文件名称：">
                        <Input v-model="menuform.name" placeholder="请输入文件名称"></Input>
                    </FormItem>
                    <FormItem prop="router" label="路由路径：">
                        <Input v-model="menuform.router" placeholder="请输入路由路径"></Input>
                    </FormItem>
                    <FormItem prop="icon" label="图标：">
                        <!--<i-button type="info" @click="isOpen = true">选择</i-button>
                        <i-button tytesttext">取消</i-button>-->
                        <Input v-model="menuform.icon" placeholder="请选择图标"></Input>
                    </FormItem>
                    <FormItem prop="component" label="文件路径：">
                        <Input v-model="menuform.component" placeholder="请输入文件路径"></Input>
                    </FormItem>
                    <FormItem prop="order" label="排序：">
                        <Input v-model="menuform.order" placeholder="请输入排序"></Input>
                    </FormItem>
                    <FormItem prop="is_show" label="是否可见：">
                        <Radio-group v-model="menuform.is_show">
                            <Radio label="1">是</Radio>
                            <Radio label="0">否</Radio>
                        </Radio-group>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <Modal
            title="选择图标"
            v-model="isOpen"
            :mask-closable="false">
            <div>
                <Button type="text" :icon="icon" @click="choseIcon(icon)" v-for="icon in iconData" :key="icon.index">
                    <span>{{icon}}</span>
                </Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
        <Spin size="large" fix v-if="spinShow"></Spin>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    /*import menuData from './data/menu_data'*/

    export default {
        name: 'menu_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                menuform: {
                    pid: 0,
                    text: '',
                    name: '',
                    router: '',
                    order: '1.0',
                    icon: '',
                    component: '',
                    is_show: '1'
                },
                rules: {
                    text: [
                        { required: true, message: '菜单名称不能为空', trigger: 'blur' }
                    ],
                    name: [
                        { required: true, message: '文件名称不能为空', trigger: 'blur' }
                    ],
                    router: [
                        { required: true, message: '路由路径不能为空', trigger: 'blur' }
                    ],
                    /*icon: [
                        { required: true, message: '图标不能为空', trigger: 'blur' }
                    ],*/
                    component: [
                        { required: true, message: '文件路径不能为空', trigger: 'blur' }
                    ]

                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                pmenus: [
                    {
                        text: '无',
                        id: 0
                    }
                    /*{
                        test: '菜单管理',
                        id: 1
                    }*/
                ],
                isOpen: false,
                iconData: [],
                spinShow: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'menu_add'){
                    this.menuform.text = '';
                    this.menuform.router = '';
                    this.menuform.pid = 0;
                    this.menuform.order = '1.0';
                    this.menuform.is_show = '1';
                    this.menuform.icon = '';
                    this.menuform.component = '';
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getData();
        },
        methods: {
            getData(){
                //this.iconData = menuData.iconData;
                this.pmenus = [
                    {
                        text: '无',
                        id: 0
                    }
                ]
                //console.log(this.$router.options.routes)
                for(var i=0;i<this.$router.options.routes.length;i++){
                    if(this.$router.options.routes[i].pid === 0){
                        //console.log(this.$router.options.routes[i])
                        var item = {
                            text: this.$router.options.routes[i].title,
                            id: this.$router.options.routes[i].id
                        }
                        this.pmenus.push(item)
                    }
                }
                //console.log(this.pmenus)
            },
            handleSubmit () {
                this.$refs.menuform.validate((valid) => {
                    //console.log(valid)
                    if (valid) {
                        this.spinShow = true;
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/permission/menu',
                            method:'POST',
                            data: {
                                icon: this.menuform.icon,
                                text: this.menuform.text,
                                name: this.menuform.name,
                                router: this.menuform.router,
                                component: this.menuform.component,
                                order: this.menuform.order,
                                pid: this.menuform.pid,
                                is_show: this.menuform.is_show
                            },
                        }).then(function (res) {
                            //console.log(res)
                            if(res.data.msg === 'success'){
                                _this.$store.commit('setMenuRspList',false);
                                _this.$store.commit('getMenuData', _this.$router);

                                var timer;
                                clearInterval(timer);
                                timer = setInterval(function () {
                                    if(!!_this.$store.state.app.ismenuLoad){
                                        /*_this.$router.push({
                                            name: 'menu_list'
                                        });*/
                                        window.location.href = window.location.href.split('menu-add').join('menu');
                                        clearInterval(timer);
                                    }
                                },300)

                                _this.$store.commit('removeTag', 'menu_add');
                                _this.$store.commit('closePage', 'menu_add');
                                //_this.$router.push({ path: '/console/menu/menu-list?' + new Date().getTime() });
                                //setTimeout(function () {
                                    //window.location.href = window.location.href.split('menu-add').join('menu-list');
                                //},1000);

                                //location. reload()
                                    //'http://localhost:8080/console/menu/menu-list';

                            }else{
                                _this.errMsg = res.data.msg
                            }
                        }).catch(function (err) {
                            _this.errMsg = err.message
                            console.log(err)
                        });
                    }
                });
            },
            choseIcon (e){
                console.log(e)
            },
            closePage () {
                this.menuform.text = '';
                this.menuform.router = '';
                this.menuform.pid = 0;
                this.menuform.order = '1.0';
                this.menuform.is_show = '1';
                this.menuform.icon = '';
                this.menuform.component = '';
                this.$store.commit('removeTag', 'menu_add');
                this.$router.push({
                    name: 'menu_list'
                });
            }
        }
    };
</script>

<style>

</style>
