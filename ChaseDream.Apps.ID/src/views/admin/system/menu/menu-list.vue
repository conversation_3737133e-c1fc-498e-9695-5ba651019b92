<style lang="less">
    @import '../../../../styles/common.less';
   /* @import './key-list.less';*/
    @import 'menu.less';
</style>

<template>
    <div class="menu-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tools-bar">
                    <router-link to="/console/system/menu-add">
                        <Button type="success">新建菜单</Button>
                    </router-link>
                    <br>
                    <br>
                </div>
                <div style="max-width: 600px;">
                    <Menu ref="sideMenu" :open-names="openNames" :theme="menuTheme" width="auto" @on-select="changeMenu">
                        <template v-for="item in menuList">
                            <Submenu :name="item.name" :key="item.name">

                                <template slot="title">
                                    <Icon :type="item.icon" :size="iconSize"></Icon>
                                    <span class="layout-text">{{ itemTitle(item) }}</span>
                                </template>
                                <div class="btns">
                                    <!--<Button shape="circle" icon="plus" @click="addMenu"></Button>-->
                                    <Button class="i-edit" shape="circle" icon="edit" @click="editMenu(item)"></Button>
                                    <Poptip
                                            confirm
                                            title="您确认删除这条内容吗？"
                                            @on-ok="menuDel(item.id)"
                                    >
                                        <Button class="i-del" shape="circle" icon="trash-a" @click=""></Button>
                                    </Poptip>
                                </div>
                                <template v-for="child in item.children">
                                    <MenuItem :name="child.name" :key="'menuitem' + child.name">
                                        <Icon :type="child.icon" :size="iconSize" :key="'icon' + child.name"></Icon>
                                        <span class="layout-text" :key="'title' + child.name">{{ itemTitle(child) }}</span>
                                        <div class="btns">
                                            <Button class="i-edit" shape="circle" icon="edit" @click="editMenu(child)"></Button>
                                            <Poptip
                                                    confirm
                                                    title="您确认删除这条内容吗？"
                                                    @on-ok="menuDel(child.id)"
                                            >
                                                <Button class="i-del" shape="circle" icon="trash-a"></Button>
                                            </Poptip>
                                        </div>
                                    </MenuItem>
                                </template>
                            </Submenu>
                        </template>
                    </Menu>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    /*import menuData from './data/menu_data.js';*/
    import menuTable from './components/canEditTable.vue';

    export default {
        name: 'menu-list',
        components: {
            menuTable,

        },
        data () {
            return {
                menuData: [],
                menuList: [],
                iconSize: 20,
                menuTheme: {
                    type: String,
                    default: 'dark'
                },
                openNames: [],
                access: parseInt(Cookies.get('uAccess')),
                menuColumns: [],
                isAccess: true
            };
        },
        computed: {
            /*avatorPath () {
             //return localStorage.avatorImgPath;
             }*/
        },
        watch: {
            '$route' (to,from) {
                if(to.name === 'menu_list'){
                    this.getData();
                }
            },
        },
        mounted () {
            //this.getUsers(1);
        },
        methods: {
            getData () {
                this.menuList = this.$store.state.app.menuList;
                this.menuTheme = this.$store.state.app.menuTheme;  //'light'
                for(var i=0;i<this.menuList.length;i++){
                    this.openNames.push(this.menuList[i].name);
                }
                //console.log(this.menuList)
            },
            changeMenu (active) {
                //console.log(active)

                //this.$emit('on-change', active);
                /*let willpush = true;
                if (this.beforePush !== undefined) {
                    if (!this.beforePush(active)) {
                        willpush = false;
                    }
                }
                if (willpush) {
                    this.$router.push({
                        name: active
                    });
                }*/
            },
            itemTitle (item) {
                if (typeof item.title === 'object') {
                    return this.$t(item.title.i18n);
                } else {
                    return item.title;
                }
            },
            editMenu (e){
                //window.event? window.event.cancelBubble = true : e.stopPropagation();
                //console.log(e)
                this.$store.commit('getmenuCurrent', e);
                var _this = this;
                setTimeout(function () {
                    _this.$router.push({
                        name: 'menu_edit'
                    });
                    //_this.$router.push('/console/system/menu-edit')
                },300)

            },
            addmenu (data){
                /*const newRouter = [{
                    path: '/console/new',
                    icon: 'new',
                    name: 'new',
                    title: '新菜单',
                    uAccess: 1,
                    component: () => import('@/views/Main.vue'),
                    children: [{
                        path: 'new-list',
                        title:'新菜单',
                        name: 'new_list',
                        isShow: true,
                        component: () => import('@/views/admin/new/new-list.vue')
                    }]
                }];*/
                const  routerList = [];
                console.log(data)
                for(var i=0;i<data.length;i++){
                    var p_path = '';//data[i].path.split('/');
                    for(var j=0;j<data[i].router.split('/').length-1;j++){
                        p_path = p_path + data[i].router.split('/')[j];
                    }

                    var routerItem = {
                        path: p_path,
                        icon: data[i].icon,
                        name: data[i].router.split('/')[data[i].router.split('/').length-2],
                        title: data[i].text,
                        uAccess: 1,
                        component: () => import('@/views/Main.vue'),
                        children: [{
                            path: data[i].router.split('/').pop(),
                            title:data[i].text,
                            name: data[i].router.split('/').pop().split('-').join('_'),
                            isShow: true,
                            component: () => import('@/views/admin/'+data[i].path+ '.vue' )
                        }]
                    }
                    routerList.push(routerItem);
                }


               // this.$store.commit('addRouter',{newrouter: routerList, router: this.$router});
                //this.$store.commit('updateMenulist');

            },

            menuDel (id){
                console.log(id)
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/permission/menu',
                    method:'DELETE',
                    data: {
                        id: id
                    },
                }).then(function (res) {
                    //console.log(res.data);
                    if(res.data.msg === 'success'){
                        _this.$store.commit('delMenu',id);
                        _this.$store.commit('getMenuData', _this.$router);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        },
        created () {
            this.getData();
        }
    };
</script>
