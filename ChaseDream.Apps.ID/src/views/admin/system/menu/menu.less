.ivu-shrinkable-menu{
    height: 100%;
    width: 100%;


}
.menu-list{
    .ivu-card-body{
        padding: 20px;
    }
}
.ivu-menu{
    .ivu-menu-submenu{
        position: relative;
        .ivu-menu{
            .btns{
                position: absolute;
                right: 40px;
                top: -40px;
                z-index: 1;
                .ivu-btn{

                }
                .ivu-poptip-confirm .ivu-poptip-popper{
                    z-index: 99;
                }
                .ivu-poptip-confirm .ivu-poptip-inner{
                    white-space: nowrap;
                }
                .i-del{
                    color: #ed3f14;
                    font-size: 20px;
                }
                .i-edit{
                    color: #2d8cf0;
                    font-size: 20px;
                }
            }
            .ivu-menu-item{
                position: relative;
                .btns{
                    position: absolute;
                    right: 40px;
                    top: 10px;
                    .ivu-btn{

                    }
                }
            }
        }
    }


    .ivu-menu-opened>*>.ivu-menu-submenu-title-icon{
        float: inherit;
        position: absolute;
        left:150px;
        top: 17px;
    }
    &.ivu-menu-vertical .ivu-menu-submenu-title-icon{
        float: inherit;
        position: absolute;
        left:150px;
        top: 17px;
    }
}
.ivu-poptip-confirm .ivu-poptip-body-message{
    color: #333;
}
