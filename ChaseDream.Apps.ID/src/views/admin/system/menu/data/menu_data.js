export const menu = [
    {
        path: '/console/user',
        icon: 'user',
        name: 'user',
        title: '用户管理',
        //component: Main,
        uAccess: 0,
        children: [
            {
                path: 'user-list',
                title: '用户列表',
                icon: 'person-stalker',
                name: 'user_list',
                isShow: true,
                uAccess: 0,
                component: () => import('@/views/user/user-list.vue')
            },
            {
                path: 'user-add',
                title: '添加用户',
                icon: 'person-add',
                name: 'user_add',
                isShow: false,
                component: () => import('@/views/user/add/user-add.vue')
            },
            {
                path: 'user-edit',
                title: '编辑用户',
                icon: 'android-create',
                name: 'user_edit',
                isShow: false,
                component: () => import('@/views/user/edit/user-update.vue')
            }

        ]
    },
    {
        path: '/console/menu',
        icon: 'menu',
        name: 'menu',
        title: '菜单管理',
        //component: Main,
        uAccess: 0,
        children: [
            {
                path: 'menu-list',
                title: '菜单管理',
                icon: 'menu',
                name: 'menu_list',
                isShow: true,
                uAccess: 0,
                component: () => import('@/views/admin/menu/menu-list.vue')
            }

        ]
    }
    ]
export const menuColumns = [
    {
        title: '文字',
        align: 'center',
        key: 'title',
        editable: true
    },
    {
        title: '路径',
        align: 'center',
        key: 'path',
        editable: true
    },
    {
        title: '图标',
        align: 'center',
        key: 'icon',
        editable: true
    },
    {
        title: '排序',
        align: 'center',
        key: 'order',
        editable: true
    },
    /*{
        title: '显示/隐藏',
        align: 'center',
        key: 'uAccess',
        editable: true
    },*/
    {
        title: '操作',
        align: 'center',
        key: 'handle',
        handle: ['add','edit', 'delete']
    }
]

export const iconData = ['home','ionic','navicon-round', 'shuffle','flag','heart','gear-a','gear-b','toggle-filled','toggle','settings','edit','trash-a','trash-b','document','document-test','clipboard','scissors','funnel','bookmark','email','email-unread','folder','filing','archive','paper-airplane','link','paperclip','compose','briefcase','medkit','at','pound','cloud','upload','more','grid','calendar'];

const menuData = {
    menu: menu,
    menuColumns: menuColumns,
    iconData: iconData
};
export default menuData;