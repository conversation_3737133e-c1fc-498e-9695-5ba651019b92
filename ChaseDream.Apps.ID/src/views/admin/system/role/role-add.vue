<style lang="less">
    @import '../../../../styles/common.less';
    @import 'role.less';
</style>

<template>
    <div class="role-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="person-add"></Icon>
                    添加角色
                </p>
                <Form ref="roleform" :model="roleform" :rules="rules" :label-width="100">
                    <FormItem prop="userName" label="角色名称：">
                        <Input v-model="roleform.name" placeholder="请输入角色名称"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'role_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                roleform: {
                    name: ''
                },
                rules: {
                    name: [
                        { required: true, message: '角色名称不能为空', trigger: 'blur' }
                    ]
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'role_add'){
                    this.roleform.name = '';
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.roleform.validate((valid) => {
                    this.errMsg = '';
                    if (valid) {
                        var dataIn = {
                            name: this.roleform.name
                        }
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/permission/role',
                            method:'POST',
                            data: dataIn,
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'role_add');
                                _this.$store.commit('closePage', 'role_add');
                                _this.$router.push({
                                    name: 'role'
                                });
                            }else{
                                _this.errMsg = res.data.msg
                            }
                        }).catch(function (err) {
                            _this.errMsg = err.errors.message
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.roleform.name = '';
                this.$store.commit('removeTag', 'user_add');
                this.$router.push({
                    name: 'role'
                });
            }
        }
    };
</script>

<style>

</style>
