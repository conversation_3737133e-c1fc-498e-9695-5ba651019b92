<style lang="less">
    @import '../../../../styles/common.less';
    @import 'role.less';
</style>

<template>
    <div class="assign-purview">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="merge"></Icon>
                    分配权限 （{{role_name}}）
                </p>
                <div style="display: flex">
                    <Form class="permissions-list" :label-width="20" style="width: 50%;">
                        <FormItem v-for="item,index in permissions" :key="item.index">
                            <Select v-model="item.method" style="width:85px">
                                <Option v-for="potion in methodList" :value="potion" :key="potion.index">{{ potion }}</Option>
                            </Select>
                            <Input v-model="item.router" placeholder="请输入地址" style="width: 300px" @on-change	="addItem(index)"></Input>
                            <Button type="text" shape="circle" icon="close-circled" @click="removeItem(index)" v-if="!!item.isDel"></Button>
                        </FormItem>
                        <FormItem>
                            <Button @click="handleSubmit" type="primary">提交</Button>
                            <Button @click="closePage">取消</Button>
                        </FormItem>
                    </Form>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'assign_purview',
        components: {
            // canEditTable
        },
        data () {
            return {
                permissions:[
                    {
                        router: '/admin/permission/my_menu',
                        method: '*',
                        isDel: false
                    },
                    {
                        router: '/admin/permission/my_permission',
                        method: '*',
                        isDel: false
                    },
                    {
                        router: '',
                        method: '*',
                        isDel: false
                    },
                    {
                        router: '',
                        method: '*',
                        isDel: false
                    },
                    {
                        router: '',
                        method: '*',
                        isDel: false
                    }
                ],
                methodList:['*','GET','POST','PUT','DELETE'],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                id: 0,
                role_name: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'assign_purview'){
                    this.permissions = [
                        {
                            router: '/admin/permission/my_menu',
                            method: '*'
                        },
                        {
                            router: '/admin/permission/my_permission',
                            method: '*'
                        },
                        {
                            router: '',
                            method: '*'
                        },
                        {
                            router: '',
                            method: '*'
                        },
                        {
                            router: '',
                            method: '*'
                        }
                    ];
                    this.id = 0;
                    this.role_name = '';
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.permissions = [
                {
                    router: '/admin/permission/my_menu',
                    method: '*'
                },
                {
                    router: '/admin/permission/my_permission',
                    method: '*'
                },
                {
                    router: '',
                    method: '*'
                },
                {
                    router: '',
                    method: '*'
                },
                {
                    router: '',
                    method: '*'
                }
            ];
            this.id = 0;
            this.getData();
            //console.log(window.localStorage.getItem('role_name'))
        },
        methods: {
            getData(){
                this.id = parseInt(window.location.href.split('/').pop());
                this.role_name = window.localStorage.getItem('role_name');
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/permission/permission_for_role/'+ this.id,
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(res.data.data.length < 1){
                            return
                        }else if(res.data.data.length < 5){
                            for(var i=0;i<res.data.data.length;i++){
                                _this.permissions[i].router = res.data.data[i].router;
                                _this.permissions[i].method = res.data.data[i].method;
                                _this.permissions[i].isDel = true;

                            }
                        }else {
                            _this.permissions = res.data.data;
                            for(var i=0;i<_this.permissions.length;i++){
                                _this.permissions[i].isDel = true;
                            }
                            var item = {
                                router: '',
                                method: '*',
                                isDel: false
                            }
                            _this.permissions.push(item);
                        }
                    }else{

                    }
                }).catch(function (err) {
                    /*_this.$Notice.error({
                        title: err.msg
                    });*/
                    console.log(err)
                });
            },
            removeItem (i){
                console.log(i)
                this.permissions.splice(i,1);
                if(this.permissions.length < 5){
                    var item = {
                        router: '',
                        method: '*',
                        isDel: false
                    }
                    this.permissions.push(item);
                }

            },
            handleSubmit () {
                this.errMsg = '';
                if (this.id > 0) {
                    var dataIn = {
                        role: this.id,
                        permissions: []
                    };
                    for(var i=0;i<this.permissions.length;i++){
                        if(!!this.permissions[i].router){
                            dataIn.permissions.push(this.permissions[i]);
                        }
                    }
                    console.log(dataIn)
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/permission/permission_for_role',
                        method:'POST',
                        data: dataIn,
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.closePage();
                        }else{
                            _this.errMsg = res.data.msg
                        }
                    }).catch(function (err) {
                        _this.errMsg = err.errors.message
                        console.log(err)
                    });
                }
            },
            addItem (index){
                console.log(index)
                if(index >= 4 && this.permissions.length === (index+1)){
                    var item = {
                        router: '',
                        method: '*'
                    }
                    this.permissions.push(item)
                }
            },
            closePage () {
                this.permissions = [
                    {
                        router: '',
                        method: '*'
                    },
                    {
                        router: '',
                        method: '*'
                    },
                    {
                        router: '',
                        method: '*'
                    },
                    {
                        router: '',
                        method: '*'
                    },
                    {
                        router: '',
                        method: '*'
                    }
                ];
                this.id = 0;
                this.role_name = '';
                this.$store.commit('removeTag', 'assign_purview');
                this.$store.commit('closePage', 'assign_purview');
                this.$router.push({
                    name: 'role'
                });
            }
        }
    };
</script>

<style>

</style>
