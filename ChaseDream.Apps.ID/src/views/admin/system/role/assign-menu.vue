<style lang="less">
    @import '../../../../styles/common.less';
    @import 'role.less';
</style>

<template>
    <div class="assign-menu">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="pull-request"></Icon>
                    分配菜单
                </p>
                <Transfer
                        :data="r_data"
                        :target-keys="targetKeys"
                        :list-style="listStyle"
                        :selected-keys="selectedKeys"
                        :render-format="r_render"
                        @on-selected-change="r_change"
                        @on-change="handleChange"></Transfer>

                <div class="" style="margin-top: 20px;">
                        <Button @click="handleSubmit" type="primary" style="margin-right: 10px;">提交</Button>
                        <Button @click="closePage">取消</Button>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'assign_menu',
        components: {
            // canEditTable
        },
        data () {
            return {
                r_data:[],
                targetKeys: [],
                selectedKeys: [],
                keysArr: [],
                listStyle: {
                    width: '300px',
                    height: '450px'
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                id: 0
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'assign_menu'){
                    this.id = 0;
                    this.r_data = [];
                    this.targetKeys = [];
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.id = 0;
            this.r_data = [];
            this.targetKeys = [];
            this.getData();
        },
        methods: {
            getData(){
                this.id = parseInt(window.location.href.split('/').pop());
                //console.log(this.$store.state.app.menuList)
                var menus = this.$store.state.app.menuList;
                for(var i=0;i<menus.length;i++){
                    if(!!menus[i].id){
                        var item = {
                            key: menus[i].id,
                            label: menus[i].title,
                            disabled: false
                        }
                        this.r_data.push(item);
                        for(var j=0;j<menus[i].children.length;j++){
                            if(!!menus[i].children[j].id){
                                var inner = {
                                    key: menus[i].children[j].id,
                                    label: ' &nbsp;&nbsp;&nbsp; ' + menus[i].children[j].title + ' - ' + menus[i].title,
                                    disabled: false
                                }
                                this.r_data.push(inner);
                            }
                        }
                    }
                }
                var tagsList = this.$store.state.app.tagsList;
                //console.log(tagsList)
                /*for(var i=0;i<tagsList.length;i++){
                    if(!!tagsList[i].id){
                        var item = {
                            key: tagsList[i].id,
                            label: tagsList[i].title,
                            disabled: false
                        }
                        this.r_data.push(item)
                    }
                }*/
                //console.log(this.r_data);
                this.getTargetKeys();

            },
            getTargetKeys (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/permission/role_menu/' + this.id,
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data.data);
                        for(var i=0;i<res.data.data.length;i++){
                            _this.keysArr.push(res.data.data[i].menu_id);
                        }
                        _this.sortTargetKeys()
                    }else{
                        _this.errMsg = res.data.msg;
                    }
                }).catch(function (err) {
                    _this.errMsg = err.errors.message;
                    console.log(err)
                });
            },
            sortTargetKeys (){
                for(var i=0;i<this.r_data.length;i++){
                    for(var j=0;j<this.keysArr.length;j++){
                        if(this.r_data[i].key === this.keysArr[j]){
                            this.targetKeys.push(this.keysArr[j]);
                        }
                    }
                }
            },
            r_render (item) {
                //console.log(item)
                return item.label; //+ ' - ' + item.description;
            },
            r_change(e,n){
                console.log(e)
                console.log(n)
                //console.log(this.selectedKeys)
                //this.selectedKeys.push(43)
            },
            handleChange (newTargetKeys) {
                console.log(newTargetKeys)
                this.targetKeys = newTargetKeys;
            },
            reloadMockData () {
                this.r_data = this.getMockData();
                this.targetKeys = this.getTargetKeys();
            },
            handleSubmit () {
                //console.log(this.targetKeys)
                //if (this.targetKeys.length > 0) {
                    var dataIn = {
                        role_id: this.id,
                        menu_id: this.targetKeys
                    }
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/permission/role_menu',
                        method:'POST',
                        data: dataIn,
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.closePage();
                        }else{
                            _this.errMsg = res.data.msg
                        }
                    }).catch(function (err) {
                        _this.errMsg = err.errors.message
                        console.log(err)
                    });
                //}
            },
            closePage () {
                this.id = 0;
                this.$store.commit('removeTag', 'assign_menu');
                this.$store.commit('closePage', 'assign_menu');
                this.$router.push({
                    name: 'role'
                });
            }
        }
    };
</script>

<style>

</style>
