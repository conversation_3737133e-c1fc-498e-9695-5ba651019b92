<style lang="less">
    @import '../../../../styles/common.less';
    @import './role.less';
</style>

<template>
    <div class="role">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/system/role-add">
                        <Button type="success" icon="person-add">添加角色</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table2" @on-delete="handleDel" v-model="roleData" :columns-list="editColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getUsers" v-if="showPage"></Page>
                </div>

            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import canEditTable from './components/canEditTable.vue';
    import tableData from './data/table_data.js';
    import util from '@/libs/util.js';

    export default {
        name: 'role',
        components: {
            canEditTable
        },
        data () {
            return {
                editColumns: [],
                roleData: [],
                total: 0,
                showPage: false,
                page_size: 10,
                current: 1,
                isAccess: true
            };
        },
        computed: {
            /*avatorPath () {
             //return localStorage.avatorImgPath;
             }*/
        },
        watch: {
            '$route' (to,from) {
                if(to.name === 'role'){
                    //this.current = 1
                    this.totalPage = 0;
                    this.page_Size = 10;
                    this.tabledata = [];
                    this.getRoles(1);
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getRoles(1);
        },
        methods: {
            getData () {
                this.editColumns = tableData.editColumns;
            },
            getRoles (n){
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/permission/role',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res)
                        _this.roleData = res.data.data;

                        for(var i=0;i<_this.roleData.length;i++){
                            /*if(_this.roleData[i].name === '管理员'){
                                this
                            }*/
                        }
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_Size){
                            _this.showPage = true;
                        }
                        /*for(var i=0;i<_this.editInlineData.length;i++){
                         if(_this.editInlineData[i].role == 1){
                         _this.editInlineData[i].role = '管理员';
                         }else if(_this.editInlineData[i].role == 2){
                         _this.editInlineData[i].role = '操作员';
                         }else if(_this.editInlineData[i].role == 3){
                         _this.editInlineData[i].role = '拼鸡';
                         }
                         }*/
                    }else{

                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            handleNetConnect (state) {
                this.breakConnect = state;
            },
            handleLowSpeed (state) {
                this.lowNetSpeed = state;
            },
            getCurrentData () {
                this.showCurrentTableData = true;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            handleCellChange (val, index, key) {
                this.$Message.success('修改了第 ' + (index + 1) + ' 行列名为 ' + key + ' 的数据');
            },
            handleChange (val, index) {
                this.$Message.success('修改了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
