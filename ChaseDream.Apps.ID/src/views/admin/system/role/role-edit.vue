<style lang="less">
    @import '../../../../styles/common.less';
    @import 'role.less';
</style>

<template>
    <div class="role-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑角色
                </p>
                <Form ref="roleform" :model="roleform" :rules="rules" :label-width="80">
                    <FormItem prop="username" label="角色名称：">
                        <Input v-model="roleform.name"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'role_edit',
        components: {

        },
        data () {
            return {
                roleform: {
                    name: ''
                },
                rules: {
                    name: [
                        { required: true, message: '角色名称不能为空', trigger: 'blur' },
                    ]
                },
                access_token: this.$store.state.user.access_token,
                isAccess: true,
                errMsg: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'role_edit'){
                    this.roleform = this.$store.state.role.currentRow;
                    // console.log(this.userform)
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false;
            }
            this.roleform = this.$store.state.role.currentRow;
            //this.userform = JSON.parse(Cookies.get('currentRow'));
            //console.log(this.userform)
        },
        methods: {
            handleSubmit () {
                this.$refs.roleform.validate((valid) => {
                    this.errMsg = '';
                    if (valid) {
                        var dataIn = {
                            name: this.roleform.name,
                            id: this.roleform.id
                        }
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/permission/role',
                            method:'PUT',
                            data: dataIn,
                        }).then(function (res) {
                            //console.log(res.data);
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'role_edit');
                                _this.$store.commit('closePage', 'role_edit');
                                _this.$router.push({
                                    name: 'role'
                                });
                            }else{
                                _this.$Notice.error({
                                    title: res.data.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.$Notice.error({
                                title: err.msg
                            });
                            //console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.roleform = {}
                this.$store.commit('removeTag', 'role_edit');
                this.$store.commit('closePage', 'role_edit');
                this.$router.push({
                    name: 'role'
                });
            }

        }
    };
</script>

<style>

</style>
