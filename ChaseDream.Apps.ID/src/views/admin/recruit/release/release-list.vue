<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="release-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/recruit/release-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getFormlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>
                    <can-edit-table refs="table" @on-delete="handleDel" v-model="rows" :columns-list="columns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getFormlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/recruit_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'release_list',
        components: {
            canEditTable
        },
        data () {
            return {
                rows: [],
                columns: [],
                s_columns: [],
                s_row: [{
                    id: '',
                    tid: '',
                    subject: '',
                    owner_name: '',
                    form_subject: '',
                    count: ''
                }],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true,
                owners: []
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'release_list'){
                    this.s_row = [{
                        id: '',
                        tid: '',
                        subject: '',
                        owner_name: '',
                        form_subject: '',
                        count: ''
                    }];
                    this.getOwner();
                    this.getFormlist(1);
                }
            }
        },
        mounted () {
            this.getOwner();
            this.getFormlist(1);
        },
        methods: {
            getData () {
                this.columns = tData.rColumns;
                this.s_columns = tData.rsColumns;
            },
            getFormlist (n){
                var s = [];
                var text = '';
                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].tid){
                    s.push('tid=' + this.s_row[0].tid);
                }
                if(!!this.s_row[0].subject){
                    s.push('subject=' + this.s_row[0].subject);
                }
                if(!!this.s_row[0].owner_name){
                    s.push('owner_name=' + this.s_row[0].owner_name);
                }
                if(!!this.s_row[0].form_subject){
                    s.push('form_subject=' + this.s_row[0].form_subject);
                }
                if(!!this.s_row[0].count){
                    s.push('count=' + this.s_row[0].count);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }
                //console.log(test)
                this.rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/thread' + text,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                        for(var i=0;i<_this.rows.length;i++){
                            for(var j=0;j>_this.owners.length;i++){
                                if(_this.rows[i].owner_uid === _this.owners[j].id){
                                    _this.rows[i].owner_name = _this.owners[j].name
                                }
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            getOwner (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/owner',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.owners = res.data.data.rows;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
