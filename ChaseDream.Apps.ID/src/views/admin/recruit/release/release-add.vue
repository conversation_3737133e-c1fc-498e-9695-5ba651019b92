<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="release-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    创建招募帖
                </p>
                <Form ref="form" :model="form"  :label-width="85">
                    <FormItem label="FormId：">
                        <Input v-model="fid" placeholder="请输入FormId" style="width: 200px;"></Input>
                        <Button @click="findKey" type="primary" style="height: 40px;">确定</Button>
                    </FormItem>
                    <FormItem label="标签：">
                        <p class="tags" v-if="!!formtag.val">
                            <Tooltip :content="formtag.val" placement="top">
                                <span class="tag" v-clipboard:copy="'['+ formtag.val +']'" v-clipboard:success="onCopy" v-clipboard:error="onError">{{formtag.name}}</span>
                            </Tooltip>
                        </p>
                    </FormItem>
                    <FormItem prop="type" label="发帖用户：">
                        <RadioGroup v-model="form.sender_uid">
                            <Radio :label="user.id" v-for="user in users" :key="user.index">{{user.nickname}}</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="fid" label="帖子版块：">
                        <Select v-model="form.fid" style="width:200px" @on-change="getType" :label-in-value="true">
                            <OptionGroup :label="item.name" v-for="item in forum_nav" :key="item.index">
                                <Option v-for="opt in item.forums" :value="opt.fid" :key="opt.index">{{ opt.name }}</Option>
                            </OptionGroup>
                        </Select>
                        <Select v-model="form.typeid" style="width:200px">
                            <Option v-for="item in types" :value="item.typeid" :key="item.index">{{ item.name }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="帖子标题：">
                        <Input v-model="form.subject" placeholder="请输入帖子标题"></Input>
                    </FormItem>
                    <FormItem label="HTML：">
                        <Input type="textarea" v-model="form.thread_html" :autosize="{minRows: 4,maxRows: 6}" placeholder=""></Input>
                    </FormItem>
                    <FormItem label="" style="position: relative">
                        <span style="position: relative;z-index: 10;padding-right: 20px;background: #fff;">设置用户回复帖</span>
                        <span class="line" style="position: absolute;left: 0;top: 15px;width: 100%;height: 1px;background: #DDDEE1;padding-left: 84px;z-index: 0;"></span>
                    </FormItem>
                    <FormItem label="表单标签：">
                        <p class="tags">
                            <Tooltip :content="tag.val" placement="top" v-for="tag in tags" :key="tag.index">
                                <span class="tag" v-clipboard:copy="'['+ tag.val +']'" v-clipboard:success="onCopy" v-clipboard:error="onError">{{tag.name}}</span>
                            </Tooltip>
                        </p>
                    </FormItem>
                    <FormItem prop="type" label="招募主理人：">
                        <RadioGroup v-model="form.owner_uid" @on-change="get_owner_name">
                            <Radio :label="owner.id" v-for="owner in owners" :key="owner.index">
                                {{owner.name}}
                                <span style="display: inline-block;padding: 0 5px;background: #E8E9EB;margin-right: 10px;height: 24px;line-height: 24px;border-radius: 2px;cursor: pointer;" v-clipboard:copy="'[admin'+ owner.id +']'" v-clipboard:success="onCopy" v-clipboard:error="onError">[admin{{owner.id}}]</span>
                            </Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="回复内容：（非HTML）">
                        <Input type="textarea" v-model="form.owner_html" :autosize="{minRows: 4,maxRows: 6}" placeholder=""></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">发布</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'release_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    sender_uid: 0,
                    subject: '',
                    thread_html: '',
                    owner_uid: 0,
                    owner_html: '',
                    fid: 0,
                    typeid: 0,
                    forum: ''
                },
                fid: '',
                formtag: {},
                tags: [],
                users: [],
                owners: [],
                forum_nav: [],
                types: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'release_add'){
                    this.form.sender_uid = 0;
                    this.form.subject = '';
                    this.form.thread_html = '';
                    this.form.owner_uid = 0;
                    this.form.owner_html = '';
                    this.form.fid = 0;
                    this.form.typeid = 0;
                    this.form.forum = '';
                    this.fid = '';
                    this.formtag = {};
                    this.tags = [];
                    this.users = [];
                    this.owners = [];
                    this.forum_nav = [];
                    this.types = [];
                    this.$refs['form'].resetFields();
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getData();
        },
        methods: {
            getData(){
                this.getUsers();
                this.getOwners();
                this.getForum_nav();
            },
            findKey(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/form/' + this.fid,
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        //_this.form = res.data.data;
                        _this.tags = [];
                        _this.formtag = {};
                        _this.formtag.name = 'Form' + _this.fid;
                        _this.formtag.val = 'Form' + _this.fid;
                       // _this.tags.push({name:'Form' + _this.fid,val:'Form' + _this.fid});
                        if(!!res.data.data.wechat){
                            var item = {
                                name: '微信号',
                                val: 'wechat'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.qq){
                            var item = {
                                name: 'QQ号',
                                val: 'qq'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.test_date){
                            var item = {
                                name: '考试日期',
                                val: 'test_date'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.test_plan){
                            var item = {
                                name: '考试计划',
                                val: 'test_plan'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.city){
                            var item = {
                                name: '城市',
                                val: 'city'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.status){
                            var item = {
                                name: '当前情况',
                                val: 'status'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.ps){
                            var item = {
                                name: '附言',
                                val: 'ps'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.bachelor){
                            var item = {
                                name: '本科学校',
                                val: 'bachelor'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.industry){
                            var item = {
                                name: '工作行业',
                                val: 'industry'
                            }
                            _this.tags.push(item);
                        }
                        if(!!res.data.data.appeal){
                            var item = {
                                name: '目前诉求',
                                val: 'appeal'
                            }
                            _this.tags.push(item);
                        }
                        _this.tags.push({name: '昵称',val: 'username'});
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getUsers (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/user',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.users = res.data.data.rows;

                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getOwners (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/owner',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.owners = res.data.data.rows;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {
                var dataIn = {
                    subject: this.form.subject,
                    sender_uid: this.form.sender_uid,
                    thread_html: this.form.thread_html,
                    owner_uid: this.form.owner_uid,
                    owner_name: this.form.owner_name,
                    owner_html: this.form.owner_html,
                    fid: this.form.fid
                }
                if(!!this.form.typeid){
                    dataIn.typeid = this.form.typeid;
                }
                if(!dataIn.sender_uid){
                    _this.$Notice.error({
                        title: '请选择发帖用户！'
                    });
                    return false;
                }
                if(!dataIn.fid){
                    _this.$Notice.error({
                        title: '请选择帖子版块！'
                    });
                    return false;
                }
                if(!dataIn.subject){
                    _this.$Notice.error({
                        title: '帖子标题不能为空！'
                    });
                    return false;
                }
                if(!dataIn.thread_html){
                    _this.$Notice.error({
                        title: '帖子内容不能为空！'
                    });
                    return false;
                }
                if(!dataIn.owner_uid){
                    _this.$Notice.error({
                        title: '请选择招募主理人！'
                    });
                    return false;
                }
                if(!dataIn.owner_html){
                    _this.$Notice.error({
                        title: '回复内容不能为空！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/thread',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'release_add');
                        _this.$store.commit('closePage', 'release_add');
                        _this.$router.push({
                            name: 'release_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            get_owner_name(e){
                for(var i=0;i<this.owners.length;i++){
                    if(this.owners[i].id === e){
                        this.form.owner_name = this.owners[i].name
                    }
                }
                console.log(this.form.owner_name)
            },
            onCopy (e){
                this.$Message.success({
                    top: 200,
                    content: '复制成功！'
                });
            },
            onError (){
                this.$Message.error({
                    top: 200,
                    content: '复制失败！'
                });
            },
            getForum_nav (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/forum_nav',
                    method:'GET',
                    params: {
                        page: _this.current,
                        pageSize: _this.pageSize
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.forum_nav = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            getType (e){
                /*this.threadform.typeid = '';
                 for(var i=0;i<this.forum_nav.length;i++){
                 for(var j=0;j<this.forum_nav[i].forums.length;j++){
                 if(this.forum_nav[i].forums[j].fid === e){
                 this.types = this.forum_nav[i].forums[j].threadtypes;
                 }
                 }

                 }*/
                this.form.forum = e.label;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/thread/forum_nav_sub',
                    method:'GET',
                    params: {
                        fid: e.value
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.types = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            closePage () {
                this.form.sender_uid = 0;
                this.form.subject = '';
                this.form.thread_html = '';
                this.form.owner_uid = 0;
                this.form.owner_html = '';
                this.fid = '';
                this.formtag = {};
                this.tags = [];
                this.users = [];
                this.owners = [];
                this.$store.commit('removeTag', 'release_add');
                this.$router.push({
                    name: 'release_list'
                });
            }
        }
    };
</script>

<style>

</style>
