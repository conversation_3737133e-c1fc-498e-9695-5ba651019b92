.verticalChx{
  .ivu-checkbox-wrapper{
    display: block;
  }
}
.pr_form{
  .ivu-card-body{
    padding-right: 480px;
    position: relative;
    min-height: 800px;

    .preview{
      width: 460px;
      position: absolute;
      right: 10px;
      top: 16px;
      padding: 10px;
      background: #F5F7FC;

      h3{
        font-size: 14px;
        margin-bottom: 20px;
        font-weight: normal;
        /*margin-top: 10px;*/
      }
      .ivu-form-item{
        margin-bottom: 10px;
      }
      .ivu-form-label-top .ivu-form-item-label{
        padding: 0;
      }
      .ivu-checkbox-wrapper{
        margin-right: 5px;
      }
      .ivu-input-wrapper,.ivu-select{
        width: 300px;
      }
    }
  }
}
.file-list{
  div{
    width: 120px;
    height:120px;
    position: relative;

    img{
      width: 120px;
      height:120px;
      border: 1px solid #e9ebf0;
    }
    .del{
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0px;
      background: rgba(225,225,225,.5);
      color: red;
      display: none;

      .ivu-icon{
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: -11px;
        margin-left: -8px;
        cursor: pointer;
      }
    }
    &:hover .del{
      display: block;
    }
  }
}
.main .ivu-btn.b_plus{
  width:120px;
  height: 120px;
  border: 1px dashed #DDDEE1;
  color: #DDDEE1;
  font-size: 80px;
  padding: 0;
}
.btns .ivu-poptip{
  vertical-align: middle;
  float: right;
}
p.tags{
  span.tag{
    display: inline-block;
    padding: 0 5px;
    background: #E8E9EB;
    margin-right: 10px;
    height: 24px;
    line-height: 24px;
    border-radius: 2px;
    cursor: pointer;
  }
}

.detail-list{
  .edittable-detail{
    .ivu-table-cell{
      padding-left: 8px;
      padding-right: 8px;
    }
  }
}
.s_p_t{
  margin-top: 10px;
  .ivu-table-cell{
    padding-left: 8px;
    padding-right: 8px;
    span{
      line-height: 20px;
      display: inline-block;
    }
  }
}

.attach_upload{
  background: #F0F1F5;
  padding: 0 10px;
  border: 1px solid #F0F1F5;
  .ivu-upload-select{
    .ivu-btn{
      padding-left: 0;
      color: #495060;
      box-shadow: none;
      &:hover{
        color: #495060;
      }
    }
    .ivu-icon-link{
      font-size: 20px;
      color: rgb(170, 171, 173);
      transform: rotate(-45deg);
      -moz-transform:rotate(-45deg);
      -webkit-transform:rotate(-45deg);
      filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
      padding-right: 10px;
    }
  }
  .file-list div{
    width: 100%;
    height: auto;
    .del{
      position: inherit;
      background: none;
      display: inline-block;
      width: auto;
      margin-left: 20px;
      color: #AAABAD;
    }
  }
}

.ivu-table{
  .row_green td{
    background: #edfbf5;//#ECF0D1;//#D0D98B;s
  }
  .row_grey td{
    background: #f7f7f7;//#EDEDEC;//#D2D1D0;
    color: #ccc;
    //.ivu-poptip-rel span
  }
  .row_red td{
    background: #f9f0f6; //#F1D9E9;//#E8BFDB;
  }
  .row_purple td{
    background: #ffeef0;//#EECFD3;//#BA3745;
  }
  .row_orange td{
    background: #dff8ff;//#edf8fb;//#E0F3F3;//#9DD9D9;
  }
  .row_l_grey td{
    background: #ffede1;//#FCECCD;//#F2AE30;
  }
}
.listTable{
  .ivu-table-wrapper{
    border-top: none;
  }
  .ivu-table-header{
    display: none;
  }
}
.detail-list .searchTable{
  .ivu-select-dropdown{
    max-height: 260px;
  }

}
.main {
  .searchTable .ivu-table{
    td{
      height: 50px;
      background-color: #f2f3f6;

      input.ivu-input{
        height:28px;
        line-height: 28px;
      }
    }
    .ivu-table-cell{
      padding-left: 6px;
      padding-right: 6px;
    }
    .ivu-select-single{
      .ivu-select-selection,.ivu-select-placeholder{
        height:28px;
        line-height: 28px;
        border-radius: 0;
      }
      .ivu-select-selected-value{
        height:28px;
        line-height: 28px;
      }
    }
  }
}

.row_details{
  p{
    display: flex;
    font-size: 14px;

    strong{
      width: 20%;
      text-align: right;
      padding-right:20px;
    }
    span{
      width: 80%;
      word-break: break-word;
      position: relative;

      textarea{
        width: 100%;
        position: absolute;
        left: 0;
        top:0;
        bottom:0;
        resize: none;
        border:none;
        box-shadow: none;
        /*background: #000;
        color: #fff;*/
      }
    }
  }
  .ivu-modal{
    width: 1000px !important;
  }
}
.release-add .ivu-form .ivu-form-item-label,.release-edit .ivu-form .ivu-form-item-label{
  line-height: 24px;
}



