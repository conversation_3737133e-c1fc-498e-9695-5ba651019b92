export const bColumns = [
    {
        title: 'ID',
        align: 'center',
        width: 80,
        key: 'id',
        render: (h, params) => {
            return h('div', {}, [
                h('p', {}, params.row.id),
                h('p', {}, [h('a', {
                    attrs: {
                        href: `https://forum.chasedream.com/admin.php?frames=yes&action=mynav&operation=stealhandle&uids=${params.row.uid}&usernames=${params.row.username}`,
                        target: '_blank'
                    }
                }, '盗')])
            ])
        }
    },
    {
        title: 'UID/用户名',
        key: 'uid',
        width: 100,
        align: 'center',
        render: (h, params) => {
            return h('div', {}, [
                h('p', {}, [h('a', {
                    attrs: {
                        href: `https://forum.chasedream.com/?${params.row.uid}`,
                        target: '_blank'
                    }
                }, params.row.uid)]),

                params.row.isAdmin ? h('p', {}, [h('a', {
                    attrs: {
                        href: `https://forum.chasedream.com/admin.php?frames=yes&action=members&operation=edit&uid=${params.row.uid}`,
                        target: '_blank'
                    }
                }, params.row.username)]) : h('p', {}, params.row.username)
            ])
        }
    },
    {
        title: 'Email',
        align: 'left',
        width: 110,
        key: 'email',
        render: (h, params) => {
            let email = decodeURIComponent(params.row.email)
            const arr = email.split('@')
            const prefix = arr[0].substring(0, 3)

            return params.row.isAdmin && !params.row.forceHidden ? h('p', {}, email) : h('p', {}, `${prefix}***@${arr[1]}`)
        }
    },
    {
        title: '手机',
        align: 'center',
        key: 'mobile',
        width: 110,
        render: (h, params) => {
            let mobile = params.row.mobile.toString()
            if (mobile.length < 6) return mobile
            const middle = mobile.length / 2

            mobile = `${mobile.substring(0, middle - 2)}****${mobile.substring(middle + 2, mobile.length)}`
            return h('div', {}, [
                h('p', {}, params.row.areacode),
                h('p', {}, params.row.isAdmin && !params.row.forceHidden ? params.row.mobile : mobile)
            ])
        }
    },
    {
        title: '注册/最后访问IP',
        align: 'center',
        key: 'ip',
        width: 110,
        handle: ['ipText']
    },
    {
        title: '浏览器信息',
        align: 'left',
        key: 'browserinfo',
    },
    {
        title: '注册/最后访问时间',
        align: 'center',
        key: 'dateline',
        width: 120,
        render: (h, params) => {
            var y = new Date(parseInt(params.row.regdateline + '000')).getFullYear();
            var M = new Date(parseInt(params.row.regdateline + '000')).getMonth() + 1;
            var d = new Date(parseInt(params.row.regdateline + '000')).getDate();
            var hs = new Date(parseInt(params.row.regdateline + '000')).getHours();
            var m = new Date(parseInt(params.row.regdateline + '000')).getMinutes();
            var s = new Date(parseInt(params.row.regdateline + '000')).getSeconds();
            M = M.toString().length > 1 ? M : ('0' + M);
            d = d.toString().length > 1 ? d : ('0' + d);
            hs = hs.toString().length > 1 ? hs : ('0' + hs);
            m = m.toString().length > 1 ? m : ('0' + m);
            s = s.toString().length > 1 ? s : ('0' + s);

            var regdateline = y + '-' + M + '-' + d + ' ' + hs + ':' + m

            y = new Date(parseInt(params.row.dateline + '000')).getFullYear();
            M = new Date(parseInt(params.row.dateline + '000')).getMonth() + 1;
            d = new Date(parseInt(params.row.dateline + '000')).getDate();
            hs = new Date(parseInt(params.row.dateline + '000')).getHours();
            m = new Date(parseInt(params.row.dateline + '000')).getMinutes();
            s = new Date(parseInt(params.row.dateline + '000')).getSeconds();
            M = M.toString().length > 1 ? M : ('0' + M);
            d = d.toString().length > 1 ? d : ('0' + d);
            hs = hs.toString().length > 1 ? hs : ('0' + hs);
            m = m.toString().length > 1 ? m : ('0' + m);
            s = s.toString().length > 1 ? s : ('0' + s);

            var dateline = y + '-' + M + '-' + d + ' ' + hs + ':' + m

            return h('div', {}, [
                h('p', {}, regdateline),
                h('p', {}, dateline)
            ])
        }
    },
];

export const bsColumns = [
    {
        title: 'ID',
        align: 'center',
        width: 80,
        key: 'handle',
        handle: ['id']
    },
    {
        title: 'UID/用户名',
        key: 'handle',
        width: 100,
        align: 'center',
        handle: ['uid']
    },
    {
        title: 'Email',
        align: 'center',
        width: 110,
        key: 'handle',
        handle: ['email']
    },
    {
        title: '手机',
        align: 'center',
        key: 'handle',
        width: 110,
        handle: ['mobile']
    },
    {
        title: '注册/最后访问IP',
        align: 'center',
        key: 'handle',
        width: 110,
        handle: ['ip']
    },
    {
        title: '浏览器信息',
        align: 'left',
        key: 'handle',
        handle: ['browserinfo']
    },
    {
        title: '注册/最后访问时间',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['dateline']
    },
];

const tData = {
    bColumns: bColumns,
    bsColumns: bsColumns
};

export default tData;
