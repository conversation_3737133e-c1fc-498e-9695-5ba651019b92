import util from '@/libs/util.js';
export const fColumns = [
    {
        title: '表单id',
        align: 'center',
        width: 90,
        key: 'id'
    },
    {
        title: '表单名',
        align: 'left',
        key: 'subject'
    },
    {
        title: '表单类型',
        align: 'center',
        width: 100,
        key: 'type',
        render: (h, params) => {
            var typeStr = '';
            if (params.row.type === 0) {
                typeStr = 'GMAT'
            } else if (params.row.type === 7) {
                typeStr = 'GRE'
            } else if (params.row.type === 1) {
                typeStr = 'TOFEL'
            } else if (params.row.type === 2) {
                typeStr = 'IELTS'
            } else if (params.row.type === 3) {
                typeStr = 'MBA'
            } else if (params.row.type === 4) {
                typeStr = 'Master'
            } else if (params.row.type === 5) {
                typeStr = 'Career'
            } else if (params.row.type === 6) {
                typeStr = '常规'
            }
            return h('span', {}, typeStr)
        }
    },
    {
        title: '收集表单元素',
        align: 'left',
        width: 180,
        key: 'elements',
        render: (h, params) => {
            return h('span', {}, params.row.elements.join('、'))
        }
    },
    {
        title: '已收集记录',
        align: 'center',
        width: 100,
        key: 'total',
        render: (h, params) => {
            //return h('span', {},params.row.total+'('+ params.row.new +')')
            return h('router-link', {
                /* attrs: {
                     href: '/console/recruit/details-list?id=' + params.row.id
                 },*/
                props: {
                    to: '/console/recruit/details-list?form_id=' + params.row.id
                },
                style: {

                }
            }, [
                h('span', {
                    style: {
                    }
                }, params.row.total),
                h('span', {
                    attrs: {
                        //src: '../../../../images/release/o-line.png'
                        //class: 'o-line'
                    },
                    style: {
                        color: '#D93434'
                    }
                }, params.row.new > 0 ? '(' + params.row.new + ')' : '')
            ])
        }
    },
    {
        title: '创建时间',
        align: 'center',
        width: 150,
        key: 'created_at',
        render: (h, params) => {
            return h('span', {}, params.row.created_at.split('T').join(' ').split('.')[0])
        }
    },
    {
        title: '编辑',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['edit']  //,'delete'
    }
];
export const fsColumns = [
    {
        title: '表单id',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['id']
    },
    {
        title: '表单名',
        align: 'left',
        key: 'handle',
        handle: ['subject']
    },
    {
        title: '表单类型',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['type']
    },
    {
        title: '收集表单元素',
        align: 'left',
        width: 180,
        key: ''//elements
    },
    {
        title: '已收集记录',
        align: 'center',
        width: 100,
        key: ''//total
    },
    {
        title: '创建时间',
        align: 'center',
        width: 150,
        /*key: 'handle',
        handle: ['created']*/
    },
    {
        title: '编辑',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['clear']
    }
];
export const rColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 60,
        align: 'center'
    },
    {
        title: 'TID',
        align: 'center',
        width: 90,
        key: 'tid'
        /*render: (h, params) => {
            return h('div', {
            }, [
                h('p',{},params.row.location),
                h('p',{},params.row.event_begin_date)
            ])
        }*/
    },
    {
        title: '帖子标题',
        align: 'left',
        key: 'subject',
        render: (h, params) => {
            if (!!params.row.tid && params.row.tid !== 'null') {
                return h('a', {
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.subject)
            } else {
                return h('span', {}, params.row.subject)
            }

        }
    },
    {
        title: '主理人',
        align: 'center',
        key: 'owner_name',
        width: 120
    },
    {
        title: '表单名',
        align: 'center',
        key: 'form_subject',
        width: 180
    },
    {
        title: '表单收集数',
        align: 'center',
        width: 100,
        key: 'count',
        render: (h, params) => {
            return h('router-link', {
                props: {
                    to: '/console/recruit/details-list?tid=' + params.row.tid
                },
            }, [
                h('span', {}, params.row.count)
            ])
        }
    },
    {
        title: '编辑',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];
export const rsColumns = [
    {
        title: 'ID',
        width: 60,
        align: 'center',
        key: 'handle',
        handle: ['id']
    },
    {
        title: 'TID',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['tid']
    },
    {
        title: '帖子标题',
        align: 'left',
        key: 'handle',
        handle: ['subject']
    },
    {
        title: '主理人',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['owner_name']
    },
    {
        title: '表单名',
        align: 'center',
        key: 'handle',
        width: 180,
        handle: ['form_subject']
    },
    {
        title: '表单收集数',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['count']
    },
    {
        title: '编辑',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['clear']
    }
];
export const uColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'id',
        align: 'center'
    },
    {
        title: '账号',
        align: 'left',
        key: 'username',
    },
    {
        title: '昵称',
        align: 'center',
        width: 180,
        key: 'nickname',
    },
    /*{
        title: '密码',
        align: 'center',
        width: 180,
        key: 'password',
        handle: ['view']
    },*/
    /*{
        title: '状态',
        align: 'center',
        width: 100,
        key: 'status',
        render: (h, params) => {
            var style = {}
            if(!params.row.status){
                style.color = '#ed3f14';
            }else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size:12
                }
            })
        }
    },*/
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']
    },
];
export const usColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'handle',
        align: 'center',
        handle: ['id']
    },
    {
        title: '账号',
        align: 'left',
        key: 'handle',
        handle: ['username']
    },
    {
        title: '昵称',
        align: 'center',
        width: 180,
        key: 'handle',
        handle: ['nickname']
    },
    {
        title: '密码',
        align: 'center',
        width: 180,
        key: 'password'
    },
    /*{
     title: '状态',
     align: 'center',
     width: 100,
     key: 'status',
     render: (h, params) => {
     var style = {}
     if(!params.row.status){
     style.color = '#ed3f14';
     }else {
     style.color = '#19be6b';
     }
     return h('Icon', {

     style: style,
     props: {
     type: 'record',
     size:12
     }
     })
     }
     },*/
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['clear']
    },
];
export const oColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'id',
        align: 'center'
    },
    {
        title: '名称',
        align: 'left',
        key: 'name',
        width: 200
        /*render: (h, params) => {
            return h('a',{
                attrs: {
                    href: params.row.url1,
                    target: '_blank'
                }
            },params.row.subject)
        }*/
    },
    {
        title: '类型',
        align: 'center',
        width: 100,
        key: 'type',
        render: (h, params) => {
            var type = '微信';
            if (params.row.type === 1) {
                type = 'QQ';
            } else if (params.row.type === 2) {
                type = '企业微信';
            }
            return h('span', {}, type)
        }
    },
    {
        title: '账号',
        align: 'center',
        width: 150,
        key: 'account'
    },
    {
        title: '二维码',
        align: 'center',
        key: 'qr_code',
        handle: ['image']
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['edit']  // ,'delete'
    }
];
export const osColumns = [
    {
        title: 'ID',
        width: 60,
        key: 'handle',
        align: 'center',
        handle: ['id']

    },
    {
        title: '名称',
        align: 'left',
        key: 'handle',
        width: 200,
        handle: ['name']
    },
    {
        title: '类型',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['type']
    },
    {
        title: '账号',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['account']
    },
    {
        title: '二维码',
        align: 'center',
        /*key: 'handle',
        handle: ['qr_code'],*/
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['clear']
    }
];
export const dColumns = [
    /*{
        title: 'ID',
        align: 'center',
        key: 'id',
        width: 40,
    },*/
    {
        title: 'ID',
        align: 'center',
        key: 'handle',
        width: 55,
        handle: ['id_text']
        /*render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },'[ ' + params.row.form_id + ' ]'),
                h('p',{},params.row.id)
            ])
        }*/
    },
    {
        title: '表单名',
        align: 'left',
        key: 'form_subject',
        width: 120,
    },
    {
        title: '表单类型',
        align: 'center',
        width: 65,
        key: 'type_name'
    },
    {
        title: '论坛用户名',
        align: 'left',
        width: 120,
        key: 'handle',
        handle: ['username_text']
        //render: (h, params) => {
        /*if(params.row.behavior.length > 0){
            return h('Poptip',{
                props: {
                    transfer: true,
                    placement: 'bottom-end',
                    width: '260'
                }
            },[
                h('p',{
                    style:{
                        background: '#F78686'
                    }
                },params.row.username),
                h('div',{
                        slot: 'content'
                    },
                    params.row.behavior.map((item,index) => {
                        item.opt_date = item.created_at.split('T').join(' ').split(':')
                        item.opt_date.pop();
                        return h('div',{
                            style: {
                                borderBottom: index !== params.row.behavior.length -1 ? '1px solid #E9EAEC' : 'none',
                                padding: '8px 0'
                            }
                        },[
                            h('p',{
                                style: {
                                    lineHeight: '20px'
                                }
                            },[
                                h('span',{
                                    style: {
                                        color: '#F44242'
                                    }
                                }, item.record_code + ' : ' + item.record_type),
                                h('span',{
                                    style:{
                                        float: 'right',
                                        color: '#999'
                                    }
                                }, item.opt_date.join(':'))
                            ]),
                            h('p',{
                                style: {
                                    lineHeight: '20px',
                                    whiteSpace: 'initial'
                                }
                            },item.desc)
                        ]);
                    })
                )
            ])

        }else{
            return h('span',{},params.row.username)
        }*/
        /*if(params.row.behavior.length > 0){

            return h('Poptip',{
                props: {
                    transfer: true,
                    placement: 'bottom-end',
                    width: '260'
                }
            },[
                h('p',{
                    style:{
                        background: '#F78686'
                    }
                },params.row.username),
                h('div',{
                        slot: 'content'
                    },
                    params.row.behavior.map((item,index) => {
                        item.opt_date = item.created_at.split('T').join(' ').split(':')
                        item.opt_date.pop();
                        return h('div',{
                            style: {
                                borderBottom: index !== params.row.behavior.length -1 ? '1px solid #E9EAEC' : 'none',
                                padding: '8px 0'
                            }
                        },[
                            h('p',{
                                style: {
                                    lineHeight: '20px'
                                }
                            },[
                                h('span',{
                                    style: {
                                        color: '#F44242'
                                    }
                                }, item.record_code + ' : ' + item.record_type),
                                h('span',{
                                    style:{
                                        float: 'right',
                                        color: '#999'
                                    }
                                }, item.opt_date.join(':'))
                            ]),
                            h('p',{
                                style: {
                                    lineHeight: '20px',
                                    whiteSpace: 'initial'
                                }
                            },item.desc)
                        ]);
                    })
                )
            ])

        }else{
            return h('span',{},params.row.username)
        }*/
        /*var time = '';
        var thread = 0;
        var reply = 0;
        var friend = 0;
        if(!!params.row.forum_data){
            var t = params.row.forum_data.split('|');
            time = t[0];
            thread = t[1];
            reply = t[2];
            friend = t[3];
        }

        return h('div',{},[
            h('a',{
                attrs: {
                    href: 'https://forum.chasedream.com/space-username-'+ params.row.username +'.html',
                    target: '_blank'
                }
            },params.row.username),
            h('p',{},!!params.row.forum_data ? time : ''),
            h('div',{},[
                h('a',{
                    attrs: {
                        href: 'https://forum.chasedream.com/home.php?mod=space&uid='+ params.row.uid +'&do=thread&view=me&type=thread&from=space',
                        target: '_blank'
                    },
                    style: {
                        display: 'inline-block',
                        marginRight: '5px'
                    }
                }, !!params.row.forum_data ? '主 ' + thread : ''),
                h('a',{
                    attrs: {
                        href: 'https://forum.chasedream.com/home.php?mod=space&uid='+ params.row.uid +'&do=thread&view=me&type=reply&from=space',
                        target: '_blank'
                    },
                    style: {
                        display: 'inline-block',
                        marginRight: '5px'
                    }
                }, !!params.row.forum_data ? ' 回 ' + reply : ''),
                h('a',{
                    attrs: {
                        href: 'https://forum.chasedream.com/home.php?mod=space&uid='+ params.row.uid +'&do=friend&view=me&from=space',
                        target: '_blank'
                    },
                    style: {
                        display: 'inline-block',
                        marginRight: '5px'
                    }
                },!!params.row.forum_data ? ' 友 ' + friend : ''),
            ]),
        ])*/
        //}
    },
    {
        title: '微信号/QQ号码',
        align: 'center',
        width: 110,
        key: 'handle',
        handle: ['wechat_text']
        /*render: (h, params) => {
            return h('div',{},[
                h('div',{
                    style:{
                        cursor: 'pointer',
                        color: '#19be6b'
                    },
                    domProps: {
                        innerHTML: '<span class="copyText" data-clipboard-action="copy" test="'+ params.row.wechat +'">'+ params.row.wechat +'</span>'
                    }
                }),
                h('div',{
                    style:{
                        cursor: 'pointer'
                    },
                    domProps: {
                        innerHTML: '<span class="copyText" data-clipboard-action="copy" test="'+ params.row.qq +'">'+ params.row.qq +'</span>'
                    }
                })
            ]);
            /!*return h('div',{
                style:{
                    cursor: 'pointer'
                },
                domProps: {
                    innerHTML: '<span class="copyText" data-clipboard-action="copy" test="'+ params.row.wechat +'">'+ params.row.wechat +'</span>'
                }
            })*!/
        }*/
    },
    /*{
        title: 'QQ号码',
        align: 'center',
        width: 85,
        key: 'qq',
        render: (h, params) => {
            return h('div',{
                style:{
                    cursor: 'pointer'
                },
                domProps: {
                    innerHTML: '<span class="copyText" data-clipboard-action="copy" test="'+ params.row.qq +'">'+ params.row.qq +'</span>'
                }
            })
        }
    },*/
    {
        title: '手机号码',
        align: 'left',
        width: 95,
        key: 'handle',
        handle: ['mobile_text']
        /*render: (h, params) => {
            var area_code = params.row.mobile.split('-')[0];
            var mobile_num = params.row.mobile.split('-')[1];
            return h('div',{},[
                h('p',{
                    style: {
                        position: 'relative'
                    }
                },[
                    h('span',{},area_code),
                    h('a',{
                        attrs: {
                            href: 'https://www.baidu.com/s?wd=' + mobile_num,
                            target: '_blank'
                        }
                    },[
                        h('Icon',{
                            style:{
                                color: '#1f85f4',
                                transform: 'rotate(-45deg)',
                                webkitTransform: 'rotate(-45deg)',
                                mozTransform: 'rotate(-45deg)',
                                filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=3)',
                                verticalAlign: 'middle',
                                position: 'absolute',
                                right: 0,
                                top: 0
                            },
                            props: {
                                type: 'link',
                                size:18
                            }
                        })
                    ])
                ]),
                h('p',{
                    style:{
                        cursor: 'pointer'
                    },
                    domProps: {
                        innerHTML: '<span class="copyText" data-clipboard-action="copy" test="'+ mobile_num +'">'+ mobile_num +'</span>'
                    }
                }), //mobile_num
            ])
        }*/
    },
    {
        title: '城市',
        align: 'center',
        width: 70,
        key: 'city',
        render: (h, params) => {
            var location = params.row.city.split('-')
            if (location.length > 2) {
                return h('div', {}, [
                    h('p', {}, location[0]),
                    h('p', {}, location[1]),
                    h('p', {}, location[2]),
                ])
            } else {
                return h('div', {}, [
                    h('p', {}, location[0]),
                    h('p', {}, location[1])
                ])
            }

        }
    },
    {
        title: '当前情况',
        align: 'left',
        width: 120,
        key: 'status',
        render: (h, params) => {
            var text = [];
            var status1 = params.row.status1.split(', ').join(',').split(',');
            var status4 = params.row.status4.split(', ').join(',').split(',');
            return h('div', {
                style: {
                    paddingTop: '7px',
                    paddingBottom: '7px'
                }
            }, [
                h('div', {}, [
                    status1.map((item) => {
                        return h('p', {}, item)
                    })
                ]),
                h('p', {}, !!params.row.status2 ? params.row.status2 : ''),
                h('div', {}, [
                    status4.map((item) => {
                        return h('p', {}, item)
                    })
                ]),
            ])
        }
    },
    {
        title: '附言',
        align: 'center',
        width: 100,
        key: 'ps',
        render: (h, params) => {
            return h('p', {
                style: {
                    maxHeight: '36px',
                    overflow: 'hidden',
                    cursor: 'pointer'
                },
                attrs: {
                    title: params.row.ps
                }
                //title:
            }, params.row.ps)
        }
    },
    {
        title: '备注',
        align: 'center',
        /*width: 100,*/
        /*minWidth: 100,*/
        key: 'note',
        /*render: (h, params) => {
            return h('p',{
                attrs:{
                    title: currentRow.note
                },
                style: {
                    maxheight: '36px',
                    overflow: 'hidden',
                    cursor: 'pointer'
                }
            },params.row.note)
        }*/
        key: 'handle',
        handle: ['note_edit']
    },
    {
        title: '提交IP/地址',
        align: 'center',
        width: 110,
        key: 'handle',
        handle: ['ip_text']
        /*render: (h, params) => {
            return h('div',{
                style:{

                }
            },[
                h('p',{
                    style:{
                        cursor: 'pointer'
                    },
                    domProps: {
                        innerHTML: '<span class="copyText" data-clipboard-action="copy" test="'+ params.row.ip +'">'+ params.row.ip +'</span>'
                    }

                },params.row.ip),
                h('p',{
                    style:{

                    }
                },!!params.row.ip_location ? params.row.ip_location : '')
            ])
        }*/
    },
    {
        title: '提交时间',
        align: 'left',
        width: 50,
        key: 'created_at',
        render: (h, params) => {
            //var test = params.row.created_at.split('T')[0] + ' ' + params.row.created_at.split('T')[1].split(':')[0] + ':' + params.row.created_at.split('T')[1].split(':')[1];
            var t = params.row.created_at.split('T')[0];
            return h('div', {}, [
                h('p', {}, t.split('-')[0]),
                h('p', {}, t.split('-')[1] + '-' + t.split('-')[2]),
                h('p', {}, params.row.created_at.split('T')[1].split(':')[0] + ':' + params.row.created_at.split('T')[1].split(':')[1])
            ])
        }
    },
    {
        title: '处理操作',
        align: 'left',
        width: 125,
        key: 'handle',
        handle: ['operate']
    }
];
export const dsColumns = [
    /*{
        title: 'ID',
        align: 'center',
        key: 'handle',
        width: 40,
        handle: ['id']
    },*/
    {
        title: 'ID',
        align: 'center',
        key: 'handle',
        width: 55,
        handle: ['form_id']
    },
    {
        title: '表单名',
        align: 'left',
        width: 120,
        key: 'handle',
        handle: ['form_subject']
    },
    {
        title: '表单类型',
        align: 'center',
        width: 65,
        key: 'handle',
        handle: ['type']
    },
    {
        title: '论坛用户名',
        align: 'left',
        width: 120,
        key: 'handle',
        handle: ['username']
    },
    {
        title: '微信号/QQ号码',
        align: 'center',
        width: 110,
        key: 'handle',
        handle: ['wechat']
    },
    /*{
        title: 'QQ号码',
        align: 'center',
        width: 85,
        key: 'handle',
        handle: ['qq']
    },*/
    {
        title: '手机号码',
        align: 'center',
        width: 95,
        key: 'handle',
        handle: ['mobile']
    },
    {
        title: '城市',
        align: 'center',
        width: 70,
        key: 'handle',
        handle: ['city']
    },
    {
        title: '当前情况',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['status']
    },
    {
        title: '附言',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['ps']
    },
    {
        title: '备注',
        align: 'center',
        /*width: 100,*/
        /* minWidth: 100,*/
        key: 'handle',
        handle: ['note']
    },
    {
        title: '提交IP/地址',
        align: 'center',
        width: 110,
        key: 'handle',
        handle: ['ip']
    },
    {
        title: '提交时间',
        align: 'left',
        width: 50,
        /*key: 'handle',
        handle: ['created']*/
    },
    {
        title: '处理操作',
        align: 'left',
        width: 125,
        key: 'handle',
        handle: ['opt', 'clear']
    }
];
export const bColumns = [
    {
        title: 'UID',
        key: 'id',
        width: 40,
        align: 'center'
    },
    {
        title: '昵称',
        align: 'left',
        key: 'username',
        width: 110
    },
    {
        title: '电话',
        align: 'center',
        key: 'mobile',
        width: 110
    },
    {
        title: 'Email',
        align: 'left',
        width: 110,
        key: 'email'
    },
    {
        title: '分类',
        align: 'center',
        width: 95,
        key: 'record_type'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'opt',
        render: (h, params) => {
            var opt_text = []
            for (var i = 0; i < params.row.opt.length; i++) {
                opt_text.push(params.row.opt[i].opt_text)
            }
            return h('span', {}, opt_text.join('、'))
        }
    },
    {
        title: '描述',
        align: 'left',
        key: 'desc',
        render: (h, params) => {
            return h('div', {
            }, [
                h('span', {
                    style: {
                        //color: '#1f85f4'
                    }
                }, params.row.desc),
                h('a', {
                    style: {
                        display: !!params.row.attach ? 'inline-block' : 'none'
                    },
                    attrs: {
                        href: params.row.attach,
                        target: '_blank'
                    }
                }, [
                    h('Icon', {
                        style: {
                            color: '#1f85f4',
                            transform: 'rotate(-45deg)',
                            webkitTransform: 'rotate(-45deg)',
                            mozTransform: 'rotate(-45deg)',
                            filter: 'progid:DXImageTransform.Microsoft.BasicImage(rotation=3)',
                            marginLeft: '5px',
                            verticalAlign: 'middle',
                        },
                        props: {
                            type: 'link',
                            size: 18
                        }
                    })
                ])
            ])

        }
    },
    {
        title: '处理时间',
        align: 'left',
        key: 'created_at',
        width: 80,
        render: (h, params) => {
            var text = params.row.created_at.split('T').join(' ').split(':')
            text.pop();
            return h('span', {}, text.join(':'))
        }
    },
    {
        title: '操作人',
        align: 'center',
        key: 'operator',
        width: 80
    },
    {
        title: '编辑',
        align: 'center',
        width: 50,
        key: 'handle',
        handle: ['edit']
    }
];
export const bsColumns = [
    {
        title: 'UID',
        key: 'handle',
        width: 40,
        align: 'center',
        handle: ['id']
    },
    {
        title: '昵称',
        align: 'left',
        key: 'handle',
        width: 110,
        handle: ['username']
    },
    {
        title: '电话',
        align: 'center',
        key: 'handle',
        width: 110,
        handle: ['mobile']
    },
    {
        title: 'Email',
        align: 'left',
        width: 110,
        key: 'handle',
        handle: ['email']
    },
    {
        title: '分类',
        align: 'center',
        width: 95,
        key: 'handle',
        handle: ['record_type']
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['opt']
    },
    {
        title: '描述',
        align: 'left',
        key: 'handle',
        handle: ['desc']
    },
    {
        title: '处理时间',
        align: 'left',
        key: 'handle',
        width: 80,
        /*handle: ['created']*/
    },
    {
        title: '操作人',
        align: 'center',
        key: 'handle',
        width: 80,
        handle: ['operator']
    },
    {
        title: '编辑',
        align: 'center',
        width: 50,
        key: 'handle',
        handle: ['clear']
    }
];

const tData = {
    fColumns: fColumns,
    fsColumns: fsColumns,
    rColumns: rColumns,
    rsColumns: rsColumns,
    uColumns: uColumns,
    usColumns: usColumns,
    oColumns: oColumns,
    osColumns: osColumns,
    dColumns: dColumns,
    dsColumns: dsColumns,
    bColumns: bColumns,
    bsColumns: bsColumns
};

export default tData;
