<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="owner-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑表单
                </p>
                <Form ref="form" :model="form" :label-width="90">
                    <FormItem label="主理人名称：">
                        <Input v-model="form.name" placeholder="请输入主理人名称"></Input>
                    </FormItem>
                    <FormItem prop="type" label="类型：">
                        <RadioGroup v-model="form.type">
                            <Radio :label="0">微信</Radio>
                            <Radio :label="1">QQ</Radio>
                            <Radio :label="2">企业微信</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="账号：" v-if="form.type !== 2">
                        <Input v-model="form.account" placeholder="请输入账号"></Input>
                    </FormItem>
                    <FormItem prop="type" label="二维码：">
                        <RadioGroup v-model="code_type">
                            <Radio :label="0">上传二维码</Radio>
                            <Radio :label="1">二维码URL</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="">
                        <div v-if="code_type === 0">
                            <Upload action="/api/v1/admin/event/upload"
                                    :before-upload="uploadImg"
                                    :show-upload-list="true"
                                    v-if="files.length === 0"
                            >
                                <Button type="ghost" icon="ios-plus-empty" class="b_plus"></Button>
                            </Upload>
                            <div class="file-list" v-if="files.length > 0">
                                <div v-for="file in files">
                                    <!--{{file.name}}-->
                                    <img :src="file.src" alt="">
                                    <p class="del">
                                        <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <Input v-model="form.qr_code" placeholder="请输入二维码URL" v-if="code_type === 1"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem class="btns">
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                        <!--<Button @click="delRow" type="error" style="float: right;">删除</Button>-->
                        <Poptip
                                confirm
                                placement="top-end"
                                :transfer="true"
                                title="您确定要删除这条数据吗?"
                                @on-ok="delRow"
                                ok-text="确定"
                                cancel-text="取消">
                                <Button type="error" style="float: right;">删除</Button>
                        </Poptip>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

export default {
    name: 'owner_edit',
    components: {

    },
    data () {
        return {
            form: {
                name: '',
                type: 0,
                account: '',
                qr_code: '',
                image: ''
            },
            code_type: 1,
            files: [],
            errMsg: '',
            access_token: this.$store.state.user.access_token,
            isAccess: true
        };
    },
    computed: {

    },
    watch: {
        '$route' (to) {
           if(to.name === 'eUser_edit'){
               this.form = {}
               //this.form.type = 0;
               this.code_type = 1;
               this.files = [];
               this.errMsg = '';
               //this.form.id = window.location.href.split('/').pop();
               /*if(!this.form.id){
                   this.$router.push({
                       name: 'form_list'
                   });
               }*/
               this.getData();
           }
        },
    },
    mounted () {
        if(parseInt(Cookies.get('uAccess')) === 1){
            this.isAccess = false
        }
        //this.userform = JSON.parse(Cookies.get('currentRow'));
        //this.form = {}
        /*this.form.id = window.location.href.split('/').pop();
        if(!this.userform.id){
            this.$router.push({
                name: 'form_list'
            });
        }*/
        this.getData();
    },
    methods: {
        getData (){
            this.form = this.$store.state.recruit.currentRow;
        },
        handleSubmit () {
            if(!this.form.name){
                this.$Notice.error({
                    title: '主理人名称不能为空！'
                });
                return false;
            }
            if(this.form.type !== 2){
                if(!this.form.account){
                    this.$Notice.error({
                        title: '账号不能为空！'
                    });
                    return false;
                }
            }
            if(!this.form.image && !this.form.qr_code){
                this.$Notice.error({
                    title: '请上传或输入二维码！'
                });
                return false;
            }
            var dataIn = {
                id: this.form.id,
                name: this.form.name,
                type: this.form.type,
                qr_code: !!this.form.qr_code ? this.form.qr_code : this.form.image
            }
            if(this.form.type !== 2){
                dataIn.account = this.form.account;
            }
            var _this = this;
            util.ajax({
                url:'/api/v1/admin/recruit/owner',
                method:'PUT',
                data: dataIn,
            }).then(function (res) {
                //console.log(res.data);
                if(res.data.msg === 'success'){
                    _this.$Notice.success({
                        title: '提交成功！'
                    });
                    _this.$store.commit('removeTag', 'owner_edit');
                    _this.$store.commit('closePage', 'owner_edit');
                    _this.$router.push({
                        name: 'owner_list'
                    });
                }else{

                }
            }).catch(function (err) {
                console.log(err)
            });

        },
        uploadImg (file){
            var form = new FormData();
            form.append('file', file)
            const _this = this;
            util.ajax({
                url:'/api/v1/admin/recruit/owner/upload',
                method:'POST',
                dataType: 'JSON',
                contentType: false,
                processData: false,
                data: form
            }).then(function (res) {
                if(res.data.msg === 'success'){
                    _this.$Notice.success({
                        title: '上传成功！'
                    });
                    _this.form.image = res.data.data.fullpath;
                    _this.files.push({
                        //id: res.data.data.aid,
                        src: res.data.data.fullpath
                    })
                    _this.form.qr_code = '';
                    _this.errMsg = '';
                }else{
                    console.log(res)
                    _this.$Notice.error({
                        title: res.data.errors.message
                    });
                }
            }).catch(function (err) {
                console.log(err)
                _this.$Notice.error({
                    title: err.msg
                });
            });
            return false;
        },
        delImg (file){
            this.files = [];
            this.form.image = '';
        },
        delRow(){
            var _this = this;
            util.ajax({
                url:'/api/v1/admin/recruit/owner',
                method:'DELETE',
                data: {
                    id: this.form.id
                },
            }).then(function (res) {
                //console.log(res.data);
                if(res.data.msg === 'success'){
                    _this.$store.commit('removeTag', 'owner_edit');
                    _this.$store.commit('closePage', 'owner_edit');
                    _this.$router.push({
                        name: 'owner_list'
                    });
                }else{

                }
            }).catch(function (err) {
                console.log(err)
            });
        },
        closePage () {
            this.form = {}
            this.$store.commit('removeTag', 'owner_edit');
            this.$store.commit('closePage', 'owner_edit');
            this.$router.push({
                name: 'owner_list'
            });
        }
    }
};
</script>

<style>

</style>
