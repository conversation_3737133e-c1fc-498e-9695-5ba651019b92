<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="owner-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加主理人
                </p>
                <Form ref="form" :model="form" :label-width="90">
                    <FormItem label="主理人名称：">
                        <Input v-model="form.name" placeholder="请输入主理人名称"></Input>
                    </FormItem>
                    <FormItem prop="type" label="类型：">
                        <RadioGroup v-model="form.type">
                            <Radio :label="0">微信</Radio>
                            <Radio :label="1">QQ</Radio>
                            <Radio :label="2">企业微信</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="账号：" v-if="form.type !== 2">
                        <Input v-model="form.account" placeholder="请输入账号"></Input>
                    </FormItem>
                    <FormItem prop="type" label="二维码：">
                        <RadioGroup v-model="form.code_type">
                            <Radio :label="0">上传二维码</Radio>
                            <Radio :label="1">二维码URL</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem label="">
                        <div v-if="form.code_type === 0">
                            <Upload action="/api/v1/admin/event/upload"
                                    :before-upload="uploadImg"
                                    :show-upload-list="true"
                                    v-if="files.length === 0"
                            >
                                <Button type="ghost" icon="ios-plus-empty" class="b_plus"></Button>
                            </Upload>
                            <div class="file-list" v-if="files.length > 0">
                                <div v-for="file in files">
                                    <!--{{file.name}}-->
                                    <img :src="file.src" alt="">
                                    <p class="del">
                                        <Icon type="trash-a" @click="delImg(file)" size="22">删除</Icon>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <Input v-model="form.qr_code" placeholder="请输入二维码URL" v-if="form.code_type === 1"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">发布</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'owner_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    name: '',
                    type: 0,
                    account: '',
                    qr_code: '',
                    code_type: 0,
                    image: ''
                },
                files: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'release_add'){
                    this.form.name = '';
                    this.form.type = 0;
                    this.form.account = '';
                    this.form.qr_code = '';
                    this.form.code_type = 0;
                    this.form.image = '';
                    this.$refs['form'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                if(!this.form.name){
                    this.$Notice.error({
                        title: '主理人名称不能为空！'
                    });
                    return false;
                }
                if(this.form.type !== 2){
                    if(!this.form.account){
                        this.$Notice.error({
                            title: '账号不能为空！'
                        });
                        return false;
                    }
                }
                if(!this.form.image && !this.form.qr_code){
                    this.$Notice.error({
                        title: '请上传或输入二维码！'
                    });
                    return false;
                }
                var dataIn = {
                    id: this.form.id,
                    name: this.form.name,
                    type: this.form.type,
                    qr_code: !!this.form.qr_code ? this.form.qr_code : this.form.image
                }
                if(this.form.type !== 2){
                    dataIn.account = this.form.account;
                }

                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/owner',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '提交成功！'
                        });
                        _this.$store.commit('removeTag', 'owner_add');
                        _this.$store.commit('closePage', 'owner_add');
                        _this.$router.push({
                            name: 'owner_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            uploadImg (file){
                var form = new FormData();
                form.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/owner/upload',
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        _this.form.image = res.data.data.fullpath;
                        _this.files.push({
                            //id: res.data.data.aid,
                            src: res.data.data.fullpath
                        })
                        _this.form.qr_code = '';
                        _this.errMsg = '';
                    }else{
                        console.log(res)
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (file){
                this.files = [];
                this.form.image = '';
            },
            closePage () {
                this.form.name = '';
                this.form.type = 0;
                this.form.account = '';
                this.form.qr_code = '';
                this.form.code_type = 0;
                this.form.image = '';
                this.$store.commit('removeTag', 'owner_add');
                this.$router.push({
                    name: 'owner_list'
                });
            }
        }
    };
</script>

<style>

</style>
