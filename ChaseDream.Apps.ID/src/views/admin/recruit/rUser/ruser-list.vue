<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="ruser-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/recruit/ruser-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <!--<can-edit-table refs="table1" v-model="s_row" @on-search="getFormlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>--><!--class="listTable"-->
                    <can-edit-table refs="table" @on-delete="handleDel" v-model="rows" :columns-list="columns" ></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getFormlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/recruit_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'ruser_list',
        components: {
            canEditTable
        },
        data () {
            return {
                rows: [],
                columns: [],
                s_columns: [],
                s_row: [{
                    id: '',
                    username: '',
                    nickname: ''
                }],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'ruser_list'){
                    this.s_row = [{
                        id: '',
                        username: '',
                        nickname: ''
                    }];
                    this.getFormlist(1);
                }
            }
        },
        mounted () {
            this.getFormlist(1);
        },
        methods: {
            getData () {
                this.columns = tData.uColumns;
                this.s_columns = tData.usColumns;
            },
            getFormlist (n){
                var s = [];
                var text = '';
                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].username){
                    s.push('username=' + this.s_row[0].username);
                }
                if(!!this.s_row[0].nickname){
                    s.push('nickname=' + this.s_row[0].nickname);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }
                this.rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/user' + text,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.rows = res.data.data.rows;
                        for(var i=0;i<_this.rows.length;i++){
                            _this.rows[i].uPassword = '';
                        }

                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>


