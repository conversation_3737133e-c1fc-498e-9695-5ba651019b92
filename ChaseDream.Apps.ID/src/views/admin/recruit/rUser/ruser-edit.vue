<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="eUser-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑发帖用户
                </p>
                <Form ref="form" :model="form" :rules="rules" :label-width="80">
                    <FormItem prop="username" label="账号：">
                        <Input v-model="form.username" readonly></Input>
                    </FormItem>
                    <FormItem prop="nickname" label="昵称：">
                        <Input v-model="form.nickname" placeholder="请输入昵称"></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="form.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'ruser_edit',
        components: {

        },
        data () {
            return {
                form: {
                    nickname: '',
                    username: '',
                    password: ''
                },
                rules: {
                    username: [
                        { required: true, message: '账号不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                    ],
                    nickname: [
                        { required: true, message: '昵称不能为空', trigger: 'blur' },
                        { type: 'string', min: 2, message: '昵称不能小于2个字符', trigger: 'blur' }
                    ]

                },
                access_token: this.$store.state.user.access_token,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'ruser_edit'){
                    this.form = {}
                    this.form = this.$store.state.recruit.currentRow;
                    /*if(!this.form.id){
                        this.$router.push({
                            name: 'ruser_list'
                        });
                    }*/
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            //this.userform = JSON.parse(Cookies.get('currentRow'));
            this.form = {}
            this.form = this.$store.state.recruit.currentRow;
            console.log(this.form)
            if(!this.form.id){
                this.$router.push({
                    name: 'rUser_list'
                });
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        var dataIn = {
                            nickname: this.form.nickname,
                            username: this.form.username,
                            id: this.form.id
                        }
                        if(!!this.form.password){
                            dataIn.password = this.form.password;
                        }
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/recruit/user',
                            method:'PUT',
                            data: dataIn,
                        }).then(function (res) {
                            //console.log(res.data);
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'ruser_edit');
                                _this.$store.commit('closePage', 'ruser_edit');
                                _this.$router.push({
                                    name: 'ruser_list'
                                });
                            }else{

                            }
                        }).catch(function (err) {
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.form = {}
                this.$store.commit('removeTag', 'ruser_edit');
                this.$store.commit('closePage', 'ruser_edit');
                this.$router.push({
                    name: 'ruser_list'
                });
            }

        }
    };
</script>

<style>

</style>
