<style lang="less">
@import "../../../../styles/common.less";
@import "../recruit.less";
</style>

<template>
  <div class="loginlog-list">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <div class="box">
            <Checkbox v-model="gte30" @on-change="getFormlist(1)"
              >大于30天</Checkbox
            >
            <RadioGroup v-model="cruStatus" @on-change="getFormlist(1)">
              <Radio
                v-for="item in status"
                :label="item.value"
                :key="item.value"
              >
                {{ item.label }}
              </Radio>
            </RadioGroup>
            <Checkbox v-model="sameIp">相同IP</Checkbox>
            <Input
              v-if="sameIp"
              v-model="sameIpNum"
              style="width: 50px; margin-right: 5px;"
              @on-enter="getFormlist(1)"
            />
            <Input
              v-if="sameIp"
              v-model="atLeastNum"
              style="width: 50px;"
              @on-enter="getFormlist(1)"
            />
          </div>
          <div class="detail-list">
            <can-edit-table
              refs="table1"
              v-model="s_row"
              @on-search="getFormlist(1)"
              @on-updata="set_s_data"
              :columns-list="s_columns"
              class="searchTable"
            ></can-edit-table>
            <can-edit-table
              class="edittable-detail listTable"
              refs="table"
              @quick-search="quick_search"
              @on-delete="handleDel"
              v-model="rows"
              :columns-list="columns"
            ></can-edit-table>
          </div>
          <div class="page-bar">
            <Page
              :total="total"
              :page-size="page_size"
              :current="current"
              @on-change="getFormlist"
              v-if="showPage"
            ></Page>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import util from "@/libs/util.js";
import tData from "../data/loginlog_data.js";
import canEditTable from "./components/canEditTable.vue";
import ClipboardJS from "clipboard";

export default {
  name: "loginlog_list",
  components: {
    canEditTable,
  },
  data() {
    return {
      rows: [],
      columns: [],
      s_columns: [],
      s_row: [
        {
          id: "",
          uid: "",
          username: "",
          mobile: "",
          email: "",
          ip: "",
          regip: "",
          browserinfo: "",
        },
      ],
      total: 0,
      showPage: false,
      page_size: 20,
      current: 1,
      isAccess: true,
      status: [
        {
          label: "全部",
          value: "-1",
        },
        {
          label: "成功",
          value: "1",
        },
        {
          label: "失败",
          value: "0",
        },
      ],
      cruStatus: "-1",
      gte30: false,
      sameIp: false,
      sameIpNum: 3,
      atLeastNum: 2,
      forceHidden: true,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "loginlog_list") {
        this.getFormlist(1);
      }
    },
  },
  mounted() {
    this.getFormlist(1);
    var _this = this;
    this.clipboard = new ClipboardJS(".copyText");
    this.clipboard.on("success", function(e) {
      _this.$Message.success("复制成功！");
      e.clearSelection();
    });
    this.clipboard.on("error", (e) => {
      console.log(e);
      _this.$Message.error("复制失败！");
      e.clearSelection();
    });
  },
  methods: {
    getData() {
      this.columns = tData.bColumns;
      this.s_columns = tData.bsColumns;

      this.get_user_info();
    },
    getFormlist(n) {
      var s = [];
      var text = "";

      if (!!this.s_row[0].id) {
        s.push("id=" + this.s_row[0].id);
      }
      if (!!this.s_row[0].uid) {
        s.push("uid=" + this.s_row[0].uid);
      }
      if (!!this.s_row[0].username) {
        s.push("username=" + this.s_row[0].username);
      }
      if (!!this.s_row[0].mobile) {
        s.push("mobile=" + this.s_row[0].mobile);
      }
      if (!!this.s_row[0].email) {
        s.push("email=" + this.s_row[0].email);
      }
      if (!!this.s_row[0].regip) {
        s.push("regip=" + this.s_row[0].regip);
      }
      if (!!this.s_row[0].ip) {
        s.push("ip=" + this.s_row[0].ip);
      }
      if (this.s_row[0].browserinfo) {
        s.push("browserinfo=" + this.s_row[0].browserinfo);
      }

      s.push("dateline=" + this.gte30);
      if (this.cruStatus >= 0) {
        s.push("loginsuccess=" + this.cruStatus);
      }
      if (this.sameIp) {
        s.push("sameIp=" + this.sameIp);
        s.push("sameIpNum=" + this.sameIpNum);
        s.push("atLeastNum=" + this.atLeastNum);
      }

      if (s.length > 0) {
        text = "?" + s.join("&");
      }
      this.rows = [];
      this.total = 0;
      this.current = n;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/loginlog" + text,
          method: "GET",
          params: {
            page: _this.current,
            page_size: _this.page_size,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.rows = res.data.data.rows;
            _this.total = res.data.data.count;
            if (_this.total > _this.page_size) {
              _this.showPage = true;
            }
            _this.get_user_info();
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_s_data(obj) {
      this.s_row = obj;
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
    },
    quick_search(row, str) {
      if (str === "ip") {
        this.s_row = [
          {
            id: "",
            uid: "",
            username: "",
            mobile: "",
            email: "",
            ip: row.ip,
            regip: "",
            browserinfo: "",
          },
        ];
      } else if (str === "regip") {
        this.s_row = [
          {
            id: "",
            uid: "",
            username: "",
            mobile: "",
            email: "",
            ip: "",
            regip: row.regip,
            browserinfo: "",
          },
        ];
      }
      this.getFormlist(1);
    },
    get_user_info() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/info",
          method: "GET",
          params: {},
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            var user_info = res.data.data.user;
            if (user_info.role_name.indexOf("管理员") === -1) {
              if (_this.columns[0].key == "id") {
                _this.columns.splice(0, 1);
                _this.s_columns.splice(0, 1);
              }
            }
            _this.set_perms(user_info);
          } else {
            _this.$Notice.error({
              title: res.data.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_perms(info) {
      if (this.rows.length > 0) {
        for (var i = 0; i < this.rows.length; i++) {
          this.rows[i].isAdmin = false;
          if (info.role_name.indexOf("管理员") !== -1) {
            this.rows[i].isAdmin = true;
          }
          this.rows[i].forceHidden = this.forceHidden;
        }
        var list_data = [];
        list_data = JSON.parse(JSON.stringify(this.rows));
        this.rows = [];
        this.rows = JSON.parse(JSON.stringify(list_data));
      } else {
        var _this = this;
        setTimeout(function() {
          _this.set_perms(info);
        }, 600);
      }
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style lang="less" scoped="scoped">
.box {
  height: 40px;
  display: flex;
  justify-content: left;
  align-items: center;
  font-size: 12px;
  margin-top: 5px;
  margin-bottom: 5px;
}

/deep/ .ivu-card-body {
  padding-top: 0;
}

/deep/ .ivu-card-body .ivu-input {
  height: 30px;
}
</style>
