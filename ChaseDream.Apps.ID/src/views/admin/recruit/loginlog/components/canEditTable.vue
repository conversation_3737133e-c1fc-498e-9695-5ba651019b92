<style lang="less">
@import "./editable-table.less";
</style>

<template>
  <div>
    <Table
      :ref="refs"
      :row-class-name="rowClassName"
      :columns="columnsList"
      :data="thisTableData"
      border
    ></Table>
  </div>
</template>

<script>
const idInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.id,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "id", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "id", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};

const uidInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.uid,
      placeholder: "UID",
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "uid", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "uid", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};

const usernameInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.username,
      placeholder: "用户名",
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "username", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "username", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const mobileInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.mobile,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "mobile", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "mobile", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};

const emailInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.email,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "email", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "email", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};

const datelineInput = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h("Input", {
      props: {
        value: currentRow.wechat,
        placeholder: "注册时间",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
        fontSize: "12px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "regdateline", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "regdateline", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
    h("Input", {
      props: {
        value: currentRow.qq,
        placeholder: "最后访问时间",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
        fontSize: "12px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "dateline", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "dateline", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
  ]);
};

const ipInput = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h("Input", {
      props: {
        value: currentRow.regip,
        placeholder: "注册IP",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
        fontSize: "12px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "regip", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "regip", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
    h("Input", {
      props: {
        value: currentRow.ip,
        placeholder: "最后访问IP",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
        fontSize: "12px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "ip", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "ip", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
  ]);
};

const browserinfoInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.browserinfo,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "browserinfo", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "browserinfo", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};

const ipText = (vm, h, currentRow, index) => {
  return h(
    "div",
    {
      style: {},
    },
    [
      h(
        "p",
        {
          style: {
            position: "relative",
            textAlign: "left",
            margin: "0 -7px",
          },
        },
        [
          h(
            "span",
            {
              style: {
                cursor: "pointer",
              },
              domProps: {
                innerHTML:
                  '<span class="copyText" data-clipboard-action="copy" data-clipboard-text="' +
                  currentRow.regip +
                  '">' +
                  currentRow.regip +
                  "</span>",
              },
            },
            currentRow.regip
          ),
          h("Icon", {
            props: {
              type: "search",
              size: 18,
            },
            style: {
              color: "#1f85f4",
              verticalAlign: "middle",
              position: "absolute",
              right: 0,
              top: 0,
              cursor: "pointer",
            },
            on: {
              click: () => {
                vm.$emit("quick-search", currentRow, "regip");
              },
            },
          }),
        ]
      ),
      h(
        "p",
        {
          style: {
            position: "relative",
            textAlign: "left",
            margin: "0 -7px",
          },
        },
        [
          h(
            "span",
            {
              style: {
                cursor: "pointer",
              },
              domProps: {
                innerHTML:
                  '<span class="copyText" data-clipboard-action="copy" data-clipboard-text="' +
                  currentRow.ip +
                  '">' +
                  currentRow.ip +
                  "</span>",
              },
            },
            currentRow.ip
          ),
          h("Icon", {
            props: {
              type: "search",
              size: 18,
            },
            style: {
              color: "#1f85f4",
              verticalAlign: "middle",
              position: "absolute",
              right: 0,
              top: 0,
              cursor: "pointer",
            },
            on: {
              click: () => {
                vm.$emit("quick-search", currentRow, "ip");
              },
            },
          }),
        ]
      ),
    ]
  );
};

const clearButton = (vm, h, currentRow, index) => {
  return h(
    "span",
    {
      props: {},
      style: {
        color: "#2d8cf0",
        cursor: "pointer",
        // margin: "0 0 0 10px",
      },
      on: {
        click: () => {
          vm.$set(vm.thisTableData[0], "id", "");
          vm.$set(vm.thisTableData[0], "uid", "");
          vm.$set(vm.thisTableData[0], "username", "");
          vm.$set(vm.thisTableData[0], "mobile", "");
          vm.$set(vm.thisTableData[0], "email", "");
          vm.$set(vm.thisTableData[0], "ip", "");
          vm.$set(vm.thisTableData[0], "regip", "");
          vm.$set(vm.thisTableData[0], "browserinfo", "");
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    "清除"
  );
};
export default {
  name: "canEditTable",
  props: {
    refs: String,
    columnsList: Array,
    value: Array,
    url: String,
    editIncell: {
      type: Boolean,
      default: false,
    },
    hoverShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      columns: [],
      thisTableData: [],
      edittingStore: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let vm = this;
      let editableCell = this.columnsList.filter((item) => {
        if (item.editable) {
          if (item.editable === true) {
            return item;
          }
        }
      });
      let cloneData = JSON.parse(JSON.stringify(this.value));
      let res = [];
      res = cloneData.map((item, index) => {
        let isEditting = false;
        if (this.thisTableData[index]) {
          if (this.thisTableData[index].editting) {
            isEditting = true;
          } else {
            for (const cell in this.thisTableData[index].edittingCell) {
              if (this.thisTableData[index].edittingCell[cell] === true) {
                isEditting = true;
              }
            }
          }
        }
        if (isEditting) {
          return this.thisTableData[index];
        } else {
          this.$set(item, "editting", false);
          let edittingCell = {};
          editableCell.forEach((item) => {
            edittingCell[item.key] = false;
          });
          this.$set(item, "edittingCell", edittingCell);
          return item;
        }
      });
      this.thisTableData = res;
      this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
      this.columnsList.forEach((item) => {
        if (item.editable) {
          item.render = (h, param) => {
            let currentRow = this.thisTableData[param.index];
            if (!currentRow.editting) {
              if (this.editIncell) {
                return h(
                  "Row",
                  {
                    props: {
                      type: "flex",
                      align: "middle",
                      justify: "center",
                    },
                  },
                  [
                    h(
                      "Col",
                      {
                        props: {
                          span: "22",
                        },
                      },
                      [
                        !currentRow.edittingCell[param.column.key]
                          ? h("span", currentRow[item.key])
                          : cellInput(this, h, param, item),
                      ]
                    ),
                    h(
                      "Col",
                      {
                        props: {
                          span: "2",
                        },
                      },
                      [
                        currentRow.edittingCell[param.column.key]
                          ? saveIncellEditBtn(this, h, param)
                          : incellEditBtn(this, h, param),
                      ]
                    ),
                  ]
                );
              } else {
                return h("span", currentRow[item.key]);
              }
            } else {
              return h("Input", {
                props: {
                  type: "text",
                  value: currentRow[item.key],
                },
                on: {
                  "on-change"(event) {
                    let key = param.column.key;
                    vm.edittingStore[param.index][key] = event.target.value;
                  },
                },
              });
            }
          };
        }
        if (item.handle) {
          item.render = (h, param) => {
            let currentRowData = this.thisTableData[param.index];
            let children = [];
            item.handle.forEach((item) => {
              if (item === "id") {
                children.push(idInput(this, h, currentRowData, param.index));
              } else if (item === "uid") {
                children.push(uidInput(this, h, currentRowData, param.index));
                children.push(
                  usernameInput(this, h, currentRowData, param.index)
                );
              } else if (item === "mobile") {
                children.push(
                  mobileInput(this, h, currentRowData, param.index)
                );
              } else if (item === "email") {
                children.push(emailInput(this, h, currentRowData, param.index));
              } else if (item === "dateline") {
                children.push(
                  clearButton(this, h, currentRowData, param.index)
                );
              } else if (item === "ip") {
                children.push(ipInput(this, h, currentRowData, param.index));
              } else if (item === "ipText") {
                children.push(ipText(this, h, currentRowData, param.index));
              } else if (item === "browserinfo") {
                children.push(
                  browserinfoInput(this, h, currentRowData, param.index)
                );
              }
            });
            return h("div", children);
          };
        }
      });
    },
    handleBackdata(data) {
      let clonedData = JSON.parse(JSON.stringify(data));
      clonedData.forEach((item) => {
        delete item.editting;
        delete item.edittingCell;
        delete item.saving;
      });
      return clonedData;
    },
    rowClassName(row, index) {
      if (row.loginsuccess == 0) {
        return "row_red";
      } else if (row.loginsuccess == 1) {
        return "row_green";
      } else {
        return "";
      }
    },
  },
  watch: {
    value(data) {
      this.init();
    },
  },
};
</script>
