<style lang="less">
@import "./editable-table.less";
</style>

<template>
  <div>
    <Table
      :ref="refs"
      :row-class-name="rowClassName"
      :columns="columnsList"
      :data="thisTableData"
      border
    ></Table>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
const operateButton = (vm, h, currentRow, index) => {
  currentRow.show = false;
  var reason_code = 0;
  var reason = "";
  var oldVal = currentRow.opt;
  var isReport = false;
  if (!!currentRow.report) {
    currentRow.isReport = true;
  }
  return h("div", {}, [
    h(
      "Radio",
      {
        props: {
          value: currentRow.opt === 0 ? true : false,
        },
        on: {
          "on-change": (status) => {
            if (!!status) {
              var _this = this;
              util
                .ajax({
                  url: "/api/v1/admin/recruit/detail/opt",
                  method: "POST",
                  data: {
                    id: currentRow.id,
                    opt: 0,
                    reason: "",
                    reason_code: "",
                  },
                })
                .then(function(res) {
                  if (res.data.msg === "success") {
                    vm.$set(vm.thisTableData[index], "opt", 0);
                    vm.$emit("set-opt", currentRow, index, 0);
                  } else {
                    vm.$set(vm.thisTableData[index], "opt", -1);
                    vm.$set(vm.thisTableData[index], "opt", oldVal);
                  }
                })
                .catch(function(err) {
                  vm.$set(vm.thisTableData[index], "opt", -1);
                  vm.$set(vm.thisTableData[index], "opt", oldVal);
                  console.log(err);
                });
            }
          },
        },
      },
      "未"
    ),
    h(
      "Radio",
      {
        props: {
          value: currentRow.opt === 5 ? true : false,
        },
        on: {
          "on-change": (status) => {
            if (!!status) {
              var _this = this;
              util
                .ajax({
                  url: "/api/v1/admin/recruit/detail/opt",
                  method: "POST",
                  data: {
                    id: currentRow.id,
                    opt: 5,
                    reason: "",
                    reason_code: "",
                  },
                })
                .then(function(res) {
                  if (res.data.msg === "success") {
                    vm.$set(vm.thisTableData[index], "opt", 5);
                    vm.$emit("set-opt", currentRow, index, 5);
                  } else {
                    vm.$set(vm.thisTableData[index], "opt", -1);
                    vm.$set(vm.thisTableData[index], "opt", oldVal);
                  }
                })
                .catch(function(err) {
                  vm.$set(vm.thisTableData[index], "opt", -1);
                  vm.$set(vm.thisTableData[index], "opt", oldVal);
                  console.log(err);
                });
            }
          },
        },
      },
      "待"
    ),
    h("Icon", {
      props: {
        type: "wand",
        loading: currentRow.saving,
        size: 18,
      },
      style: {
        marginLeft: "8px",
        color: "#19be6b",
        cursor: "pointer",
        verticalAlign: "middle",
        display: !!currentRow.isReport ? "none" : "inline-block",
      },
      on: {
        click: () => {
          vm.$emit("set-report", currentRow, index);
        },
      },
    }),
    h("Icon", {
      props: {
        type: "document-test",
        loading: currentRow.saving,
        size: 20,
      },
      style: {
        marginLeft: "8px",
        marginTop: "3px",
        color: "#19be6b",
        cursor: "pointer",
        verticalAlign: "middle",
        display: !currentRow.isReport ? "none" : "inline-block",
      },
      on: {
        click: () => {
          vm.$emit("get-report", currentRow);
        },
      },
    }),
    h("p", {}, ""),
    h(
      "Radio",
      {
        props: {
          value: currentRow.opt === 1 ? true : false,
        },
        on: {
          "on-change": (status) => {
            if (!!status) {
              var _this = this;
              util
                .ajax({
                  url: "/api/v1/admin/recruit/detail/opt",
                  method: "POST",
                  data: {
                    id: currentRow.id,
                    opt: 1,
                    reason: "",
                    reason_code: "",
                  },
                })
                .then(function(res) {
                  if (res.data.msg === "success") {
                    vm.$set(vm.thisTableData[index], "opt", 1);
                    vm.$emit("set-opt", currentRow, index, 1);
                  } else {
                    vm.$set(vm.thisTableData[index], "opt", -1);
                    vm.$set(vm.thisTableData[index], "opt", oldVal);
                  }
                })
                .catch(function(err) {
                  vm.$set(vm.thisTableData[index], "opt", -1);
                  vm.$set(vm.thisTableData[index], "opt", oldVal);
                  console.log(err);
                });
            }
          },
        },
      },
      "完"
    ),
    h(
      "Radio",
      {
        props: {
          value: currentRow.opt === 2 ? true : false,
        },
        on: {
          "on-change": (status) => {
            if (!!status) {
              var _this = this;
              util
                .ajax({
                  url: "/api/v1/admin/recruit/detail/opt",
                  method: "POST",
                  data: {
                    id: currentRow.id,
                    opt: 2,
                    reason: "",
                    reason_code: "",
                  },
                })
                .then(function(res) {
                  if (res.data.msg === "success") {
                    vm.$set(vm.thisTableData[index], "opt", 2);
                    vm.$emit("set-opt", currentRow, index, 2);
                  } else {
                    vm.$set(vm.thisTableData[index], "opt", -1);
                    vm.$set(vm.thisTableData[index], "opt", oldVal);
                  }
                })
                .catch(function(err) {
                  vm.$set(vm.thisTableData[index], "opt", -1);
                  vm.$set(vm.thisTableData[index], "opt", oldVal);
                  console.log(err);
                });
            }
          },
        },
      },
      "略"
    ),
    h("p", {}, ""),
    h(
      "Poptip",
      {
        props: {
          vModel: currentRow.show,
          transfer: true,
          placement: "bottom-end",
          width: "330",
        },
        on: {
          "on-popper-hide": () => {
            vm.$set(vm.thisTableData[index], "opt", -1);
            vm.$set(vm.thisTableData[index], "opt", oldVal);
          },
        },
      },
      [
        h("Radio", {
          props: {
            value: currentRow.opt === 3 ? true : false,
          },
          on: {
            "on-change": (status) => {},
          },
        }),
        h(
          "div",
          {
            slot: "content",
          },
          [
            h(
              "h3",
              {
                style: {
                  height: "30px",
                  lineHeight: "30px;",
                  borderBottom: "1px solid #E9EAEC",
                  color: "#495060",
                  fontSize: "14px",
                  marginBottom: "10px",
                  fontWeight: "normal",
                },
              },
              "原因"
            ),
            h(
              "RadioGroup",
              {
                props: {
                  value: reason_code,
                },
                on: {
                  "on-change": (status) => {
                    reason_code = status;
                  },
                },
              },
              [
                h(
                  "Radio",
                  {
                    props: {
                      label: 0,
                    },
                  },
                  "群内广告"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 1,
                    },
                  },
                  "加群过多"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 2,
                    },
                  },
                  "群内僵尸"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 3,
                    },
                  },
                  "违规前科"
                ),
              ]
            ),
            h("Input", {
              style: {
                display: "block",
                marginTop: "20px",
                marginBottom: "14px",
                background: "#F5F6F7",
                width: "100%",
                height: "50px",
              },
              props: {
                type: "textarea",
                autoSize: "{minRows: 2,maxRows: 5}",
                placeholder: "请输入原因",
                value: reason,
              },
              on: {
                "on-change": (status) => {
                  reason = status.target.value;
                },
              },
            }),
            h(
              "Button",
              {
                style: {
                  display: "block",
                  width: "100%",
                  height: "34px",
                  background: "#2D8CF0",
                  color: "#fff",
                  fontSize: "14px",
                },
                on: {
                  click: () => {
                    var record_type = "";
                    if (reason_code === 0) {
                      record_type = "群内广告";
                    } else if (reason_code === 1) {
                      record_type = "加群过多";
                    } else if (reason_code === 2) {
                      record_type = "群内僵尸";
                    } else if (reason_code === 3) {
                      record_type = "违规前科";
                    }
                    var _this = this;
                    util
                      .ajax({
                        url: "/api/v1/admin/recruit/detail/opt",
                        method: "POST",
                        data: {
                          id: currentRow.id,
                          opt: 3,
                          reason: reason,
                          reason_code: "2." + reason_code,
                          record_type: record_type,
                        },
                      })
                      .then(function(res) {
                        if (res.data.msg === "success") {
                          vm.$set(vm.thisTableData[index], "opt", 3);
                          vm.$emit("set-opt", currentRow, index, 3);
                          vm.$emit("on-updata");
                        } else {
                          vm.$set(vm.thisTableData[index], "opt", 1);
                          vm.$set(vm.thisTableData[index], "opt", oldVal);
                        }
                      })
                      .catch(function(err) {
                        vm.$set(vm.thisTableData[index], "opt", 1);
                        vm.$set(vm.thisTableData[index], "opt", oldVal);
                        console.log(err);
                      });
                  },
                },
              },
              "确定"
            ),
          ]
        ),
      ]
    ),
    h(
      "Poptip",
      {
        props: {
          transfer: true,
          placement: "bottom-end",
          width: "260",
        },
      },
      [
        h(
          "span",
          {
            style: {
              marginLeft: "-7px",
              marginRight: "7px",
              color: currentRow.behavior.length > 0 ? "#ed3f14" : "",
              cursor: "pointer",
              verticalAlign: "middle",
            },
          },
          "拒"
        ),
        h(
          "div",
          {
            slot: "content",
          },
          currentRow.behavior.map((item, index) => {
            item.opt_date = item.created_at
              .split("T")
              .join(" ")
              .split(":");
            return h(
              "div",
              {
                style: {
                  borderBottom:
                    index !== currentRow.behavior.length - 1
                      ? "1px solid #E9EAEC"
                      : "none",
                  padding: "8px 0",
                },
              },
              [
                h(
                  "p",
                  {
                    style: {
                      lineHeight: "20px",
                    },
                  },
                  [
                    h(
                      "span",
                      {
                        style: {
                          color: "#F44242",
                        },
                      },
                      item.record_code + " : " + item.record_type
                    ),
                    h(
                      "span",
                      {
                        style: {
                          float: "right",
                          color: "#999",
                        },
                      },
                      item.opt_date[0] + ":" + item.opt_date[1]
                    ),
                  ]
                ),
                h(
                  "p",
                  {
                    style: {
                      lineHeight: "20px",
                      whiteSpace: "initial",
                    },
                  },
                  item.desc
                ),
              ]
            );
          })
        ),
      ]
    ),
    h(
      "Poptip",
      {
        props: {
          transfer: true,
          placement: "bottom-end",
          width: "330",
        },
        on: {
          "on-ok": () => {
            if (!!status) {
              var _this = this;
              util
                .ajax({
                  url: "/api/v1/admin/recruit/detail/opt",
                  method: "POST",
                  data: {
                    id: currentRow.id,
                    opt: 4,
                    reason: currentRow.reason,
                    reason_code: currentRow.reason_code,
                  },
                })
                .then(function(res) {
                  if (res.data.msg === "success") {
                    vm.$set(vm.thisTableData[index], "opt", 4);
                  } else {
                    vm.$set(vm.thisTableData[index], "opt", -1);
                    vm.$set(vm.thisTableData[index], "opt", oldVal);
                  }
                })
                .catch(function(err) {
                  vm.$set(vm.thisTableData[index], "opt", -1);
                  vm.$set(vm.thisTableData[index], "opt", oldVal);
                  console.log(err);
                });
            }
          },
          "on-popper-hide": () => {
            vm.$set(vm.thisTableData[index], "opt", -1);
            vm.$set(vm.thisTableData[index], "opt", oldVal);
          },
        },
      },
      [
        h(
          "Radio",
          {
            props: {
              value: currentRow.opt === 4 ? true : false,
            },
          },
          "封"
        ),
        h(
          "div",
          {
            slot: "content",
          },
          [
            h(
              "h3",
              {
                style: {
                  height: "30px",
                  lineHeight: "30px;",
                  borderBottom: "1px solid #E9EAEC",
                  color: "#495060",
                  fontSize: "14px",
                  marginBottom: "10px",
                  fontWeight: "normal",
                },
              },
              "原因"
            ),
            h(
              "RadioGroup",
              {
                props: {
                  value: reason_code,
                },
                on: {
                  "on-change": (status) => {
                    reason_code = status;
                  },
                },
              },
              [
                h(
                  "Radio",
                  {
                    props: {
                      label: 0,
                    },
                  },
                  "群内广告"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 1,
                    },
                  },
                  "加群过多"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 2,
                    },
                  },
                  "群内僵尸"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 3,
                    },
                  },
                  "违规前科"
                ),
              ]
            ),
            h("Input", {
              style: {
                display: "block",
                marginTop: "20px",
                marginBottom: "14px",
                background: "#F5F6F7",
                width: "100%",
                height: "50px",
              },
              props: {
                type: "textarea",
                autoSize: "{minRows: 2,maxRows: 5}",
                placeholder: "请输入原因",
                value: reason,
              },
              on: {
                "on-change": (status) => {
                  reason = status.target.value;
                },
              },
            }),
            h(
              "Button",
              {
                style: {
                  display: "block",
                  width: "100%",
                  height: "34px",
                  background: "#2D8CF0",
                  color: "#fff",
                  fontSize: "14px",
                },
                on: {
                  click: () => {
                    var record_type = "";
                    if (reason_code === 0) {
                      record_type = "群内广告";
                    } else if (reason_code === 1) {
                      record_type = "加群过多";
                    } else if (reason_code === 2) {
                      record_type = "群内僵尸";
                    } else if (reason_code === 3) {
                      record_type = "违规前科";
                    }
                    var _this = this;
                    util
                      .ajax({
                        url: "/api/v1/admin/recruit/detail/opt",
                        method: "POST",
                        data: {
                          id: currentRow.id,
                          opt: 4,
                          reason: reason,
                          reason_code: "2." + reason_code,
                          record_type: record_type,
                        },
                      })
                      .then(function(res) {
                        if (res.data.msg === "success") {
                          vm.$set(vm.thisTableData[index], "opt", 4);
                          vm.$emit("set-opt", currentRow, index, 4);
                          vm.$emit("on-updata");
                        } else {
                          vm.$set(vm.thisTableData[index], "opt", oldVal);
                        }
                      })
                      .catch(function(err) {
                        vm.$set(vm.thisTableData[index], "opt", oldVal);
                        console.log(err);
                      });
                  },
                },
              },
              "确定"
            ),
          ]
        ),
      ]
    ),
    h(
      "Poptip",
      {
        props: {
          transfer: true,
          placement: "bottom-end",
          width: "330",
        },
        on: {
          "on-ok": () => {
            if (!!status) {
              var _this = this;
              util
                .ajax({
                  url: "/api/v1/admin/recruit/detail/opt",
                  method: "POST",
                  data: {
                    id: currentRow.id,
                    opt: 6,
                    reason: currentRow.reason,
                    reason_code: currentRow.reason_code,
                  },
                })
                .then(function(res) {
                  //console.log(res.data);
                  if (res.data.msg === "success") {
                    vm.$set(vm.thisTableData[index], "opt", 6);
                    vm.$emit("set-opt", currentRow, index, 6);
                  } else {
                    vm.$set(vm.thisTableData[index], "opt", -1);
                    vm.$set(vm.thisTableData[index], "opt", oldVal);
                  }
                })
                .catch(function(err) {
                  vm.$set(vm.thisTableData[index], "opt", -1);
                  vm.$set(vm.thisTableData[index], "opt", oldVal);
                  console.log(err);
                });
            }
          },
          "on-popper-hide": () => {
            vm.$set(vm.thisTableData[index], "opt", -1);
            vm.$set(vm.thisTableData[index], "opt", oldVal);
          },
        },
      },
      [
        h(
          "Radio",
          {
            style: {
              marginRight: 0,
            },
            props: {
              value: currentRow.opt === 6 ? true : false,
            },
            on: {
              "on-change": (status) => {},
            },
          },
          "踢"
        ),
        h(
          "div",
          {
            slot: "content",
          },
          [
            h(
              "h3",
              {
                style: {
                  height: "30px",
                  lineHeight: "30px;",
                  borderBottom: "1px solid #E9EAEC",
                  color: "#495060",
                  fontSize: "14px",
                  marginBottom: "10px",
                  fontWeight: "normal",
                },
              },
              "原因"
            ),
            h(
              "RadioGroup",
              {
                props: {
                  value: reason_code,
                },
                on: {
                  "on-change": (status) => {
                    reason_code = status;
                  },
                },
              },
              [
                h(
                  "Radio",
                  {
                    props: {
                      label: 0,
                    },
                  },
                  "群内广告"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 1,
                    },
                  },
                  "加群过多"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 2,
                    },
                  },
                  "群内僵尸"
                ),
                h(
                  "Radio",
                  {
                    props: {
                      label: 3,
                    },
                  },
                  "违规前科"
                ),
              ]
            ),
            h("Input", {
              style: {
                display: "block",
                marginTop: "20px",
                marginBottom: "14px",
                background: "#F5F6F7",
                width: "100%",
                height: "50px",
              },
              props: {
                type: "textarea",
                autoSize: "{minRows: 2,maxRows: 5}",
                placeholder: "请输入原因",
                value: reason,
              },
              on: {
                "on-change": (status) => {
                  reason = status.target.value;
                },
              },
            }),
            h(
              "Button",
              {
                style: {
                  display: "block",
                  width: "100%",
                  height: "34px",
                  background: "#2D8CF0",
                  color: "#fff",
                  fontSize: "14px",
                },
                on: {
                  click: () => {
                    var record_type = "";
                    if (reason_code === 0) {
                      record_type = "群内广告";
                    } else if (reason_code === 1) {
                      record_type = "加群过多";
                    } else if (reason_code === 2) {
                      record_type = "群内僵尸";
                    } else if (reason_code === 3) {
                      record_type = "违规前科";
                    }
                    var _this = this;
                    util
                      .ajax({
                        url: "/api/v1/admin/recruit/detail/opt",
                        method: "POST",
                        data: {
                          id: currentRow.id,
                          opt: 6,
                          reason: reason,
                          reason_code: "2." + reason_code,
                          record_type: record_type,
                        },
                      })
                      .then(function(res) {
                        if (res.data.msg === "success") {
                          vm.$set(vm.thisTableData[index], "opt", 6);
                          vm.$emit("set-opt", currentRow, index, 6);
                          vm.$emit("on-updata");
                        } else {
                          vm.$set(vm.thisTableData[index], "opt", oldVal);
                        }
                      })
                      .catch(function(err) {
                        vm.$set(vm.thisTableData[index], "opt", oldVal);
                        console.log(err);
                      });
                  },
                },
              },
              "确定"
            ),
          ]
        ),
      ]
    ),
  ]);
};
const editButton = (vm, h, currentRow, index) => {
  return h("Icon", {
    props: {
      type: "edit",
      loading: currentRow.saving,
      size: 22,
    },
    style: {
      margin: "0 8px",
      color: "#2d8cf0",
      cursor: "pointer",
    },
    on: {
      click: () => {
        let cr = currentRow;
        vm.$router.push("/console/recruit/form-edit/" + currentRow.id);
      },
    },
  });
};
const deleteButton = (vm, h, currentRow, index) => {
  return h(
    "Poptip",
    {
      props: {
        confirm: true,
        title: "您确定要删除这条数据吗?",
        transfer: true,
      },
      on: {
        "on-ok": () => {
          var _this = this;
          util
            .ajax({
              url: "/api/v1/admin/recruit/form",
              method: "DELETE",
              data: {
                id: currentRow.id,
              },
            })
            .then(function(res) {
              if (res.data.msg === "success") {
                vm.thisTableData.splice(index, 1);
                vm.$emit("input", vm.handleBackdata(vm.thisTableData));
                vm.$emit(
                  "on-delete",
                  vm.handleBackdata(vm.thisTableData),
                  index
                );
              } else {
              }
            })
            .catch(function(err) {
              console.log(err);
            });
        },
      },
    },
    [
      h("Icon", {
        style: {
          margin: "0 8px",
          color: "#ed3f14",
          cursor: "pointer",
        },
        props: {
          type: "trash-a",
          placement: "top",
          size: 24,
        },
      }),
    ]
  );
};
const idInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.id,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "id", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "id", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const form_idInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.form_id,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "form_id", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "form_id", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const form_subjectInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.form_subject,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "form_subject", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "form_subject", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const typeInput = (vm, h, currentRow, index) => {
  var typeList = [
    {
      id: -2,
      name: "全部",
    },
    {
      id: 0,
      name: "GMAT",
    },
    {
      id: 7,
      name: "GRE",
    },
    {
      id: 1,
      name: "TOFEL",
    },
    {
      id: 2,
      name: "IELTS",
    },
    {
      id: 3,
      name: "MBA",
    },
    {
      id: 4,
      name: "Master",
    },
    {
      id: 5,
      name: "Career",
    },
    {
      id: 6,
      name: "常规",
    },
  ];
  return h(
    "Select",
    {
      props: {
        value: currentRow.type, // 获取选择的下拉框的值
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
      },
      on: {
        "on-change": (e) => {
          vm.$set(vm.thisTableData[0], "type", e);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    typeList.map((item) => {
      return h("Option", {
        // 下拉框的值
        props: {
          value: item.id,
          label: item.name,
        },
        style: {
          paddingLeft: 0,
          paddingRight: 0,
        },
      });
    })
  );
};
const usernameInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.username,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "username", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "username", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const wechatInput = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h("Input", {
      props: {
        value: currentRow.wechat,
        placeholder: "微信号",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
        fontSize: "12px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "wechat", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "wechat", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
    h("Input", {
      props: {
        value: currentRow.qq,
        placeholder: "QQ号码",
      },
      style: {
        width: "100%",
        height: "28px",
        lineHeight: "28px",
        fontSize: "12px",
      },
      on: {
        "on-blur": (e) => {
          vm.$set(vm.thisTableData[0], "qq", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        },
        "on-enter": (e) => {
          vm.$set(vm.thisTableData[0], "qq", e.target.value);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    }),
  ]);
};
const qqInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.qq,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "qq", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "qq", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const mobileInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.mobile,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "mobile", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "mobile", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const cityInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.city,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "city", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "city", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const ipInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.ip,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "ip", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "ip", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const statusInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.status,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "status", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "status", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const psInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.ps,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "ps", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "ps", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const noteInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.note,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "note", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "note", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const createdInput = (vm, h, currentRow, index) => {
  return h("Input", {
    props: {
      value: currentRow.created,
    },
    style: {
      width: "100%",
      height: "28px",
      lineHeight: "28px",
    },
    on: {
      "on-blur": (e) => {
        vm.$set(vm.thisTableData[0], "created", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
      },
      "on-enter": (e) => {
        vm.$set(vm.thisTableData[0], "created", e.target.value);
        vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
        vm.$emit("on-search");
      },
    },
  });
};
const optInput = (vm, h, currentRow, index) => {
  var typeList = [
    {
      id: -2,
      name: "全",
    },
    {
      id: 0,
      name: "未",
    },
    {
      id: 5,
      name: "待",
    },
    {
      id: 1,
      name: "完",
    },
    {
      id: 2,
      name: "略",
    },
    {
      id: 3,
      name: "拒",
    },
    {
      id: 4,
      name: "封",
    },
    {
      id: 6,
      name: "踢",
    },
  ];
  return h(
    "Select",
    {
      props: {
        value: currentRow.opt, // 获取选择的下拉框的值
      },
      style: {
        width: "50px",
        height: "28px",
        lineHeight: "28px",
        marginRight: "10px",
        marginLeft: "10px",
        maxHeight: "260px",
      },
      on: {
        "on-change": (e) => {
          vm.$set(vm.thisTableData[0], "opt", e);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    typeList.map((item) => {
      return h("Option", {
        // 下拉框的值
        props: {
          value: item.id,
          label: item.name,
        },
      });
    })
  );
};
const clearButton = (vm, h, currentRow, index) => {
  return h(
    "span",
    {
      props: {},
      style: {
        margin: "0 8px",
        color: "#2d8cf0",
        cursor: "pointer",
      },
      on: {
        click: () => {
          vm.$set(vm.thisTableData[0], "id", "");
          vm.$set(vm.thisTableData[0], "form_id", "");
          vm.$set(vm.thisTableData[0], "form_subject", "");
          vm.$set(vm.thisTableData[0], "type", -1);
          vm.$set(vm.thisTableData[0], "username", "");
          vm.$set(vm.thisTableData[0], "wechat", "");
          vm.$set(vm.thisTableData[0], "qq", "");
          vm.$set(vm.thisTableData[0], "city", "");
          vm.$set(vm.thisTableData[0], "mobile", "");
          vm.$set(vm.thisTableData[0], "status", "");
          vm.$set(vm.thisTableData[0], "ps", "");
          vm.$set(vm.thisTableData[0], "note", "");
          vm.$set(vm.thisTableData[0], "ip", "");
          vm.$set(vm.thisTableData[0], "created", "");
          vm.$set(vm.thisTableData[0], "opt", -1);
          vm.$emit("on-updata", vm.handleBackdata(vm.thisTableData));
          vm.$emit("on-search");
        },
      },
    },
    "清除"
  );
};
const note_editInput = (vm, h, currentRow, index) => {
  var note = currentRow.note;
  currentRow.isEdit = false;
  return h(
    "div",
    {
      style: {
        position: "relative",
        maxheight: "36px",
        overflow: "hidden",
        minHeight: "18px",
      },
    },
    [
      h(
        "p",
        {
          attrs: {
            title: currentRow.note,
          },
          style: {
            marginRight: "15px",
            cursor: "pointer",
            maxHeight: "70px",
            overflow: "hidden",
            marginTop: "7px",
            marginBottom: "7px",
          },
        },
        currentRow.note
      ),
      h(
        "Poptip",
        {
          props: {
            vModel: currentRow.show,
            transfer: true,
            placement: "bottom-end",
            width: "330",
          },
          style: {
            position: "absolute",
            bottom: "0",
            right: "0",
          },
          on: {
            "on-popper-hide": () => {},
          },
        },
        [
          h("Icon", {
            props: {
              type: "edit",
              size: 12,
            },
            style: {
              color: "#2d8cf0",
              cursor: "pointer",
            },
            attrs: {
              title: "编辑备注",
            },
          }),
          h(
            "div",
            {
              slot: "content",
            },
            [
              h(
                "h3",
                {
                  style: {
                    height: "30px",
                    lineHeight: "30px;",
                    borderBottom: "1px solid #E9EAEC",
                    color: "#495060",
                    fontSize: "14px",
                    marginBottom: "10px",
                    fontWeight: "normal",
                  },
                },
                "编辑备注"
              ),
              h("Input", {
                style: {
                  display: "block", //!!currentRow.isEdit ? 'block' : 'none',  //'block',
                  marginTop: "20px",
                  marginBottom: "14px",
                  background: "#F5F6F7",
                  width: "100%",
                },
                props: {
                  type: "textarea",
                  rows: 4,
                  placeholder: "备注",
                  value: note,
                },
                on: {
                  "on-change": (obj) => {
                    note = obj.target.value;
                  },
                },
              }),
              h(
                "Button",
                {
                  style: {
                    display: "block",
                    width: "100%",
                    height: "34px",
                    background: "#2D8CF0",
                    color: "#fff",
                    fontSize: "14px",
                  },
                  on: {
                    click: () => {
                      var _this = this;
                      util
                        .ajax({
                          url: "/api/v1/admin/recruit/detail",
                          method: "PUT",
                          data: {
                            id: currentRow.id,
                            note: note,
                          },
                        })
                        .then(function(res) {
                          if (res.data.msg === "success") {
                            vm.$emit("set-note", currentRow, index, note);
                          } else {
                          }
                        })
                        .catch(function(err) {
                          console.log(err);
                        });
                    },
                  },
                },
                "确定"
              ),
            ]
          ),
        ]
      ),
    ]
  );
};

const usernameText = (vm, h, currentRow, index) => {
  var time = "";
  var thread = 0;
  var reply = 0;
  var friend = 0;
  if (!!currentRow.forum_data) {
    var t = currentRow.forum_data.split("|");
    time = t[0];
    thread = t[1];
    reply = t[2];
    friend = t[3];
  }

  return h("div", {}, [
    h(
      "p",
      {
        style: {
          position: "relative",
        },
      },
      [
        h(
          "a",
          {
            attrs: {
              href:
                "https://forum.chasedream.com/space-username-" +
                currentRow.username +
                ".html",
              target: "_blank",
            },
          },
          currentRow.username
        ),
        h("Icon", {
          props: {
            type: "search",
            size: 18,
          },
          style: {
            color: "#1f85f4",
            verticalAlign: "middle",
            position: "absolute",
            right: 0,
            top: 0,
            cursor: "pointer",
          },
          on: {
            click: () => {
              vm.$emit("quick-search", currentRow, "username");
            },
          },
        }),
      ]
    ),
    h("p", {}, !!currentRow.forum_data ? time : ""),
    h("div", {}, [
      h(
        "a",
        {
          attrs: {
            href:
              "https://forum.chasedream.com/home.php?mod=space&uid=" +
              currentRow.uid +
              "&do=thread&view=me&type=thread&from=space",
            target: "_blank",
          },
          style: {
            display: "inline-block",
            marginRight: "5px",
          },
        },
        !!currentRow.forum_data ? "主 " + thread : ""
      ),
      h(
        "a",
        {
          attrs: {
            href:
              "https://forum.chasedream.com/home.php?mod=space&uid=" +
              currentRow.uid +
              "&do=thread&view=me&type=reply&from=space",
            target: "_blank",
          },
          style: {
            display: "inline-block",
            marginRight: "5px",
          },
        },
        !!currentRow.forum_data ? " 回 " + reply : ""
      ),
      h(
        "a",
        {
          attrs: {
            href:
              "https://forum.chasedream.com/home.php?mod=space&uid=" +
              currentRow.uid +
              "&do=friend&view=me&from=space",
            target: "_blank",
          },
          style: {
            display: "inline-block",
            marginRight: "5px",
          },
        },
        !!currentRow.forum_data ? " 友 " + friend : ""
      ),
    ]),
  ]);
};
const wechatText = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h(
      "div",
      {
        style: {
          position: "relative",
          /*textAlign: 'left'*/
        },
      },
      [
        h("p", {
          style: {
            cursor: "pointer",
            color: "#19be6b",
            paddingRight: "10px",
          },
          domProps: {
            innerHTML:
              '<span class="copyText" data-clipboard-action="copy" data-clipboard-text="' +
              currentRow.wechat +
              '">' +
              currentRow.wechat +
              "</span>",
          },
        }),
        h("Icon", {
          props: {
            type: "search",
            size: 18,
          },
          style: {
            color: "#1f85f4",
            verticalAlign: "middle",
            position: "absolute",
            right: 0,
            top: 0,
            cursor: "pointer",
            display: !!currentRow.wechat ? "block" : "none",
          },
          on: {
            click: () => {
              vm.$emit("quick-search", currentRow, "wechat");
            },
          },
        }),
      ]
    ),
    h("div", {
      style: {
        cursor: "pointer",
      },
      domProps: {
        innerHTML:
          '<span class="copyText" data-clipboard-action="copy" data-clipboard-text="' +
          currentRow.qq +
          '">' +
          currentRow.qq +
          "</span>",
      },
    }),
  ]);
};
const mobileText = (vm, h, currentRow, index) => {
  var area_code = currentRow.mobile.split("-")[0];
  var mobile_num = currentRow.mobile.split("-")[1];
  return h("div", {}, [
    h(
      "p",
      {
        style: {
          position: "relative",
        },
      },
      [
        h("span", {}, area_code),
        h(
          "a",
          {
            attrs: {
              href: "https://www.baidu.com/s?wd=" + mobile_num,
              target: "_blank",
            },
          },
          [
            h("Icon", {
              style: {
                color: "#1f85f4",
                transform: "rotate(-45deg)",
                webkitTransform: "rotate(-45deg)",
                mozTransform: "rotate(-45deg)",
                filter:
                  "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)",
                verticalAlign: "middle",
                position: "absolute",
                right: 0,
                top: 0,
              },
              props: {
                type: "link",
                size: 18,
              },
            }),
          ]
        ),
        h("Icon", {
          props: {
            type: "search",
            size: 18,
          },
          style: {
            color: "#1f85f4",
            verticalAlign: "middle",
            position: "absolute",
            right: "20px",
            top: 0,
            cursor: "pointer",
          },
          on: {
            click: () => {
              vm.$emit("quick-search", currentRow, "mobile");
            },
          },
        }),
      ]
    ),
    h("p", {
      style: {
        cursor: "pointer",
      },
      domProps: {
        innerHTML:
          '<span class="copyText" data-clipboard-action="copy" data-clipboard-text="' +
          mobile_num +
          '">' +
          mobile_num +
          "</span>",
      },
    }),
  ]);
};
const ipText = (vm, h, currentRow, index) => {
  return h(
    "div",
    {
      style: {},
    },
    [
      h(
        "p",
        {
          style: {
            position: "relative",
            textAlign: "left",
            margin: "0 -7px",
          },
        },
        [
          h(
            "span",
            {
              style: {
                cursor: "pointer",
              },
              domProps: {
                innerHTML:
                  '<span class="copyText" data-clipboard-action="copy" data-clipboard-text="' +
                  currentRow.ip +
                  '">' +
                  currentRow.ip +
                  "</span>",
              },
            },
            currentRow.ip
          ),
          h("Icon", {
            props: {
              type: "search",
              size: 18,
            },
            style: {
              color: "#1f85f4",
              verticalAlign: "middle",
              position: "absolute",
              right: 0,
              top: 0,
              cursor: "pointer",
            },
            on: {
              click: () => {
                vm.$emit("quick-search", currentRow, "ip");
              },
            },
          }),
        ]
      ),
      h(
        "p",
        {
          style: {},
        },
        !!currentRow.ip_location ? currentRow.ip_location : ""
      ),
    ]
  );
};
const idText = (vm, h, currentRow, index) => {
  return h("div", {}, [
    h(
      "p",
      {
        style: {
          color: "#19be6b",
        },
      },
      "[ " + currentRow.form_id + " ]"
    ),
    h(
      "p",
      {
        style: {
          color: "#2d8cf0",
          cursor: "pointer",
        },
        on: {
          click: () => {
            vm.$emit("details", currentRow);
          },
        },
      },
      currentRow.id
    ),
  ]);
};

export default {
  name: "canEditTable",
  props: {
    refs: String,
    columnsList: Array,
    value: Array,
    url: String,
    editIncell: {
      type: Boolean,
      default: false,
    },
    hoverShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      columns: [],
      thisTableData: [],
      edittingStore: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let vm = this;
      let editableCell = this.columnsList.filter((item) => {
        if (item.editable) {
          if (item.editable === true) {
            return item;
          }
        }
      });
      let cloneData = JSON.parse(JSON.stringify(this.value));
      let res = [];
      res = cloneData.map((item, index) => {
        let isEditting = false;
        if (this.thisTableData[index]) {
          if (this.thisTableData[index].editting) {
            isEditting = true;
          } else {
            for (const cell in this.thisTableData[index].edittingCell) {
              if (this.thisTableData[index].edittingCell[cell] === true) {
                isEditting = true;
              }
            }
          }
        }
        if (isEditting) {
          return this.thisTableData[index];
        } else {
          this.$set(item, "editting", false);
          let edittingCell = {};
          editableCell.forEach((item) => {
            edittingCell[item.key] = false;
          });
          this.$set(item, "edittingCell", edittingCell);
          return item;
        }
      });
      this.thisTableData = res;
      this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
      this.columnsList.forEach((item) => {
        if (item.editable) {
          item.render = (h, param) => {
            let currentRow = this.thisTableData[param.index];
            if (!currentRow.editting) {
              if (this.editIncell) {
                return h(
                  "Row",
                  {
                    props: {
                      type: "flex",
                      align: "middle",
                      justify: "center",
                    },
                  },
                  [
                    h(
                      "Col",
                      {
                        props: {
                          span: "22",
                        },
                      },
                      [
                        !currentRow.edittingCell[param.column.key]
                          ? h("span", currentRow[item.key])
                          : cellInput(this, h, param, item),
                      ]
                    ),
                    h(
                      "Col",
                      {
                        props: {
                          span: "2",
                        },
                      },
                      [
                        currentRow.edittingCell[param.column.key]
                          ? saveIncellEditBtn(this, h, param)
                          : incellEditBtn(this, h, param),
                      ]
                    ),
                  ]
                );
              } else {
                return h("span", currentRow[item.key]);
              }
            } else {
              return h("Input", {
                props: {
                  type: "text",
                  value: currentRow[item.key],
                },
                on: {
                  "on-change"(event) {
                    let key = param.column.key;
                    vm.edittingStore[param.index][key] = event.target.value;
                  },
                },
              });
            }
          };
        }
        if (item.handle) {
          item.render = (h, param) => {
            let currentRowData = this.thisTableData[param.index];
            let children = [];
            item.handle.forEach((item) => {
              if (item === "edit") {
                children.push(editButton(this, h, currentRowData, param.index));
              } else if (item === "delete") {
                children.push(
                  deleteButton(this, h, currentRowData, param.index)
                );
              } else if (item === "operate") {
                children.push(
                  operateButton(this, h, currentRowData, param.index)
                );
              } else if (item === "id") {
                children.push(idInput(this, h, currentRowData, param.index));
              } else if (item === "form_id") {
                children.push(
                  form_idInput(this, h, currentRowData, param.index)
                );
              } else if (item === "form_subject") {
                children.push(
                  form_subjectInput(this, h, currentRowData, param.index)
                );
              } else if (item === "type") {
                children.push(typeInput(this, h, currentRowData, param.index));
              } else if (item === "username") {
                children.push(
                  usernameInput(this, h, currentRowData, param.index)
                );
              } else if (item === "wechat") {
                children.push(
                  wechatInput(this, h, currentRowData, param.index)
                );
              } else if (item === "qq") {
                children.push(qqInput(this, h, currentRowData, param.index));
              } else if (item === "city") {
                children.push(cityInput(this, h, currentRowData, param.index));
              } else if (item === "mobile") {
                children.push(
                  mobileInput(this, h, currentRowData, param.index)
                );
              } else if (item === "ip") {
                children.push(ipInput(this, h, currentRowData, param.index));
              } else if (item === "status") {
                children.push(
                  statusInput(this, h, currentRowData, param.index)
                );
              } else if (item === "ps") {
                children.push(psInput(this, h, currentRowData, param.index));
              } else if (item === "note") {
                children.push(noteInput(this, h, currentRowData, param.index));
              } else if (item === "created") {
                children.push(
                  createdInput(this, h, currentRowData, param.index)
                );
              } else if (item === "opt") {
                children.push(optInput(this, h, currentRowData, param.index));
              } else if (item === "clear") {
                children.push(
                  clearButton(this, h, currentRowData, param.index)
                );
              } else if (item === "note_edit") {
                children.push(
                  note_editInput(this, h, currentRowData, param.index)
                );
              } else if (item === "username_text") {
                children.push(
                  usernameText(this, h, currentRowData, param.index)
                );
              } else if (item === "wechat_text") {
                children.push(wechatText(this, h, currentRowData, param.index));
              } else if (item === "mobile_text") {
                children.push(mobileText(this, h, currentRowData, param.index));
              } else if (item === "ip_text") {
                children.push(ipText(this, h, currentRowData, param.index));
              } else if (item === "id_text") {
                children.push(idText(this, h, currentRowData, param.index));
              }
            });
            return h("div", children);
          };
        }
      });
    },
    handleBackdata(data) {
      let clonedData = JSON.parse(JSON.stringify(data));
      clonedData.forEach((item) => {
        delete item.editting;
        delete item.edittingCell;
        delete item.saving;
      });
      return clonedData;
    },
    rowClassName(row, index) {
      //console.log(row)
      if (row.opt === 0) {
        return "";
      } else if (row.opt === 1) {
        return "row_green";
      } else if (row.opt === 2) {
        return "row_grey";
      } else if (row.opt === 3) {
        return "row_red";
      } else if (row.opt === 4) {
        return "row_purple";
      } else if (row.opt === 5) {
        return "row_orange";
      } else if (row.opt === 6) {
        return "row_l_grey";
      } else {
        return "";
      }
    },
  },
  watch: {
    value(data) {
      this.init();
    },
  },
};
</script>
