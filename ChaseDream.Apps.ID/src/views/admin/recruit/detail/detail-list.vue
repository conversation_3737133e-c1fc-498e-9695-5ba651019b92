<style lang="less">
@import "../../../../styles/common.less";
@import "../recruit.less";
</style>

<template>
  <div class="detail-list" style="margin: 0 -10px;min-width:1276px;">
    <Row v-if="isAccess">
      <Col span="24" class="">
        <Card>
          <div class="edittable-detail">
            <can-edit-table
              refs="table1"
              v-model="s_row"
              @on-search="getFormlist(1)"
              @on-updata="set_s_data"
              :columns-list="s_columns"
              class="searchTable"
            ></can-edit-table>
            <can-edit-table
              refs="table"
              @on-delete="handleDel"
              @on-updata="getFormlist(1)"
              @details="showDetails"
              v-model="rows"
              :columns-list="columns"
              @set-note="setNote"
              @quick-search="quick_search"
              @set-report="setReport"
              @get-report="getReport"
              @set-loading="setLoading"
              @set-opt="setOpt"
              class="listTable"
            ></can-edit-table>
          </div>
          <div class="page-bar">
            <Page
              :total="total"
              :page-size="page_size"
              :current="current"
              @on-change="getFormlist"
              v-if="showPage"
            ></Page>
          </div>
        </Card>
      </Col>
    </Row>
    <Modal v-model="details" title="招募明细详情" class="row_details">
      <p>
        <strong>ID</strong>
        <span>{{ row_details.id }}</span>
      </p>
      <p>
        <strong>PID</strong>
        <span>{{ row_details.pid }}</span>
      </p>
      <p>
        <strong>帖子ID</strong>
        <span>{{ row_details.thread_id }}</span>
      </p>
      <p>
        <strong>TID</strong>
        <span>{{ row_details.tid }}</span>
      </p>
      <p>
        <strong>表单ID</strong>
        <span>{{ row_details.form_id }}</span>
      </p>
      <p>
        <strong>表单名</strong>
        <span>{{ row_details.form_subject }}</span>
      </p>
      <p>
        <strong>表单类型</strong>
        <span>{{ row_details.type_name }}</span>
      </p>
      <p>
        <strong>微信</strong>
        <Input
          v-model="row_details.wechat"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>qq号码</strong>
        <Input
          v-model="row_details.qq"
          style="width: 300px;margin-bottom: 10px;"
        ></Input>
      </p>
      <p>
        <strong>考试日期</strong>
        <Input
          v-model="row_details.test_date"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>计划考试时间</strong>
        <Input
          v-model="row_details.test_plan"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>城市</strong>
        <Input
          v-model="row_details.city"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>当前情况</strong>
        <Input
          v-model="row_details.status1"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong> </strong>
        <Input
          v-model="row_details.status2"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong> </strong>
        <Input
          v-model="row_details.status3"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong> </strong>
        <Input
          v-model="row_details.status4"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>本科学校</strong>
        <Input
          v-model="row_details.bachelor"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>工作行业</strong>
        <Input
          v-model="row_details.industry"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>目前诉求</strong>
        <Input
          v-model="row_details.appeal"
          style="width: 300px;margin-bottom: 10px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>附言</strong>
        <Input
          v-model="row_details.ps"
          type="textarea"
          :rows="4"
          style="width: 300px;font-size: 14px;margin-bottom: 10px;"
        ></Input>
      </p>

      <p>
        <strong>备注</strong>
        <Input
          v-model="row_details.note"
          type="textarea"
          :rows="4"
          style="width: 300px;font-size: 14px;"
        ></Input>
      </p>
      <p>
        <strong>论坛用户名</strong>
        <span>{{ row_details.username }}</span>
      </p>
      <p>
        <strong>手机号码</strong>
        <span>{{ row_details.mobile }}</span>
      </p>
      <p>
        <strong>提交IP</strong>
        <span>{{ row_details.ip }}</span>
      </p>
      <p>
        <strong>提交时间</strong>
        <span>{{ row_details.created_at }}</span>
      </p>
      <div slot="footer">
        <Button type="primary" @click="saveInfo">保存</Button>
        <Button @click="close">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="show_report" title="记录">
      <div v-html="report_row"></div>
      <div slot="footer">
        <Button type="primary" size="small" @click="show_report = false"
          >关闭</Button
        >
      </div>
    </Modal>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import tData from "../data/recruit_data.js";
import canEditTable from "./components/canEditTable.vue";
import ClipboardJS from "clipboard";

export default {
  name: "detail_list",
  components: {
    canEditTable,
  },
  data() {
    return {
      rows: [],
      columns: [],
      s_columns: [],
      s_row: [
        {
          id: "",
          form_id: "",
          form_subject: "",
          type: -1,
          username: "",
          qq: "",
          mobile: "",
          ip: "",
          created: "",
          opt: -1,
        },
      ],
      total: 0,
      showPage: false,
      page_size: 20,
      current: 1,
      isAccess: true,
      row_details: {},
      details: false,
      show_report: false,
      report_row: "",
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "detail_list") {
        this.s_row = [
          {
            id: "",
            form_id: "",
            form_subject: "",
            type: -1,
            username: "",
            qq: "",
            mobile: "",
            ip: "",
            created: "",
            opt: -1,
          },
        ];
        this.row_details = {};
        this.details = false;
        this.show_report = false;
        this.report_row = "";
        this.getFormlist(1);
      }
    },
  },
  mounted() {
    this.getFormlist(1);
    var _this = this;
    this.clipboard = new ClipboardJS(".copyText");
    this.clipboard.on("success", function(e) {
      _this.$Message.success("复制成功！");
      e.clearSelection();
    });
    this.clipboard.on("error", (e) => {
      console.log(e);
      _this.$Message.error("复制失败！");
      e.clearSelection();
    });
  },
  destroyed() {
    this.clipboard.destroy();
  },
  methods: {
    getData() {
      this.columns = tData.dColumns;
      this.s_columns = tData.dsColumns;
    },
    getFormlist(n) {
      var s = [];
      var text = "";
      var str = window.location.href.split("?")[1];
      if (!!this.s_row[0].id) {
        s.push("id=" + this.s_row[0].id);
      }
      if (!!str) {
        if (str.split("=")[0] !== "id") {
          s.push(str);
        } else if (str.split("=")[0] === "id" && !this.s_row[0].id) {
          s.push(str);
        }
      }
      if (!!this.s_row[0].form_id) {
        s.push("form_id=" + this.s_row[0].form_id);
      }
      if (!!this.s_row[0].form_subject) {
        s.push("form_subject=" + this.s_row[0].form_subject);
      }
      if (this.s_row[0].type >= 0) {
        s.push("type=" + this.s_row[0].type);
      }
      if (!!this.s_row[0].username) {
        s.push("username=" + this.s_row[0].username);
      }
      if (!!this.s_row[0].wechat) {
        s.push("wechat=" + this.s_row[0].wechat);
      }
      if (!!this.s_row[0].qq) {
        s.push("qq=" + this.s_row[0].qq);
      }
      if (!!this.s_row[0].city) {
        s.push("city=" + this.s_row[0].city);
      }
      if (!!this.s_row[0].mobile) {
        s.push("mobile=" + this.s_row[0].mobile);
      }
      if (!!this.s_row[0].ip) {
        s.push("ip=" + this.s_row[0].ip);
      }
      if (!!this.s_row[0].ps) {
        s.push("ps=" + this.s_row[0].ps);
      }
      if (!!this.s_row[0].note) {
        s.push("note=" + this.s_row[0].note);
      }
      if (!!this.s_row[0].status) {
        s.push("status=" + this.s_row[0].status);
      }
      if (!!this.s_row[0].created) {
        s.push("created_at=" + this.s_row[0].created);
      }
      if (this.s_row[0].opt >= 0) {
        s.push("opt=" + this.s_row[0].opt);
      }

      if (s.length > 0) {
        text = "?" + s.join("&");
      }
      this.rows = [];
      this.total = 0;
      this.current = n;
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/detail" + text,
          method: "GET",
          params: {
            page: _this.current,
            page_size: _this.page_size,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.rows = res.data.data.rows;
            for (var i = 0; i < _this.rows.length; i++) {
              if (_this.rows[i].type === 0) {
                _this.rows[i].type_name = "GMAT";
              } else if (_this.rows[i].type === 7) {
                _this.rows[i].type_name = "GRE";
              } else if (_this.rows[i].type === 1) {
                _this.rows[i].type_name = "TOFEL";
              } else if (_this.rows[i].type === 2) {
                _this.rows[i].type_name = "IELTS";
              } else if (_this.rows[i].type === 3) {
                _this.rows[i].type_name = "MBA";
              } else if (_this.rows[i].type === 4) {
                _this.rows[i].type_name = "Master";
              } else if (_this.rows[i].type === 5) {
                _this.rows[i].type_name = "Career";
              } else if (_this.rows[i].type === 6) {
                _this.rows[i].type_name = "常规";
              }
            }
            _this.total = res.data.data.count;
            if (_this.total > _this.page_size) {
              _this.showPage = true;
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    getCity(str) {
      var _this = this;
      util
        .ajax({
          url: "https://id.chasedream.com/recruit",
          method: "POST",
          data: {
            ip_location: str,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            console.log(res);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    set_s_data(obj) {
      this.s_row = obj;
    },
    showDetails(row) {
      this.row_details = JSON.parse(JSON.stringify(row));
      this.details = true;
    },
    close() {
      this.row_details = {};
      this.details = false;
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
    },
    onCopy(e) {
      this.$Message.success({
        top: 200,
        content: "复制成功！",
      });
    },
    onError() {
      this.$Message.error({
        top: 200,
        content: "复制失败！",
      });
    },
    saveInfo() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/detail",
          method: "PUT",
          data: {
            id: this.row_details.id,
            wechat: this.row_details.wechat,
            qq: this.row_details.qq,
            test_date: this.row_details.test_date,
            test_plan: this.row_details.test_plan,
            city: this.row_details.city,
            status1: this.row_details.status1,
            status2: this.row_details.status2,
            status3: this.row_details.status3,
            status4: this.row_details.status4,
            bachelor: this.row_details.bachelor,
            industry: this.row_details.industry,
            appeal: this.row_details.appeal,
            ps: this.row_details.ps,
            note: this.row_details.note,
          },
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            _this.$Notice.success({
              title: "保存成功！",
            });
            _this.details = false;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    quick_search(row, str) {
      if (str === "mobile") {
        this.s_row = [
          {
            id: "",
            form_id: "",
            form_subject: "",
            type: -1,
            username: "",
            qq: "",
            mobile: row.mobile.split("-")[1],
            ip: "",
            created: "",
            opt: -1,
          },
        ];
      } else if (str === "wechat") {
        this.s_row = [
          {
            id: "",
            form_id: "",
            form_subject: "",
            type: -1,
            username: "",
            wechat: row.wechat,
            qq: "",
            mobile: "",
            ip: "",
            created: "",
            opt: -1,
          },
        ];
      } else if (str === "ip") {
        this.s_row = [
          {
            id: "",
            form_id: "",
            form_subject: "",
            type: -1,
            username: "",
            wechat: "",
            qq: "",
            mobile: "",
            ip: row.ip,
            created: "",
            opt: -1,
          },
        ];
      } else if (str === "username") {
        this.s_row = [
          {
            id: "",
            form_id: "",
            form_subject: "",
            type: -1,
            username: row.username,
            wechat: "",
            qq: "",
            mobile: "",
            ip: "",
            created: "",
            opt: -1,
          },
        ];
      }
      this.getFormlist(1);
    },
    setReport(row, i) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/detail/report",
          method: "POST",
          data: {
            id: row.id,
          },
        })
        .then(function(res) {
          console.log(res.data);
          if (res.data.msg === "success") {
            _this.$set(_this.rows[i], "isReport", true);
            _this.$set(_this.rows[i], "report", res.data.data.report);
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
          console.log(err);
        });
    },
    getReport(row, i) {
      this.show_report = true;
      this.report_row = row.report.split("\n").join("<br>");
    },
    setOpt(row, i, opt) {
      this.$set(this.rows[i], "opt", opt);
    },
    setNote(row, i, note) {
      this.$set(this.rows[i], "note", note);
      var list_data = [];
      list_data = JSON.parse(JSON.stringify(this.rows));
      this.rows = [];
      this.rows = JSON.parse(JSON.stringify(list_data));
    },
    setLoading(i) {
      this.$set(this.rows[i], "loading", true);
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style></style>
