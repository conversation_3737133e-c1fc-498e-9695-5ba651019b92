<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="release-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    创建用户特别行为记录
                </p>
                <Form ref="form" :model="form"  :label-width="90">
                    <FormItem label="昵称：">
                        <Input v-model="form.username" @on-blur="get_record_data" placeholder="请输入昵称"></Input>
                        <Table width="800" class="s_p_t" border :columns="col" :data="record_data" v-if="record_data.length > 0"></Table>
                    </FormItem>
                    <FormItem prop="type" label="记录类型：">
                        <div>
                            <span style="padding-right: 10px;">论坛</span>
                            <RadioGroup v-model="form.record_code" @on-change="getType">
                                <Radio :label="type.val" v-for="type in types[0]" :key="type.index">{{type.text}}</Radio>
                               <!-- <Radio label="论坛私信广告">论坛私信广告</Radio>
                                <Radio label="发帖内容有嫌疑持续追踪">发帖内容有嫌疑持续追踪</Radio>
                                <Radio label="论坛违法信息发布">论坛违法信息发布</Radio>
                                <Radio label="论坛恶意攻击他人">论坛恶意攻击他人</Radio>
                                <Radio label="论坛头像违规">论坛头像违规</Radio>
                                <Radio label="论坛签名等违规">论坛签名等违规</Radio>-->
                            </RadioGroup>
                        </div>
                        <div>
                            <span style="padding-right: 10px;">社群</span>
                            <RadioGroup v-model="form.record_code" @on-change="getType">
                                <Radio :label="type.val" v-for="type in types[1]" :key="type.index">{{type.text}}</Radio>
                                <!--<Radio label="群内广告">群内广告</Radio>
                                <Radio label="加群过多">加群过多</Radio>
                                <Radio label="群内僵尸">群内僵尸</Radio>
                                <Radio label="违规前科">违规前科</Radio>-->
                            </RadioGroup>
                        </div>
                        <div>
                            <span style="padding-right: 10px;">恢复</span>
                            <RadioGroup v-model="form.record_code" @on-change="getType">
                                <Radio :label="type.val" v-for="type in types[2]" :key="type.index">{{type.text}}</Radio>
                                <!--<Radio label="管理元错误操作，恢复清白">管理元错误操作，恢复清白</Radio>
                                <Radio label="用户知错就改，再给机会">用户知错就改，再给机会</Radio>-->
                            </RadioGroup>
                        </div>
                    </FormItem>
                    <FormItem label="具体描述：">
                        <Input type="textarea" v-model="form.desc" :autosize="{minRows: 4,maxRows: 6}" placeholder="请输入具体描述"></Input>
                    </FormItem>
                    <FormItem label="">
                        <div class="attach_upload">
                            <Upload action="/api/v1/admin/behavior/upload"
                                    :before-upload="uploadImg"
                                    :show-upload-list="true"
                            >
                                <Button type="ghost" class=""><Icon type="link" size="20"></Icon>上传附件</Button>
                            </Upload>
                            <div class="file-list" v-if="files.length > 0">
                                <div v-for="file in files">
                                    <Icon type="document-text" size="14"></Icon>
                                    {{file.name}}
                                    <!--<img :src="file.src" alt="">-->
                                    <span class="del">
                                        <Icon type="close-round" @click="delImg(file)" size="14">删除</Icon>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </FormItem>
                    <FormItem prop="type" label="操作行为：">
                        <CheckboxGroup v-model="form.behavior" @on-change="">
                            <Checkbox :label="behavior.opt_id" v-for="behavior in behaviors" :key="behavior.index">{{behavior.opt_text}}</Checkbox>
                        </CheckboxGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">发布</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'behavior_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    username: '',
                    record_type: '',
                    record_code: '',
                    desc: '',
                    attach: '',
                    opt_behavior: [],
                    behavior: []
                },
                types: [
                    [
                       {
                           val: '1.0',
                           text: '论坛发广告帖'
                       },
                       {
                           val: '1.1',
                           text: '论坛私信广告'
                       },
                       {
                           val: '1.2',
                           text: '发帖内容有嫌疑持续追踪'
                       },
                       {
                           val: '1.3',
                           text: '论坛违法信息发布'
                       },
                       {
                           val: '1.4',
                           text: '论坛恶意攻击他人'
                       },
                       {
                           val: '1.5',
                           text: '论坛头像违规'
                       },
                       {
                           val: '1.6',
                           text: '论坛签名等违规'
                       }
                   ],
                    [
                        {
                            val: '2.0',
                            text: '群内广告'
                        },
                        {
                            val: '2.1',
                            text: '加群过多'
                        },
                        {
                            val: '2.2',
                            text: '群内僵尸'
                        },
                        {
                            val: '2.3',
                            text: '违规前科'
                        }
                    ],
                    [
                        {
                            val: '3.0',
                            text: '管理元错误操作，恢复清白'
                        },
                        {
                            val: '3.1',
                            text: '用户知错就改，再给机会'
                        }
                    ]
                ],
                behaviors: [
                    {
                        opt_id: 1,
                        opt_text: '持续追踪'
                    },
                    {
                        opt_id: 2,
                        opt_text: '论坛禁言'
                    },
                    {
                        opt_id: 3,
                        opt_text: '论坛锁定'
                    },
                    {
                        opt_id: 4,
                        opt_text: '禁止访问'
                    },
                    {
                        opt_id: 5,
                        opt_text: '从群里踢出'
                    },
                    {
                        opt_id: 6,
                        opt_text: '拒绝入群'
                    },
                    {
                        opt_id: 7,
                        opt_text: '永久禁加群'
                    },
                    {
                        opt_id: 8,
                        opt_text: '无操作，无记录'
                    },
                    {
                        opt_id: 9,
                        opt_text: '恢复正常'
                    }
                ],
                col: [
                    {
                        title: 'UID',
                        key: 'id',
                        width: 40,
                        align: 'center'
                    },
                    {
                        title: '电话',
                        key: 'mobile',
                        width: 110,
                        align: 'center'
                    },
                    {
                        title: 'Email',
                        key: 'email',
                        width: 110
                    },
                    {
                        title: '分类',
                        key: 'record_type',
                        width: 95
                    },
                    {
                        title: '操作',
                        key: 'opt_behavior',
                        width: 130,
                        render: (h, params) => {
                            var opt_text = []
                            for(var i=0;i<params.row.opt.length;i++){
                                opt_text.push(params.row.opt[i].opt_text)
                            }
                            return h('span',{},opt_text.join('、'))
                        }
                    },
                    {
                        title: '描述',
                        key: 'desc',
                    },
                    {
                        title: '操作人',
                        key: 'operator',
                        width: 80,
                        align: 'center'
                    },
                    {
                        title: '处理时间',
                        key: 'time',
                        width: 80,
                        render: (h, params) => {
                            var text = params.row.created_at.split('T').join(' ').split(':')
                            text.pop();
                            return h('span',{},text.join(':'))
                        }
                    }
                ],
                record_data: [],
                files: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'behavior_add'){
                    this.form.username = '';
                    this.form.record_type = '';
                    this.form.record_code = '';
                    this.form.desc = '';
                    this.form.attach = '';
                    this.form.opt_behavior = [];
                    this.record_data = [];
                    this.files = [];
                    this.$refs['form'].resetFields();
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getData();
        },
        methods: {
            getData(){
            },
            getType(e){
                //console.log(e)
            },
            get_record_data (){
                if(!this.form.username){
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/behavior/search/' + this.form.username,
                    method:'GET',
                    params: {
                        /*page: _this.current,
                        page_size: _this.page_size*/
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.record_data = res.data.data;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {
                if(!this.form.username){
                    this.$Notice.error({
                        title: '昵称不能为空！'
                    });
                    return false;
                }
                if(!this.form.record_code){
                    this.$Notice.error({
                        title: '请选择记录类型！'
                    });
                    return false;
                }
                if(!this.form.behavior.length){
                    this.$Notice.error({
                        title: '请选择操作行为！'
                    });
                    return false;
                }
                this.form.opt_behavior = [];
                if(this.form.behavior.length > 0){
                    for(var i=0;i<this.form.behavior.length;i++){
                        for(var j=0;j<this.behaviors.length;j++){
                            if(this.form.behavior[i] === this.behaviors[j].opt_id){
                                this.form.opt_behavior.push(this.behaviors[j]);
                            }
                        }
                    }
                }
                var dataIn = {
                    username: this.form.username,
                    record_type: this.types[this.form.record_code.split('.')[0]-1][this.form.record_code.split('.')[1]].text,
                    record_code: this.form.record_code,
                    desc: this.form.desc,
                    attach: this.form.attach,
                    opt_behavior: this.form.opt_behavior
                }
                console.log(dataIn)
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/behavior',
                    method:'POST',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'behavior_add');
                        _this.$store.commit('closePage', 'behavior_add');
                        _this.$router.push({
                            name: 'behavior_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            uploadImg (file){
                console.log(file)
                var form = new FormData();
                form.append('file', file)
                const _this = this;
                util.ajax({
                    url:'/api/v1/admin/behavior/upload',
                    method:'POST',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                    data: form
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$Notice.success({
                            title: '上传成功'
                        });
                        _this.form.attach = res.data.data.fullpath;
                        _this.files = [];
                        _this.files.push({
                            src: res.data.data.fullpath,
                            name: file.name
                        })
                        _this.errMsg = '';
                    }else{
                        console.log(res)
                        _this.$Notice.error({
                            title: res.data.errors.message
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
                return false;
            },
            delImg (file){
                this.files = [];
                this.form.attach = '';
            },
            closePage () {
                this.form.username = '';
                this.form.record_type = '';
                this.form.record_code = '';
                this.form.desc = '';
                this.form.attach = '';
                this.form.opt_behavior = [];
                //this.types = [];
                //this.behaviors = [];
                this.$store.commit('removeTag', 'behavior_add');
                this.$router.push({
                    name: 'behavior_list'
                });
            }
        }
    };
</script>

<style>

</style>
