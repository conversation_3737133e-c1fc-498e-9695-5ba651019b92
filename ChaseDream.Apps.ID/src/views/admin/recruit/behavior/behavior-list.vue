<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="behavior-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/recruit/behavior-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="detail-list">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getFormlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>
                    <can-edit-table class="edittable-detail listTable" refs="table" @on-delete="handleDel" v-model="rows" :columns-list="columns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getFormlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/recruit_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'behavior_list',
        components: {
            canEditTable
        },
        data () {
            return {
                rows: [],
                columns: [],
                s_columns: [],
                s_row: [{
                    id: '',
                    username: '',
                    mobile: '',
                    email: '',
                    record_type: '',
                    opt: '',
                    desc: '',
                    created: '',
                    operator: ''
                }],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'behavior_list'){
                    var s_row = [{
                        id: '',
                        username: '',
                        mobile: '',
                        email: '',
                        record_type: '',
                        opt: '',
                        desc: '',
                        created: '',
                        operator: ''
                    }];
                    this.getFormlist(1);
                }
            }
        },
        mounted () {
            this.getFormlist(1);
        },
        methods: {
            getData () {
                this.columns = tData.bColumns;
                this.s_columns = tData.bsColumns;
            },
            getFormlist (n){
                var s = [];
                var text = '';
                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].username){
                    s.push('username=' + this.s_row[0].username);
                }
                if(!!this.s_row[0].mobile){
                    s.push('mobile=' + this.s_row[0].mobile);
                }
                if(!!this.s_row[0].email){
                    s.push('email=' + this.s_row[0].email);
                }
                if(!!this.s_row[0].record_type){
                    s.push('record_type=' + this.s_row[0].record_type);
                }
                if(this.s_row[0].opt > 0){
                    s.push('opt_id=' + this.s_row[0].opt);
                }
                if(!!this.s_row[0].desc){
                    s.push('desc=' + this.s_row[0].desc);
                }
                if(!!this.s_row[0].created){
                    s.push('created_at=' + this.s_row[0].created);
                }
                if(!!this.s_row[0].operator){
                    s.push('operator=' + this.s_row[0].operator);
                }

                if(s.length > 0){
                    text ='?' + s.join('&')
                }
                this.rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/behavior' + text,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
