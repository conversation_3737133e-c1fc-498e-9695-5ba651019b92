<style lang="less">
@import "../../../../styles/common.less";
@import "../recruit.less";
</style>

<template>
  <div class="form-edit">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card class="pr_form">
          <p slot="title">
            <Icon type="android-create"></Icon>
            编辑表单
          </p>
          <Form ref="form" :model="form" :label-width="90">
            <FormItem prop="subject" label="标题：">
              <Input
                v-model="form.subject"
                placeholder="请输入表单标题"
              ></Input>
            </FormItem>
            <FormItem prop="type" label="类型：">
              <RadioGroup v-model="form.type">
                <Radio :label="0">GMAT</Radio>
                <Radio :label="7">GRE</Radio>
                <Radio :label="1">TOFEL</Radio>
                <Radio :label="2">IELTS</Radio>
                <Radio :label="3">MBA</Radio>
                <Radio :label="4">Master</Radio>
                <Radio :label="5">Career</Radio>
                <Radio :label="6">常规</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem prop="element" label="元素：" class="verticalChx">
              <Checkbox v-model="form.wechat" :true-value="1" :false-value="0"
                >微信号</Checkbox
              >
              <Checkbox v-model="form.qq" :true-value="1" :false-value="0"
                >QQ号</Checkbox
              >
              <Checkbox
                v-model="form.test_date"
                :true-value="1"
                :false-value="0"
                >考试日期</Checkbox
              >
              <Checkbox
                v-model="form.test_plan"
                :true-value="1"
                :false-value="0"
                >计划何时考试</Checkbox
              >
              <Checkbox v-model="form.city" :true-value="1" :false-value="0"
                >城市</Checkbox
              >
              <Checkbox v-model="form.status" :true-value="1" :false-value="0"
                >当前情况</Checkbox
              >
              <Checkbox v-model="form.bachelor" :true-value="1" :false-value="0"
                >本科学校</Checkbox
              >
              <Checkbox v-model="form.industry" :true-value="1" :false-value="0"
                >工作行业</Checkbox
              >
              <Checkbox v-model="form.appeal" :true-value="1" :false-value="0"
                >目前诉求</Checkbox
              >
              <Checkbox v-model="form.ps" :true-value="1" :false-value="0"
                >附言</Checkbox
              >
            </FormItem>
            <p class="f_err_msg">{{ errMsg }}</p>
            <FormItem class="btns">
              <Button @click="handleSubmit" type="primary">提交</Button>
              <Button @click="closePage">取消</Button>
              <!--<Button @click="delRow" type="error" style="float: right;">删除</Button>-->
              <Poptip
                confirm
                placement="top-end"
                :transfer="true"
                title="您确定要删除这条数据吗?"
                @on-ok="delRow"
                ok-text="确定"
                cancel-text="取消"
              >
                <Button type="error" style="float: right;">删除</Button>
              </Poptip>
            </FormItem>
          </Form>
          <div class="preview">
            <h3>元素效果预览</h3>
            <Form label-position="top">
              <FormItem label="">
                <Input placeholder="微信号" width="300"></Input>
              </FormItem>
              <FormItem label="">
                <Input placeholder="QQ号"></Input>
              </FormItem>
              <FormItem label="">
                <!--<Input placeholder="选择考试日期"></Input>-->
                <FormItem prop="date">
                  <DatePicker
                    type="date"
                    placeholder="选择考试日期"
                  ></DatePicker>
                </FormItem>
              </FormItem>
              <FormItem label="计划何时考试（可多选）">
                <CheckboxGroup>
                  <Checkbox label="15天内"></Checkbox>
                  <Checkbox label="16天-1个月"></Checkbox>
                  <Checkbox label="1-2个月"></Checkbox>
                  <Checkbox label="2-3个月"></Checkbox>
                  <Checkbox label="3个月以上"></Checkbox>
                </CheckboxGroup>
              </FormItem>
              <FormItem label="">
                <Select placeholder="请选择城市">
                  <Option value="beijing">北京</Option>
                  <Option value="shanghai">上海</Option>
                  <Option value="shenzhen">深圳</Option>
                  <Option value="guangzhou">广州</Option>
                </Select>
              </FormItem>
              <FormItem label="当前情况（可多选）">
                <CheckboxGroup>
                  <Checkbox label="正在准备考GMAT"></Checkbox>
                  <Checkbox label="正在准备GRE"></Checkbox>
                  <br />
                  <Checkbox label="正在准备考托福"></Checkbox>
                  <Checkbox label="正在准备考雅思"></Checkbox>
                  <br />
                  <Checkbox label="申请MBA"></Checkbox>
                  <Checkbox label="申请Master"></Checkbox>
                  <Checkbox label="申请PhD"></Checkbox>
                  <Checkbox label="申请完成等待结果"></Checkbox>
                  <br />
                  <Checkbox label="商学院在读（非本科）"></Checkbox>
                  <Checkbox label="已经从商学院毕业（非本科）"></Checkbox>
                  <Checkbox label="寻求职业发展"></Checkbox>
                  <br />
                  <Checkbox label="随便看看"></Checkbox>
                </CheckboxGroup>
              </FormItem>
              <FormItem label="">
                <Input placeholder="本科学校" width="300"></Input>
              </FormItem>
              <FormItem label="">
                <Select placeholder="工作行业">
                  <Option value="咨询">咨询</Option>
                  <Option value="金融">金融</Option>
                  <Option value="科技">科技</Option>
                  <Option value="消费">消费</Option>
                  <Option value="NGO">NGO</Option>
                  <Option value="地产">地产</Option>
                  <Option value="能源">能源</Option>
                  <Option value="医疗">医疗</Option>
                  <Option value="大学在读">大学在读</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </FormItem>
              <FormItem label="目前诉求（可多选）">
                <CheckboxGroup>
                  <Checkbox label="了解不同行业"></Checkbox>
                  <Checkbox label="认识新朋友"></Checkbox>
                  <Checkbox label="寻求职业转换"></Checkbox>
                  <Checkbox label="随便看看"></Checkbox>
                </CheckboxGroup>
              </FormItem>
              <FormItem label="">
                <Input
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="附言"
                ></Input>
              </FormItem>
            </Form>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

export default {
  name: "form_edit",
  components: {},
  data() {
    return {
      form: {},
      id: 0,
      errMsg: "",
      access_token: this.$store.state.user.access_token,
      isAccess: true,
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "form_edit") {
        this.form = {};
        this.id = window.location.href.split("/").pop();
        if (!this.id) {
          this.$router.push({
            name: "form_list",
          });
        }

        this.getData();
      }
    },
  },
  mounted() {
    if (parseInt(Cookies.get("uAccess")) === 1) {
      this.isAccess = false;
    }
    //this.userform = JSON.parse(Cookies.get('currentRow'));
    this.form = {};
    this.id = window.location.href.split("/").pop();
    if (!this.id) {
      this.$router.push({
        name: "form_list",
      });
    }
    this.getData();
  },
  methods: {
    getData() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/form/" + this.id,
          method: "GET",
          params: {},
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            _this.form = res.data.data;
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          var _this = this;
          util
            .ajax({
              url: "/api/v1/admin/recruit/form",
              method: "PUT",
              data: {
                id: _this.form.id,
                subject: _this.form.subject,
                type: _this.form.type,
                wechat: _this.form.wechat,
                qq: _this.form.qq,
                test_date: _this.form.test_date,
                test_plan: _this.form.test_plan,
                city: _this.form.city,
                status: _this.form.status,
                ps: _this.form.ps,
                bachelor: _this.form.bachelor,
                industry: _this.form.industry,
                appeal: _this.form.appeal,
              },
            })
            .then(function(res) {
              //console.log(res.data);
              if (res.data.msg === "success") {
                _this.$store.commit("removeTag", "form_edit");
                _this.$store.commit("closePage", "form_edit");
                _this.$router.push({
                  name: "form_list",
                });
              } else {
              }
            })
            .catch(function(err) {
              console.log(err);
            });
        }
      });
    },
    delRow() {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/recruit/form",
          method: "DELETE",
          data: {
            id: this.form.id,
          },
        })
        .then(function(res) {
          //console.log(res.data);
          if (res.data.msg === "success") {
            _this.$store.commit("removeTag", "form_edit");
            _this.$store.commit("closePage", "form_edit");
            _this.$router.push({
              name: "form_list",
            });
          } else {
          }
        })
        .catch(function(err) {
          console.log(err);
        });
    },
    closePage() {
      this.form = {};
      this.$store.commit("removeTag", "form_edit");
      this.$store.commit("closePage", "form_edit");
      this.$router.push({
        name: "form_list",
      });
    },
  },
};
</script>

<style></style>
