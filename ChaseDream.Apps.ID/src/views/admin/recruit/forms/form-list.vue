<style lang="less">
    @import '../../../../styles/common.less';
    @import '../recruit.less';
</style>

<template>
    <div class="form-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/recruit/form-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getFormlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>
                    <can-edit-table refs="table" @on-delete="handleDel" v-model="rows" :columns-list="columns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getFormlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/recruit_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'form_list',
        components: {
            canEditTable
        },
        data () {
            return {
                rows: [],
                columns: [],
                s_columns: [],
                s_row: [{
                    id: '',
                    subject: '',
                    type: -1,
                    created: ''
                }],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'form_list'){
                    this.s_row = [{
                        id: '',
                        subject: '',
                        type: -1,
                        created: ''
                    }];
                    this.getFormlist(1);
                }
            }
        },
        mounted () {
            this.getFormlist(1);
        },
        methods: {
            getData () {
                this.columns = tData.fColumns;
                this.s_columns = tData.fsColumns;
            },
            getFormlist (n){
                var s = [];
                var text = '';
                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].subject){
                    s.push('subject=' + this.s_row[0].subject);
                }
                if(this.s_row[0].type >= 0){
                    s.push('type=' + this.s_row[0].type + '');
                }
                if(!!this.s_row[0].created){
                    s.push('created_at=' + this.s_row[0].created);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }
                //console.log(test)
                this.rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/recruit/form'+ text,
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.rows = res.data.data.rows;
                        for(var i=0;i<_this.rows.length;i++){
                            _this.rows[i].elements = [];
                            if(!!_this.rows[i].wechat){
                                _this.rows[i].elements.push('微信号');
                            }
                            if(!!_this.rows[i].qq){
                                _this.rows[i].elements.push('QQ号');
                            }
                            if(!!_this.rows[i].test_date){
                                _this.rows[i].elements.push('考试日期');
                            }
                            if(!!_this.rows[i].test_plan){
                                _this.rows[i].elements.push('考试计划');
                            }
                            if(!!_this.rows[i].city){
                                _this.rows[i].elements.push('城市');
                            }
                            if(!!_this.rows[i].status){
                                _this.rows[i].elements.push('当前情况');
                            }
                            if(!!_this.rows[i].ps){
                                _this.rows[i].elements.push('附言');
                            }
                            if(!!_this.rows[i].bachelor){
                                _this.rows[i].elements.push('本科学校');
                            }
                            if(!!_this.rows[i].industry){
                                _this.rows[i].elements.push('工作行业');
                            }
                            if(!!_this.rows[i].appeal){
                                _this.rows[i].elements.push('目前诉求');
                            }
                        }
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
