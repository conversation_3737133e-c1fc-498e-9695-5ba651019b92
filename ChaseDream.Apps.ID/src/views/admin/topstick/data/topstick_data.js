import util from '@/libs/util.js';
export const tsColumns = [
    {
        title: 'ID',
        key: 'id',
        //key: 'success',
        width: 60,
        align: 'center',
        /*render: (h, params) => {
            var style = {}
            if(!params.row.success){
                style.color = '#ed3f14';
            }else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size:14
                }
            })
        }*/
    },
    {
        title: '帖子ID',
        key: 'tid',
        align: 'center',
        width: 100,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                    target: '_blank'
                }
            }, params.row.tid)
        }
    },
    {
        title: '标题',
        align: 'left',
        key: 'subject'
    },
    {
        title: 'MIN (分钟)',
        align: 'center',
        key: 'min',
        width: 100
    },
    {
        title: 'MAX (分钟)',
        align: 'center',
        key: 'max',
        width: 100
    },
    {
        title: '下次发送时间',
        align: 'center',
        key: 'next_tick',
        width: 180
    },
    {
        title: '发送次数',
        align: 'center',
        width: 100,
        key: 'total'
    },
    {
        title: '操作',
        align: 'center',
        width: 130,
        key: 'handle',
        handle: ['edit', 'delete']  //,'delete'
    }
];
export const uColumns = [
    {
        title: 'ID',
        width: 80,
        align: 'center',
        key: 'id'
    },
    {
        title: '账号',
        align: 'center',
        key: 'username',
        /*width: 80*/
    },
    {
        title: '昵称',
        align: 'center',
        key: 'nickname',
        width: 150,
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: `https://forum.chasedream.com/space-username-${params.row.nickname}.html`,
                    target: '_blank'
                }
            }, params.row.nickname)
        }
    },
    {
        title: '密码',
        align: 'center',
        width: 180,
        key: 'handle',
        handle: ['view']
    },
    {
        title: '状态',
        align: 'center',
        width: 80,
        key: 'status',
        render: (h, params) => {
            var style = {}
            if (!params.row.status) {
                style.color = '#ed3f14';
            } else {
                style.color = '#19be6b';
            }
            return h('Icon', {

                style: style,
                props: {
                    type: 'record',
                    size: 14
                }
            })
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['edit', 'delete']
    },
]
export const mColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    {
        title: '话术',
        align: 'center',
        key: 'message',  //  username
        /*width: 80*/
    },
    /*{
     title: '手机号码',
     align: 'center',
     key: 'mobile'
     },*/
    {
        title: '操作',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['edit', 'delete']
    },
]

const tData = {
    tsColumns: tsColumns,
    uColumns: uColumns,
    mColumns: mColumns
};

export default tData;
