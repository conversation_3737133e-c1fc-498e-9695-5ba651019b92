<style lang="less">
    @import '../../../../styles/common.less';
    @import '../topstick.less';
</style>

<template>
    <div class="setinterval">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <h3>设置间隔时间</h3>
                <br>
                <Form ref="form" :model="form" :rules="rules" :label-width="0">
                    <FormItem>
                        <Row>
                            <Col span="11">
                            <FormItem prop="mintime">
                                <Input v-model="form.min" placeholder="最小时间" @on-change=""></Input>
                            </FormItem>
                            </Col>
                            <Col span="2" style="text-align: center">-</Col>
                            <Col span="11">
                            <FormItem prop="maxtime">
                                <Input v-model="form.max" placeholder="最大时间" @on-change=""></Input>
                            </FormItem>
                            </Col>
                        </Row>
                    </FormItem>
                    <FormItem>
                        <Button type="primary" @click="handleSubmit()">提 交</Button>
                        <Button type="ghost" @click="handleReset()" style="margin-left: 8px">重 置</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'setinterval',
        components: {

        },
        data () {
            return {
                form: {
                    min: '',
                    max: '',
                    id: 0
                },
                isAccess: true,
                rules: {
                    min: [
                        { required: true, message: '最小时间不能为空', trigger: 'blur' },
                        /*{ type: 'string', min: 1, message: '用户名不能小于3个字符', trigger: 'blur' }*/
                    ],
                    max: [
                        { required: true, message: '最大时间不能为空', trigger: 'blur' },
                        /*{ type: 'string', min: 3, message: '新用户名不能小于3个字符', trigger: 'blur' }*/
                    ]
                }
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'setinterval'){
                    this.handleReset();
                    this.getSetting();
                }
            }
        },
        mounted () {
            this.getSetting();
        },
        methods: {
            handleSubmit (){
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        console.log('is valid !')
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/top_post/setting',
                            method:'PUT',
                            data: {
                                min: _this.form.min,
                                max: _this.form.max,
                                id: _this.form.id ? _this.form.id : null
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                console.log(res.data)
                                _this.$Notice.success({
                                    title: '设置成功！'
                                });
                                //_this.form = res.data.data;
                            }else{
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            console.log(err)
                            _this.$Notice.error({
                                title: err.msg
                            });
                        });
                    }
                })
            },
            getSetting (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/top_post/setting',
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.form = res.data.data;

                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleReset (){
                this.form.min = '';
                this.form.max = '';
                this.form.id = 0;
            }
        },
        created () {

        }
    };
</script>

<style>

</style>
