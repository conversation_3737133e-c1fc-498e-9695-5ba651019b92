<style lang="less">
@import "../../../../styles/common.less";
@import "../topstick.less";
</style>

<template>
  <div class="stick-users">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <div class="tool-bar" style="position: relative;margin-bottom: 20px;">
            <router-link to="/console/topstick/stick-user-add">
              <Button type="success" icon="plus">添 加</Button>
            </router-link>
            <Input
              v-model="keyword"
              @on-enter="searchUsers('search')"
              placeholder="请输入用户昵称"
              style="width:400px;margin-bottom: 10px;position: absolute;right: 0;top:0;margin-top: 0;"
            >
              <Button
                slot="append"
                icon="ios-search"
                @click="searchUsers('search')"
              ></Button>
            </Input>
          </div>
          <div class="edittable-con-1">
            <can-edit-table
              refs="uTable"
              @on-delete="handleDel"
              @showPassword="showPassword"
              v-model="uRows"
              :columns-list="uColumns"
            ></can-edit-table>
          </div>
          <div class="page-bar">
            <Page
              :total="total"
              :page-size="page_size"
              :current="current"
              @on-change="getUserslist"
              show-elevator
              v-if="showPage"
            ></Page>
          </div>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import tData from "../data/topstick_data.js";
import canEditTable from "./components/canEditTable.vue";

export default {
  name: "stick_users",
  components: {
    canEditTable,
  },
  data() {
    return {
      uRows: [],
      uColumns: [],
      total: 0,
      showPage: false,
      page_size: 20,
      current: 1,
      isAccess: true,
      keyword: "",
      s_key: "",
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "stick_users") {
        this.keyword = "";
        this.getUserslist(1);
      }
    },
  },
  mounted() {
    this.getUserslist(1);
  },
  methods: {
    getData() {
      this.uColumns = tData.uColumns;
    },
    getUserslist(n) {
      this.uRows = [];
      this.total = 0;
      this.current = n;
      var url = "/api/v1/admin/top_post/user";
      if (this.keyword != "") {
        url = "/api/v1/admin/top_post/user?s=" + this.keyword;
      }

      var _this = this;
      util
        .ajax({
          url: url,
          method: "GET",
          params: {
            page: _this.current,
            page_size: _this.page_size,
          },
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            _this.uRows = res.data.data.rows;
            for (var i = 0; i < _this.uRows.length; i++) {
              _this.uRows[i].uPassword = "";
            }
            _this.total = res.data.data.count;
            if (_this.total > _this.page_size) {
              _this.showPage = true;
            }
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    showPassword(row) {
      var _this = this;
      util
        .ajax({
          url: "/api/v1/admin/top_post/password",
          method: "POST",
          data: {
            id: row.id,
          },
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            _this.$Notice.info({
              title: res.data.data.password,
            });
          } else {
            _this.$Notice.error({
              title: res.msg,
            });
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.$Notice.error({
            title: err.msg,
          });
        });
    },
    searchUsers() {
      this.getUserslist(1);
    },
    handleDel(val, index) {
      this.$Message.success("删除了第" + (index + 1) + "行数据");
    },
  },
  created() {
    this.getData();
  },
};
</script>

<style></style>
