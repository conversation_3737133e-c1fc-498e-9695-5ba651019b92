<style lang="less">
@import "../../../../styles/common.less";
@import "../topstick.less";
</style>

<template>
  <div class="stick-add">
    <Row v-if="isAccess">
      <Col span="24" class="padding-left-10">
        <Card>
          <h3>添加帖子</h3>
          <br />
          <Form ref="form" :model="form" :rules="rules" :label-width="90">
            <FormItem prop="tid" label="帖子ID">
              <Input
                v-model="form.tid"
                placeholder="帖子ID"
                @on-change=""
              ></Input>
            </FormItem>
            <FormItem prop="min" label="MIN">
              <Input v-model="form.min" placeholder="MIN" @on-change=""></Input>
            </FormItem>
            <FormItem prop="max" label="MAX">
              <Input v-model="form.max" placeholder="MAX" @on-change=""></Input>
            </FormItem>
            <FormItem>
              <Button type="primary" @click="handleSubmit()">提 交</Button>
              <Button type="ghost" @click="closePage()" style="margin-left: 8px"
                >取 消</Button
              >
            </FormItem>
          </Form>
        </Card>
      </Col>
    </Row>
    <p v-if="!isAccess">您没有权限访问此页</p>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";

export default {
  name: "stick_add",
  components: {},
  data() {
    return {
      form: {
        tid: "",
        min: "",
        max: "",
      },
      isAccess: true,
      rules: {
        tid: [{ required: true, message: "帖子ID不能为空" }],
        min: [{ required: true, message: "MIN不能为空" }],
        max: [{ required: true, message: "MAX不能为空" }],
      },
    };
  },
  computed: {},
  watch: {
    $route(to) {
      if (to.name === "stick_add") {
        this.form.tid = "";
        this.form.min = "";
        this.form.max = "";
      }
    },
  },
  mounted() {},
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          var _this = this;
          util
            .ajax({
              url: "/api/v1/admin/top_post/thread",
              method: "POST",
              data: {
                tid: _this.form.tid,
                min: _this.form.min,
                max: _this.form.max,
              },
            })
            .then(function(res) {
              if (res.data.msg === "success") {
                _this.$store.commit("removeTag", "stick_add");
                _this.$store.commit("closePage", "stick_add");
                _this.$router.push({
                  name: "stick_list",
                });
              } else {
                //_this.errMsg = res.msg;
                _this.$Notice.error({
                  title: res.msg,
                });
              }
            })
            .catch(function(err) {
              //_this.errMsg = err.msg;
              _this.$Notice.error({
                title: err.msg,
              });
              console.log(err);
            });
        }
      });
    },
    closePage() {
      this.form.tid = "";
      this.$store.commit("removeTag", "stick_add");
      this.$router.push({
        name: "stick_list",
      });
    },
  },
  created() {},
};
</script>

<style></style>
