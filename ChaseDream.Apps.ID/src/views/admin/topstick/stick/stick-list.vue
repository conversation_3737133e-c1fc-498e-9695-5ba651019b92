<style lang="less">
    @import '../../../../styles/common.less';
    @import '../topstick.less';
</style>

<template>
    <div class="stick-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/topstick/stick-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="uTable" @on-delete="handleDel" v-model="tsRows" :columns-list="tsColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getSticklist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/topstick_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'stick_list',
        components: {
            canEditTable
        },
        data () {
            return {
                tsRows: [],
                tsColumns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'stick_list'){
                    this.getSticklist(1);
                }
            }
        },
        mounted () {
            this.getSticklist(1);
        },
        methods: {
            getData () {
                this.tsColumns = tData.tsColumns;
            },
            getSticklist (n){
                this.tsRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/top_post/thread',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.tsRows = res.data.data.rows;
                        for(var i=0;i<_this.tsRows.length;i++){
                            var t = parseInt(_this.tsRows[i].next_tick + '000')
                            _this.tsRows[i].next_tick = new Date(t).getFullYear() + '-' + add0((new Date(t).getMonth() + 1)) + '-' + add0(new Date(t).getDate()) + ' ' + add0(new Date(t).getHours()) + ':' + add0(new Date(t).getMinutes());
                        }
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                        function add0(n) {
                            if(parseInt(n) < 10){
                                return '0'+ n;
                            }else {
                                return n;
                            }
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
