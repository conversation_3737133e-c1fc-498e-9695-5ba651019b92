<style lang="less">
    @import '../../../../styles/common.less';
    @import '../topstick.less';
</style>

<template>
    <div class="message-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tool-bar">
                    <router-link to="/console/topstick/message-add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="mTable" @on-delete="handleDel" v-model="mRows" :columns-list="mColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getMsglist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import tData from '../data/topstick_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'message_list',
        components: {
            canEditTable
        },
        data () {
            return {
                mRows: [],
                mColumns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'message_list'){
                    this.getMsglist(1);
                }
            }
        },
        mounted () {
            //this.getMsglist(1);
        },
        methods: {
            getData () {
                this.mColumns = tData.mColumns;
            },
            getMsglist (n){
                this.mRows = [];
                this.total = 0;
                this.current = n;

                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/top_post/message',
                    method:'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.mRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
            this.getMsglist(1);
        }
    };
</script>

<style>

</style>
