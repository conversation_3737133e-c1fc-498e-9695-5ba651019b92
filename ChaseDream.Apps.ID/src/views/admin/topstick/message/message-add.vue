<style lang="less">
    @import '../../../../styles/common.less';
    @import '../topstick.less';
</style>

<template>
    <div class="message-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <h3>添加话术</h3>
                <br>
                <Form ref="form" :model="form" :rules="rules" :label-width="90">
                    <FormItem prop="message" label="话术">
                        <Input v-model="form.message" placeholder="话术" @on-change=""></Input>
                    </FormItem>
                    <FormItem>
                        <Button type="primary" @click="handleSubmit()">提 交</Button>
                        <Button type="ghost" @click="closePage()" style="margin-left: 8px">取 消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'message_add',
        components: {

        },
        data () {
            return {
                form: {
                    message: '',
                },
                isAccess: true,
                rules: {
                    message: [
                        { required: true, message: '话术不能为空', trigger: 'blur' },
                        /*{ type: 'string', min: 1, message: '用户名不能小于3个字符', trigger: 'blur' }*/
                    ]
                }
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'message_add'){
                    this.form.message = '';
                }
            }
        },
        mounted () {

        },
        methods: {
            handleSubmit (){
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/top_post/message',
                            method:'POST',
                            data: {
                                message: _this.form.message
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'message_add');
                                _this.$store.commit('closePage', 'message_add');
                                _this.$router.push({
                                    name: 'message_list'
                                });
                            }else{
                                //_this.errMsg = res.msg;
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            //_this.errMsg = err.msg;
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                })
            },
            closePage (){
                this.form.message = '';
                this.$store.commit('removeTag', 'message_add');
                this.$router.push({
                    name: 'message_list'
                });
            }
        },
        created () {

        }
    };
</script>

<style>

</style>
