<style lang="less">
    @import './editable-table.less';
</style>

<template>
    <div>
        <Table :ref="refs" :columns="columnsList" :data="thisTableData" border></Table>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

const viewButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'eye',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            click: () => {
                vm.$emit('details', currentRow,index);
            }
        }
    });
};
const editButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'edit',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$router.push({
                    name: 'blacklist_edit'
                });
            }
        }
    });
};
const deleteButton = (vm, h, currentRow, index) => {
    var isAdmin = false;
    for(var i=0;i<vm.$router.options.routes.length;i++){
        if(vm.$router.options.routes[i].name === 'system'){
            isAdmin = true;
        }
    }
    if(!!isAdmin){
        return h('Poptip', {
            props: {
                confirm: true,
                title: '您确定要删除这条数据吗?',
                transfer: true
            },
            on: {
                'on-ok': () => {
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/linkedin/info',
                        method:'DELETE',
                        data: {
                            id: currentRow.id
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            vm.thisTableData.splice(index, 1);
                            vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                            vm.$emit('on-delete', vm.handleBackdata(vm.thisTableData), index);
                        }else{

                        }
                    }).catch(function (err) {
                        console.log(err)
                    });
                }
            }
        }, [
            h('Icon', {
                style: {
                    margin: '0 8px',
                    color: '#ed3f14',
                    cursor: 'pointer'
                },
                props: {
                    type: 'trash-a',
                    placement: 'top',
                    size: 24
                }
            })
        ]);
    }

};
const idInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.id,
            placeholder: 'ID'
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const nameInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.name,
                placeholder: '姓名'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'name', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'name', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.uid,
                placeholder: 'UID'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'uid', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'uid', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
};
const genderInput = (vm, h, currentRow, index) => {
    var typeList =[
        {
            id: 0,
            name: '全部'
        },
        {
            id: 'Male',
            name: 'Male'
        },
        {
            id: 'Female',
            name: 'Female'
        },
        {
            id: 'N/A',
            name: 'N/A'
        }
    ];
    return h('Select', {
        props: {
            value: currentRow.gender, // 获取选择的下拉框的值
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-change': e => {
                vm.$set(vm.thisTableData[0], 'gender', e);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    },typeList.map((item) => {
        return h('Option', { // 下拉框的值
            props: {
                value: item.id,
                label: item.name
            }
        })
    }));
};
const nationalityInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.nationality,
                placeholder: '国籍'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'nationality', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'nationality', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.location,
                placeholder: '地区'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'location', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'location', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
};
const work_experienceInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.work,
                placeholder: '当前工作'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'work', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'work', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.works,
                placeholder: '全部工作'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'works', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'works', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
};
const educationInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.edu,
                placeholder: '最后学历'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'edu', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'edu', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.edus,
                placeholder: '全部学历'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'edus', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'edus', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
};

const clearButton = (vm, h, currentRow, index) => {
    return h('span', {
        props: {},
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$set(vm.thisTableData[0], 'id', '');
                vm.$set(vm.thisTableData[0], 'name', '');
                vm.$set(vm.thisTableData[0], 'uid', '');
                vm.$set(vm.thisTableData[0], 'gender', '');
                vm.$set(vm.thisTableData[0], 'nationality', '');
                vm.$set(vm.thisTableData[0], 'location', '');
                vm.$set(vm.thisTableData[0], 'work', '');
                vm.$set(vm.thisTableData[0], 'works', '');
                vm.$set(vm.thisTableData[0], 'edu', '');
                vm.$set(vm.thisTableData[0], 'edus', '');
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    }, '清除');
};
const avatarDiv = (vm, h, currentRow, index) => {
    if(!!currentRow.avatar_small){
        return h('Poptip', {
            props: {
                confirm: false,
                title: '',
                transfer: true,
                placement: 'right'
            }
        }, [
            h('img', {
                style: {
                    width: '40px',
                    borderRadius: '50%'
                },
                attrs: {
                    src: 'https://static.chasedream.com' + currentRow.avatar_small.split('/upload')[1]
                }
            }),
            h('div', {
                slot: 'content',
                'class': 'api',
                style:{
                    textAlign: 'center'
                }
            },[
                h('img', {
                    style: {
                        width: '500px'
                    },
                    attrs: {
                        src: 'https://static.chasedream.com' + currentRow.avatar_big.split('/upload')[1]
                    }
                })
            ]),
        ]);
    }else{
        return h('div',{
            style: {
                position: 'relative'
            }
        },[
            h('span', {
                style: {
                    display: 'inline-block',
                    width: '40px',
                    height: '40px',
                },
                attrs: {
                    class: 'avatar_linkedin'
                }
            },''),
            h('Icon', {
                style: {
                    position: 'absolute',
                    top: '-2px',
                    right: '-15px;',
                    color: '#19be6b',
                    cursor: 'pointer'
                },
                props: {
                    type: 'refresh',
                    size:18
                },
                on: {
                    'click': () => {
                        var is_timeout = false;
                        var is_code = false;
                        vm.$emit('show-spin', 1);

                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/linkedin/avatar/' + currentRow.id,
                            method:'GET',
                            data: {},
                            timeout: 60000,
                        }).then(function (res) {
                            vm.$emit('show-spin', 0);
                            if(res.data.msg === 'success'){
                                is_timeout = true;
                                if(!is_code){
                                    if(!!res.data.data.avatar_big){
                                        currentRow.avatar_big = res.data.data.avatar_big;
                                        currentRow.avatar_small = res.data.data.avatar_small;
                                        vm.$emit('set-avatar', currentRow,index);
                                    }
                                }
                            }else{

                            }
                        }).catch(function (err) {
                            console.log(err)
                        });
                        setTimeout(function () {
                            is_code = true;
                            if(!is_timeout){
                                vm.$emit('show-spin', 0);
                                vm.$emit('show-code');
                            }
                        },15000);
                    }
                }
            })
        ])

    }

};
export default {
    name: 'canEditTable',
    props: {
        refs: String,
        columnsList: Array,
        value: Array,
        url: String,
        editIncell: {
            type: Boolean,
            default: false
        },
        hoverShow: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            columns: [],
            thisTableData: [],
            edittingStore: []
        };
    },
    created () {
        this.init();
    },
    methods: {
        init () {
            let vm = this;
            let editableCell = this.columnsList.filter(item => {
                if (item.editable) {
                    if (item.editable === true) {
                        return item;
                    }
                }
            });
            let cloneData = JSON.parse(JSON.stringify(this.value));
            let res = [];
            res = cloneData.map((item, index) => {
                let isEditting = false;
                if (this.thisTableData[index]) {
                    if (this.thisTableData[index].editting) {
                        isEditting = true;
                    } else {
                        for (const cell in this.thisTableData[index].edittingCell) {
                            if (this.thisTableData[index].edittingCell[cell] === true) {
                                isEditting = true;
                            }
                        }
                    }
                }
                if (isEditting) {
                    return this.thisTableData[index];
                } else {
                    this.$set(item, 'editting', false);
                    let edittingCell = {};
                    editableCell.forEach(item => {
                        edittingCell[item.key] = false;
                    });
                    this.$set(item, 'edittingCell', edittingCell);
                    return item;
                }
            });
            this.thisTableData = res;
            this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
            this.columnsList.forEach(item => {
                if (item.editable) {
                    item.render = (h, param) => {
                        let currentRow = this.thisTableData[param.index];
                        if (!currentRow.editting) {
                            if (this.editIncell) {
                                return h('Row', {
                                    props: {
                                        type: 'flex',
                                        align: 'middle',
                                        justify: 'center'
                                    }
                                }, [
                                    h('Col', {
                                        props: {
                                            span: '22'
                                        }
                                    }, [
                                        !currentRow.edittingCell[param.column.key] ? h('span', currentRow[item.key]) : cellInput(this, h, param, item)
                                    ]),
                                    h('Col', {
                                        props: {
                                            span: '2'
                                        }
                                    }, [
                                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                                    ])
                                ]);
                            } else {
                                return h('span', currentRow[item.key]);
                            }
                        } else {
                            return h('Input', {
                                props: {
                                    type: 'text',
                                    value: currentRow[item.key]
                                },
                                on: {
                                    'on-change' (event) {
                                        let key = param.column.key;
                                        vm.edittingStore[param.index][key] = event.target.value;
                                    }
                                }
                            });
                        }
                    };
                }
                if (item.handle) {
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.handle.forEach(item => {
                            if (item === 'edit') {
                                children.push(editButton(this, h, currentRowData, param.index));
                            } else if (item === 'delete') {
                                children.push(deleteButton(this, h, currentRowData, param.index));
                            } else if (item === 'view') {
                                children.push(viewButton(this, h, currentRowData, param.index));
                            } else if (item === 'id') {
                                children.push(idInput(this, h, currentRowData, param.index));
                            } else if (item === 'name') {
                                children.push(nameInput(this, h, currentRowData, param.index));
                            } else if (item === 'gender') {
                                children.push(genderInput(this, h, currentRowData, param.index));
                            } else if (item === 'nationality') {
                                children.push(nationalityInput(this, h, currentRowData, param.index));
                            } else if (item === 'work_experience') {
                                children.push(work_experienceInput(this, h, currentRowData, param.index));
                            } else if (item === 'education') {
                                children.push(educationInput(this, h, currentRowData, param.index));
                            } else if (item === 'clear') {
                                children.push(clearButton(this, h, currentRowData, param.index));
                            } else if (item === 'avatar') {
                                children.push(avatarDiv(this, h, currentRowData, param.index));
                            }
                        });
                        return h('div', children);
                    };
                }
            });
        },
        handleBackdata (data) {
            let clonedData = JSON.parse(JSON.stringify(data));
            clonedData.forEach(item => {
                delete item.editting;
                delete item.edittingCell;
                delete item.saving;
            });
            return clonedData;
        }
    },
    watch: {
        value (data) {
            this.init();
        }
    }
};
</script>
