<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="info-list linkedIn-info-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tabBtns">
                    <Button type="text" icon="ios-folder" class="on">人物</Button>
                    <router-link to="/console/crawler/linkedin/organization/list">
                        <Button type="text" icon="home">机构</Button>
                    </router-link>
                    <router-link to="/console/crawler/linkedin/school/list">
                        <Button type="text" icon="ios-home">学校</Button>
                    </router-link>
                </div>

                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>


                    <can-edit-table refs="uTable" @on-delete="handleDel" @details="showDetails" v-model="iRows" :columns-list="iColumns" class="listTable" @on-updata="getlist(1)" @show-code="show_code_m" @set-avatar="set_avatar" @show-spin="show_spin"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>

        <Modal v-model="details" title="详情" class="l_resume" width="800">
            <p align="center" v-if="!!info_details.avatar_big"><img :src="'https://static.chasedream.com' + info_details.avatar_big.split('/upload')[1]" alt="" width="150" height="150" style="border-radius: 50%;"></p>
            <p align="center" v-if="!info_details.avatar_big"><span class="avatar_big"></span></p>

            <!--<h3>个人信息</h3>-->
            <div class="personal_info" style="display: flex;">
                <div class="item" v-if="!!info_details.name">
                    <strong>姓名</strong>
                    <span>{{info_details.name}}</span>
                </div>
                <div class="item" v-if="!!info_details.uid">
                    <strong>UID</strong>
                    <span>{{info_details.uid}}</span>
                </div>
                <div class="item" v-if="!!info_details.birthday">
                    <strong>生日</strong>
                    <span>{{info_details.birthday}}</span>
                </div>
                <div class="item" v-if="!!info_details.nationality">
                    <strong>国家</strong>
                    <span>{{info_details.nationality}}</span>
                </div>
                <div class="item" v-if="!!info_details.location">
                    <strong>地区</strong>
                    <span>{{info_details.location}}</span>
                </div>
                <div class="item" v-if="!!info_details.mobile">
                    <strong>手机号</strong>
                    <span>{{info_details.mobile}}</span>
                </div>
                <div class="item" v-if="!!info_details.address">
                    <strong>地址</strong>
                    <span>{{info_details.address}}</span>
                </div>
                <div class="item" v-if="!!info_details.qq">
                    <strong>QQ</strong>
                    <span>{{info_details.qq}}</span>
                </div>
                <div class="item" v-if="!!info_details.wechat">
                    <strong>微信</strong>
                    <span>{{info_details.wechat}}</span>
                </div>
                <div class="item" v-if="!!info_details.weibo">
                    <strong>微博</strong>
                    <span>{{info_details.weibo}}</span>
                </div>
                <div class="item" v-if="!!info_details.skype">
                    <strong>skype</strong>
                    <span>{{info_details.skype}}</span>
                </div>
                <div class="item" v-if="!!info_details.summary">
                    <strong>职位</strong>
                    <span>{{info_details.summary}}</span>
                </div>
                <div class="item" v-if="!!info_details.url">
                    <strong>网站</strong>
                    <span>{{info_details.url}}</span>
                </div>
            </div>
            <div v-if="!!info_details.resume">
                <h3>个人简介</h3>
                <p class="l_item" style="padding-top: 10px;margin-bottom: 20px;">{{info_details.resume}}</p>
            </div>
            <div class="l_item" v-if="!!info_details.work_experience && info_details.work_experience.length > 0">
                <h3>工作经历</h3>
                <div v-for="item in info_details.work_experience" class="l_p_i" :key="item.index" style="padding-left: 120px;position: relative">
                    <img v-if="!!item.org" :src="'https://static.chasedream.com' + item.org.logo.split('/upload')[1]" style="width: 70px;position: absolute;left: 0;top: 20px;border: 1px solid #efefef;" alt="">
                    <img v-if="!item.org" src="https://static.licdn.cn/sc/h/3dtfvv2esz58o2zimai1h3v2d" style="width: 70px;position: absolute;left: 0;top: 20px;border: 1px solid #efefef;" alt="">
                    <p><strong>职位</strong><span>{{item.title}}</span></p>
                    <p><strong>公司名称</strong><span>{{item.org_name}}</span></p>
                    <p><strong>时间</strong><span>{{item.begin_year +'/'+ item.begin_month +' - '+ item.end_year +'/'+ item.end_month}}</span></p>
                    <p><strong>地址</strong><span>{{item.city}}</span></p>
                    <p><strong>工作类别</strong><span>{{item.job_category}}</span></p>
                    <p><strong>职位内容</strong><span>{{item.job_desc}}</span></p>
                </div>
            </div>
            <div class="l_item" v-if="!!info_details.education && info_details.education.length > 0">
                <h3>教育经历</h3>
                <div v-for="item in info_details.education" class="l_p_i" :key="item.index" style="padding-left: 120px;position: relative">
                    <img v-if="!!item.school" :src="'https://static.chasedream.com' + item.school.logo.split('/upload')[1]"  style="width: 70px;position: absolute;left: 0;top: 20px;border: 1px solid #efefef;" alt="">
                    <img v-if="!item.school" src="https://static.licdn.cn/sc/h/csnc6op83cjt60ym5equoy1km" style="width: 70px;position: absolute;left: 0;top: 20px;border: 1px solid #efefef;" alt="">
                    <p><strong>大学</strong><span>{{item.school_name}}</span></p>
                    <p><strong>时间</strong><span>{{item.enrollment_year +' - '+ item.graduation_year}}</span></p>
                    <p><strong>专业</strong><span>{{item.major}}</span></p>
                    <p><strong>学位</strong><span>{{item.degree}}</span></p>
                    <p><strong>成绩</strong><span>{{item.school_record}}</span></p>
                    <p><strong>参加活动</strong><span>{{item.club_activity}}</span></p>
                    <p><strong>其它</strong><span>{{item.other}}</span></p>
                </div>
            </div>
            <div class="l_item" v-if="!!info_details.certification && info_details.certification.length > 0">
                <h3>认证</h3>
                <div v-for="item in info_details.certification" class="l_p_i" :key="item.index">
                    <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                    <p><strong>时间</strong><span>{{item.begin_year +'/'+ item.begin_month +' - '+ item.end_year +'/'+ item.end_month}}</span></p>
                    <p><strong>授权</strong><span>{{item.issue_authority}}</span></p>
                    <p><strong>网址</strong><span>{{item.link}}</span></p>
                </div>
            </div>
            <div class="l_item" v-if="!!info_details.skill && (info_details.skill.top.length > 0 || info_details.skill.others.length > 0)">
                <h3>技能认可</h3>
                <div class="tops l_p_i" v-if="!!info_details.skill.top && info_details.skill.top.length > 0">
                    <span v-for="item,index in info_details.skill.top" :key="item.index">{{index === (info_details.skill.top.length -1) ? item : item + '、'}}</span>
                </div>
                <div class="others l_p_i" v-if="!!info_details.skill.others && info_details.skill.others.length > 0">
                    <p v-for="item in info_details.skill.others" class="l_p_i">
                        <strong>{{item.name}}</strong>
                        <span v-for="it,idx in item.content" :key="it.index">{{idx === (item.content.length - 1) ? it : it + '、'}}</span>
                    </p>
                </div>
            </div>
            <div class="l_item" v-if="!!info_details.volunteer && info_details.volunteer.length > 0">
                <h3>志愿者项目</h3>
                <div v-for="item in info_details.volunteer" class="l_p_i" :key="item.index">
                    <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                    <p><strong>时间</strong><span>{{item.begin_year +'/'+ item.begin_month +' - '+ item.end_year +'/'+ item.end_month}}</span></p>
                    <p><strong>中心点</strong><span>{{item.focus}}</span></p>
                    <p><strong>组织名称</strong><span>{{item.org_name}}</span></p>
                    <p><strong>描述</strong><span>{{item.desc}}</span></p>
                </div>
            </div>
            <div class="l_item" v-if="!!info_details.skill && (info_details.skill.top.length > 0 || info_details.skill.others.length > 0)">
                <h3>个人成就</h3>
                <div class="" v-if="!!info_details.accomplishment_language && info_details.accomplishment_language.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">语言能力</h4>
                    <p v-for="item in info_details.accomplishment_language" class="l_p_i">
                        {{item.name +' - '+ item.level }}
                    </p>
                </div>
                <div class="" v-if="!!info_details.accomplishment_course && info_details.accomplishment_course.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">所学课程</h4>
                    <p v-for="item in info_details.accomplishment_course" class="l_p_i">
                        {{item.name +' - '+ item.number}}
                    </p>
                </div>
                <div v-if="!!info_details.accomplishment_patent && info_details.accomplishment_patent.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">专利发明</h4>
                    <div v-for="item in info_details.accomplishment_patent" class="l_p_i" :key="item.index">
                        <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                        <p><strong>申请日期</strong><span>{{item.dateline}}</span></p>
                        <p><strong>代码</strong><span>{{item.number}}</span></p>
                        <p><strong>网址</strong><span>{{item.website}}</span></p>
                        <p><strong>描述</strong><span>{{item.desc}}</span></p>
                    </div>
                </div>
                <div v-if="!!info_details.accomplishment_honor && info_details.accomplishment_honor.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">荣誉奖项</h4>
                    <div v-for="item in info_details.accomplishment_honor" class="l_p_i" :key="item.index">
                        <h4>{{item.name}}</h4>
                        <p><strong>时间</strong><span>{{item.dateline}}</span></p>
                        <p><strong>授权机构</strong><span>{{item.issue_authority}}</span></p>
                        <p><strong>描述</strong><span>{{item.desc}}</span></p>
                    </div>
                </div>
                <div v-if="!!info_details.accomplishment_organization && info_details.accomplishment_organization.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">参与组织</h4>
                    <div v-for="item in info_details.accomplishment_organization" class="l_p_i" :key="item.index">
                        <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                        <p><strong>时间</strong><span>{{item.begin_year +'/'+ item.begin_month +' - '+ item.end_year +'/'+ item.end_month}}</span></p>
                        <p><strong>职位</strong><span>{{item.title}}</span></p>
                        <p><strong>描述</strong><span>{{item.desc}}</span></p>
                    </div>
                </div>
                <div v-if="!!info_details.accomplishment_project && info_details.accomplishment_project.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">所做项目</h4>
                    <div v-for="item in info_details.accomplishment_project" class="l_p_i" :key="item.index">
                        <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                        <p><strong>时间</strong><span>{{item.begin_year +'/'+ item.begin_month +' - '+ item.end_year +'/'+ item.end_month}}</span></p>
                        <p><strong>网址</strong><span>{{item.website}}</span></p>
                        <p><strong>描述</strong><span>{{item.desc}}</span></p>
                    </div>
                </div>
                <div v-if="!!info_details.accomplishment_publication && info_details.accomplishment_publication.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">出版作品</h4>
                    <div v-for="item in info_details.accomplishment_publication" class="l_p_i" :key="item.index">
                        <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                        <p><strong>时间</strong><span>{{item.dateline}}</span></p>
                        <p><strong>出版社</strong><span>{{item.company}}</span></p>
                        <p><strong>网址</strong><span>{{item.website}}</span></p>
                        <p><strong>描述</strong><span>{{item.desc}}</span></p>
                    </div>
                </div>
                <div v-if="!!info_details.accomplishment_test && info_details.accomplishment_test.length > 0">
                    <h4 style="color:#2b85e4;margin:10px 0 0 10px;font-size: 16px;">测试成绩</h4>
                    <div v-for="item in info_details.accomplishment_test" class="l_p_i" :key="item.index">
                        <p><strong>项目名称</strong><span>{{item.name}}</span></p>
                        <p><strong>时间</strong><span>{{item.dateline}}</span></p>
                        <p><strong>分数</strong><span>{{item.score}}</span></p>
                        <p><strong>描述</strong><span>{{item.desc}}</span></p>
                    </div>
                </div>
            </div>
            <p align="right" style="font-size: 12px;color: #ccc;" v-if="!!info_details.update_by || !!info_details.updated_at">
                <span v-if="!!info_details.update_by">{{info_details.update_by}} </span><br>
                <span v-if="!!info_details.created_at"> {{info_details.created_time}}</span>
            </p>
            <div slot="footer" style="position: relative;">
                <Poptip
                        confirm
                        title="您确定要删除这条数据吗?"
                        @on-ok="ok"
                        ok-text="确定"
                        cancel-text="取消"
                        v-if="!!isAdmin"
                        :transfer="true"
                        popper-class="delRow"

                >
                    <Icon type="trash-a" size="24" style="color: #ed3f14;"></Icon>
                </Poptip>
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <Modal v-model="show_code" title="请输入验证码" class="l_code" width="400">
            <div>
                <Input v-model="verification_code" placeholder="请输入验证码" style="width: 300px"></Input>
            </div>
            <div slot="footer" style="position: relative;">
                <Button type="primary" @click="sent_code">提交</Button>
                <Button type="text" @click="show_code = false">关闭</Button>
            </div>
        </Modal>
        <Spin size="large" fix v-if="spinShow">
            <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
            <p>正在抓取中......</p>
        </Spin>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import lData from '../data/linkedin_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'l_info_list',
        components: {
            canEditTable
        },
        data () {
            return {
                iRows: [],
                iColumns: [],
                s_row:[
                    {
                        id: '',
                        name: '',
                        uid: '',
                        gender: '',
                        nationality: '',
                        location: '',
                        org: '',
                        orgs: '',
                        edu: '',
                        edus: ''
                    }
                ],
                s_columns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                details: false,
                info_details:{},
                errMsg: '',
                isAccess: true,
                isAdmin: false,
                show_code: false,
                verification_code: '',
                spinShow: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_info_list'){
                    this.iColumns = [];
                    this.s_row = [
                        {
                            id: '',
                            name: '',
                            uid: '',
                            gender: '',
                            nationality: '',
                            location: '',
                            org: '',
                            orgs: '',
                            edu: '',
                            edus: ''
                        }
                    ];
                    this.s_columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_Size = 20;
                    this.current = 1;
                    this.details = false;
                    this.info_details = {};
                    this.isAdmin = false;
                    this.show_code = false;
                    this.verification_code = '';
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.tab1 = '';
                this.tab1 = 'tab1';
                this.iColumns = lData.iColumns;
                this.s_columns = lData.i_sColumns;
                this.getlist(1);
                for(var i=0;i<this.$router.options.routes.length;i++){
                    if(this.$router.options.routes[i].name === 'system'){
                        this.isAdmin = true;
                    }
                }
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].name){
                    s.push('name=' + this.s_row[0].name);
                }
                if(!!this.s_row[0].uid){
                    s.push('uid=' + this.s_row[0].uid);
                }
                if(!!this.s_row[0].gender){
                    s.push('gender=' + this.s_row[0].gender);
                }
                if(!!this.s_row[0].nationality){
                    s.push('nationality=' + this.s_row[0].nationality);
                }
                if(!!this.s_row[0].location){
                    s.push('location=' + this.s_row[0].location);
                }
                if(!!this.s_row[0].work){
                    s.push('work=' + this.s_row[0].work);
                }
                if(!!this.s_row[0].works){
                    s.push('works=' + this.s_row[0].works);
                }
                if(!!this.s_row[0].edu){
                    s.push('edu=' + this.s_row[0].edu);
                }
                if(!!this.s_row[0].edus){
                    s.push('edus=' + this.s_row[0].edus);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.iRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/info' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.iRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            showDetails (row,index){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/info',
                    method:'POST',
                    data: {
                        uid: row.uid,
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.info_details = res.data.data;
                        _this.info_details.index = index;
                        _this.info_details.created_time = '';
                        if(!!_this.info_details.created_at){
                            var c_date = new Date(parseInt(_this.info_details.created_at + '000'));
                            var y = c_date.getFullYear();
                            var m = ((c_date.getMonth() + 1) + '').length > 1 ? c_date.getMonth() + 1 : '0' + (c_date.getMonth() + 1);
                            var d = (c_date.getDate() + '').length > 1 ? c_date.getDate() : '0' + c_date.getDate();
                            var hh = (c_date.getHours() + '').length > 1 ? c_date.getHours() : '0' + c_date.getHours();
                            var mm = (c_date.getMinutes() + '').length > 1 ? c_date.getMinutes() : '0' + c_date.getMinutes();
                            var s = (c_date.getSeconds() + '').length > 1 ? c_date.getSeconds() : '0' + c_date.getSeconds();
                            _this.info_details.created_time = y +'-'+ m +'-'+ d +' '+ hh +':'+ mm +':'+ s;
                        }
                        _this.details = true;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            close (){
                this.info_details = {};
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            ok () {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/info',
                    method:'DELETE',
                    data: {
                        id: this.info_details.id
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.iRows.splice(_this.info_details.index, 1);
                        var list_data = [];
                        list_data = JSON.parse(JSON.stringify(_this.iRows));
                        _this.iRows = [];
                        _this.iRows = JSON.parse(JSON.stringify(list_data));
                        _this.details = false;
                        _this.handleDel(_this.iRows,_this.info_details.index);
                        _this.info_details = {};
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            clear_data(){
                this.$Message.success('操作成功！');
                this.getlist(1);
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            show_code_m (){
                this.verification_code = '';
                this.show_code = true;
            },
            sent_code (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/avatar/' + this.verification_code,
                    method:'POST',
                    data: {

                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.show_code = false;
                        _this.verification_code = '';
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            set_avatar(row, index){
                this.iRows[index].avatar_big = row.avatar_big;
                this.iRows[index].avatar_small = row.avatar_small;
                var list_data = [];
                list_data = JSON.parse(JSON.stringify(this.iRows));
                this.iRows = [];
                this.iRows = list_data;
            },
            show_spin (n){
                if(!!n){
                    this.spinShow = true;
                }else {
                    this.spinShow = false;
                }
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
