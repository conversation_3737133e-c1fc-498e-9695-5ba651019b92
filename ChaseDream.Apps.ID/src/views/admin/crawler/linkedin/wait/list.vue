<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="wait-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tabBtns">
                    <Button type="text" icon="ios-list" class="on">爬取列表</Button>
                    <router-link to="/console/crawler/linkedin/account/list">
                        <Button type="text" icon="ios-person">账户</Button>
                    </router-link>
                    <router-link to="/console/crawler/linkedin/log/list">
                        <Button type="text" icon="document-text">日志</Button>
                    </router-link>
                </div>

                <div class="tool-bar">
                    <router-link to="/console/crawler/linkedin/wait/add">
                        <Button type="success" icon="plus" style="margin-right: 10px;">添 加</Button>
                    </router-link>

                    <Poptip v-model="show_batch" v-if="del_list.length > 0">
                        <Button type="error" icon="trash-a">批量删除</Button>
                        <div slot="content">
                            <div>
                                <p><Icon type="help-circled" size="18" style="color: #ff9900;margin-right: 10px;"></Icon>您确定要删除这批数据吗?</p>
                                <div style="margin: 5px 0 10px;">
                                    <Checkbox v-model="delinfo">删除资料库</Checkbox>
                                    <Checkbox v-model="isrecrawl">重新爬取</Checkbox>
                                </div>
                            </div>
                            <div align="right">
                                <Button type="primary" size="small" @click="batch_delete" style="width: 40px;height: 24px;font-size: 12px;border-radius: 3px;">确定</Button>
                                <Button type="text" @click="close_batch" size="small" style="width: 40px;height: 24px;font-size: 12px;border-radius: 3px;">取消</Button>
                            </div>
                        </div>
                    </Poptip>
                    <Button type="error" icon="trash-a" disabled v-if="del_list.length === 0">批量删除</Button>
                    <br><br>
                </div>
                <div style="float: right;margin-top: -60px;margin-right: 20px;">
                    <RadioGroup v-model="keyword" type="button" size="large" @on-change="getlist(1)" style="display: inline-block;margin-right: 20px;">
                        <Radio :label="3">全部</Radio>
                        <Radio :label="0"><Icon type="record" style="color: #ff9900"></Icon></Radio>
                        <Radio :label="1"><Icon type="record" style="color: #19be6b"></Icon></Radio>
                        <Radio :label="2"><Icon type="record" style="color: #ed3f14"></Icon></Radio>
                    </RadioGroup>
                    <p style="display: inline-block"><span>待爬取数：<strong style="font-size: 20px;color: #ff9900;">{{n_num}}</strong></span><span style="display: inline-block;margin-left: 10px;">总数：<strong style="font-size: 20px;color:#19be6b;">{{t_num}}</strong></span></p>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table ref="table_h" refs="table1" v-model="s_row" @on-search="getlist(1)" @on-selectall="select_all" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable"></can-edit-table>

                    <can-edit-table ref="table_d" refs="selection" @on-delete="handleDel" @on-batch="set_del_list" v-model="wRows" :columns-list="wColumns" class="listTable"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>



        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import lData from '../data/linkedin_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'l_wait_list',
        components: {
            canEditTable
        },
        data () {
            return {
                tab2: 'tab2',
                wRows: [],
                wColumns: [],
                s_row:[
                    {
                        url: '',
                    }
                ],
                s_columns: [],
                t_num: 0,
                n_num: 0,
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                keyword: 3,
                del_list: [],
                show_batch: false,
                delinfo: false,
                isrecrawl: false,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_wait_list'){
                    this.wColumns = [];
                    this.s_row = [
                        {
                            url: ''
                        }
                    ];
                    this.s_columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_Size = 20;
                    this.current = 1;
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.wColumns = lData.wColumns;
                this.s_columns = lData.wsColumns;
                this.getlist(1);
                this.get_wait_info();
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].url){
                    s.push('url=' + this.s_row[0].url);
                }
                if(!!this.s_row[0].status){
                    s.push('error_message=' + this.s_row[0].status);
                }

                if(s.length > 0){
                    text =s.join('&')
                }

                this.wRows = [];
                this.total = 0;
                this.current = n;
                var status = '';
                if(this.keyword !== 3){
                    status = '?status=' + this.keyword;
                }
                if(!!status){
                    text = '&' + text;
                }else {
                    text = '?' + text;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/wait_list' + status + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.wRows = res.data.data.rows;

                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            get_wait_info (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/wait_list/info',
                    method: 'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.t_num = res.data.data.total;
                        _this.n_num = res.data.data.waiting

                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            set_del_list (list){
                if(list.length === this.wRows.length){
                    this.$refs.table_h.select_status(true);
                }else {
                    this.$refs.table_h.select_status(false);
                }
                this.del_list = list;
            },
            batch_delete (){
                var dataIn = {
                    wait_list: []
                };
                if(!!this.isrecrawl){
                    for(var i=0;i<this.del_list.length;i++){
                        var item = {
                            id: this.del_list[i].id,
                            lid: !!this.delinfo ? this.del_list[i].lid : null,
                            recrawl: 1,
                            url: this.del_list[i].url
                        };
                        dataIn.wait_list.push(item)
                    }
                }else {
                    for(var i=0;i<this.del_list.length;i++){
                        var item = {
                            id: this.del_list[i].id,
                            lid: !!this.delinfo ? this.del_list[i].lid : null,
                        };
                        dataIn.wait_list.push(item)
                    }
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/wait_list',
                    method:'DELETE',
                    data: dataIn,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.close_batch()
                        _this.$Message.success('批量删除成功');
                        _this.getData();
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            close_batch(){
                this.show_batch = false;
            },
            clear_data(){
                this.$Message.success('操作成功！');
                this.getlist(1);
            },
            set_s_data (obj){
                this.s_row = obj;
            },
            select_all (status){
                this.$refs.table_d.select_all(status);
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
