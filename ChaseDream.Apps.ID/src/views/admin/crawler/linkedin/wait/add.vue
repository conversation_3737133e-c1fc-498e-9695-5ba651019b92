<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="linkedin-account-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加Linkedin帐号
                </p>
                <Form ref="form" :model="form" :rules="rules" :label-width="90">
                    <FormItem prop="urls" label="URL：">
                        <Input v-model="form.urls" type="textarea" :autosize="{minRows: 4,maxRows: 5}" placeholder="请输入URL"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'l_wait_add',
        components: {
        },
        data () {
            return {
                form: {
                    urls: '',
                },
                rules: {
                    urls: [
                        { required: true, message: 'URL不能为空', trigger: 'blur' }
                    ],
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_wait_add'){
                    this.form.urls = '';

                    this.$refs['userForm'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/linkedin/wait_list',
                            method:'POST',
                            data: {
                                urls: _this.form.urls
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'l_wait_add');
                                _this.$store.commit('closePage', 'l_wait_add');
                                _this.$router.push({
                                    name: 'l_wait_list'
                                });
                            }else{
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.form.urls = '';
                this.$store.commit('removeTag', 'l_wait_add');
                this.$router.push({
                    name: 'l_wait_list'
                });
            }
        }
    };
</script>

<style>

</style>
