.tabBtns{
  border-bottom: 1px solid #dddee1;
  margin-bottom: 20px;
  button.ivu-btn{
    font-size: 17px;
    width: 130px;
    color: #a6b2bf;
    margin-right: 16px;
    padding: 16px;
    outline: none;
    box-shadow: none;
    &.on{
      color: #1f85f4;
      border-bottom: 2px solid;
      margin-bottom: -1px;
    }
  }
}

.l_resume{
  .ivu-modal-body{
    font-size: 14px;
  }
  h3{
    background: #EFFAF5;
    font-size: 18px;
    color: #444;
    padding:5px 10px;
  }
  .personal_info{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 20px;
    margin-top: 15px;

    .item{
      width: 50%;
      position: relative;
      padding-left: 60px;
      word-wrap: break-word;
      strong{
        margin-right: 10px;
        width: 50px;
        text-align: right;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }
  .l_item{
    margin-bottom: 10px;
    padding:0 10px;
    .l_p_i{
      padding: 10px;
      border-bottom: 1px dotted #e9ebf0;
      &:last-child{
        border: none;
      }
      h4{
        font-size: 16px;
      }
      p{
       position: relative;
        padding-left: 100px;
        line-height: 24px;
        min-height: 24px;
        strong{
          width: 100px;
          position: absolute;
          left: 0;
        }
        span{

        }
      }
    }
  }
}
.avatar_linkedin{
  background: url("../../../../images/admin/tx.png") no-repeat;
  background-size: 100%;
  border-radius: 50%;
}
.avatar_big{
  width: 150px;
  height: 150px;
  background: url("../../../../images/admin/tx.png") no-repeat;
  background-size: 100%;
  display: inline-block;
  border-radius: 50%;
}
.info_details{

  .i_d_t{
    position: relative;
    padding-left: 200px;
    min-height: 170px;
    border-bottom: 1px dotted #efefef;
    margin-bottom: 20px;
    font-size: 14px;

    .logo{
      width: 150px;
      height: 150px;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
  .i_d_d{
    font-size: 14px;
    h2{
      margin-bottom: 10px;
    }
  }
  .i_d_c{
    margin-top: 20px;
    font-size: 14px;
    p{
      padding-left: 100px;
      position: relative;

      strong{
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }
}
.row_details{

  p{
    display: flex;
    font-size: 14px;

    strong{
      width: 30%;
      text-align: right;
      padding-right:20px;
    }
    span{
      width: 70%;
      word-break: break-word;
      position: relative;

      textarea{
        width: 100%;
        position: absolute;
        left: 0;
        top:0;
        bottom:0;
        resize: none;
        border:none;
        box-shadow: none;
        /*background: #000;
        color: #fff;*/
      }
    }
  }
  &.msg_details{
    .ivu-modal{
      width: 800px !important;
    }
    strong{
      width: 25%;
    }
    span{
      width:75%;
    }
  }
}
.listTable{
  .ivu-table-wrapper{
    border-top: none;
  }
  .ivu-table-header{
    display: none;
  }
}
.main {
  .searchTable .ivu-table{
    td{
      height: 50px;
      background-color: #f2f3f6;

      input.ivu-input{
        height:28px;
        line-height: 28px;
      }
    }
    .ivu-table-cell{
      padding-left: 6px;
      padding-right: 6px;
    }
    .ivu-select-single{
      .ivu-select-selection,.ivu-select-placeholder{
        height:28px;
        line-height: 28px;
        border-radius: 0;
      }
      .ivu-select-selected-value{
        height:28px;
        line-height: 28px;
      }
    }
  }
}
.l_resume{
  .ivu-modal-footer{
    .ivu-poptip.ivu-poptip-confirm{
      position: absolute;
      left: 0;
      top: 0;

    }
    .delRow{
      .ivu-poptip-confirm .ivu-poptip-body .ivu-icon{
        position: absolute;
        left: 15px;
      }
    }
  }
}
.demo-spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}
.info-list{
  .ivu-spin-fix {
    position: fixed;
  }
}

