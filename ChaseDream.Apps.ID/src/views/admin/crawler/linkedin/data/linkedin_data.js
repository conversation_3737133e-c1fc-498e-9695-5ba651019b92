export const iColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 70,
        align: 'center'
    },
    {
        title: '头像',
        align: 'center',
        key: 'handle',
        width: 80,
        handle: ['avatar']
    },
    {
        title: '姓名/UID',
        align: 'center',
        key: 'name',
        render: (h, params) => {
            if (!!params.row.url) {
                return h('div', {}, [
                    h('a', {
                        attrs: {
                            href: params.row.url,
                            target: '_blank'
                        }
                    }, params.row.name),
                    h('p', {}, params.row.uid)
                ])
            } else {
                return h('div', {}, [
                    h('p', {}, params.row.name),
                    h('p', {}, params.row.uid)
                ])
            }
        }
    },
    {
        title: '性别',
        align: 'center',
        width: 80,
        key: 'gender',
        render: (h, params) => {
            if (params.row.gender === 'Male') {
                return h('Icon', {
                    style: {
                        color: '#19be6b'
                    },
                    props: {
                        type: 'man',
                        size: 30
                    }
                })
            } else if (params.row.gender === 'Female') {
                return h('Icon', {
                    style: {
                        color: '#ed3f14'
                    },
                    props: {
                        type: 'woman',
                        size: 30
                    }
                })
            } else if (params.row.gender === 'N/A') {
                return h('span', {}, 'N/A')
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '地区',
        align: 'center',
        key: 'nationality',
        width: 150,
        render: (h, params) => {
            return h('div', {
                style: {

                }
            }, [
                h('p', {
                    style: {
                    }
                }, params.row.nationality),
                h('p', {
                    style: {

                    }
                }, params.row.location)
            ])
        }
    },
    {
        title: '工作',
        align: 'center',
        key: 'work_experience',
        width: 200,
        render: (h, params) => {
            if (params.row.work_experience.length > 0) {
                return h('div', {
                    style: {

                    }
                }, [
                    h('p', {
                        style: {
                        }
                    }, params.row.work_experience[0].org_name),
                    h('p', {
                        style: {

                        }
                    }, params.row.work_experience[0].title),
                    h('p', {
                        style: {

                        }
                    }, params.row.work_experience[0].begin_year + '/' + params.row.work_experience[0].begin_month + ' - ' + params.row.work_experience[0].end_year + '/' + params.row.work_experience[0].end_month)
                ])
            } else {
                return h('div', {}, '')
            }
        }
    },
    {
        title: '教育',
        align: 'center',
        key: 'education',
        width: 200,
        render: (h, params) => {
            if (params.row.education.length > 0) {
                return h('div', {
                    style: {

                    }
                }, [
                    h('p', {
                        style: {
                        }
                    }, params.row.education[0].school_name),
                    h('p', {
                        style: {

                        }
                    }, params.row.education[0].major),
                    h('p', {
                        style: {

                        }
                    }, params.row.education[0].enrollment_year + ' - ' + params.row.education[0].graduation_year)
                ])
            } else {
                return h('div', {}, '')
            }
        }
    },
    {
        title: '刷新时间/创建时间',
        align: 'center',
        key: 'created_at',
        width: 100,
        render: (h, params) => {
            if (!!params.row.created_at) {
                var c_date = new Date(parseInt(params.row.created_at + '000'));
                var y = c_date.getFullYear();
                var m = ((c_date.getMonth() + 1) + '').length > 1 ? c_date.getMonth() + 1 : '0' + (c_date.getMonth() + 1);
                var d = (c_date.getDate() + '').length > 1 ? c_date.getDate() : '0' + c_date.getDate();
                var hh = (c_date.getHours() + '').length > 1 ? c_date.getHours() : '0' + c_date.getHours();
                var mm = (c_date.getMinutes() + '').length > 1 ? c_date.getMinutes() : '0' + c_date.getMinutes();
                var s = (c_date.getSeconds() + '').length > 1 ? c_date.getSeconds() : '0' + c_date.getSeconds();
                return h('span', {
                }, y + '-' + m + '-' + d)  //  +' '+ hh +':'+ mm +':'+ s
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['view']
    }
];
export const i_sColumns = [
    {
        title: 'ID',
        key: 'handle',
        width: 70,
        align: 'center',
        handle: ['id']
    },
    {
        title: '头像',
        align: 'center',
        key: 'avatar_small',
        width: 80,
    },
    {
        title: '姓名/UID',
        align: 'center',
        key: 'handle',
        handle: ['name']
    },
    {
        title: '性别',
        align: 'center',
        width: 80,
        key: 'handle',
        handle: ['gender']
    },
    {
        title: '地区',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['nationality']
    },
    {
        title: '工作',
        align: 'center',
        key: 'handle',
        width: 200,
        handle: ['work_experience']
    },
    {
        title: '教育',
        align: 'center',
        key: 'handle',
        width: 200,
        handle: ['education']
    },
    {
        title: '刷新时间/创建时间',
        align: 'center',
        key: 'created_at',
        width: 100,
        render: (h, params) => {
            if (!!params.row.created_at) {
                var c_date = new Date(parseInt(params.row.created_at + '000'));
                var y = c_date.getFullYear();
                var m = ((c_date.getMonth() + 1) + '').length > 1 ? c_date.getMonth() + 1 : '0' + (c_date.getMonth() + 1);
                var d = (c_date.getDate() + '').length > 1 ? c_date.getDate() : '0' + c_date.getDate();
                var hh = (c_date.getHours() + '').length > 1 ? c_date.getHours() : '0' + c_date.getHours();
                var mm = (c_date.getMinutes() + '').length > 1 ? c_date.getMinutes() : '0' + c_date.getMinutes();
                var s = (c_date.getSeconds() + '').length > 1 ? c_date.getSeconds() : '0' + c_date.getSeconds();
                return h('span', {
                }, y + '-' + m + '-' + d)  //  +' '+ hh +':'+ mm +':'+ s
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['clear']
    }
];

export const wColumns = [
    {
        //title: '',
        type: 'selection',
        width: 80,
        align: 'center'
    },
    {
        title: 'URL',
        align: 'left',
        key: 'url',
        render: (h, params) => {
            return h('a', {
                attrs: {
                    href: params.row.url,
                    target: '_blank'
                }
            }, params.row.url)
        }
    },
    {
        title: '状态',
        align: 'center',
        key: 'status',
        width: 120,
        filterMultiple: false,
        filterMethod(value, row) {
            if (value === 0) {

            } else if (value === 1) {

            } else if (value === 2) {

            }
        },
        render: (h, params) => {
            var style = {}
            if (params.row.status === 0) {
                style.color = '#ff9900';
            } else if (params.row.status === 1) {
                style.color = '#19be6b';
            } else if (params.row.status === 2) {
                style.color = '#ed3f14';
            }

            if (params.row.status !== 2) {
                return h('Icon', {

                    style: style,
                    props: {
                        type: 'record',
                        size: 14
                    }
                })
            } else {
                return h('Poptip', {
                    props: {
                        confirm: false,
                        title: '',
                        transfer: true
                    }
                }, [
                    h('Icon', {

                        style: style,
                        props: {
                            type: 'record',
                            size: 14
                        }
                    }),
                    h('div', {
                        slot: 'content',
                        'class': 'api',
                        style: {
                            textAlign: 'center'
                        }
                    }, [
                        h('p', {
                            style: {
                                color: '#ed3f14'
                            },
                        }, params.row.error_message)
                    ]),
                ]);
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['delete']
    }
];
export const wsColumns = [
    {
        title: '',
        width: 80,
        align: 'center',
        key: 'handle',
        handle: ['select']

    },
    {
        title: 'URL',
        align: 'left',
        key: 'handle',
        handle: ['url']
    },
    {
        title: '状态',
        align: 'center',
        key: 'status',
        width: 120,
        handle: ['status']
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['clear']
    }
];
export const oColumns = [
    {
        title: 'LOGO',
        align: 'center',
        key: 'logo',
        width: 80,
        render: (h, params) => {
            if (!!params.row.logo) {
                if (!!params.row.website) {
                    return h('a', {
                        attrs: {
                            href: params.row.website,
                            target: '_blank'
                        }
                    }, [
                        h('img', {
                            style: {
                                width: '40px'
                            },
                            attrs: {
                                src: 'https://static.chasedream.com' + params.row.logo.split('/upload')[1]
                            }
                        })
                    ])
                } else {
                    return h('img', {
                        style: {
                            width: '40px'
                        },
                        attrs: {
                            src: 'https://static.chasedream.com' + params.row.logo.split('/upload')[1]
                        }
                    })
                }
            } else {//
                if (!!params.row.website) {
                    return h('a', {
                        attrs: {
                            href: params.row.website,
                            target: '_blank'
                        }
                    }, [
                        h('img', {
                            style: {
                                width: '40px'
                            },
                            attrs: {
                                src: 'https://static.licdn.cn/sc/h/3dtfvv2esz58o2zimai1h3v2d'
                            }
                        })
                    ])
                } else {
                    return h('img', {
                        style: {
                            width: '40px'
                        },
                        attrs: {
                            src: 'https://static.licdn.cn/sc/h/3dtfvv2esz58o2zimai1h3v2d'
                        }
                    })
                }
            }

        }
    },
    {
        title: '机构名称',
        align: 'left',
        key: 'name',
        render: (h, params) => {
            if (!!params.row.url) {
                return h('a', {
                    attrs: {
                        href: 'https://www.linkedin.com' + params.row.url,
                        target: '_blank'
                    }
                }, params.row.name)
            } else {
                return h('span', {}, params.row.name)
            }
        }
    },
    {
        title: '行业',
        align: 'center',
        key: 'industry',
        width: 150
    },
    {
        title: '公司类型',
        align: 'center',
        key: 'company_type',
        width: 120
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['view']
    }
];
export const sColumns = [
    {
        title: 'LOGO',
        align: 'center',
        key: 'logo',
        width: 80,
        render: (h, params) => {
            if (!!params.row.logo) {
                if (!!params.row.website) {
                    return h('a', {
                        attrs: {
                            href: params.row.website,
                            target: '_blank'
                        }
                    }, [
                        h('img', {
                            style: {
                                width: '40px'
                            },
                            attrs: {
                                src: 'https://static.chasedream.com' + params.row.logo.split('/upload')[1]
                            }
                        })
                    ])
                } else {
                    return h('img', {
                        style: {
                            width: '40px'
                        },
                        attrs: {
                            src: 'https://static.chasedream.com' + params.row.logo.split('/upload')[1]
                        }
                    })
                }
            } else {
                if (!!params.row.website) {
                    return h('a', {
                        attrs: {
                            href: params.row.website,
                            target: '_blank'
                        }
                    }, [
                        h('img', {
                            style: {
                                width: '40px'
                            },
                            attrs: {
                                src: 'https://static.licdn.cn/sc/h/csnc6op83cjt60ym5equoy1km'
                            }
                        })
                    ])
                } else {
                    return h('img', {
                        style: {
                            width: '40px'
                        },
                        attrs: {
                            src: 'https://static.licdn.cn/sc/h/csnc6op83cjt60ym5equoy1km'
                        }
                    })
                }
            }
        }
    },
    {
        title: '学校名称',
        align: 'left',
        key: 'name',
        render: (h, params) => {
            if (!!params.row.url) {
                return h('a', {
                    attrs: {
                        href: 'https://www.linkedin.com' + params.row.url,
                        target: '_blank'
                    }
                }, params.row.name)
            } else {
                return h('span', {}, params.row.name)
            }
        }
    },
    {
        title: '联系电话',
        align: 'center',
        key: 'phone',
        width: 150
    },
    {
        title: '地点',
        align: 'center',
        key: 'school_location',
        width: 150
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['view']
    }
];
export const aColumns = [
    {
        title: 'ID',
        width: 80,
        align: 'center',
        key: 'id'
    },
    {
        title: '用户名',
        align: 'center',
        key: 'username'
    },
    {
        title: 'PIN',
        align: 'center',
        key: 'pin'
    },
    {
        title: '状态',
        align: 'center',
        key: 'status',
        width: 80,
        filterMultiple: false,
        render: (h, params) => {
            var style = {}
            if (params.row.status === 0) {
                style.color = '#19be6b';
            } else if (params.row.status === -1) {
                style.color = '#ed3f14';
            }

            if (params.row.status !== -1) {
                return h('Icon', {

                    style: style,
                    props: {
                        type: 'record',
                        size: 14
                    }
                })
            } else {
                return h('Poptip', {
                    props: {
                        confirm: false,
                        title: '',
                        transfer: true,
                        trigger: "hover"
                    }
                }, [
                    h('Icon', {
                        style: style,
                        props: {
                            type: 'record',
                            size: 14
                        }
                    }),
                    h('div', {
                        slot: 'content',
                        'class': 'api',
                        style: {
                            textAlign: 'center'
                        }
                    }, [
                        h('p', {
                            style: {
                                //width: '80px'
                                color: '#ed3f14'
                            },
                        }, params.row.error_message)
                    ]),
                ]);
            }
        }
    },
    {
        title: '#',
        align: 'center',
        width: 80,
        key: 'handle',
        handle: ['clear']
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];
export const lColumns = [
    {
        title: 'ID',
        width: 80,
        align: 'center',
        key: 'id'
    },
    {
        title: '用户名',
        align: 'center',
        key: 'username',
        width: 150
    },
    {
        title: '消息',
        align: 'center',
        key: 'message'
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 150,
        render: (h, params) => {
            if (!!params.row.created_at) {
                var c_date = new Date(parseInt(params.row.created_at + '000'));
                var y = c_date.getFullYear();
                var m = ((c_date.getMonth() + 1) + '').length > 1 ? c_date.getMonth() + 1 : '0' + (c_date.getMonth() + 1);
                var d = (c_date.getDate() + '').length > 1 ? c_date.getDate() : '0' + c_date.getDate();
                var hh = (c_date.getHours() + '').length > 1 ? c_date.getHours() : '0' + c_date.getHours();
                var mm = (c_date.getMinutes() + '').length > 1 ? c_date.getMinutes() : '0' + c_date.getMinutes();
                var s = (c_date.getSeconds() + '').length > 1 ? c_date.getSeconds() : '0' + c_date.getSeconds();
                return h('span', {
                }, y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + s)
            } else {
                return h('span', {}, '')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['view']
    }
];

const linkedinData = {
    iColumns: iColumns,
    i_sColumns: i_sColumns,
    wColumns: wColumns,
    wsColumns: wsColumns,
    oColumns: oColumns,
    sColumns: sColumns,
    aColumns: aColumns,
    lColumns: lColumns
};

export default linkedinData;
