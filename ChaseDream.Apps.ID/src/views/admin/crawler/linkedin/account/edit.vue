<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="linkedin-account-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑Linkedin账号
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="80">
                    <FormItem prop="username" label="用户名：">
                        <Input v-model="userform.username" readonly></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'l_account_edit',
        components: {

        },
        data () {
            return {
                userform: {
                    username: '',
                    password: ''
                },
                rules: {
                    username: [
                        { required: true, message: '账号不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                    ]
                },
                access_token: this.$store.state.user.access_token,
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_account_edit'){
                    this.userform = {}
                    this.userform = this.$store.state.stick.currentRow;
                    if(!this.userform.id){
                        this.$router.push({
                            name: 'l_account_list'
                        });
                    }
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.userform = {}
            this.userform = this.$store.state.stick.currentRow;
            if(!this.userform.id){
                this.$router.push({
                    name: 'l_account_list'
                });
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.userForm.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/linkedin/account',
                            method:'PUT',
                            data: {
                                username: _this.userform.username,
                                id: _this.userform.id,
                                password: _this.userform.password,
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'l_account_edit');
                                _this.$store.commit('closePage', 'l_account_edit');
                                _this.$router.push({
                                    name: 'l_account_list'
                                });
                            }else{

                            }
                        }).catch(function (err) {
                            console.log(err)
                        });
                    }
                });
            },
            closePage () {
                this.userform = {}
                this.$store.commit('removeTag', 'stick_user_edit');
                this.$store.commit('closePage', 'l_account_edit');
                this.$router.push({
                    name: 'l_account_list'
                });
            }

        }
    };
</script>

<style>

</style>
