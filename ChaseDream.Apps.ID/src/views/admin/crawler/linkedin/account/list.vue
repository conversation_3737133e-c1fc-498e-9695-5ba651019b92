<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="account-list" style="min-width:1000px;">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tabBtns">
                    <router-link to="/console/crawler/linkedin/wait/list">
                        <Button type="text" icon="ios-list">爬取列表</Button>
                    </router-link>
                    <Button type="text" icon="ios-person" class="on">账户</Button>
                    <router-link to="/console/crawler/linkedin/log/list">
                        <Button type="text" icon="document-text">日志</Button>
                    </router-link>
                </div>

                <div class="tool-bar">
                    <router-link to="/console/crawler/linkedin/account/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="uTable" @on-delete="handleDel" @on-clear="clear_data" v-model="aRows" :columns-list="aColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import lData from '../data/linkedin_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'l_account_list',
        components: {
            canEditTable
        },
        data () {
            return {
                aRows: [],
                aColumns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_account_list'){
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.aColumns = lData.aColumns;
                this.getlist(1);
            },
            getlist (n){
                this.aRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/account',
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.aRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            clear_data(){
                this.$Message.success('操作成功！');
                this.getlist(1);
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
