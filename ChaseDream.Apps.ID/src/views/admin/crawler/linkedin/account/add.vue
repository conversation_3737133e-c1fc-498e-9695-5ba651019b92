<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="linkedin-account-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加Linkedin帐号
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="100">
                    <FormItem prop="username" label="账号：">
                        <Input v-model="userform.username" placeholder="请输入账号"></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem prop="pin" label="短信验证码：">
                        <Input v-model="userform.pin" placeholder="请输入短信验证码" style="width:250px;"></Input>
                        <Button @click="verify" type="success" style="height: 40px;">验证</Button>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'l_account_add',
        components: {

        },
        data () {
            return {
                userform: {
                    username: '',
                    password: '',
                    pin: ''
                },
                rules: {
                    username: [
                        { required: true, message: '账号不能为空', trigger: 'blur' },
                        { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '密码不能为空', trigger: 'blur' },
                        { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                    ]
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_account_add'){
                    this.userform.username = '';
                    this.userform.password = '';
                    this.userform.pin = '';

                    this.$refs['userForm'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                this.$refs.userForm.validate((valid) => {
                    if (valid) {
                        var _this = this;
                        util.ajax({
                            url:'/api/v1/admin/linkedin/account',
                            method:'POST',
                            data: {
                                username: _this.userform.username,
                                password: _this.userform.password
                            },
                        }).then(function (res) {
                            if(res.data.msg === 'success'){
                                _this.$store.commit('removeTag', 'l_account_add');
                                _this.$store.commit('closePage', 'l_account_add');
                                _this.$router.push({
                                    name: 'l_account_list'
                                });
                            }else{
                                _this.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            _this.$Notice.error({
                                title: err.msg
                            });
                            console.log(err)
                        });
                    }
                });
            },
            verify (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/account/verify',
                    method:'POST',
                    data: {
                        username: _this.userform.username,
                        pin: _this.userform.pin
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'l_account_add');
                        _this.$store.commit('closePage', 'l_account_add');
                        _this.$router.push({
                            name: 'l_account_list'
                        });
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.userform.username = '';
                this.userform.password = '';
                this.userform.pin = '';
                this.$store.commit('removeTag', 'l_account_add');
                this.$router.push({
                    name: 'l_account_list'
                });
            }
        }
    };
</script>

<style>

</style>
