<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="log-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tabBtns">
                    <router-link to="/console/crawler/linkedin/wait/list">
                        <Button type="text" icon="ios-list">爬取列表</Button>
                    </router-link>
                    <router-link to="/console/crawler/linkedin/account/list">
                        <Button type="text" icon="ios-person">账户</Button>
                    </router-link>
                    <Button type="text" icon="document-text" class="on">日志</Button>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="uTable" @on-delete="handleDel" @showHtml="showHtml" v-model="lRows" :columns-list="lColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="HTML" class="log_html" width="800">
            <div>
                <img :src="screenshot" alt="" style="width: 100%;">
            </div>
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import lData from '../data/linkedin_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'l_log_list',
        components: {
            canEditTable
        },
        data () {
            return {
                lRows: [],
                lColumns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                screenshot: '',
                details: false,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_log_list'){
                    this.lRows = [];
                    this.lColumns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.log_html = '';
                    this.details = false;
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.lColumns = lData.lColumns;
                this.getlist(1);
            },
            getlist (n){
                this.lRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/crawl_log',
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.lRows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            showHtml (row){
                this.screenshot = 'https://static.chasedream.com' + row.screenshot.split('/upload')[1];
                this.details = true;
            },
            close (){
                this.screenshot = '';
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
