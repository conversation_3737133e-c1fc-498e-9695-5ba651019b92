<style lang="less">
    @import '../../../../styles/common.less';
    @import './linkedin.less';
</style>

<template>
    <div class="linkedin">

        <p v-if="!isAccess">您没有权限访问此页</p>

    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'linkedin_info',
        components: {

        },
        data () {
            return {
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'linkedin_info'){
                    this.getData();
                }
            }
        },
        mounted () {
        },
        methods: {
            getData () {
                this.$router.push({
                    name: 'l_info_list'
                });
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
