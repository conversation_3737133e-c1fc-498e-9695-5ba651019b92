<style lang="less">
    @import '../../../../../styles/common.less';
    @import '../linkedin.less';
</style>

<template>
    <div class="organization-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <div class="tabBtns">
                    <router-link to="/console/crawler/linkedin/info/list">
                        <Button type="text" icon="ios-folder">人物</Button>
                    </router-link>
                    <Button type="text" icon="home" class="on">机构</Button>
                    <router-link to="/console/crawler/linkedin/school/list">
                        <Button type="text" icon="ios-home">学校</Button>
                    </router-link>
                </div>
                <div class="edittable-con-1">
                    <can-edit-table refs="uTable" @on-delete="handleDel" @details="showDetails" v-model="oRows" :columns-list="oColumns"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="详情" class="info_details" width="800">
            <div class="i_d_t">
                <div class="logo" align="center" v-if="!!info_details.logo"><img :src="'https://static.chasedream.com' + info_details.logo.split('/upload')[1]" alt="" width="150" height="150"></div>
                <div class="logo" align="center" v-if="!info_details.logo"><img src="https://static.licdn.cn/sc/h/3dtfvv2esz58o2zimai1h3v2d" alt="" width="150" height="150"></div>

                <div>
                    <h2>{{info_details.name}}</h2>
                    <h4>{{info_details.industry}}</h4>
                    <p>{{info_details.company_location}} - {{info_details.linkedin_followers}} 位关注者</p>
                    <p v-if="!!info_details.summary" v-html="info_details.summary.split('\n\n').join('<br>').split('\n').join('<br>')"></p>
                </div>
            </div>
            <div class="i_d_d">
                <h2>公司简介</h2>
                <p v-if="info_details.desc" v-html="info_details.desc.split('\n').join('<br>')"></p>
            </div>
            <div class="i_d_c">
                <p><strong>网站</strong><a :href="info_details.website"><span>{{info_details.website}}</span></a></p>
                <p><strong>行业</strong><span>{{info_details.industry}}</span></p>
                <p><strong>规模</strong><span>{{info_details.staff_size}}</span></p>
                <p><strong>总部</strong><span>{{info_details.location}}</span></p>
                <p><strong>类型</strong><span>{{info_details.company_type}}</span></p>
                <p><strong>创立</strong><span>{{info_details.established_time}}</span></p>
                <p><strong>领域</strong><span>{{info_details.focus_areas}}</span></p>
            </div>



            <p align="right" style="font-size: 12px;color: #ccc;" v-if="!!info_details.update_by || !!info_details.updated_at">
                <span v-if="!!info_details.update_by">{{info_details.update_by}} </span><br>
                <span v-if="!!info_details.created_at"> {{info_details.created_time}}</span>
            </p>
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import lData from '../data/linkedin_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'l_organization_list',
        components: {
            canEditTable
        },
        data () {
            return {
                oRows: [],
                oColumns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                details: false,
                info_details:{},
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'l_log_list'){
                    this.oRows = [];
                    this.oColumns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.details = false;
                    this.info_details = {};
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.oColumns = lData.oColumns;
                this.getlist(1);
            },
            getlist (n){
                this.oRows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/organization',
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.oRows = res.data.data.rows;

                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            showDetails (row){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/linkedin/organization/' + row.id,
                    method:'GET',
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.info_details = res.data.data;
                        _this.info_details.created_time = '';
                        if(!!_this.info_details.created_at){
                            var c_date = new Date(parseInt(_this.info_details.created_at + '000'));
                            var y = c_date.getFullYear();
                            var m = ((c_date.getMonth() + 1) + '').length > 1 ? c_date.getMonth() + 1 : '0' + (c_date.getMonth() + 1);
                            var d = (c_date.getDate() + '').length > 1 ? c_date.getDate() : '0' + c_date.getDate();
                            var hh = (c_date.getHours() + '').length > 1 ? c_date.getHours() : '0' + c_date.getHours();
                            var mm = (c_date.getMinutes() + '').length > 1 ? c_date.getMinutes() : '0' + c_date.getMinutes();
                            var s = (c_date.getSeconds() + '').length > 1 ? c_date.getSeconds() : '0' + c_date.getSeconds();
                            _this.info_details.created_time = y +'-'+ m +'-'+ d +' '+ hh +':'+ mm +':'+ s;
                        }
                        _this.details = true;
                    }else{
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            close (){
                this.info_details = {};
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
