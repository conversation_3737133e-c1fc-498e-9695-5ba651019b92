<style lang="less">
    @import '../../../../styles/common.less';
    //@import '../monitor.less';
</style>

<template>
    <div class="wechat-account-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    添加
                </p>
                <Form :model="form" :label-width="150">
                    <FormItem prop="wx_id" label="微信ID：" required>
                        <Input v-model="form.wx_id" placeholder="请输入微信ID" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="uid" label="论坛UID：" required>
                        <Input v-model="form.uid" placeholder="请输入论坛UID" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="remark" label="备注：">
                        <!--<Input v-model="form.remark" placeholder="请输入备注" style="width:300px;"></Input>-->
                        <Input v-model="form.remark" type="textarea" :autosize="{minRows: 5,maxRows: 5}" placeholder="请输入备注" style="width:300px;"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'majia_info_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    wx_id: '',
                    uid: '',
                    remark: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'majia_info_add'){
                    this.form = {
                        wx_id: '',
                        uid: '',
                        remark: ''
                    }
                    this.$refs['form'].resetFields();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit () {
                if(!this.form.wx_id){
                    this.$Notice.error({
                        title: '微信ID不能为空！'
                    });
                    return false;
                }
                if(!this.form.uid){
                    this.$Notice.error({
                        title: '论坛UID不能为空！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/majia',
                    method:'POST',
                    data: this.form,
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'majia_info_add');
                        _this.$store.commit('closePage', 'majia_info_add');
                        _this.$router.push({
                            name: 'majia_info_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {
                    wx_id: '',
                    uid: '',
                    remark: ''
                }
                this.$store.commit('removeTag', 'majia_info_add');
                this.$router.push({
                    name: 'majia_info_list'
                });
            }
        }
    };
</script>

<style>

</style>
