<style lang="less">
    @import '../../../../styles/common.less';
    @import '../monitor.less';
</style>

<template>
    <div class="wechat member-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <br>
                <!--<div class="tool-bar">
                    <router-link to="/console/wechat/group/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <br><br>
                </div>-->
                <p align="right">共<span style="color:#19be6b"> {{total}} </span>条记录</p>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable sp_table"></can-edit-table>
                    <can-edit-table refs="uTable" @on-delete="handleDel" v-model="Rows" :columns-list="Columns" @on-updata="getlist(1)" class="listTable sp_table"></can-edit-table>
                </div>
                <!--<div class="edittable-con-1">
                    <can-edit-table refs="table" @on-delete="handleDel" @details="show_details" v-model="Rows" :columns-list="Columns" class="sp_table"></can-edit-table>
                </div>-->
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="详情" class="row_details">
            <p>
                <strong>ID</strong>
                <span>{{row_details.id}}</span>
            </p>
            <p>
                <strong>群名</strong>
                <span>{{row_details.name}}</span>
            </p>
            <p>
                <strong>群管理员</strong>
                <span>{{row_details.admin}}</span>
            </p>
            <p>
                <strong>TID</strong>
                <span>{{row_details.tid}}</span>
            </p>
            <p>
                <strong>FID</strong>
                <span>{{row_details.fid}}</span>
            </p>
            <p>
                <strong>帖子标题</strong>
                <span>{{row_details.subject}}</span>
            </p>
            <p>
                <strong>qr_code</strong>
                <span>{{row_details.qr_code}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span v-if="!!row_details.created_at">{{row_details.created_at.split('T')[0] +' '+ row_details.created_at.split('T')[1].split('.')[0]}}</span>
            </p>
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from '../data/monitor_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'wxmonitor_member_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                Columns: [],
                Rows:[],
                s_row:[{
                    id: '',
                    wechat: '',
                    forum_username: '',
                    forum_uid: ''
                }],
                s_columns: [],
                row_details: {},
                details: false,
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                status: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wxmonitor_member_list'){
                    this.isAccess = true;
                    this.Columns = [];
                    this.s_columns = [];
                    this.s_row = [
                        {
                            id: '',
                            chatroom_name: '',
                            member_count: '',
                            admin: '',
                            announcement: '',
                            note: ''
                        }
                    ];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.status = '';
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.Columns = wechatData.meColumns;
                this.s_columns = wechatData.me_sColumns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].chatroom_id){
                    s.push('chatroom_id=' + this.s_row[0].chatroom_id + '@chatroom');
                }
                if(!!this.s_row[0].chatroom_name){
                    s.push('chatroom_name=' + this.s_row[0].chatroom_name);
                }
                if(!!this.s_row[0].wx_id){
                    s.push('wx_id=' + this.s_row[0].wx_id);
                }
                if(!!this.s_row[0].wx_nickname){
                    s.push('wx_nickname=' + this.s_row[0].wx_nickname);
                }
                if(!!this.s_row[0].wx){
                    s.push('wx=' + this.s_row[0].wx);
                }
                if(this.s_row[0].join_method === 0 || this.s_row[0].join_method === 1){
                    s.push('join_method=' + this.s_row[0].join_method);
                }
                if(!!this.s_row[0].last_message_content){
                    s.push('last_message_content=' + this.s_row[0].last_message_content);
                }
                if(this.s_row[0].cd_worker === 0 || this.s_row[0].cd_worker === 1){
                    s.push('cd_worker=' + this.s_row[0].cd_worker);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.Rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/wechat_group_monitor_account' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.Rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            show_details (row){
                //console.log(row)
                this.row_details = row;
                this.details = true;
            },
            show_status (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/status',
                    method: 'GET',
                    params: {
                       /* page: _this.current,
                        page_size: _this.page_size*/
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.status = res.data.data.status;

                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            close (){
                this.row_details = {};
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            clear_data(){
                this.$Message.success('操作成功！');
                this.getlist(1);
            },
            set_s_data (obj){
                this.s_row = obj;
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
