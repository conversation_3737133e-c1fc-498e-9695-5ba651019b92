<style lang="less">
    @import '../../../../styles/common.less';
    @import '../monitor.less';
</style>

<template>
    <div class="wechat">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <br>
                <!--<div classtestol-bar">
                    <router-link to="/console/wxmonitor/group/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    &lt;!&ndash;<Button type="text" icon="eye" @click="show_status" v-if="!status" style="float: right;color: #19be6b">查看微信账号状态</Button>
                    <span v-if="!!status" style="float: right;color: #19be6b;margin-right: 20px;">{{status}}</span>&ndash;&gt;
                    <br><br>
                </div>-->
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable sp_table"></can-edit-table>
                    <can-edit-table refs="uTable" @on-delete="handleDel" v-model="Rows" :columns-list="Columns" @on-updata="getlist(1)" @details="show_details" class="listTable sp_table"></can-edit-table>
                </div>
               <!-- <div class="edittable-con-1">
                    <can-edit-table refs="table" @on-delete="handleDel" @details="show_details" v-model="Rows" :columns-list="Columns"></can-edit-table>
                </div>-->
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>
        <Modal v-model="details" title="详情" class="row_details">
            <p>
                <strong>ID</strong>
                <span>{{row_details.id}}</span>
            </p>
            <p>
                <strong>群ID</strong>
                <span>{{row_details.chatroom_id}}</span>
            </p>
            <p>
                <strong>群名</strong>
                <span>{{row_details.chatroom_name}}</span>
            </p>
            <p>
                <strong>群管理员</strong>
                <span>{{row_details.admin}}</span>
            </p>
            <p>
                <strong>群成员数</strong>
                <span>{{row_details.member_count}}</span>
            </p>
            <p>
                <strong>群公告</strong>
                <span>{{row_details.announcement}}</span>
            </p>
            <p>
                <strong>最后发言内容</strong>
                <span>{{row_details.last_message_content}}</span>
            </p>
            <p>
                <strong>最后发言时间</strong>
                <span>{{row_details.last_message_time_s}}</span>
            </p>
            <p>
                <strong>TID</strong>
                <span>{{row_details.tid}}</span>
            </p>
            <p>
                <strong>FID</strong>
                <span>{{row_details.fid}}</span>
            </p>
            <p>
                <strong>同步帖子</strong>
                <span>{{'https://forum.chasedream.com/thread-' + row_details.tid + '-1-1.html'}}</span>
            </p>
            <p>
                <strong>qr_code</strong>
                <span>{{row_details.qr_code}}</span>
            </p>
            <p>
                <strong>创建时间</strong>
                <span>{{row_details.created_at_s}}</span>
                <!--<span v-if="!!row_details.created_at">{{row_details.created_at.split('T')[0] +' '+ row_details.created_at.split('T')[1].split('.')[0]}}</span>-->
            </p>
            <div slot="footer">
                <Button type="primary" @click="close">关闭</Button>
            </div>
        </Modal>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from '../data/monitor_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'wxmonitor_group_list',
        components: {
            canEditTable
        },
        data () {
            return {
                isAccess: true,
                Columns: [],
                Rows:[],
                s_row: [
                    {
                        id: '',
                        chatroom_name: '',
                        member_count: '',
                        admin: '',
                        announcement: '',
                        note: ''
                    }
                ],
                s_columns: [],
                row_details: {},
                details: false,
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                status: ''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wxmonitor_group_list'){
                    this.isAccess = true;
                    this.Columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20;
                    this.current = 1;
                    this.status = '';
                    this.s_row = [
                        {
                            id: '',
                            chatroom_name: '',
                            member_count: '',
                            admin: '',
                            announcement: '',
                            note: ''
                        }
                    ];
                    this.s_columns = [],
                    this.getData();
                }
            }
        },
        mounted () {

        },
        methods: {
            getData () {
                this.Columns = wechatData.gColumns;
                this.s_columns = wechatData.gsColumns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].chatroom_id){
                    s.push('chatroom_id=' + this.s_row[0].chatroom_id + '@chatroom');
                }
                if(!!this.s_row[0].chatroom_name){
                    s.push('chatroom_name=' + this.s_row[0].chatroom_name);
                }
                if(!!this.s_row[0].admin){
                    s.push('admin=' + this.s_row[0].admin);
                }
                if(!!this.s_row[0].announcement){
                    s.push('announcement=' + this.s_row[0].announcement);
                }
                if(!!this.s_row[0].note){
                    s.push('note=' + this.s_row[0].note);
                }
                if(!!this.s_row[0].send_post){
                    s.push('send_post=' + this.s_row[0].send_post);
                }
                if(!!this.s_row[0].tid){
                    s.push('tid=' + this.s_row[0].tid);
                }
                if(!!this.s_row[0].fid){
                    s.push('fid=' + this.s_row[0].fid);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.Rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/wechat_group_monitor' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.Rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            show_details (row){
                console.log(row)
                this.row_details = row;
                if(!!this.row_details.last_message_time){
                    var d_t = new Date(parseInt(this.row_details.last_message_time + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    this.row_details.last_message_time_s = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;
                }
                if(!!this.row_details.created_at){
                    var d_t = new Date(parseInt(this.row_details.created_at + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    this.row_details.created_at_s = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;
                    //return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
                }
                this.details = true;
            },
            show_status (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/status',
                    method: 'GET',
                    params: {
                       /* page: _this.current,
                        page_size: _this.page_size*/
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        console.log(res.data)
                        _this.status = res.data.data.status;

                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            close (){
                this.row_details = {};
                this.details = false;
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            clear_data(){
                this.$Message.success('操作成功！');
                this.getlist(1);
            },
            set_s_data (obj){
                this.s_row = obj;
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
