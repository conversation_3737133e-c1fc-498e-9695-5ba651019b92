<style lang="less">
    @import '../../../../styles/common.less';
    @import '../monitor.less';
</style>

<template>
    <div class="wechat">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    创建监控群
                </p>
                <Form ref="wxForm" :model="form" :label-width="90">
                    <FormItem prop="name" label="群名：" required>
                        <!--<Input v-model="form.name" placeholder="请输入群名" style="width:300px;"></Input>-->
                        <!--<Select
                                v-model="form.name"
                                filterable


                                :loading="loading">
                            <Option v-for="(option, index) in options" :value="option.id" :key="index">{{option.payload.topic}}</Option>
                        </Select>-->
                        <Select v-if="randering" v-model="form.id" filterable ref="store">
                            <Option v-for="item in options" :value="item.id" :key="item.index">{{ item.payload.topic }}</Option>
                        </Select>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">创建</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from '../data/monitor_data.js';

    export default {
        name: 'wxmonitor_group_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                form: {
                    id: ''
                },
                loading: false,
                options: [],
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true,
                randering: false
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wxmonitor_group_add'){
                    this.$refs.wxForm.resetFields();
                    this.$refs.store.clearSingleSelect(); // 清空
                    this.form = {
                        id: ''
                    };
                    this.options = [];
                    this.loading = false;
                    this.errMsg = '';
                    this.randering = false;
                    this.get_options();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.get_options();
        },
        methods: {
            get_options (){
                this.options = [];
                this.randering = false;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/self_group_list',
                    method: 'GET'
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.options = res.data.data;
                        _this.randering = true;
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleSubmit () {
                if(!this.form.id){
                    this.$Notice.error({
                        title: '群名不能为空！'
                    });
                    return false;
                }

                var dataIn = {};
                for(var i=0;i<this.options.length;i++){
                    if(this.options[i].id === this.form.id){
                        dataIn = this.options[i].payload
                    }
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/wechat_group_monitor',
                    method:'POST',
                    data: {
                        payload: dataIn
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'wxmonitor_group_add');
                        _this.$store.commit('closePage', 'wxmonitor_group_add');
                        this.$refs.wxForm.resetFields();
                        this.$refs.store.clearSingleSelect(); // 清空
                        _this.$router.push({
                            name: 'wxmonitor_group_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.form = {
                    name: ''
                };
                this.options = [];
                this.loading = false;
                this.$store.commit('removeTag', 'wxmonitor_group_add');
                this.$refs.wxForm.resetFields();
                this.$refs.store.clearSingleSelect(); // 清空
                this.$router.push({
                    name: 'wxmonitor_group_list'
                });
            }
        }
    };
</script>

<style>

</style>
