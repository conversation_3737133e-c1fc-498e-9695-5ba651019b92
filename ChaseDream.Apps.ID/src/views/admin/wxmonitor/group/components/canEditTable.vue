<style lang="less">
    @import './editable-table.less';
</style>

<template>
    <div>
        <Table :ref="refs" :columns="columnsList" :data="thisTableData" border></Table>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

const viewButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'eye',//currentRow.editting ? 'success' : 'primary', //'test',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            click: () => {
                vm.$emit('details', currentRow);
            }
        }
    }); //, currentRow.editting ? '保存' : '编辑'
};
const editButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'edit',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                //vm.$router.push('/console/wechat/group/edit/'+currentRow.id)
                let cr = currentRow;
                vm.$store.commit('getCurrentRow', cr);
                vm.$router.push({
                    name: 'wechat_group_edit'
                });
            }
        }
    });
};
const deleteButton = (vm, h, currentRow, index) => {
    return h('Poptip', {
        props: {
            confirm: true,
            title: '您确定要删除这条数据吗?',
            transfer: true
        },
        on: {
            'on-ok': () => {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/wechat_group_monitor',
                    method:'DELETE',
                    data: {
                        id: currentRow.id
                    },
                }).then(function (res) {
                    //console.log(res.data);
                    if(res.data.msg === 'success'){
                        vm.thisTableData.splice(index, 1);
                        vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                        vm.$emit('on-delete', vm.handleBackdata(vm.thisTableData), index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        }
    }, [
        h('Icon', {
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            props: {
                type: 'trash-a', // error
                placement: 'top',
                //icon: 'trash-a'
                size: 24
            }
        }) //, '删除'
    ]);
};
const idInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.id,
                placeholder: 'ID'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'id', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'id', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.chatroom_id,
                placeholder: '群ID'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'chatroom_id', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'chatroom_id', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
    /*return h('Input', {
        props: {
            value: currentRow.id,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });*/
};
const chatroom_nameInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.chatroom_name,
                placeholder: '群名称'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'chatroom_name', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'chatroom_name', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.admin,
                placeholder: '群管理'
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'admin', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'admin', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
    /*return h('Input', {
        props: {
            value: currentRow.chatroom_name,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'chatroom_name', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'chatroom_name', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });*/
};
const adminInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.admin,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'admin', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'admin', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const announcementInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.announcement,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'announcement', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'announcement', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const last_message_contentInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.last_message_content,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'last_message_content', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'last_message_content', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const noteInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.note,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'note', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'note', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const tidInput = (vm, h, currentRow, index) => {
    return h('div',{},[
        h('Input', {
            props: {
                value: currentRow.tid,
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'tid', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'tid', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        }),
        h('Input', {
            props: {
                value: currentRow.fid,
            },
            style: {
                width: '100%',
                height: '28px',
                lineHeight: '28px'
            },
            on: {
                'on-blur': (e) => {
                    vm.$set(vm.thisTableData[0], 'fid', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                },
                'on-enter': (e) => {
                    vm.$set(vm.thisTableData[0], 'fid', e.target.value);
                    vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                    vm.$emit('on-search');
                }
            }
        })
    ]);
    /*return h('Input', {
        props: {
            value: currentRow.tid,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'tid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'tid', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });*/
};
const clearButton = (vm, h, currentRow, index) => {
    return h('span', {
        props: {},
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$set(vm.thisTableData[0], 'id', '');
                vm.$set(vm.thisTableData[0], 'chatroom_id', '');
                vm.$set(vm.thisTableData[0], 'chatroom_name', '');
                vm.$set(vm.thisTableData[0], 'member_count', '');
                vm.$set(vm.thisTableData[0], 'admin', '');
                vm.$set(vm.thisTableData[0], 'announcement', '');
                vm.$set(vm.thisTableData[0], 'last_message_content', '');
                vm.$set(vm.thisTableData[0], 'note', '');
                vm.$set(vm.thisTableData[0], 'tid', '');
                vm.$set(vm.thisTableData[0], 'fid', '');
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    }, '清除');
};
const send_postButton = (vm, h, currentRow, index) => {
    if(currentRow.send_post === true){ //1
        return h('Icon', {
            props: {
                type: 'stop',
                loading: currentRow.saving,
                size: 22
            },
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            on: {
                click: () => {
                    util.ajax({
                        url:'/api/v1/admin/wechat_group/wechat_group_monitor/send_post',
                        method:'POST',
                        data: {
                            id: currentRow.id,
                            send_post: false
                        },
                    }).then(function (res) {
                        console.log(res)
                        if(res.data.msg === 'success'){
                            currentRow.send_post = false;//0
                        }else{
                            vm.$Notice.error({
                                title: res.msg
                            });
                        }
                    }).catch(function (err) {
                        console.log(err)
                        vm.$Notice.error({
                            title: err.msg
                        });
                    });
                }
            }
        });
    }else if( currentRow.send_post === false){ //0
        if(!!currentRow.tid){
            return h('Icon', {
                props: {
                    type: 'play',
                    loading: currentRow.saving,
                    size: 22
                },
                style: {
                    margin: '0 8px',
                    color: '#19be6b',
                    cursor: 'pointer'
                },
                on: {
                    click: () => {
                        util.ajax({
                            url:'/api/v1/admin/wechat_group/wechat_group_monitor/send_post',
                            method:'POST',
                            data: {
                                id: currentRow.id,
                                tid: currentRow.tid,
                                chatroom_id: currentRow.chatroom_id,
                                send_post: true
                            },
                        }).then(function (res) {
                            console.log(res)
                            if(res.data.msg === 'success'){
                                currentRow.send_post = true;//1
                            }else{
                                vm.$Notice.error({
                                    title: res.msg
                                });
                            }
                        }).catch(function (err) {
                            console.log(err)
                            vm.$Notice.error({
                                title: err.msg
                            });
                        });
                    }
                }
            });
        }else {
            var tid = '';
            currentRow.show = false;
            return h('Poptip', {
                props: {
                    vModel: currentRow.show,
                    transfer: true,
                    placement: 'bottom-end',
                    width: '330'
                }
            }, [
                h('Icon', {
                    style: {
                        margin: '0 8px',
                        color: '#19be6b',
                        cursor: 'pointer'
                    },
                    props: {
                        type: 'play', // error
                        placement: 'top',
                        //icon: 'trash-a'
                        size: 22
                    }
                }), //, '删除'
                h('div', {
                    slot: 'content'
                },[
                    h('h3',{
                        style: {
                            height: '30px',
                            lineHeight: '30px;',
                            borderBottom: '1px solid #E9EAEC',
                            color: '#495060',
                            fontSize: '14px',
                            marginBottom: '10px',
                            fontWeight: 'normal'
                        }
                    },'请输入TID！'),
                    h('Input',{
                        style: {
                            display: 'block',
                            marginTop: '20px',
                            marginBottom: '14px',
                            background: '#fff',
                            width: '100%',
                            height: '50px'
                        },
                        props:{
                            type: 'text',
                            placeholder: '请输入TID',
                            value: tid
                        },
                        on:{
                            'on-change':(status)=>{
                                //console.log(status.target.value)
                                tid = status.target.value;
                            }
                        }
                    }),
                    h('Button',{
                        style:{
                            display: 'block',
                            width: '100%',
                            height: '34px',
                            background: '#2D8CF0',
                            color: '#fff',
                            fontSize: '14px'
                        },
                        on: {
                            click: () => {
                                if(!tid){
                                    vm.$Notice.error({
                                        title: '请输入TID'
                                    });
                                    return false;
                                }
                                var _this = this;
                                util.ajax({
                                    url:'/api/v1/admin/wechat_group/wechat_group_monitor/send_post',
                                    method:'POST',
                                    data: {
                                        id: currentRow.id,
                                        chatroom_id: currentRow.chatroom_id,
                                        tid: tid,
                                        send_post: true
                                    },
                                }).then(function (res) {
                                    //console.log(res.data);
                                    if(res.data.msg === 'success'){
                                        currentRow.send_post = 1;
                                        tid = '';
                                        currentRow.show = false;
                                    }else{
                                        vm.$Notice.error({
                                            title: res.msg
                                        });
                                    }
                                }).catch(function (err) {
                                    console.log(err)
                                    vm.$Notice.error({
                                        title: err.msg
                                    });
                                });
                            }
                        }
                    },'确定')
                ])
            ]);
        }

    }

};
const switchButton = (vm, h, currentRow, index) => {
    if(currentRow.status === 0){
        return h('Icon', {
            props: {
                type: 'stop',
                loading: currentRow.saving,
                size: 22
            },
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            on: {
                click: () => {
                    util.ajax({
                        url:'/api/v1/admin/wechat_group/wechat_group_monitor/switch',
                        method:'POST',
                        data: {
                            id: currentRow.id,
                            status: -1,
                        },
                    }).then(function (res) {
                        console.log(res)
                        if(res.data.msg === 'success'){
                            currentRow.status = -1;
                        }else{
                            vm.$Notice.error({
                                title: res.msg
                            });
                        }
                    }).catch(function (err) {
                        console.log(err)
                        vm.$Notice.error({
                            title: err.msg
                        });
                    });
                }
            }
        });
    }else if(currentRow.status === -1){
        return h('Icon', {
            props: {
                type: 'play',
                loading: currentRow.saving,
                size: 22
            },
            style: {
                margin: '0 8px',
                color: '#19be6b',
                cursor: 'pointer'
            },
            on: {
                click: () => {
                    util.ajax({
                        url:'/api/v1/admin/wechat_group/wechat_group_monitor/switch',
                        method:'POST',
                        data: {
                            id: currentRow.id,
                            status: 0,
                        },
                    }).then(function (res) {
                        console.log(res)
                        if(res.data.msg === 'success'){
                            currentRow.status = 0;
                        }else{
                            vm.$Notice.error({
                                title: res.msg
                            });
                        }
                    }).catch(function (err) {
                        console.log(err)
                        vm.$Notice.error({
                            title: err.msg
                        });
                    });
                }
            }
        });
    }
};
export default {
    name: 'canEditTable',
    props: {
        refs: String,
        columnsList: Array,
        value: Array,
        url: String,
        editIncell: {
            type: Boolean,
            default: false
        },
        hoverShow: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            columns: [],
            thisTableData: [],
            edittingStore: []
        };
    },
    created () {
        this.init();
    },
    methods: {
        init () {
            let vm = this;
            let editableCell = this.columnsList.filter(item => {
                if (item.editable) {
                    if (item.editable === true) {
                        return item;
                    }
                }
            });
            let cloneData = JSON.parse(JSON.stringify(this.value));
            let res = [];
            res = cloneData.map((item, index) => {
                let isEditting = false;
                if (this.thisTableData[index]) {
                    if (this.thisTableData[index].editting) {
                        isEditting = true;
                    } else {
                        for (const cell in this.thisTableData[index].edittingCell) {
                            if (this.thisTableData[index].edittingCell[cell] === true) {
                                isEditting = true;
                            }
                        }
                    }
                }
                if (isEditting) {
                    return this.thisTableData[index];
                } else {
                    this.$set(item, 'editting', false);
                    let edittingCell = {};
                    editableCell.forEach(item => {
                        edittingCell[item.key] = false;
                    });
                    this.$set(item, 'edittingCell', edittingCell);
                    return item;
                }
            });
            this.thisTableData = res;
            this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
            this.columnsList.forEach(item => {
                if (item.editable) {
                    item.render = (h, param) => {
                        let currentRow = this.thisTableData[param.index];
                        if (!currentRow.editting) {
                            if (this.editIncell) {
                                return h('Row', {
                                    props: {
                                        type: 'flex',
                                        align: 'middle',
                                        justify: 'center'
                                    }
                                }, [
                                    h('Col', {
                                        props: {
                                            span: '22'
                                        }
                                    }, [
                                        !currentRow.edittingCell[param.column.key] ? h('span', currentRow[item.key]) : cellInput(this, h, param, item)
                                    ]),
                                    h('Col', {
                                        props: {
                                            span: '2'
                                        }
                                    }, [
                                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                                    ])
                                ]);
                            } else {
                                return h('span', currentRow[item.key]);
                            }
                        } else {
                            return h('Input', {
                                props: {
                                    type: 'text',
                                    value: currentRow[item.key]
                                },
                                on: {
                                    'on-change' (event) {
                                        let key = param.column.key;
                                        vm.edittingStore[param.index][key] = event.target.value;
                                    }
                                }
                            });
                        }
                    };
                }
                if (item.handle) {
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.handle.forEach(item => {
                            if (item === 'edit') {
                                children.push(editButton(this, h, currentRowData, param.index));
                            } else if (item === 'delete') {
                                children.push(deleteButton(this, h, currentRowData, param.index));
                            } else if (item === 'view') {
                                children.push(viewButton(this, h, currentRowData, param.index));
                            } else if(item === 'clear'){
                                children.push(clearButton(this, h, currentRowData, param.index));
                            } else if(item === 'id'){
                                children.push(idInput(this, h, currentRowData, param.index));
                            } else if(item === 'chatroom_name'){
                                children.push(chatroom_nameInput(this, h, currentRowData, param.index));
                            } else if(item === 'admin'){
                                children.push(adminInput(this, h, currentRowData, param.index));
                            } else if(item === 'announcement'){
                                children.push(announcementInput(this, h, currentRowData, param.index));
                            } else if(item === 'last_message_content'){
                                children.push(last_message_contentInput(this, h, currentRowData, param.index));
                            } else if(item === 'note'){
                                children.push(noteInput(this, h, currentRowData, param.index));
                            } else if(item === 'tid'){
                                children.push(tidInput(this, h, currentRowData, param.index));
                            } else if(item === 'switch'){
                                children.push(switchButton(this, h, currentRowData, param.index));
                            } else if(item === 'send_post'){
                                children.push(send_postButton(this, h, currentRowData, param.index));
                            }
                        });
                        return h('div', children);
                    };
                }
            });
        },
        handleBackdata (data) {
            let clonedData = JSON.parse(JSON.stringify(data));
            clonedData.forEach(item => {
                delete item.editting;
                delete item.edittingCell;
                delete item.saving;
            });
            return clonedData;
        }
    },
    watch: {
        value (data) {
            this.init();
        }
    }
};
</script>
