export const  gColumns = [
    {
        title: 'ID/群ID',
        align: 'center',
        key: 'id',
        width: 120,
        render: (h, params) => {
            var url = window.location.href.split('console')[0];
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                }, params.row.id),
               /* h('p',{},params.row.chatroom_id)*/
                h('a',{
                    attrs: {
                        href: url + 'console/wxmonitor/message/list?chatroom_id=' + params.row.chatroom_id,
                        target: '_blank'
                    }
                }, params.row.chatroom_id.split('@')[0]),
            ])
        }
    },
    {
        title: '群名称/群管理',
        align: 'center',
        key: 'chatroom_name',
        width: 200,
        render: (h, params) => {
            var url = window.location.href.split('console')[0];
            return h('div',{}, [
                /*h('p',{
                    style: {
                        color: '#19be6b'
                    }
                }, params.row.chatroom_name),*/
                h('a',{
                    attrs: {
                        href: url + 'console/wxmonitor/message/list?chatroom_id=' + params.row.chatroom_id,
                        target: '_blank'
                    }
                }, params.row.chatroom_name),
                h('p',{},params.row.admin)
            ])
        }
    },
    {
        title: '群成员数',
        align: 'center',
        key: 'member_count',
        width: 60
    },
    {
        title: '最后发言时间/创建时间',
        align: 'center',
        key: 'created_at',
        width: 130,
        render: (h, params) => {
            if(!!params.row.last_message_time || !!params.row.created_at){
                var last_message_time = '/';
                var created_at = '/';
                if(!!params.row.message){
                    /*var d_t = new Date(parseInt(params.row.last_message_time + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    last_message_time = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;*/
                    last_message_time = params.row.message.created_at.split('T')[0] + ' ' + params.row.message.created_at.split('T')[1].split('+')[0];
                }
                if(!!params.row.created_at){
                    var d_t = new Date(parseInt(params.row.created_at + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    created_at = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;
                    //return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
                }

                //return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
                return h('div',{}, [
                    h('p',{
                        style: {
                            color: '#19be6b'
                        }
                    },last_message_time),
                    h('p',{},created_at)
                ])

            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '最后发言内容',
        align: 'center',
        key: 'text',
        render: (h, params) => {
            if(!!params.row.message){
                if(params.row.message.type === 7){
                    return h('p',{
                        style: {
                            maxHeight: '36px',
                            overflow: 'hidden',
                            cursor: 'pointer'
                        },
                        attrs: {
                            title: params.row.message.text
                        },
                        domProps: {
                            innerHTML: params.row.message.text
                        },

                    }) //,params.row.last_message_content
                }else if(params.row.message.type === 6 || !!params.row.message.isimage){
                    /*return h('img', {
                        style: {
                            width: '35px',
                            //borderRadius: '50%'
                        },
                        attrs: {
                            src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.message.attachment
                        }
                    })*/
                    return h('Poptip', {
                        props: {
                            confirm: false,
                            title: '',
                            transfer: true,
                            placement: 'bottom'
                        }
                    }, [
                        h('img', {
                            style: {
                                width: '35px',
                            },
                            attrs: {
                                src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.message.attachment
                            }
                        }),
                        h('div', {
                            slot: 'content',
                            'class': 'api',
                            style:{
                                textAlign: 'center'
                            }
                        },[
                            h('img', {
                                style: {
                                    width: 'auto',
                                    maxWidth: '500px',
                                    maxHeight: '500px'
                                },
                                attrs: {
                                    src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.message.attachment
                                }
                            })
                        ]),
                    ])
                }else if(params.row.message.type === 14){
                    return h('a',{
                        attrs: {
                            href: params.row.message.text,
                            target: '_blank'
                        },
                        style: {
                            display: 'inline-block',
                            maxHeight: '36px',
                            overflow: 'hidden',
                        }
                    }, params.row.message.text)
                }else {
                    var url = window.location.href.split('console')[0];
                    return h('a',{
                        attrs: {
                            href: url +  'api/v1/admin/wechat_group/wechat_group_monitor_message/download/' + params.row.message.id,
                            target: '_blank'
                        },
                        style: {
                        }
                    }, '下载')
                }
            }
        }
    },
    {
        title: '类型',
        align: 'center',
        key: 'type',
        width: 60,
        render: (h, params) => {
            var text = '';
            if(!!params.row.message){
                if(params.row.message.type === 5){
                    text = '表情';
                }else if(params.row.message.type === 6){
                    text = '图片';
                }else if(params.row.message.type === 7){
                    text = '文本';
                }else if(params.row.message.type === 14){
                    text = '链接';
                }else if(params.row.message.type === 1){
                    text = '附件';
                }else if(params.row.message.type === 2){
                    text = '语音';
                }else if(params.row.message.type === 15){
                    text = '视频';
                }
            }
            return h('span',{},text)
        }
    },
    /*{
        title: '最后发言时间',
        align: 'center',
        key: 'last_message_time',
        width: 110,
        render: (h, params) => {
            if(!!params.row.last_message_time){
                var d_t = new Date(parseInt(params.row.last_message_time + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
            }else {
                return h('span',{},'')
            }
        }
    },*/
    /*{
        title: '群管理员',
        align: 'center',
        key: 'admin',
        width: 100
    },*/
    {
        title: '群公告',
        align: 'center',
        key: 'announcement',
        width: 150
    },
    {
        title: '同步帖子',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: ['send_post']
    },
    {
        title: 'TID/FID',
        align: 'center',
        key: 'tid',
        width: 100,
        render: (h, params) => {
            if(!!params.row.tid){
                return h('div',{}, [
                    h('a',{
                        attrs: {
                            href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                            target: '_blank'
                        }
                    }, params.row.tid),
                    h('p',{},params.row.fid)
                ])
            }else {
                return h('span',{},)
            }
            /*return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.tid),
                h('p',{},params.row.fid)
            ])*/
        }
    },
    /*{
        title: '同步帖子',
        align: 'center',
        key: 'tid',
        width: 120,
        render: (h, params) => {
            if(!!params.row.tid){
                return h('a',{
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            }else {
                return h('span',{},)
            }
        }
    },*/

    {
        title: '监控',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: ['switch']
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['view','delete'] /*'edit','view','delete'*/
    }
];
export const  gsColumns = [
    {
        title: 'ID/群ID',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['id']
    },
    {
        title: '群名称/群管理',
        align: 'center',
        key: 'handle',
        width: 200,
        handle: ['chatroom_name']
    },
    {
        title: '群成员数',
        align: 'center',
        key: 'member_count',
        width: 60
    },
    {
        title: '最后发言时间/创建时间',
        align: 'center',
        key: 'created_at',
        width: 130

    },
    {
        title: '最后发言内容',
        align: 'center',
        key: 'handle',
        //handle: ['last_message_content']
    },
    {
        title: '类型',
        align: 'center',
        key: 'type',
        width: 60,
        //handle: ['type']
    },
    {
        title: '群公告',
        align: 'center',
        key: 'handle',
        width: 150,
        handle: ['announcement']
    },
    {
        title: '同步帖子',
        align: 'center',
        width: 60,
        /* key: 'handle',
         handle: ['send_post']*/
    },
    /*{
        title: '群备注',
        align: 'center',
        key: 'handle',
        handle: ['note']
    },*/
    {
        title: 'TID/FID',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['tid']
    },
    /*{
        title: '同步帖子',
        align: 'center',
        key: 'tid',
        width: 120,
        //handle: ['tid']
    },*/
    {
        title: '监控',
        align: 'center',
        width: 60,
        /*key: 'handle',
        handle: ['switch']*/
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['clear'] /*'edit','view','delete'*/
    }
];
export const  aColumns = [
    {
        title: 'ID', ///微信ID
        key: 'id',
        width: 100,
        align: 'center',
        render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.id),
               // h('p',{},params.row.wx_id)
            ])
        }
    },
    /*{
        title: '微信ID',
        key: 'wx_id',
        width: 100,
        align: 'center'
    },*/
    /*{
        title: 'ID/WXID',
        key: 'id',
        width: 120,
        align: 'center',
        render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.id),
                h('p',{},params.row.wx_id)
            ])
        }
    },*/
    {
        title: '微信头像',
        align: 'center',
        key: 'avatar',
        width: 60,
        render: (h, params) => {
            if(!!params.row.avatar){
                return h('img', {
                    style: {
                        width: '35px',
                        borderRadius: '50%'
                    },
                    attrs: {
                        src: params.row.avatar
                    }
                })
            }else{
                return h('span', {},'')
            }

        }
    },
    {
        title: '微信昵称/微信号',
        align: 'center',
        key: 'wx_nickname',
         width: 100,
        render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.wx_nickname),
                h('p',{},params.row.wx)
            ])
        }
    },
    /*{
        title: '微信号',
        align: 'center',
        key: 'wx_id',
        width: 100,
    },*/
    /*{
        title: '微信昵称/微信号',
        align: 'center',
        key: 'wx',
       /!* width: 120,*!/
        render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.wx_nickname),
                h('p',{},params.row.wx)
            ])
        }
    },*/
    {
        title: '微信性别/论坛性别',
        align: 'center',
        key: 'gender',
        width: 80,
        render: (h, params) => {
            var text = '';
            var f_text = '';
            if(params.row.gender === 0){
                text = '保密'
            }else if(params.row.gender === 1){
                text = '男'
            }else if(params.row.gender === 2){
                text = '女'
            }
            if(params.row.forum_gender === 0){
                f_text = '保密'
            }else if(params.row.forum_gender === 1){
                f_text = '男'
            }else if(params.row.forum_gender === 2){
                f_text = '女'
            }
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },text),
                h('p',{},f_text)
            ])
        }
    },
    /*{
        title: 'CD用户名/CD ID号',
        align: 'center',
        key: 'forum_uid',
        width: 120,
        render: (h, params) => {
            return h('div',{}, [
                h('p',{},params.row.forum_username),
                h('p',{},params.row.forum_uid)
            ])
        }
    },*/

    {
        title: 'CD用户名/CD ID号',
        align: 'center',
        key: 'forum_username',
        width: 90,
        render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.forum_username),
                h('p',{},params.row.forum_uid)
            ])
        }
    },
    /*{
        title: 'CD ID号',
        align: 'center',
        key: 'forum_uid',
        width: 90
    },*/
    {
        title: 'CD实名手机号',
        align: 'center',
        key: 'forum_mobile',
        width: 90
    },
    /*{
        title: '地区',
        align: 'center',
        key: 'area',
        width: 100
    },*/
    {
        title: '微信备注',
        align: 'center',
        key: 'note',
        render: (h, params) => {
            return h('p',{
                style: {
                    maxHeight: '36px',
                    overflow: 'hidden',
                    cursor: 'pointer'
                },
                attrs: {
                    title: params.row.note
                }

            },params.row.note)
        }
    },
    /*{
        title: '群出入日志',
        align: 'center',
        key: 'forum_uid',
        width: 100
    },*/
    /*{
        title: '标签',
        align: 'center',
        key: 'label',
        width: 100
    },
    {
        title: '描述',
        align: 'center',
        key: 'desc'
    },
    {
        title: '个性签名',
        align: 'center',
        key: 'sign',
        width: 100
    },*/
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 90,
        render: (h, params) => {
            if(!!params.row.created_at){
                var d_t = new Date(parseInt(params.row.created_at + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['edit']  /*'edit','delete'*/
    },
]
export const  s_Columns = [
    {
        title: 'ID', ///微信ID
        key: 'handle',
        width: 100,
        align: 'center',
        handle: ['id']
    },
   /* {
        title: '微信ID',
        key: 'handle',
        width: 90,
        align: 'center',
        handle: ['wx_id']
    },*/
    {
        title: '微信头像',
        key: 'handle',
        width: 60,
        align: 'center',
        handle: ['avatar']
    },
    {
        title: '微信昵称/微信号',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['wx_nickname']
    },
    /*{
        title: '微信号',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['wx_id']
    },*/
    {
        title: '微信性别/论坛性别',
        align: 'center',
        width: 80,
        key: 'handle',
        handle: ['gender']
    },
    {
        title: 'CD用户名/CD ID号',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['forum_username']
    },
    /*{
        title: 'CD ID号',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['forum_uid']
    },*/
    {
        title: 'CD实名手机号',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['forum_mobile']
    },
    {
        title: '微信备注',
        align: 'center',
        key: 'handle',
        handle: ['note']
    },
    /*{
        title: '地区',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['area']
    },*/
    /*{
        title: '群出入日志',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['forum_uid']
    },*/
    /*{
        title: '备注',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['note']
    },
    {
        title: '标签',
        align: 'center',
        width: 150,
        key: 'handle',
        handle: ['label']
    },
    {
        title: '描述',
        align: 'center',
        /!*width: 150,*!/
        key: 'handle',
        handle: ['desc']
    },*/
   /* {
        title: '个性签名',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['sign']
    },*/
    {
        title: '创建时间',
        align: 'center',
        width: 90,
        key: 'handle',
        handle: ['created_at']
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['clear']
    },
]
export const  mColumns = [
    {
        title: '状态',
        align: 'center',
        key: 'status',
        width: 60,
        render: (h, params) => {
            var style = {};
            var text = '';
            if(params.row.status === 0){
                style.color = '#ff9900';
                text = '待处理';
            }else if(params.row.status === 1){
                style.color = '#19be6b';
                text = '已转发';
            }else if(params.row.status === 2){
                style.color = '#ed3f14';
                text = '失败';
            }
            return h('Tooltip',{
                props: {
                    content: text,
                    placement: 'right'
                }
            },[
                h('Icon', {
                    style: style,
                    props: {
                        type: 'record',
                        size:14
                    }
                })
            ])
        }
    },
    {
        title: 'TID',
        align: 'center',
        key: 'tid',
        width: 90,
        render: (h, params) => {
            if(!!params.row.tid){
                return h('a',{
                    attrs: {
                        href: 'https://forum.chasedream.com/thread-' + params.row.tid + '-1-1.html',
                        target: '_blank'
                    }
                }, params.row.tid)
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '群名称',
        align: 'center',
        key: 'topic',
        width: 120
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'wx_id',
        width: 90
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'wx_name',
        width: 120
    },
    {
        title: '文本',
        align: 'center',
        key: 'text',
        render: (h, params) => {
            return h('p',{
                domProps: {
                    innerHTML: params.row.text
                },
                style: {
                    maxHeight: '36px',
                    overflow: 'hidden',
                    cursor: 'pointer'
                },
                attrs: {
                    title: params.row.note
                }
            })
        }
    },
    {
        title: '类型',
        align: 'center',
        key: 'type',
        width: 100,
        render: (h, params) => {
            var text = '';
            if(params.row.type === 5){
                text = '表情';
            }else if(params.row.type === 6){
                text = '图片';
            }else if(params.row.type === 7){
                text = '文本';
            }else if(params.row.type === 14){
                text = '链接';
            }else if(params.row.type === 1){
                text = '附件';
            }else if(params.row.type === 2){
                text = '语音';
            }else if(params.row.type === 15){
                text = '视频';
            }
            return h('span',{},text)
        }
    },
    /*{
        title: '错误信息',
        align: 'center',
        key: 'error_message',
        width: 120
    },*/
    {
        title: '操作',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: ['view']//'edit','delete'
    },
]
export const  s_mColumns = [
    {
        title: '状态',
        align: 'center',
        key: 'handle',
        width: 60,
        handle: ['status']
    },
    {
        title: 'TID',
        align: 'center',
        key: 'handle',
        width: 90,
        handle: ['tid']
    },
    {
        title: '群名称',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['topic']
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'handle',
        width: 90,
        handle: ['wx_id']
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['wx_name']
    },
    {
        title: '文本',
        align: 'center',
        key: 'handle',
        handle: ['text']
    },
    {
        title: '类型',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['type']
    },
    {
        title: '操作',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: ['clear']
    },
]
export const  meColumns = [
    {
        title: 'ID',
        align: 'center',
        key: 'id',
        width: 50
    },
    {
        title: '群ID',
        align: 'center',
        key: 'chatroom_id',
        width: 120,
        render: (h, params) => {
            if(!!params.row.chatroom_id){
                return h('span',{},params.row.chatroom_id.split('@')[0])
            }else {
                return h('span',{},'')
            }

        }
    },
    {
        title: '群名称',
        align: 'center',
        key: 'chatroom_name',
        width: 120,
        render: (h, params) => {
            if(!!params.row.wechat_group_monitor){
                return h('span',{},params.row.wechat_group_monitor.chatroom_name)
            }else {
                return h('span',{},'')
            }

        }
    },

    {
        title: '微信ID',
        align: 'center',
        key: 'wx_id',
        width: 120
    },
    /*{
        title: '微信昵称/微信号',
        align: 'center',
        key: 'wx_nickname',
        width: 90,

    },*/
    {
        title: '微信昵称/微信号',
        align: 'center',
        key: 'wx_nickname',
        width: 90,
        render: (h, params) => {
            return h('div',{}, [
                h('p',{
                    style: {
                        color: '#19be6b'
                    }
                },params.row.wx_nickname),
                h('p',{},params.row.wx)
            ])
        }
    },
    /*{
        title: '群内昵称',
        align: 'center',
        key: 'wx_group_nickname',
        width: 120
    },*/
    {
        title: '入群方式',
        align: 'center',
        key: 'join_method',
        width: 100,
        render: (h, params) => {
            var text = '';
            if(params.row.type === 5){
                text = '表情';
            }else if(params.row.type === 6){
                text = '图片';
            }else if(params.row.type === 7){
                text = '文本';
            }else if(params.row.type === 14){
                text = '链接';
            }else if(params.row.type === 1){
                text = '附件';
            }else if(params.row.type === 2){
                text = '语音';
            }else if(params.row.type === 15){
                text = '视频';
            }
            return h('span',{},text)
        }
    },
    /*{
        title: '本群最后发言时间',
        align: 'center',
        key: 'last_message_time',
        render: (h, params) => {
            if(!!params.row.last_message_time){
                var d_t = new Date(parseInt(params.row.last_message_time + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
            }else {
                return h('span',{},'')
            }
        }
    },*/
    {
        title: '最后发言内容',
        align: 'center',
        key: 'text',
        render: (h, params) => {
            if(!!params.row.message){
                if(params.row.message.type === 7){
                    return h('p',{
                        style: {
                            maxHeight: '36px',
                            overflow: 'hidden',
                            cursor: 'pointer'
                        },
                        attrs: {
                            title: params.row.message.text
                        },
                        domProps: {
                            innerHTML: params.row.message.text
                        },

                    }) //,params.row.last_message_content
                }else if(params.row.message.type === 6 || !!params.row.message.isimage){
                    /*return h('img', {
                        style: {
                            width: '35px',
                            //borderRadius: '50%'
                        },
                        attrs: {
                            src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.message.attachment
                        }
                    })*/
                    return h('Poptip', {
                        props: {
                            confirm: false,
                            title: '',
                            transfer: true,
                            placement: 'bottom'
                        }
                    }, [
                        h('img', {
                            style: {
                                width: '35px',
                            },
                            attrs: {
                                src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.message.attachment
                            }
                        }),
                        h('div', {
                            slot: 'content',
                            'class': 'api',
                            style:{
                                textAlign: 'center'
                            }
                        },[
                            h('img', {
                                style: {
                                    width: 'auto',
                                    maxWidth: '500px',
                                    maxHeight: '500px'
                                },
                                attrs: {
                                    src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.message.attachment
                                }
                            })
                        ]),
                    ])
                }else if(params.row.message.type === 14){
                    return h('a',{
                        attrs: {
                            href: params.row.message.text,
                            target: '_blank'
                        },
                        style: {
                            display: 'inline-block',
                            maxHeight: '36px',
                            overflow: 'hidden',
                        }
                    }, params.row.message.text)
                }else {
                    var url = window.location.href.split('console')[0];
                    return h('a',{
                        attrs: {
                            href: url +  'api/v1/admin/wechat_group/wechat_group_monitor_message/download/' + params.row.message.id,
                            target: '_blank'
                        },
                        style: {

                        }
                    }, '下载')
                }
            }
        }
    },
    {
        title: '类型',
        align: 'center',
        key: 'type',
        width: 60,
        render: (h, params) => {
            var text = '';
            if(!!params.row.message){
                if(params.row.message.type === 5){
                    text = '表情';
                }else if(params.row.message.type === 6){
                    text = '图片';
                }else if(params.row.message.type === 7){
                    text = '文本';
                }else if(params.row.message.type === 14){
                    text = '链接';
                }else if(params.row.message.type === 1){
                    text = '附件';
                }else if(params.row.message.type === 2){
                    text = '语音';
                }else if(params.row.message.type === 15){
                    text = '视频';
                }
            }
            return h('span',{},text)
        }
    },
    {
        title: 'CD工作人员',
        align: 'center',
        key: 'cd_worker',
        width: 60,
        render: (h, params) => {
            var isCD = false;
            if(!!params.row.cd_worker){
                isCD = true;
            }
            return h('span',{}, isCD ? '是' : '否')
        }
    },
    {
        title: '进群时间/本群最后发言时间',
        align: 'center',
        key: 'join_time',
        width: 130,
        render: (h, params) => {
            if(!!params.row.join_time || !!params.row.last_message_time){
                var join_time = '/';
                var last_message_time = '/';
                if(!!params.row.join_time){
                    var d_t = new Date(parseInt(params.row.join_time + '000'));
                    var y = d_t.getFullYear();
                    var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                    var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                    var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                    var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                    var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                    join_time = y + '-' + M + '-' + d + ' ' + hh + ':' + m + ':' + s;
                }
                if(!!params.row.message){
                    last_message_time = params.row.message.created_at.split('T')[0] + ' ' + params.row.message.created_at.split('T')[1].split('+')[0];
                }

                //return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
                return h('div',{}, [
                    h('p',{
                        style: {
                            color: '#19be6b'
                        }
                    },join_time),
                    h('p',{},last_message_time)
                ])

            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: []//'edit','delete''view'
    }
]
export const  me_sColumns = [
    {
        title: 'ID',
        align: 'center',
        key: 'handle',
        width: 50,
        handle: ['id']
    },
    {
        title: '群ID',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['chatroom_id']
    },
    {
        title: '群名称',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['chatroom_name']
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['wx_id']
    },
    {
        title: '微信昵称/微信号',
        align: 'center',
        key: 'wx_id',
        width: 90,
        handle: ['wx_nickname']
    },
    {
        title: '入群方式',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['join_method']
    },
    {
        title: '最后发言内容',
        align: 'center',
        key: 'handle',
        //handle: ['last_message_content']
    },
    {
        title: '类型',
        align: 'center',
        key: 'handle',
        width: 60
        //handle: ['type']
    },
    {
        title: 'CD工作人员',
        align: 'center',
        key: 'handle',
        width: 60,
        handle: ['cd_worker']
    },
    {
        title: '进群时间/本群最后发言时间',
        align: 'center',
        key: 'join_time',
        width: 130
    },
    {
        title: '操作',
        align: 'center',
        width: 60,
        key: 'handle',
        handle: ['clear']//'edit','delete''view'
    }
]
export const  mesColumns = [
    /*{
        title: 'ID',
        align: 'center',
        key: 'id',
        width: 50,
    },*/
    {
        title: '状态',
        align: 'center',
        key: 'status',
        width: 80,
        render: (h, params) => {
            var style = {};
            var text = '';
            if(params.row.status === 0){
                style.color = '#ff9900';
                text = '待处理';
            }else if(params.row.status === 1){
                style.color = '#19be6b';
                text = '已转发';
            }else if(params.row.status === 2){
                style.color = '#bbbec4';
                text = '失败';
            }else if(params.row.status === -1){
                style.color = '#ed3f14';
                text = '禁止转发';
            }
            return h('Tooltip',{
                props: {
                    content: text,
                    placement: 'right'
                }
            },[
                h('Icon', {
                    style: style,
                    props: {
                        type: 'record',
                        size:14
                    }
                })
            ])
        }
    },
    {
        title: '群ID',
        align: 'center',
        key: 'chatroom_id',
        width: 100,
        render: (h, params) => {
            if(!!params.row.chatroom_id){
                return h('span',{},params.row.chatroom_id.split('@')[0])
            }else {
                return h('span',{},'')
            }

        }
    },
    {
        title: '群名称',
        align: 'center',
        key: 'wechat_group_monitor',
        width: 100,
        render: (h, params) => {
            if(!!params.row.wechat_group_monitor){
                return h('span',{},params.row.wechat_group_monitor.chatroom_name)
            }else {
                return h('span',{},'')
            }

        }
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'wx_id',
        width: 100
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'wx_nickname',
        width: 100
    },
    /*{
        title: 'PostTime',
        align: 'center',
        key: 'method',
        width: 100,
    },*/
    {
        title: '内容',
        align: 'center',
        key: 'text',
        /*render: (h, params) => {
            return h('p',{
                style: {
                    maxHeight: '36px',
                    overflow: 'hidden',
                    cursor: 'pointer'
                },
                attrs: {
                    title: params.row.test
                }

            },params.row.test)
        }*/
        render: (h, params) => {
            if(params.row.type === 7){
                return h('p',{
                    style: {
                        maxHeight: '36px',
                        overflow: 'hidden',
                        cursor: 'pointer'
                    },
                    attrs: {
                        title: params.row.text
                    }
                }, params.row.text)
            }else if(params.row.type === 6 || !!params.row.isimage){
                /*return h('img', {
                    style: {
                        width: '35px',
                        //borderRadius: '50%'
                    },
                    attrs: {
                        src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.attachment
                    }
                })*/
                return h('Poptip', {
                    props: {
                        confirm: false,
                        title: '',
                        transfer: true,
                        placement: 'bottom'
                    }
                }, [
                    h('img', {
                        style: {
                            width: '35px',
                        },
                        attrs: {
                            src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.attachment
                        }
                    }),
                    h('div', {
                        slot: 'content',
                        'class': 'api',
                        style:{
                            textAlign: 'center'
                        }
                    },[
                        h('img', {
                            style: {
                                width: 'auto',
                                maxWidth: '500px',
                                maxHeight: '500px'
                            },
                            attrs: {
                                src: 'https://forum.chasedream.com/data/attachment/forum/' + params.row.attachment
                            }
                        })
                    ]),
                ])
            }else if(params.row.type === 14){
                return h('a',{
                    attrs: {
                        href: params.row.text,
                        target: '_blank'
                    },
                    style: {
                        display: 'inline-block',
                        maxHeight: '36px',
                        overflow: 'hidden',
                    }
                }, params.row.text)
            }else {
                var url = window.location.href.split('console')[0];
                return h('a',{
                    attrs: {
                        href: url +  'api/v1/admin/wechat_group/wechat_group_monitor_message/download/' + params.row.id,
                        target: '_blank'
                    },
                    style: {

                    }
                }, '下载')
            }

        }
    },
    {
        title: '内容类型',
        align: 'center',
        key: 'type',
        width: 100,
        render: (h, params) => {
            var text = '';
            if(params.row.type === 5){
             text = '表情';
             }else if(params.row.type === 6){
             text = '图片';
             }else if(params.row.type === 7){
             text = '文本';
             }else if(params.row.type === 14){
             text = '链接';
             }else if(params.row.type === 1){
             text = '附件';
             }else if(params.row.type === 2){
             text = '语音';
             }else if(params.row.type === 15){
             text = '视频';
             }
            return h('span',{},text)
        }
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 90,
        render: (h, params) => {
            if(!!params.row.created_at){
                /*var d_t = new Date(parseInt(params.row.created_at + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)*/
                var ts = params.row.created_at.split('T');
                if(ts.length > 1){
                    return h('div',{},[
                        h('p',{},ts[0]),
                        h('p',{},ts[1].split('+')[0])
                    ])
                }else {
                    return h('span',{},h('p',{},ts[0]))
                }
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: []//'edit','delete','view'
    }
]
export const  mes_sColumns = [
    {
        title: '状态',
        align: 'center',
        key: 'handle',
        width: 80,
        handle: ['status']
    },
    {
        title: '群ID',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['chatroom_id']
    },
    {
        title: '群名称',
        align: 'center',
        key: 'chatroom_name',
        width: 100,
        handle: ['chatroom_name']
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['wx_id']
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['wx_nickname']
    },
    /*{
     title: 'PostTime',
     align: 'center',
     key: 'method',
     width: 100,
     },*/
    {
        title: '内容',
        align: 'center',
        key: 'handle',
        handle: ['text']
    },
    {
        title: '内容类型',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['type']
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 90
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['clear']//'edit','delete','view'
    }
]
export const  logColumns = [
    {
        title: 'ID',
        align: 'center',
        key: 'id',
        width: 60,
    },
    {
        title: '群ID',
        align: 'center',
        key: 'chatroom_id',
        width: 100,
        render: (h, params) => {
            if(!!params.row.chatroom_id){
                return h('span',{},params.row.chatroom_id.split('@')[0])
            }else {
                return h('span',{},'')
            }

        }
    },
    {
        title: '群名称',
        align: 'center',
        key: 'chatroom_name',
        render: (h, params) => {
            if(!!params.row.wechat_group_monitor){
                return h('span',{},params.row.wechat_group_monitor.chatroom_name)
            }else {
                return h('span',{},'')
            }

        }
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'wx_id',
        width: 120
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'wx_nickname',
        width: 100,
        render: (h, params) => {
            if(!!params.row.wx_group_user){
                return h('span',{},params.row.wx_group_user.wx_nickname)
            }else {
                return h('span',{},'')
            }

        }
    },
    {
        title: '出入',
        align: 'center',
        key: 'in_out',
        width: 80,
        render: (h, params) => {
            var text = '';
            if(!!params.row.in_out){
                text = '入群';
            }else {
                text = '出群';
            }
            return h('span',{},text)
        }
    },
    {
        title: '方式',
        align: 'center',
        key: 'method',
        width: 100,
        render: (h, params) => {
            var text = '';
            /*if(params.row.type === 5){
                test = '表情';
            }else if(params.row.type === 6){
                test = '图片';
            }else if(params.row.type === 7){
                test = '文本';
            }else if(params.row.type === 14){
                test = '链接';
            }else if(params.row.type === 1){
                test = '附件';
            }else if(params.row.type === 2){
                test = '语音';
            }else if(params.row.type === 15){
                test = '视频';
            }*/
            //return h('span',{},params.row.method)
            if(!!params.row.wx_group_method){
                return h('span',{},params.row.wx_group_method.wx_nickname)
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 90,
        render: (h, params) => {
            if(!!params.row.created_at){
                var d_t = new Date(parseInt(params.row.created_at + '000'));
                var y = d_t.getFullYear();
                var M = (d_t.getMonth() + 1) < 10 ? '0' + (d_t.getMonth() + 1) : d_t.getMonth() + 1;
                var d = d_t.getDate() < 10 ? '0' + d_t.getDate() : d_t.getDate();
                var hh = d_t.getHours() < 10 ? '0' + d_t.getHours() : d_t.getHours();
                var m = d_t.getMinutes() < 10 ? '0' + d_t.getMinutes() : d_t.getMinutes();
                var s = d_t.getSeconds() < 10 ? '0' + d_t.getSeconds() : d_t.getSeconds();
                return h('span',{}, y + '-' + M + '-' + d + ' \n\b ' + hh + ':' + m + ':' + s)
            }else {
                return h('span',{},'')
            }
        }
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: []//'edit','delete','view'
    }
]
export const  log_sColumns = [
    {
        title: 'ID',
        align: 'center',
        key: 'handle',
        width: 60,
        handle: ['id']
    },
    {
        title: '群ID',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['chatroom_id']
    },
    {
        title: '群名称',
        align: 'center',
        key: 'handle',
        handle: ['chatroom_name']
    },
    {
        title: '微信ID',
        align: 'center',
        key: 'handle',
        width: 120,
        handle: ['wx_id']
    },
    {
        title: '微信昵称',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['wx_nickname']
    },
    {
        title: '出入',
        align: 'center',
        key: 'handle',
        width: 80,
        handle: ['in_out']
    },
    {
        title: '方式',
        align: 'center',
        key: 'handle',
        width: 100,
        handle: ['method']
    },
    {
        title: '创建时间',
        align: 'center',
        key: 'created_at',
        width: 90
    },
    {
        title: '操作',
        align: 'center',
        width: 100,
        key: 'handle',
        handle: ['clear']
    }
]
export const  mjColumns = [
    {
        title: '微信ID',
        align: 'center',
        key: 'wx_id',
        width: 120
    },
    {
        title: 'UID',
        align: 'center',
        key: 'uid',
        width: 200,
        render: (h, params) => {
            return h('a',{
                attrs: {
                    href: 'https://forum.chasedream.com/space-uid-'+ params.row.uid +'.html',
                    target: '_blank'
                }
            }, params.row.uid)
        }
    },
    {
        title: '开始/停止',
        align: 'center',
        key: 'handle',
        width: 90,
        handle: ['status']
    },
    {
        title: '备注',
        align: 'center',
        key: 'remark',
        /*width: 60*/
    },
    {
        title: '操作',
        align: 'center',
        width: 120,
        key: 'handle',
        handle: ['edit','delete']
    }
];

const tagData = {
    gColumns: gColumns,
    gsColumns: gsColumns,
    aColumns: aColumns,
    mColumns: mColumns,
    s_Columns: s_Columns,
    s_mColumns: s_mColumns,
    meColumns: meColumns,
    me_sColumns: me_sColumns,
    mesColumns: mesColumns,
    mes_sColumns: mes_sColumns,
    logColumns: logColumns,
    log_sColumns: log_sColumns,
    mjColumns: mjColumns
};

export default tagData;
