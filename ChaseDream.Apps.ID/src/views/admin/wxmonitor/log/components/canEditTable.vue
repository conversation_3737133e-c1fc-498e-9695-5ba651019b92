<style lang="less">
    @import './editable-table.less';
</style>

<template>
    <div>
        <Table :ref="refs" :columns="columnsList" :data="thisTableData" border></Table>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

const viewButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'eye',//currentRow.editting ? 'success' : 'primary', //'test',
            loading: currentRow.saving,
            size: 22
        },
        style: {
           // margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            click: () => {
                vm.$emit('details', currentRow);
            }
        }
    }); //, currentRow.editting ? '保存' : '编辑'
};
const editButton = (vm, h, currentRow, index) => {
    return h('Icon', {
        props: {
            type: 'edit',
            loading: currentRow.saving,
            size: 22
        },
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                //vm.$router.push('/console/wechat/group/edit/'+currentRow.id)
                let cr = currentRow;
                vm.$store.commit('getCurrentRow', cr);
                vm.$router.push({
                    name: 'wechat_group_edit'
                });
            }
        }
    });
};
const deleteButton = (vm, h, currentRow, index) => {
    return h('Poptip', {
        props: {
            confirm: true,
            title: '您确定要删除这条数据吗?',
            transfer: true
        },
        on: {
            'on-ok': () => {
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group',
                    method:'DELETE',
                    data: {
                        id: currentRow.id
                    },
                }).then(function (res) {
                    //console.log(res.data);
                    if(res.data.msg === 'success'){
                        vm.thisTableData.splice(index, 1);
                        vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                        vm.$emit('on-delete', vm.handleBackdata(vm.thisTableData), index);
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        }
    }, [
        h('Icon', {
            style: {
                margin: '0 8px',
                color: '#ed3f14',
                cursor: 'pointer'
            },
            props: {
                type: 'trash-a', // error
                placement: 'top',
                //icon: 'trash-a'
                size: 24
            }
        }) //, '删除'
    ]);
};
const idInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.id,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const chatroom_idInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.chatroom_id,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'chatroom_id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'chatroom_id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const chatroom_nameInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.chatroom_name,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'chatroom_name', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'chatroom_name', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const wx_idInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.wx_id,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'wx_id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'wx_id', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const wx_nicknameInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.wx_nickname,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'wx_nickname', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'wx_nickname', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const in_outInput = (vm, h, currentRow, index) => {
    var typeList =[
        {
            id: 0,
            name: '出群'
        },
        {
            id: 1,
            name: '入群'
        }
    ];
    return h('Select', {
        props: {
            value: currentRow.in_out, // 获取选择的下拉框的值
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-change': e => {
                vm.$set(vm.thisTableData[0], 'in_out', e);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    },typeList.map((item) => {
        return h('Option', { // 下拉框的值
            props: {
                value: item.id,
                label: item.name
            }
        })
    }));
};
const methodInput = (vm, h, currentRow, index) => {
    return h('Input', {
        props: {
            value: currentRow.method,
        },
        style: {
            width: '100%',
            height: '28px',
            lineHeight: '28px'
        },
        on: {
            'on-blur': (e) => {
                vm.$set(vm.thisTableData[0], 'method', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
            },
            'on-enter': (e) => {
                vm.$set(vm.thisTableData[0], 'method', e.target.value);
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    });
};
const clearButton = (vm, h, currentRow, index) => {
    return h('span', {
        props: {},
        style: {
            margin: '0 8px',
            color: '#2d8cf0',
            cursor: 'pointer'
        },
        on: {
            'click': () => {
                vm.$set(vm.thisTableData[0], 'id', '');
                vm.$set(vm.thisTableData[0], 'chatroom_id', '');
                vm.$set(vm.thisTableData[0], 'chatroom_name', '');
                vm.$set(vm.thisTableData[0], 'wx_id', '');
                vm.$set(vm.thisTableData[0], 'wx_nickname', '');
                vm.$set(vm.thisTableData[0], 'in_out', '');
                vm.$set(vm.thisTableData[0], 'method', '');
                vm.$emit('on-updata', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-search');
            }
        }
    }, '清除');
};
export default {
    name: 'canEditTable',
    props: {
        refs: String,
        columnsList: Array,
        value: Array,
        url: String,
        editIncell: {
            type: Boolean,
            default: false
        },
        hoverShow: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            columns: [],
            thisTableData: [],
            edittingStore: []
        };
    },
    created () {
        this.init();
    },
    methods: {
        init () {
            let vm = this;
            let editableCell = this.columnsList.filter(item => {
                if (item.editable) {
                    if (item.editable === true) {
                        return item;
                    }
                }
            });
            let cloneData = JSON.parse(JSON.stringify(this.value));
            let res = [];
            res = cloneData.map((item, index) => {
                let isEditting = false;
                if (this.thisTableData[index]) {
                    if (this.thisTableData[index].editting) {
                        isEditting = true;
                    } else {
                        for (const cell in this.thisTableData[index].edittingCell) {
                            if (this.thisTableData[index].edittingCell[cell] === true) {
                                isEditting = true;
                            }
                        }
                    }
                }
                if (isEditting) {
                    return this.thisTableData[index];
                } else {
                    this.$set(item, 'editting', false);
                    let edittingCell = {};
                    editableCell.forEach(item => {
                        edittingCell[item.key] = false;
                    });
                    this.$set(item, 'edittingCell', edittingCell);
                    return item;
                }
            });
            this.thisTableData = res;
            this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
            this.columnsList.forEach(item => {
                if (item.editable) {
                    item.render = (h, param) => {
                        let currentRow = this.thisTableData[param.index];
                        if (!currentRow.editting) {
                            if (this.editIncell) {
                                return h('Row', {
                                    props: {
                                        type: 'flex',
                                        align: 'middle',
                                        justify: 'center'
                                    }
                                }, [
                                    h('Col', {
                                        props: {
                                            span: '22'
                                        }
                                    }, [
                                        !currentRow.edittingCell[param.column.key] ? h('span', currentRow[item.key]) : cellInput(this, h, param, item)
                                    ]),
                                    h('Col', {
                                        props: {
                                            span: '2'
                                        }
                                    }, [
                                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                                    ])
                                ]);
                            } else {
                                return h('span', currentRow[item.key]);
                            }
                        } else {
                            return h('Input', {
                                props: {
                                    type: 'text',
                                    value: currentRow[item.key]
                                },
                                on: {
                                    'on-change' (event) {
                                        let key = param.column.key;
                                        vm.edittingStore[param.index][key] = event.target.value;
                                    }
                                }
                            });
                        }
                    };
                }
                if (item.handle) {
                    item.render = (h, param) => {
                        let currentRowData = this.thisTableData[param.index];
                        let children = [];
                        item.handle.forEach(item => {
                            if (item === 'edit') {
                                children.push(editButton(this, h, currentRowData, param.index));
                            } else if (item === 'delete') {
                                children.push(deleteButton(this, h, currentRowData, param.index));
                            } else if (item === 'view') {
                                children.push(viewButton(this, h, currentRowData, param.index));
                            } else if(item === 'clear'){
                                children.push(clearButton(this, h, currentRowData, param.index));
                            } else if(item === 'id'){
                                children.push(idInput(this, h, currentRowData, param.index));
                            } else if(item === 'chatroom_id'){
                                children.push(chatroom_idInput(this, h, currentRowData, param.index));
                            } else if(item === 'chatroom_name'){
                                children.push(chatroom_nameInput(this, h, currentRowData, param.index));
                            } else if(item === 'wx_id'){
                                children.push(wx_idInput(this, h, currentRowData, param.index));
                            }  else if(item === 'wx_nickname'){
                                children.push(wx_nicknameInput(this, h, currentRowData, param.index));
                            } else if(item === 'in_out'){
                                children.push(in_outInput(this, h, currentRowData, param.index));
                            } else if(item === 'method'){
                                children.push(methodInput(this, h, currentRowData, param.index));
                            }
                        });
                        return h('div', children);
                    };
                }
            });
        },
        handleBackdata (data) {
            let clonedData = JSON.parse(JSON.stringify(data));
            clonedData.forEach(item => {
                delete item.editting;
                delete item.edittingCell;
                delete item.saving;
            });
            return clonedData;
        }
    },
    watch: {
        value (data) {
            this.init();
        }
    }
};
</script>
