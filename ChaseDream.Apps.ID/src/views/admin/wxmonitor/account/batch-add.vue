<style lang="less">
    @import '../../../../styles/common.less';
    @import '../monitor.less';
</style>

<template>
    <div class="batch-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="plus"></Icon>
                    批量添加
                </p>
                <Form>
                    <FormItem>
                        <Input v-model="accounts" type="textarea" :rows="6" placeholder="微信号	论坛用户名"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <br><br>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'wxmonitor_account_batch_add',
        components: {
            // canEditTable
        },
        data () {
            return {
                accounts: '',
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wxmonitor_account_batch_add'){
                    this.accounts = '';
                    this.errMsg = '';
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
        },
        methods: {
            handleSubmit (arr) {
                //console.log(arr.join('/n'));
                if(!this.accounts){
                    this.$Notice.error({
                        title: '请输入请输入微信号	论坛用户名！！'
                    });
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/account/batch',
                    method:'POST',
                    data: {
                        accounts: this.accounts
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'wxmonitor_account_batch_add');
                        _this.$store.commit('closePage', 'wxmonitor_account_batch_add');
                        _this.$router.push({
                            name: 'wxmonitor_account_list'
                        });
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    //_this.errMsg = err.msg;
                    _this.$Notice.error({
                        title: err.msg
                    });
                    console.log(err)
                });
            },
            closePage () {
                this.$store.commit('removeTag', 'wxmonitor_account_batch_add');
                this.$router.push({
                    name: 'wxmonitor_account_list'
                });
            }
        }
    };
</script>

<style>

</style>
