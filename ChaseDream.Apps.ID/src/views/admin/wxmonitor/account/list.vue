<style lang="less">
    @import '../../../../styles/common.less';
    @import '../monitor.less';
</style>

<template>
    <div class="wechat-account-list">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <!--<div class="tool-bar">
                    <router-link to="/console/wechat/account/add">
                        <Button type="success" icon="plus">添 加</Button>
                    </router-link>
                    <router-link to="/console/wechat/account/batch-add">
                        <Button type="success" icon="plus">批量添加</Button>
                    </router-link>
                    <br><br>
                </div>-->
                <p align="right">共<span style="color:#19be6b"> {{total}} </span>条记录</p>
                <div class="edittable-con-1">
                    <can-edit-table refs="table1" v-model="s_row" @on-search="getlist(1)" @on-updata="set_s_data" :columns-list="s_columns" class="searchTable sp_table"></can-edit-table>
                    <can-edit-table refs="uTable" @on-delete="handleDel" v-model="Rows" :columns-list="Columns" @on-updata="getlist(1)" class="listTable sp_table"></can-edit-table>
                </div>
                <div class="page-bar">
                    <Page :total="total" :page-size="page_size" :current="current" @on-change="getlist" v-if="showPage"></Page>
                </div>
            </Card>
            </Col>
        </Row>



        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import wechatData from '../data/monitor_data.js';
    import canEditTable from './components/canEditTable.vue';

    export default {
        name: 'wxmonitor_account_list',
        components: {
            canEditTable
        },
        data () {
            return {
                Rows: [],
                Columns: [],
                s_row:[{
                    id: '',
                    wx_id: '',
                    wx_nickname: '',
                    gender: '',
                    forum_username: '',
                    forum_uid: '',
                    forum_mobile: '',
                    note: ''
                }],
                s_columns: [],
                total: 0,
                showPage: false,
                page_size: 20,
                current: 1,
                errMsg: '',
                isAccess: true
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wechat_account_list'){
                    this.Rows = [];
                    this.Columns = [];
                    this.s_row = [{
                        id: '',
                        wx_id: '',
                        wx_nickname: '',
                        gender: '',
                        forum_username: '',
                        forum_uid: '',
                        forum_mobile: '',
                        note: ''
                    }];
                    this.s_columns = [];
                    this.total = 0;
                    this.showPage = false;
                    this.page_size = 20,
                    this.getData();
                }
            }
        },
        mounted () {
            ///this.getData();
        },
        methods: {
            getData () {
                this.Columns = wechatData.aColumns;
                this.s_columns = wechatData.s_Columns;
                this.getlist(1);
            },
            getlist (n){
                var s = [];
                var text = '';

                if(!!this.s_row[0].id){
                    s.push('id=' + this.s_row[0].id);
                }
                if(!!this.s_row[0].wx_id){
                    s.push('wx_id=' + this.s_row[0].wx_id);
                }
                if(!!this.s_row[0].wx_nickname){
                    s.push('wx_nickname=' + this.s_row[0].wx_nickname);
                }
                if(!!this.s_row[0].wx){
                    s.push('wx=' + this.s_row[0].wx);
                }
                if(this.s_row[0].gender === 0 || this.s_row[0].gender === 1 || this.s_row[0].gender === 2){
                    s.push('gender=' + this.s_row[0].gender);
                }
                if(this.s_row[0].forum_gender === 0 || this.s_row[0].forum_gender === 1 || this.s_row[0].forum_gender === 2){
                    s.push('forum_gender=' + this.s_row[0].forum_gender);
                }
                if(!!this.s_row[0].forum_username){
                    s.push('forum_username=' + this.s_row[0].forum_username);
                }
                if(!!this.s_row[0].forum_uid){
                    s.push('forum_uid=' + this.s_row[0].forum_uid);
                }
                if(!!this.s_row[0].forum_mobile){
                    s.push('forum_mobile=' + this.s_row[0].forum_mobile);
                }
                if(!!this.s_row[0].note){
                    s.push('note=' + this.s_row[0].note);
                }
                if(s.length > 0){
                    text ='?' + s.join('&')
                }

                this.Rows = [];
                this.total = 0;
                this.current = n;
                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/wechat_group_monitor_user' + text,
                    method: 'GET',
                    params: {
                        page: _this.current,
                        page_size: _this.page_size
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //console.log(res.data)
                        _this.Rows = res.data.data.rows;
                        _this.total = res.data.data.count;
                        if(_this.total > _this.page_size){
                            _this.showPage = true;
                        }
                    }else{
                        _this.$Notice.error({
                            title: res.data.msg
                        });
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.$Notice.error({
                        title: err.msg
                    });
                });
            },
            handleDel (val, index) {
                this.$Message.success('删除了第' + (index + 1) + '行数据');
            },
            clear_data(){
                this.$Message.success('操作成功！');
                this.getlist(1);
            },
            set_s_data (obj){
                this.s_row = obj;
            }
        },
        created () {
            this.getData();
        }
    };
</script>

<style>

</style>
