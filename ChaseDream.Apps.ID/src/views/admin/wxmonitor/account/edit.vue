<style lang="less">
    @import '../../../../styles/common.less';
    @import '../monitor.less';
</style>

<template>
    <div class="wechat-account-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑关联帐号
                </p>
                <Form :model="form" :label-width="150">
                    <FormItem prop="wx_nickname" label="微信号：" required>
                        <Input v-model="form.wx" placeholder="请输入微信号" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="forum_username" label="CD用户名：" required>
                        <Input v-model="form.forum_username" placeholder="请输入CD用户名" style="width:300px;"></Input>
                    </FormItem>
                    <FormItem prop="forum_mobile" label="CD实名手机号：">
                        <Input v-model="form.forum_mobile" placeholder="请输入CD实名手机号" style="width:300px;"></Input>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';

    export default {
        name: 'wxmonitor_account_edit',
        components: {

        },
        data () {
            return {
                form: {},
                access_token: this.$store.state.user.access_token,
                isAccess: true,
                errMsg:''
            };
        },
        computed: {

        },
        watch: {
            '$route' (to) {
                if(to.name === 'wxmonitor_account_edit'){
                    this.form = {}
                    this.getData();
                }
            },
        },
        mounted () {
            if(parseInt(Cookies.get('uAccess')) === 1){
                this.isAccess = false
            }
            this.getData();
        },
        methods: {
            getData (){
                this.form = this.$store.state.users.currentRow;
                if(!this.form.forum_mobile){
                    this.form.forum_mobile = '';
                }
                /*if(!this.form.id){
                    this.$router.push({
                        name: 'wxmonitor_account_list'
                    });
                }*/
            },
            handleSubmit () {
                if(!this.form.wx_nickname){
                    this.$Notice.error({
                        title: '微信号不能为空！'
                    });
                    return false;
                }
                if(!this.form.forum_username){
                    this.$Notice.error({
                        title: 'CD用户名不能为空！'
                    });
                    return false;
                }
                /*if(!this.form.forum_mobile){
                    this.$Notice.error({
                        title: 'CD实名手机号不能为空！'
                    });
                    return false;
                }*/

                var _this = this;
                util.ajax({
                    url:'/api/v1/admin/wechat_group/wechat_group_monitor_user',
                    method:'PUT',
                    data: this.form,
                }).then(function (res) {
                    //console.log(res.data);
                    if(res.data.msg === 'success'){
                        _this.$store.commit('removeTag', 'wxmonitor_account_edit');
                        _this.$store.commit('closePage', 'wxmonitor_account_edit');
                        _this.$router.push({
                            name: 'wxmonitor_account_list'
                        });
                    }else{

                    }
                }).catch(function (err) {
                    console.log(err)
                });
            },
            closePage () {
                this.form = {}
                this.$store.commit('removeTag', 'wxmonitor_account_edit');
                this.$store.commit('closePage', 'wxmonitor_account_edit');
                this.$router.push({
                    name: 'wxmonitor_account_list'
                });
            }

        }
    };
</script>

<style>

</style>
