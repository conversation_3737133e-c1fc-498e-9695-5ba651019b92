.login{
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    /*background-image: url('https://file.iviewui.com/iview-admin/login_bg.jpg');*/
    background-size: cover;
    background-position: center;
    position: relative;
    background-color: #fff;
    .m-login-con{
        .logo{
            padding: 60px 10px 50px;
            text-align: center;
        }
        .form-con{
            .ivu-form-item{
                margin:0;
                padding: 0 15px;
                .ivu-form-item-content{
                    margin-left: 0 !important;
                }
                .ivu-form-item-label{
                    line-height: 35px;
                    font-size: 15px;
                    color: #4d4d4d;
                }
            }
            .ivu-form-item.mobile_email,.ivu-form-item.password{
                border-bottom: 1px solid #dbdbdb;
            }
            .ivu-form-item.mobile_email .ivu-form-item-content,.ivu-form-item.password .ivu-form-item-content{
                margin-left: 60px !important;
            }
            .ivu-form-item.mobile_email.mobile-item .ivu-form-item-content{
                position: relative;
                padding-left: 50px;

                .area_code_btn{
                    position: absolute;
                    left: 0;
                    font-size: 14px;
                    line-height: 55px;
                    .ivu-icon{
                        margin-left: 5px;
                    }
                }
            }
            .ivu-input{
                height: 55px;
                border: none;
                border-radius: 0;
                transition: none;
                padding: 4px 15px;
                font-size: 16px;
                box-shadow: none;
            }
            .cH{
                .addYz{
                    padding: 0 15px;
                    font-size: 14px;
                }
            }
            .terms{
                padding: 0 15px;
                .ivu-checkbox-wrapper{
                    font-size: 14px;
                }
            }
            .re-btn{
                padding: 0 15px;
                .ivu-btn-long{
                    height: 43px;
                    margin-bottom: 10px;
                    font-size: 15px;
                    border-radius: 0;
                }
                .ivu-btn-long.ivu-btn-ghost{
                    border-color: #2bc345;
                }
                p{
                    text-align: center;
                    a{
                        color: #808080;
                        font-size: 13px;
                    }
                }
            }
            .l_err_msg{
                padding: 0 15px;
                font-size: 14px;
            }

        }
        .weichat-login{
            text-align: center;
            margin-top: 30px;
            padding-bottom: 20px;
            span{
                display: block;
                text-align: center;
            }
        }
    }
  .form-tabs{
    display: flex;
    border-bottom: 1px solid #dbdbdb;
    div{
      width: 50%;
      text-align: center;
      font-size: 16px;
      color: #bfbfbf;
      line-height: 44px;
    }
    div.on{
      color: #3aa1ec;
      position: relative;
      span{
        width: 30px;
        height:2px;
        background: #3aa1ec;
        position: absolute;
        left: 50%;
        bottom: 0;
        margin-left: -15px;
      }
    }
  }
    .user,.code{
        width: 228px;
        height: 215px; /*275*/
        padding: 0 10px 10px;
        background: #f5fbff;
       /* position: relative;
        right: 12px;
        top: 250px;
        padding: 10px;
        background: #f5fbff;
        z-index: 999;*/

        h3{
            font-size: 12px;
            font-weight: bold;
            line-height: 24px;
            margin-bottom: 5px;
            color: #616161;
        }
        i.tab_icon{
            width: 24px;
            height: 24px;
            position: absolute;
            right: 5px;
            top: 5px;
            background: url("../images/register/to_code.png") no-repeat;
            cursor: pointer;
        }
        .ivu-dropdown{
            position: absolute;
            left: 10px;
            top: 2px;
        }
        .ivu-form-item{
            margin-bottom: 8px;
        }
        .ivu-form-item-content{
            line-height: 22px;
            a{
                color: #5A5A5A;
                text-decoration: underline;

                .ivu-btn {
                    margin-left: 10px;
                    background: #54A6E0;
                    color: #fff;
                }
            }
            .ivu-input{
                height: 26px;
                font-size: 12px;
            }
            .geetest_holder.geetest_wind{
                min-width: 200px;
            }

        }
        p.l_err_msg{
          margin-bottom: 5px;
          margin-top: -5px;
          min-height: 15px;
        }
        .re-btn{
            text-align: center;
            .ivu-btn{
                width: 70px;
                height:26px;
                padding: 0;
                border-radius: 0;

                span{
                    line-height: 26px;
                    font-size: 12px;
                    color: #fff;
                    display: inline-block;
                }
            }
        }
        .ivu-input{
            border-radius: 0;
        }
        .wx_Code{
            text-align: center;
            height: 110px;
            margin-top: 20px;
            img{
                vertical-align: top;
            }

        }
    }
    .user{
        .mobile_email{
            .ivu-input{
                padding-left: 65px;
            }
        }
        .form-tabs{
          margin-bottom: 8px;

          div{
            line-height: 20px;
            font-size: 12px;
            cursor: pointer;
            &.on span{
              bottom: -1px;
            }
          }
        }
    }
    .code{
      height: 214px;
        p{
            text-align: center;
            font-size: 12px;
            line-height: 22px;
            margin-top: 15px;
            color: #5A5A5A;
        }
      i.tab_icon{
        background-image: url("../images/register/to_pc.png");
      }
    }
}
#captcha{
    padding-bottom: 5px;
}
.l_err_msg{
    color: #ed3f14;
    font-size: 12px;
    margin-bottom: 10px;
}
.wcLogin{
    .ivu-modal-header{
        h3{
            font-size: 26px;
            line-height: 1.5;
            font-weight: 600;
        }
        p{
            font-size: 14px;
            font-weight: normal;
        }
    }
    .ivu-modal-body{
        background: #f8fafb;
        .steps{
            display: flex;
            justify-content:space-between;
            .item{
                width: 46%;
                text-align: center;
                p{
                    font-size: 14px;
                    font-weight: 600;
                    text-align: center;
                    margin-bottom: 10px;
                }
            }
        }
    }
}
.geetest_holder.geetest_mobile.geetest_ant.geetest_popup .geetest_popup_box{
    min-width: 190px !important;
}
.login_code{
    .ivu-poptip-body{
        padding: 0;

        .inner{
            height: 150px;
            width: 150px;
            overflow: auto;
            overflow-y: scroll;

            .ivu-btn-group>.ivu-btn{
                text-align: left;
            }
        }
    }
}
.login_pc_code{
    width: 150px;
    height: 140px;
    overflow: auto scroll;
}

.bind_mobile_modal{
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal{
    width: 80% !important;
    top:0;
    max-height: 90%;
    overflow: auto;
    overflow-y: scroll;

    h3{
      font-size: 20px;
      line-height: 22px;
      margin-bottom: 20px;
    }
    img{
      vertical-align: top;
    }
    p{
      margin-bottom: 20px;
      font-size: 16px;
    }
    .ivu-btn{
      background: #3AA2EC;
      height: 46px;
      border-radius: 0;
      font-size: 20px;
      margin: 10px 0 20px;
    }
    .law{
      h4,p{
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
      }
    }
    .btns{
      margin-top: 10px;
      span{
        display: inline-block;
        width: 48px;
        height: 48px;
        &.toOpen{
          background: url("../images/register/toOpen.png") no-repeat;
        }
        &.toClose{
          background: url("../images/register/toClose.png") no-repeat;
        }
      }
    }
    .bind{
      .ivu-btn{
        background: #2CC446;
      }
    }
  }
  .ivu-modal-footer{
    display: none;
  }
  .mobileItem .ivu-form-item-content{
    position: relative;

    .area_code_btn{
      width: 75px;
      position: absolute;
      left: 0;
      top:0;
      z-index: 10;
      height: 40px;
      font-size: 16px;
      line-height: 40px;
    }
    .ivu-input{
      padding-left: 75px;
    }
  }
  .ivu-input{
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    background: #F8F8F8;
    border: none;
    border-radius: 0;
  }
  .validate_code .ivu-form-item-content{
    position: relative;

    .ivu-btn{
      position: absolute;
      right: 5px;
      top: 5px;
      height: 30px;
      line-height: 30px;
      width: 100px;
      font-size: 12px;
      margin: 0;
      background: none;
      padding: 0;
      background: #fff;

      &.wait-btn{
        background: #efefef;
        color: #999;
      }
    }

  }
  .ivu-form-item{
    margin-bottom: 10px;

  }
}

.bind-mobile{
  width: 430px;
  padding: 10px 10px 20px 67px;
  background: url("../images/register/bind_pc.png") no-repeat 20px 10px;
  font-size: 14px;

  .lawCon{
    padding-right: 20px;
  }
  h3{
    color: #444;
    line-height: 32px;
    margin-bottom: 16px;
    font-size: 14px;
  }
  p{
    line-height: 22px;
    color: #444;
    &.l_err_msg{
      color: #ed3f14;
    }
  }
  .law{
    h4{
      font-weight: normal;
      color: #9A9A9A;
      margin-bottom: 10px;
    }
    p{
      color: #9A9A9A;
    }

  }
  .ivu-btn-primary{
    width: 130px;
    height: 23px;
    background: #54A6E0;
    color: #fff;
    padding: 0;
    line-height: 23px;
    border-radius: 0;
    margin: 10px 0 16px;
  }
  .btns{
    margin-top: 10px;
    text-align: center;
    span{
      display: inline-block;
      width: 24px;
      height: 24px;
      &.toOpen{
        background: url("../images/register/toOpen.png") no-repeat;
        background-size: 100%;
      }
      &.toClose{
        background: url("../images/register/toClose.png") no-repeat;
        background-size: 100%;
      }
    }
  }
  .ivu-input{
   /* width: 230px;*/
    height: 24px;
    padding: 2px 4px;
    line-height: 17px;
    border: 1px solid;
    border-color: #848484 #E0E0E0 #E0E0E0 #848484;
    background: #FFF url(https://forum.chasedream.com/static/image/common/px.png) repeat-x 0 0;
    font: 12px/1.5 Tahoma, 'Microsoft Yahei', 'Simsun';
    color: #444;
    border-radius: 0;
  }
  .mobileItem{
    .ivu-input{
      padding-left: 60px;
    }
    .ivu-dropdown{
      position: absolute;
      left: 5px;
      top: 2px;
    }
  }
  .validate_code{
    .ivu-input{
      width: 128px;
    }
    .ivu-btn{
      position: absolute;
      right: 0;
      top: 5px;
      height: 24px;
      line-height: 24px;
      padding: 0;
      border:none;
      color: #fff;
      width: 70px;
      background: #51a5df;
      border-radius: 0;
      &.wait-btn{
        background: #bfbfbf;
      }
    }
  }
  .nicknameForm{
    .ivu-form-item{
      margin-bottom: 15px;

      .ivu-dropdown-rel{
        a{
          color: #444;
        }
      }
    }
    .ivu-btn-success{
      width: 96px;
      height: 24px;
      color: #fff;
      padding: 0;
      line-height: 23px;
      border-radius: 0;
      margin: 10px 0 16px;
    }
  }
}
.ivu-poptip.pop_area_code{
  position: absolute;
  left: 0;
  top: 0;
  .ivu-poptip-rel .ivu-btn{
    padding: 0;
    border: none;
    background: none;
    box-shadow: none;
    padding-left: 7px;
  }
}
.ivu-poptip-popper.pop_area_code{
  width: 208px;
  .ivu-poptip-inner{
    .useCitys{
      word-break: break-word;
      white-space: normal;

      h4{
        margin: 10px 0 5px;
      }
      span{
        display: inline-block;
        width: 23%;
        text-align: center;
        cursor: pointer;
        &:hover{
          background: #e9ebf0;
        }
      }
    }
    .ivu-select-dropdown{
      height: 143px;
    }
  }
}
.login .user.o_user{
  width: auto;
  background: #fff;
}
#tcaptcha_transform{
  /*transform:scale(0.6,0.6);
  top: -75px !important;*/
}
