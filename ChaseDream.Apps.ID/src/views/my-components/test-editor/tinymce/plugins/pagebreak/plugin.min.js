!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('7', tinymce.util.Tools.resolve), g('1', ['7'], function (a) { return a('tinymce.Env'); }), g('2', ['7'], function (a) { return a('tinymce.PluginManager'); }), g('8', [], function () { var a = function (a) { return a.getParam('pagebreak_separator', '<!-- pagebreak -->'); }, b = function (a) { return a.getParam('pagebreak_split_block', !1); }; return {getSeparatorHtml: a, shouldSplitBlock: b}; }), g('4', ['1', '8'], function (a, b) { var c = function () { return 'mce-pagebreak'; }, d = function () { return '<img src="' + a.transparentSrc + '" class="' + c() + '" data-mce-resize="false" data-mce-placeholder />'; }, e = function (a) { var c = b.getSeparatorHtml(a), e = new RegExp(c.replace(/[\?\.\*\[\]\(\)\{\}\+\^\$\:]/g, function (a) { return '\\' + a; }), 'gi'); a.on('BeforeSetContent', function (a) { a.content = a.content.replace(e, d()); }), a.on('PreInit', function () { a.serializer.addNodeFilter('img', function (d) { for (var e, f, g = d.length; g--;) if (e = d[g], f = e.attr('class'), f && f.indexOf('mce-pagebreak') !== -1) { var h = e.parent; if (a.schema.getBlockElements()[h.name] && b.shouldSplitBlock(a)) { h.type = 3, h.value = c, h.raw = !0, e.remove(); continue; }e.type = 3, e.value = c, e.raw = !0; } }); }); }; return {setup: e, getPlaceholderHtml: d, getPageBreakClass: c}; }), g('3', ['4'], function (a) { var b = function (b) { b.addCommand('mcePageBreak', function () { b.settings.pagebreak_split_block ? b.insertContent('<p>' + a.getPlaceholderHtml() + '</p>') : b.insertContent(a.getPlaceholderHtml()); }); }; return {register: b}; }), g('5', ['4'], function (a) { var b = function (b) { b.on('ResolveName', function (c) { c.target.nodeName === 'IMG' && b.dom.hasClass(c.target, a.getPageBreakClass()) && (c.name = 'pagebreak'); }); }; return {setup: b}; }), g('6', [], function () { var a = function (a) { a.addButton('pagebreak', {title: 'Page break', cmd: 'mcePageBreak'}), a.addMenuItem('pagebreak', {text: 'Page break', icon: 'pagebreak', cmd: 'mcePageBreak', context: 'insert'}); }; return {register: a}; }), g('0', ['1', '2', '3', '4', '5', '6'], function (a, b, c, d, e, f) { return b.add('pagebreak', function (a) { c.register(a), f.register(a), d.setup(a), e.setup(a); }), function () {}; }), d('0')(); }());
