!(function () {
    var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('7', tinymce.util.Tools.resolve), g('2', ['7'], function (a) { return a('tinymce.PluginManager'); }), g('8', ['7'], function (a) { return a('tinymce.util.Tools'); }), g('11', [], function () { function a (a, b) { return function () { a.apply(b, arguments); }; } function b (b) { if (typeof this !== 'object') throw new TypeError('Promises must be constructed via new'); if (typeof b !== 'function') throw new TypeError('not a function'); this._state = null, this._value = null, this._deferreds = [], h(b, a(d, this), a(e, this)); } function c (a) { var b = this; return this._state === null ? void this._deferreds.push(a) : void i(function () { var c = b._state ? a.onFulfilled : a.onRejected; if (c === null) return void (b._state ? a.resolve : a.reject)(b._value); var d; try { d = c(b._value); } catch (b) { return void a.reject(b); }a.resolve(d); }); } function d (b) { try { if (b === this) throw new TypeError('A promise cannot be resolved with itself.'); if (b && (typeof b === 'object' || typeof b === 'function')) { var c = b.then; if (typeof c === 'function') return void h(a(c, b), a(d, this), a(e, this)); } this._state = !0, this._value = b, f.call(this); } catch (a) { e.call(this, a); } } function e (a) { this._state = !1, this._value = a, f.call(this); } function f () { for (var a = 0, b = this._deferreds.length; a < b; a++)c.call(this, this._deferreds[a]); this._deferreds = null; } function g (a, b, c, d) { this.onFulfilled = typeof a === 'function' ? a : null, this.onRejected = typeof b === 'function' ? b : null, this.resolve = c, this.reject = d; } function h (a, b, c) { var d = !1; try { a(function (a) { d || (d = !0, b(a)); }, function (a) { d || (d = !0, c(a)); }); } catch (a) { if (d) return; d = !0, c(a); } } if (window.Promise) return window.Promise; var i = b.immediateFn || typeof setImmediate === 'function' && setImmediate || function (a) { setTimeout(a, 1); }, j = Array.isArray || function (a) { return Object.prototype.toString.call(a) === '[object Array]'; }; return b.prototype['catch'] = function (a) { return this.then(null, a); }, b.prototype.then = function (a, d) { var e = this; return new b(function (b, f) { c.call(e, new g(a, d, b, f)); }); }, b.all = function () { var a = Array.prototype.slice.call(arguments.length === 1 && j(arguments[0]) ? arguments[0] : arguments); return new b(function (b, c) { function d (f, g) { try { if (g && (typeof g === 'object' || typeof g === 'function')) { var h = g.then; if (typeof h === 'function') return void h.call(g, function (a) { d(f, a); }, c); }a[f] = g, --e === 0 && b(a); } catch (a) { c(a); } } if (a.length === 0) return b([]); for (var e = a.length, f = 0; f < a.length; f++)d(f, a[f]); }); }, b.resolve = function (a) { return a && typeof a === 'object' && a.constructor === b ? a : new b(function (b) { b(a); }); }, b.reject = function (a) { return new b(function (b, c) { c(a); }); }, b.race = function (a) { return new b(function (b, c) { for (var d = 0, e = a.length; d < e; d++)a[d].then(b, c); }); }, b; }), g('12', [], function () { function a (a, b) { return e(document.createElement('canvas'), a, b); } function b (b) { var d, e; return d = a(b.width, b.height), e = c(d), e.drawImage(b, 0, 0), d; } function c (a) { return a.getContext('2d'); } function d (a) { var b = null; try { b = a.getContext('webgl') || a.getContext('experimental-webgl'); } catch (a) {} return b || (b = null), b; } function e (a, b, c) { return a.width = b, a.height = c, a; } return {create: a, clone: b, resize: e, get2dContext: c, get3dContext: d}; }), g('13', [], function () { function a (a) { var b = document.createElement('a'); return b.href = a, b.pathname; } function b (b) { var c, d, e, f; return b.indexOf('data:') === 0 ? (b = b.split(','), f = /data:([^;]+)/.exec(b[0]), f ? f[1] : '') : (e = {jpg: 'image/jpeg', jpeg: 'image/jpeg', png: 'image/png'}, c = a(b).split('.'), d = c[c.length - 1], d && (d = d.toLowerCase()), e[d]); } return {guessMimeType: b}; }), g('14', [], function () { function a (a) { return a.naturalWidth || a.width; } function b (a) { return a.naturalHeight || a.height; } return {getWidth: a, getHeight: b}; }), g('o', ['11', '12', '13', '14'], function (a, b, c, d) { function e (b) { return new a(function (a) { function c () { b.removeEventListener('load', c), a(b); }b.complete ? a(b) : b.addEventListener('load', c); }); } function f (a) { return e(a).then(function (a) { var c, e; return e = b.create(d.getWidth(a), d.getHeight(a)), c = b.get2dContext(e), c.drawImage(a, 0, 0), e; }); } function g (a) { return e(a).then(function (a) { var b = a.src; return b.indexOf('blob:') === 0 ? i(b) : b.indexOf('data:') === 0 ? k(b) : f(a).then(function (a) { return k(a.toDataURL(c.guessMimeType(b))); }); }); } function h (b) { return new a(function (a) { function c () { d.removeEventListener('load', c), a(d); } var d = new Image(); d.addEventListener('load', c), d.src = URL.createObjectURL(b), d.complete && c(); }); } function i (b) { return new a(function (a) { var c = new XMLHttpRequest(); c.open('GET', b, !0), c.responseType = 'blob', c.onload = function () { this.status == 200 && a(this.response); }, c.send(); }); } function j (a) { var b, c, d, e, f, g; if (a = a.split(','), e = /data:([^;]+)/.exec(a[0]), e && (f = e[1]), b = atob(a[1]), window.WebKitBlobBuilder) { for (g = new WebKitBlobBuilder(), c = new ArrayBuffer(b.length), d = 0; d < c.length; d++)c[d] = b.charCodeAt(d); return g.append(c), g.getBlob(f); } for (c = new Uint8Array(b.length), d = 0; d < c.length; d++)c[d] = b.charCodeAt(d); return new Blob([c], {type: f}); } function k (b) { return new a(function (a) { a(j(b)); }); } function l (a) { return a.indexOf('blob:') === 0 ? i(a) : a.indexOf('data:') === 0 ? k(a) : null; } function m (b, c, d) { return c = c || 'image/png', HTMLCanvasElement.prototype.toBlob ? new a(function (a) { b.toBlob(function (b) { a(b); }, c, d); }) : k(b.toDataURL(c, d)); } function n (b) { return new a(function (a) { var c = new FileReader(); c.onloadend = function () { a(c.result); }, c.readAsDataURL(b); }); } function o (a) { return n(a).then(function (a) { return a.split(',')[1]; }); } function p (a) { URL.revokeObjectURL(a.src); } return {blobToImage: h, imageToBlob: g, blobToDataUri: n, blobToBase64: o, imageToCanvas: f, canvasToBlob: m, revokeImageUrl: p, uriToBlob: l, dataUriToBlobSync: j}; }), g('p', ['11', 'o', '13', '12'], function (a, b, c, d) { function e (a, c) { function e () { return c; } function f (d, e) { return b.canvasToBlob(a, d || c, e); } function g (b, d) { return a.toDataURL(b || c, d); } function h (a, b) { return g(a, b).split(',')[1]; } function i () { return d.clone(a); } return {getType: e, toBlob: f, toDataURL: g, toBase64: h, toCanvas: i}; } function f (a) { return b.blobToImage(a).then(function (a) { var c = b.imageToCanvas(a); return b.revokeImageUrl(a), c; }).then(function (b) { return e(b, a.type); }); } function g (b, c) { return new a(function (a) { a(e(b, c)); }); } function h (a) { var d = c.guessMimeType(a.src); return b.imageToCanvas(a).then(function (a) { return e(a, d); }); } return {fromBlob: f, fromCanvas: g, fromImage: h}; }), g('c', ['o', 'p'], function (a, b) { var c = function (b) { return a.blobToImage(b); }, d = function (b) { return a.imageToBlob(b); }, e = function (b) { return a.blobToDataUri(b); }, f = function (b) { return a.blobToBase64(b); }, g = function (a) { return b.fromBlob(a); }, h = function (c) { return a.uriToBlob(c).then(b.fromBlob); }, i = function (a) { return b.fromImage(a); }, j = function (a, b, c) { return a.toBlob(b, c); }, k = function (b, c, d) { return a.dataUriToBlobSync(b.toDataURL(c, d)); }; return {blobToImage: c, imageToBlob: d, blobToDataUri: e, blobToBase64: f, blobToImageResult: g, dataUriToImageResult: h, imageToImageResult: i, imageResultToBlob: j, imageResultToBlobSync: k}; }), g('15', [], function () { function a (a, b, c) { return a = parseFloat(a), a > c ? a = c : a < b && (a = b), a; } function b () { return [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1]; } function c (a, b) { var c, d, e, f, g = [], h = new Array(10); for (c = 0; c < 5; c++) { for (d = 0; d < 5; d++)g[d] = b[d + 5 * c]; for (d = 0; d < 5; d++) { for (f = 0, e = 0; e < 5; e++)f += a[d + 5 * e] * g[e]; h[d + 5 * c] = f; } } return h; } function d (b, c) { return c = a(c, 0, 1), b.map(function (b, d) { return d % 6 === 0 ? b = 1 - (1 - b) * c : b *= c, a(b, 0, 1); }); } function e (b, d) { var e; return d = a(d, -1, 1), d *= 100, d < 0 ? e = 127 + d / 100 * 127 : (e = d % 1, e = e === 0 ? l[d] : l[Math.floor(d)] * (1 - e) + l[Math.floor(d) + 1] * e, e = 127 * e + 127), c(b, [e / 127, 0, 0, 0, 0.5 * (127 - e), 0, e / 127, 0, 0, 0.5 * (127 - e), 0, 0, e / 127, 0, 0.5 * (127 - e), 0, 0, 0, 1, 0, 0, 0, 0, 0, 1]); } function f (b, d) { var e, f, g, h; return d = a(d, -1, 1), e = 1 + (d > 0 ? 3 * d : d), f = 0.3086, g = 0.6094, h = 0.082, c(b, [f * (1 - e) + e, g * (1 - e), h * (1 - e), 0, 0, f * (1 - e), g * (1 - e) + e, h * (1 - e), 0, 0, f * (1 - e), g * (1 - e), h * (1 - e) + e, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1]); } function g (b, d) { var e, f, g, h, i; return d = a(d, -180, 180) / 180 * Math.PI, e = Math.cos(d), f = Math.sin(d), g = 0.213, h = 0.715, i = 0.072, c(b, [g + e * (1 - g) + f * -g, h + e * -h + f * -h, i + e * -i + f * (1 - i), 0, 0, g + e * -g + 0.143 * f, h + e * (1 - h) + 0.14 * f, i + e * -i + f * -0.283, 0, 0, g + e * -g + f * -(1 - g), h + e * -h + f * h, i + e * (1 - i) + f * i, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1]); } function h (b, d) { return d = a(255 * d, -255, 255), c(b, [1, 0, 0, 0, d, 0, 1, 0, 0, d, 0, 0, 1, 0, d, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1]); } function i (b, d, e, f) { return d = a(d, 0, 2), e = a(e, 0, 2), f = a(f, 0, 2), c(b, [d, 0, 0, 0, 0, 0, e, 0, 0, 0, 0, 0, f, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1]); } function j (b, e) { return e = a(e, 0, 1), c(b, d([0.393, 0.769, 0.189, 0, 0, 0.349, 0.686, 0.168, 0, 0, 0.272, 0.534, 0.131, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], e)); } function k (b, e) { return e = a(e, 0, 1), c(b, d([0.33, 0.34, 0.33, 0, 0, 0.33, 0.34, 0.33, 0, 0, 0.33, 0.34, 0.33, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], e)); } var l = [0, 0.01, 0.02, 0.04, 0.05, 0.06, 0.07, 0.08, 0.1, 0.11, 0.12, 0.14, 0.15, 0.16, 0.17, 0.18, 0.2, 0.21, 0.22, 0.24, 0.25, 0.27, 0.28, 0.3, 0.32, 0.34, 0.36, 0.38, 0.4, 0.42, 0.44, 0.46, 0.48, 0.5, 0.53, 0.56, 0.59, 0.62, 0.65, 0.68, 0.71, 0.74, 0.77, 0.8, 0.83, 0.86, 0.89, 0.92, 0.95, 0.98, 1, 1.06, 1.12, 1.18, 1.24, 1.3, 1.36, 1.42, 1.48, 1.54, 1.6, 1.66, 1.72, 1.78, 1.84, 1.9, 1.96, 2, 2.12, 2.25, 2.37, 2.5, 2.62, 2.75, 2.87, 3, 3.2, 3.4, 3.6, 3.8, 4, 4.3, 4.7, 4.9, 5, 5.5, 6, 6.5, 6.8, 7, 7.3, 7.5, 7.8, 8, 8.4, 8.7, 9, 9.4, 9.6, 9.8, 10]; return {identity: b, adjust: d, multiply: c, adjustContrast: e, adjustBrightness: h, adjustSaturation: f, adjustHue: g, adjustColors: i, adjustSepia: j, adjustGrayscale: k}; }), g('q', ['12', 'p', '15'], function (a, b, c) { function d (c, d) { function e (a, b) { var c, d, e, f, g, h = a.data, i = b[0], j = b[1], k = b[2], l = b[3], m = b[4], n = b[5], o = b[6], p = b[7], q = b[8], r = b[9], s = b[10], t = b[11], u = b[12], v = b[13], w = b[14], x = b[15], y = b[16], z = b[17], A = b[18], B = b[19]; for (g = 0; g < h.length; g += 4)c = h[g], d = h[g + 1], e = h[g + 2], f = h[g + 3], h[g] = c * i + d * j + e * k + f * l + m, h[g + 1] = c * n + d * o + e * p + f * q + r, h[g + 2] = c * s + d * t + e * u + f * v + w, h[g + 3] = c * x + d * y + e * z + f * A + B; return a; } var f, g = c.toCanvas(), h = a.get2dContext(g); return f = e(h.getImageData(0, 0, g.width, g.height), d), h.putImageData(f, 0, 0), b.fromCanvas(g, c.getType()); } function e (c, d) { function e (a, b, c) { function d (a, b, c) { return a > c ? a = c : a < b && (a = b), a; } var e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u; for (g = Math.round(Math.sqrt(c.length)), h = Math.floor(g / 2), e = a.data, f = b.data, t = a.width, u = a.height, j = 0; j < u; j++) for (i = 0; i < t; i++) { for (k = l = m = 0, o = 0; o < g; o++) for (n = 0; n < g; n++)p = d(i + n - h, 0, t - 1), q = d(j + o - h, 0, u - 1), r = 4 * (q * t + p), s = c[o * g + n], k += e[r] * s, l += e[r + 1] * s, m += e[r + 2] * s; r = 4 * (j * t + i), f[r] = d(k, 0, 255), f[r + 1] = d(l, 0, 255), f[r + 2] = d(m, 0, 255); } return b; } var f, g, h = c.toCanvas(), i = a.get2dContext(h); return f = i.getImageData(0, 0, h.width, h.height), g = i.getImageData(0, 0, h.width, h.height), g = e(f, g, d), i.putImageData(g, 0, 0), b.fromCanvas(h, c.getType()); } function f (c) { return function (d, e) { function f (a, b) { var c, d = a.data; for (c = 0; c < d.length; c += 4)d[c] = b[d[c]], d[c + 1] = b[d[c + 1]], d[c + 2] = b[d[c + 2]]; return a; } var g, h, i = d.toCanvas(), j = a.get2dContext(i), k = new Array(256); for (h = 0; h < k.length; h++)k[h] = c(h, e); return g = f(j.getImageData(0, 0, i.width, i.height), k), j.putImageData(g, 0, 0), b.fromCanvas(i, d.getType()); }; } function g (a) { return function (b, e) { return d(b, a(c.identity(), e)); }; } function h (a) { return function (b) { return d(b, a); }; } function i (a) { return function (b) { return e(b, a); }; } return {invert: h([-1, 0, 0, 0, 255, 0, -1, 0, 0, 255, 0, 0, -1, 0, 255, 0, 0, 0, 1, 0]), brightness: g(c.adjustBrightness), hue: g(c.adjustHue), saturate: g(c.adjustSaturation), contrast: g(c.adjustContrast), grayscale: g(c.adjustGrayscale), sepia: g(c.adjustSepia), colorize: function (a, b, e, f) { return d(a, c.adjustColors(c.identity(), b, e, f)); }, sharpen: i([0, -1, 0, -1, 5, -1, 0, -1, 0]), emboss: i([-2, -1, 0, -1, 1, 1, 0, 1, 2]), gamma: f(function (a, b) { return 255 * Math.pow(a / 255, 1 - b); }), exposure: f(function (a, b) { return 255 * (1 - Math.exp(-(a / 255) * b)); }), colorFilter: d, convoluteFilter: e}; }), g('16', ['11', 'o', '12', '14'], function (a, b, c, d) { function e (a, b, c) { var g = d.getWidth(a), h = d.getHeight(a), i = b / g, j = c / h, k = !1; (i < 0.5 || i > 2) && (i = i < 0.5 ? 0.5 : 2, k = !0), (j < 0.5 || j > 2) && (j = j < 0.5 ? 0.5 : 2, k = !0); var l = f(a, i, j); return k ? l.then(function (a) { return e(a, b, c); }) : l; } function f (b, e, f) { return new a(function (a) { var g = d.getWidth(b), h = d.getHeight(b), i = Math.floor(g * e), j = Math.floor(h * f), k = c.create(i, j), l = c.get2dContext(k); l.drawImage(b, 0, 0, g, h, 0, 0, i, j), a(k); }); } return {scale: e}; }), g('r', ['12', 'p', '16'], function (a, b, c) { function d (c, d) { var e = c.toCanvas(), f = a.create(e.width, e.height), g = a.get2dContext(f), h = 0, i = 0; return d = d < 0 ? 360 + d : d, d != 90 && d != 270 || a.resize(f, f.height, f.width), d != 90 && d != 180 || (h = f.width), d != 270 && d != 180 || (i = f.height), g.translate(h, i), g.rotate(d * Math.PI / 180), g.drawImage(e, 0, 0), b.fromCanvas(f, c.getType()); } function e (c, d) { var e = c.toCanvas(), f = a.create(e.width, e.height), g = a.get2dContext(f); return d == 'v' ? (g.scale(1, -1), g.drawImage(e, 0, -f.height)) : (g.scale(-1, 1), g.drawImage(e, -f.width, 0)), b.fromCanvas(f, c.getType()); } function f (c, d, e, f, g) { var h = c.toCanvas(), i = a.create(f, g), j = a.get2dContext(i); return j.drawImage(h, -d, -e), b.fromCanvas(i, c.getType()); } function g (a, d, e) { return c.scale(a.toCanvas(), d, e).then(function (c) { return b.fromCanvas(c, a.getType()); }); } return {rotate: d, flip: e, crop: f, resize: g}; }), g('d', ['q', 'r'], function (a, b) { var c = function (b) { return a.invert(b); }, d = function (b) { return a.sharpen(b); }, e = function (b) { return a.emboss(b); }, f = function (b, c) { return a.gamma(b, c); }, g = function (b, c) { return a.exposure(b, c); }, h = function (b, c, d, e) { return a.colorize(b, c, d, e); }, i = function (b, c) { return a.brightness(b, c); }, j = function (b, c) { return a.hue(b, c); }, k = function (b, c) { return a.saturate(b, c); }, l = function (b, c) { return a.contrast(b, c); }, m = function (b, c) { return a.grayscale(b, c); }, n = function (b, c) { return a.sepia(b, c); }, o = function (a, c) { return b.flip(a, c); }, p = function (a, c, d, e, f) { return b.crop(a, c, d, e, f); }, q = function (a, c, d) { return b.resize(a, c, d); }, r = function (a, c) { return b.rotate(a, c); }; return {invert: c, sharpen: d, emboss: e, brightness: i, hue: j, saturate: k, contrast: l, grayscale: m, sepia: n, colorize: h, gamma: f, exposure: g, flip: o, crop: p, resize: q, rotate: r}; }), h('e', Array), h('f', Error), g('a', ['e', 'f'], function (a, b) { var c = function () {}, d = function (a, b) { return function () { return a(b.apply(null, arguments)); }; }, e = function (a) { return function () { return a; }; }, f = function (a) { return a; }, g = function (a, b) { return a === b; }, h = function (b) { for (var c = new a(arguments.length - 1), d = 1; d < arguments.length; d++)c[d - 1] = arguments[d]; return function () { for (var d = new a(arguments.length), e = 0; e < d.length; e++)d[e] = arguments[e]; var f = c.concat(d); return b.apply(null, f); }; }, i = function (a) { return function () { return !a.apply(null, arguments); }; }, j = function (a) { return function () { throw new b(a); }; }, k = function (a) { return a(); }, l = function (a) { a(); }, m = e(!1), n = e(!0); return {noop: c, compose: d, constant: e, identity: f, tripleEquals: g, curry: h, not: i, die: j, apply: k, call: l, never: m, always: n}; }), g('1g', [], function () { return typeof window !== 'undefined' ? window : Function('return this;')(); }), g('17', ['1g'], function (a) { var b = function (b, c) { for (var d = void 0 !== c ? c : a, e = 0; e < b.length && void 0 !== d && d !== null; ++e)d = d[b[e]]; return d; }, c = function (a, c) { var d = a.split('.'); return b(d, c); }, d = function (a, b) { return void 0 !== a[b] && a[b] !== null || (a[b] = {}), a[b]; }, e = function (b, c) { for (var e = void 0 !== c ? c : a, f = 0; f < b.length; ++f)e = d(e, b[f]); return e; }, f = function (a, b) { var c = a.split('.'); return e(c, b); }; return {path: b, resolve: c, forge: e, namespace: f}; }), g('s', ['17'], function (a) { var b = function (b, c) { return a.resolve(b, c); }, c = function (a, c) { var d = b(a, c); if (void 0 === d) throw a + ' not available on this browser'; return d; }; return {getOrDie: c}; }), g('g', ['s'], function (a) { var b = function () { return a.getOrDie('URL'); }, c = function (a) { return b().createObjectURL(a); }, d = function (a) { b().revokeObjectURL(a); }; return {createObjectURL: c, revokeObjectURL: d}; }), h('h', clearTimeout), g('i', ['7'], function (a) { return a('tinymce.util.Delay'); }), g('j', ['7'], function (a) { return a('tinymce.util.Promise'); }), g('k', ['7'], function (a) { return a('tinymce.util.URI'); }), g('b', [], function () { var a = function (a) { return a.getParam('imagetools_toolbar', 'rotateleft rotateright | flipv fliph | crop editimage imageoptions'); }, b = function (a) { return a.getParam('imagetools_proxy'); }; return {getToolbarItems: a, getProxyUrl: b}; }), g('l', [], function () { function a (a) { function b (a) { return /^[0-9\.]+px$/.test(a); } var c, d; return c = a.style.width, d = a.style.height, c || d ? b(c) && b(d) ? {w: parseInt(c, 10), h: parseInt(d, 10)} : null : (c = a.width, d = a.height, c && d ? {w: parseInt(c, 10), h: parseInt(d, 10)} : null); } function b (a, b) { var c, d; b && (c = a.style.width, d = a.style.height, (c || d) && (a.style.width = b.w + 'px', a.style.height = b.h + 'px', a.removeAttribute('data-mce-style')), c = a.width, d = a.height, (c || d) && (a.setAttribute('width', b.w), a.setAttribute('height', b.h))); } function c (a) { return {w: a.naturalWidth, h: a.naturalHeight}; } return {getImageSize: a, setImageSize: b, getNaturalImageSize: c}; }), h('1m', Object), g('1h', ['a', '1m'], function (a, b) { var c = a.never, d = a.always, e = function () { return f; }, f = (function () { var f = function (a) { return a.isNone(); }, g = function (a) { return a(); }, h = function (a) { return a; }, i = function () {}, j = {fold: function (a, b) { return a(); }, is: c, isSome: c, isNone: d, getOr: h, getOrThunk: g, getOrDie: function (a) { throw new Error(a || 'error: getOrDie called on none.'); }, or: h, orThunk: g, map: e, ap: e, each: i, bind: e, flatten: e, exists: c, forall: d, filter: e, equals: f, equals_: f, toArray: function () { return []; }, toString: a.constant('none()')}; return b.freeze && b.freeze(j), j; }()), g = function (a) { var b = function () { return a; }, h = function () { return k; }, i = function (b) { return g(b(a)); }, j = function (b) { return b(a); }, k = {fold: function (b, c) { return c(a); }, is: function (b) { return a === b; }, isSome: d, isNone: c, getOr: b, getOrThunk: b, getOrDie: b, or: h, orThunk: h, map: i, ap: function (b) { return b.fold(e, function (b) { return g(b(a)); }); }, each: function (b) { b(a); }, bind: j, flatten: b, exists: j, forall: j, filter: function (b) { return b(a) ? k : f; }, equals: function (b) { return b.is(a); }, equals_: function (b, d) { return b.fold(c, function (b) { return d(a, b); }); }, toArray: function () { return [a]; }, toString: function () { return 'some(' + a + ')'; }}; return k; }, h = function (a) { return a === null || void 0 === a ? f : g(a); }; return {some: g, none: e, from: h}; }), h('1i', String), g('18', ['1h', 'e', 'f', '1i'], function (a, b, c, d) { var e = (function () { var a = b.prototype.indexOf, c = function (b, c) { return a.call(b, c); }, d = function (a, b) { return u(a, b); }; return void 0 === a ? d : c; }()), f = function (b, c) { var d = e(b, c); return d === -1 ? a.none() : a.some(d); }, g = function (a, b) { return e(a, b) > -1; }, h = function (a, b) { return t(a, b).isSome(); }, i = function (a, b) { for (var c = [], d = 0; d < a; d++)c.push(b(d)); return c; }, j = function (a, b) { for (var c = [], d = 0; d < a.length; d += b) { var e = a.slice(d, d + b); c.push(e); } return c; }, k = function (a, c) { for (var d = a.length, e = new b(d), f = 0; f < d; f++) { var g = a[f]; e[f] = c(g, f, a); } return e; }, l = function (a, b) { for (var c = 0, d = a.length; c < d; c++) { var e = a[c]; b(e, c, a); } }, m = function (a, b) { for (var c = a.length - 1; c >= 0; c--) { var d = a[c]; b(d, c, a); } }, n = function (a, b) { for (var c = [], d = [], e = 0, f = a.length; e < f; e++) { var g = a[e], h = b(g, e, a) ? c : d; h.push(g); } return {pass: c, fail: d}; }, o = function (a, b) { for (var c = [], d = 0, e = a.length; d < e; d++) { var f = a[d]; b(f, d, a) && c.push(f); } return c; }, p = function (a, b) { if (a.length === 0) return []; for (var c = b(a[0]), d = [], e = [], f = 0, g = a.length; f < g; f++) { var h = a[f], i = b(h); i !== c && (d.push(e), e = []), c = i, e.push(h); } return e.length !== 0 && d.push(e), d; }, q = function (a, b, c) { return m(a, function (a) { c = b(c, a); }), c; }, r = function (a, b, c) { return l(a, function (a) { c = b(c, a); }), c; }, s = function (b, c) { for (var d = 0, e = b.length; d < e; d++) { var f = b[d]; if (c(f, d, b)) return a.some(f); } return a.none(); }, t = function (b, c) { for (var d = 0, e = b.length; d < e; d++) { var f = b[d]; if (c(f, d, b)) return a.some(d); } return a.none(); }, u = function (a, b) { for (var c = 0, d = a.length; c < d; ++c) if (a[c] === b) return c; return -1; }, v = b.prototype.push, w = function (a) { for (var d = [], e = 0, f = a.length; e < f; ++e) { if (!b.prototype.isPrototypeOf(a[e])) throw new c('Arr.flatten item ' + e + ' was not an array, input: ' + a); v.apply(d, a[e]); } return d; }, x = function (a, b) { var c = k(a, b); return w(c); }, y = function (a, b) { for (var c = 0, d = a.length; c < d; ++c) { var e = a[c]; if (b(e, c, a) !== !0) return !1; } return !0; }, z = function (a, b) { return a.length === b.length && y(a, function (a, c) { return a === b[c]; }); }, A = b.prototype.slice, B = function (a) { var b = A.call(a, 0); return b.reverse(), b; }, C = function (a, b) { return o(a, function (a) { return !g(b, a); }); }, D = function (a, b) { for (var c = {}, e = 0, f = a.length; e < f; e++) { var g = a[e]; c[d(g)] = b(g, e); } return c; }, E = function (a) { return [a]; }, F = function (a, b) { var c = A.call(a, 0); return c.sort(b), c; }, G = function (b) { return b.length === 0 ? a.none() : a.some(b[0]); }, H = function (b) { return b.length === 0 ? a.none() : a.some(b[b.length - 1]); }; return {map: k, each: l, eachr: m, partition: n, filter: o, groupBy: p, indexOf: f, foldr: q, foldl: r, find: s, findIndex: t, flatten: w, bind: x, forall: y, exists: h, contains: g, equal: z, reverse: B, chunk: j, difference: C, mapToObject: D, pure: E, sort: F, range: i, head: G, last: H}; }), g('19', ['s'], function (a) { return function () { var b = a.getOrDie('FileReader'); return new b(); }; }), g('1a', ['s'], function (a) { return function () { var b = a.getOrDie('XMLHttpRequest'); return new b(); }; }), g('u', ['19', '1a', 'j', '8'], function (a, b, c, d) { var e = function (a) { return a !== null && void 0 !== a; }, f = function (a, b) { var c; return c = b.reduce(function (a, b) { return e(a) ? a[b] : void 0; }, a), e(c) ? c : null; }, g = function (a, e) { return new c(function (c) { var f; f = new b(), f.onreadystatechange = function () { f.readyState === 4 && c({status: f.status, blob: this.response}); }, f.open('GET', a, !0), d.each(e, function (a, b) { f.setRequestHeader(b, a); }), f.responseType = 'blob', f.send(); }); }, h = function (b) { return new c(function (c) { var d = new a(); d.onload = function (a) { var b = a.target; c(b.result); }, d.readAsText(b); }); }, i = function (a) { var b; try { b = JSON.parse(a); } catch (a) {} return b; }; return {traverse: f, readBlob: h, requestUrlAsBlob: g, parseJson: i}; }), g('t', ['18', 'a', 'j', 'u'], function (a, b, c, d) { var e = [{code: 404, message: 'Could not find Image Proxy'}, {code: 403, message: 'Rejected request'}, {code: 0, message: 'Incorrect Image Proxy URL'}], f = [{type: 'key_missing', message: 'The request did not include an api key.'}, {type: 'key_not_found', message: 'The provided api key could not be found.'}, {type: 'domain_not_trusted', message: 'The api key is not valid for the request origins.'}], g = function (a) { return a === 400 || a === 403 || a === 500; }, h = function (c) { var d = a.find(e, function (a) { return c === a.code; }).fold(b.constant('Unknown ImageProxy error'), function (a) { return a.message; }); return 'ImageProxy HTTP error: ' + d; }, i = function (a) { var b = h(a); return c.reject(b); }, j = function (c) { return a.find(f, function (a) { return a.type === c; }).fold(b.constant('Unknown service error'), function (a) { return a.message; }); }, k = function (a) { var b = d.parseJson(a), c = d.traverse(b, ['error', 'type']), e = c ? j(c) : 'Invalid JSON in service error message'; return 'ImageProxy Service error: ' + e; }, l = function (a, b) { return d.readBlob(b).then(function (a) { var b = k(a); return c.reject(b); }); }, m = function (a, b) { return g(a) ? l(a, b) : i(a); }; return {handleServiceErrorResponse: m, handleHttpError: i, getHttpErrorMsg: h, getServiceErrorMsg: j}; }), g('m', ['j', '8', 't', 'u'], function (a, b, c, d) { function e (b) { return d.requestUrlAsBlob(b, {}).then(function (b) { return b.status < 200 || b.status >= 300 ? c.handleHttpError(b.status) : a.resolve(b.blob); }); } var f = function (a, b) { var c = a.indexOf('?') === -1 ? '?' : '&'; return /[?&]apiKey=/.test(a) || !b ? a : a + c + 'apiKey=' + encodeURIComponent(b); }, g = function (b, e) { return d.requestUrlAsBlob(f(b, e), {'Content-Type': 'application/json;charset=UTF-8', 'tiny-api-key': e}).then(function (b) { return b.status < 200 || b.status >= 300 ? c.handleServiceErrorResponse(b.status, b.blob) : a.resolve(b.blob); }); }, h = function (a, b) { return b ? g(a, b) : e(a); }; return {getUrl: h}; }), h('v', Math), h('w', setTimeout), g('x', ['7'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('y', ['7'], function (a) { return a('tinymce.ui.Factory'); }), g('z', [], function () { return function () { function a (a) { var b; return b = f.splice(++g), f.push(a), {state: a, removed: b}; } function b () { if (d()) return f[--g]; } function c () { if (e()) return f[++g]; } function d () { return g > 0; } function e () { return g !== -1 && g < f.length - 1; } var f = [], g = -1; return {data: f, add: a, undo: b, redo: c, canUndo: d, canRedo: e}; }; }), h('1b', document), h('1c', Image), g('1d', ['7'], function (a) { return a('tinymce.geom.Rect'); }), g('1e', ['j'], function (a) { var b = function (b) { return new a(function (a) { var c = function () { b.removeEventListener('load', c), a(b); }; b.complete ? a(b) : b.addEventListener('load', c); }); }; return {loadImage: b}; }), g('1j', ['7'], function (a) { return a('tinymce.dom.DomQuery'); }), g('1k', ['7'], function (a) { return a('tinymce.util.Observable'); }), g('1l', ['7'], function (a) { return a('tinymce.util.VK'); }), g('1f', ['1j', '1d', 'y', '1k', '8', '1l'], function (a, b, c, d, e, f) { var g = 0; return function (h, i, j, k, l) { function m (a, b) { return {x: b.x + a.x, y: b.y + a.y, w: b.w, h: b.h}; } function n (a, b) { return {x: b.x - a.x, y: b.y - a.y, w: b.w, h: b.h}; } function o () { return n(j, h); } function p (a, c, d, e) { var f, g, i, k, l; f = c.x, g = c.y, i = c.w, k = c.h, f += d * a.deltaX, g += e * a.deltaY, i += d * a.deltaW, k += e * a.deltaH, i < 20 && (i = 20), k < 20 && (k = 20), l = h = b.clamp({x: f, y: g, w: i, h: k}, j, a.name === 'move'), l = n(j, l), y.fire('updateRect', {rect: l}), v(l); } function q () { function b (a) { var b, d = c.get('DragHelper'); return new d(D, {document: k.ownerDocument, handle: D + '-' + a.name, start: function () { b = h; }, drag: function (c) { p(a, b, c.deltaX, c.deltaY); }}); }a('<div id="' + D + '" class="' + C + 'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(k), e.each(B, function (b) { a('#' + D, k).append('<div id="' + D + '-' + b + '"class="' + C + 'croprect-block" style="display: none" data-mce-bogus="all">'); }), e.each(z, function (b) { a('#' + D, k).append('<div id="' + D + '-' + b.name + '" class="' + C + 'croprect-handle ' + C + 'croprect-handle-' + b.name + '"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="' + b.label + '" aria-grabbed="false">'); }), A = e.map(z, b), s(h), a(k).on('focusin focusout', function (b) { a(b.target).attr('aria-grabbed', b.type === 'focus'); }), a(k).on('keydown', function (a) { function b (a, b, d, e, f) { a.stopPropagation(), a.preventDefault(), p(c, d, e, f); } var c; switch (e.each(z, function (b) { if (a.target.id === D + '-' + b.name) return c = b, !1; }), a.keyCode) { case f.LEFT:b(a, c, h, -10, 0); break; case f.RIGHT:b(a, c, h, 10, 0); break; case f.UP:b(a, c, h, 0, -10); break; case f.DOWN:b(a, c, h, 0, 10); break; case f.ENTER:case f.SPACEBAR:a.preventDefault(), l(); } }); } function r (b) { var c; c = e.map(z, function (a) { return '#' + D + '-' + a.name; }).concat(e.map(B, function (a) { return '#' + D + '-' + a; })).join(','), b ? a(c, k).show() : a(c, k).hide(); } function s (b) { function c (b, c) { c.h < 0 && (c.h = 0), c.w < 0 && (c.w = 0), a('#' + D + '-' + b, k).css({left: c.x, top: c.y, width: c.w, height: c.h}); }e.each(z, function (c) { a('#' + D + '-' + c.name, k).css({left: b.w * c.xMul + b.x, top: b.h * c.yMul + b.y}); }), c('top', {x: i.x, y: i.y, w: i.w, h: b.y - i.y}), c('right', {x: b.x + b.w, y: b.y, w: i.w - b.x - b.w + i.x, h: b.h}), c('bottom', {x: i.x, y: b.y + b.h, w: i.w, h: i.h - b.y - b.h + i.y}), c('left', {x: i.x, y: b.y, w: b.x - i.x, h: b.h}), c('move', b); } function t (a) { h = a, s(h); } function u (a) { i = a, s(h); } function v (a) { t(m(j, a)); } function w (a) { j = a, s(h); } function x () { e.each(A, function (a) { a.destroy(); }), A = []; } var y, z, A, B, C = 'mce-', D = C + 'crid-' + g++; return z = [{name: 'move', xMul: 0, yMul: 0, deltaX: 1, deltaY: 1, deltaW: 0, deltaH: 0, label: 'Crop Mask'}, {name: 'nw', xMul: 0, yMul: 0, deltaX: 1, deltaY: 1, deltaW: -1, deltaH: -1, label: 'Top Left Crop Handle'}, {name: 'ne', xMul: 1, yMul: 0, deltaX: 0, deltaY: 1, deltaW: 1, deltaH: -1, label: 'Top Right Crop Handle'}, {name: 'sw', xMul: 0, yMul: 1, deltaX: 1, deltaY: 0, deltaW: -1, deltaH: 1, label: 'Bottom Left Crop Handle'}, {name: 'se', xMul: 1, yMul: 1, deltaX: 0, deltaY: 0, deltaW: 1, deltaH: 1, label: 'Bottom Right Crop Handle'}], B = ['top', 'right', 'bottom', 'left'], q(k), y = e.extend({toggleVisibility: r, setClampRect: w, setRect: t, getInnerRect: o, setInnerRect: v, setViewPortRect: u, destroy: x}, d); }; }), g('10', ['1b', '1c', '1d', 'y', 'j', '8', '1e', '1f'], function (a, b, c, d, e, f, g, h) { var i = function (e) { var f = d.get('Control'), i = f.extend({Defaults: {classes: 'imagepanel'}, selection: function (a) { return arguments.length ? (this.state.set('rect', a), this) : this.state.get('rect'); }, imageSize: function () { var a = this.state.get('viewRect'); return {w: a.w, h: a.h}; }, toggleCropRect: function (a) { this.state.set('cropEnabled', a); }, imageSrc: function (d) { var e = this, f = new b(); f.src = d, g.loadImage(f).then(function () { var b, d, g = e.state.get('viewRect'); if (d = e.$el.find('img'), d[0])d.replaceWith(f); else { var h = a.createElement('div'); h.className = 'mce-imagepanel-bg', e.getEl().appendChild(h), e.getEl().appendChild(f); }b = {x: 0, y: 0, w: f.naturalWidth, h: f.naturalHeight}, e.state.set('viewRect', b), e.state.set('rect', c.inflate(b, -20, -20)), g && g.w === b.w && g.h === b.h || e.zoomFit(), e.repaintImage(), e.fire('load'); }); }, zoom: function (a) { return arguments.length ? (this.state.set('zoom', a), this) : this.state.get('zoom'); }, postRender: function () { return this.imageSrc(this.settings.imageSrc), this._super(); }, zoomFit: function () { var a, b, c, d, e, f, g, h = this; g = 10, a = h.$el.find('img'), b = h.getEl().clientWidth, c = h.getEl().clientHeight, d = a[0].naturalWidth, e = a[0].naturalHeight, f = Math.min((b - g) / d, (c - g) / e), f >= 1 && (f = 1), h.zoom(f); }, repaintImage: function () { var a, b, c, d, e, f, g, h, i, j, k; k = this.getEl(), i = this.zoom(), j = this.state.get('rect'), g = this.$el.find('img'), h = this.$el.find('.mce-imagepanel-bg'), e = k.offsetWidth, f = k.offsetHeight, c = g[0].naturalWidth * i, d = g[0].naturalHeight * i, a = Math.max(0, e / 2 - c / 2), b = Math.max(0, f / 2 - d / 2), g.css({left: a, top: b, width: c, height: d}), h.css({left: a, top: b, width: c, height: d}), this.cropRect && (this.cropRect.setRect({x: j.x * i + a, y: j.y * i + b, w: j.w * i, h: j.h * i}), this.cropRect.setClampRect({x: a, y: b, w: c, h: d}), this.cropRect.setViewPortRect({x: 0, y: 0, w: e, h: f})); }, bindStates: function () { function a (a) { b.cropRect = new h(a, b.state.get('viewRect'), b.state.get('viewRect'), b.getEl(), function () { b.fire('crop'); }), b.cropRect.on('updateRect', function (a) { var c = a.rect, d = b.zoom(); c = {x: Math.round(c.x / d), y: Math.round(c.y / d), w: Math.round(c.w / d), h: Math.round(c.h / d)}, b.state.set('rect', c); }), b.on('remove', b.cropRect.destroy); } var b = this; b.state.on('change:cropEnabled', function (a) { b.cropRect.toggleVisibility(a.value), b.repaintImage(); }), b.state.on('change:zoom', function () { b.repaintImage(); }), b.state.on('change:rect', function (c) { var d = c.value; b.cropRect || a(d), b.cropRect.setRect(d); }); }}); return new i(e); }; return {create: i}; }), g('n', ['c', 'd', 'g', 'v', 'w', 'x', 'y', 'j', '8', 'z', '10'], function (a, b, c, d, e, f, g, h, i, j, k) {
        function l (a) { return {blob: a, url: c.createObjectURL(a)}; } function m (a) { a && c.revokeObjectURL(a.url); } function n (a) { i.each(a, m); } function o (c, h, o, p) {
            function q (a) { var b, c, e, f; b = O.find('#w')[0], c = O.find('#h')[0], e = parseInt(b.value(), 10), f = parseInt(c.value(), 10), O.find('#constrain')[0].checked() && ja && ka && e && f && (a.control.settings.name === 'w' ? (f = d.round(e * la), c.value(f)) : (e = d.round(f * ma), b.value(e))), ja = e, ka = f; } function r (a) { return d.round(100 * a) + '%'; } function s () { O.find('#undo').disabled(!na.canUndo()), O.find('#redo').disabled(!na.canRedo()), O.statusbar.find('#save').disabled(!na.canUndo()); } function t () { O.find('#undo').disabled(!0), O.find('#redo').disabled(!0); } function u (a) { a && V.imageSrc(a.url); } function v (a) { return function () { var b = i.grep(ia, function (b) { return b.settings.name !== a; }); i.each(b, function (a) { a.hide(); }), a.show(), a.focus(); }; } function w (a) { R = l(a), u(R); } function x (a) { h = l(a), u(h), n(na.add(h).removed), s(); } function y () { var c = V.selection(); a.blobToImageResult(h.blob).then(function (a) { b.crop(a, c.x, c.y, c.w, c.h).then(oa).then(function (a) { x(a), B(); }); }); } function z (b) { var c = [].slice.call(arguments, 1); return function () { var d = R || h; a.blobToImageResult(d.blob).then(function (a) { b.apply(this, [a].concat(c)).then(oa).then(w); }); }; } function A (b) { var c = [].slice.call(arguments, 1); return function () { a.blobToImageResult(h.blob).then(function (a) { b.apply(this, [a].concat(c)).then(oa).then(x); }); }; } function B () { u(h), m(R), v(P)(), s(); } function C (a, b) { R ? b() : e(function () { a-- > 0 ? C(a, b) : c.windowManager.alert('Error: failed to apply image operation.'); }, 10); } function D () { R ? (x(R.blob), B()) : C(100, D); } function E () { var a = V.zoom(); a < 2 && (a += 0.1), V.zoom(a); } function F () { var a = V.zoom(); a > 0.1 && (a -= 0.1), V.zoom(a); } function G () { h = na.undo(), u(h), s(); } function H () { h = na.redo(), u(h), s(); } function I () { o(h.blob), O.close(); } function J (a) { return g.create('Form', {layout: 'flex', direction: 'row', labelGap: 5, border: '0 0 1 0', align: 'center', pack: 'center', padding: '0 10 0 10', spacing: 5, flex: 0, minHeight: 60, defaults: {classes: 'imagetool', type: 'button'}, items: a}); } function K (b, c) { return J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {text: 'Apply', subtype: 'primary', onclick: D}]).hide().on('show', function () { t(), a.blobToImageResult(h.blob).then(function (a) { return c(a); }).then(oa).then(function (a) { var b = l(a); u(b), m(R), R = b; }); }); } function L (b, c, d, e, f) {
                function g (b) {
                    a.blobToImageResult(h.blob).then(function (a) { return c(a, b); }).then(oa).then(function (a) {
                        var b = l(a); u(b), m(R), R = b;
                    });
                } return J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {type: 'slider', flex: 1, ondragend: function (a) { g(a.value); }, minValue: e, maxValue: f, value: d, previewFilter: r}, {type: 'spacer', flex: 1}, {text: 'Apply', subtype: 'primary', onclick: D}]).hide().on('show', function () { this.find('slider').value(d), t(); });
            } function M (b, c) { function d () { var b, d, e; b = O.find('#r')[0].value(), d = O.find('#g')[0].value(), e = O.find('#b')[0].value(), a.blobToImageResult(h.blob).then(function (a) { return c(a, b, d, e); }).then(oa).then(function (a) { var b = l(a); u(b), m(R), R = b; }); } return J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {type: 'slider', label: 'R', name: 'r', minValue: 0, value: 1, maxValue: 2, ondragend: d, previewFilter: r}, {type: 'slider', label: 'G', name: 'g', minValue: 0, value: 1, maxValue: 2, ondragend: d, previewFilter: r}, {type: 'slider', label: 'B', name: 'b', minValue: 0, value: 1, maxValue: 2, ondragend: d, previewFilter: r}, {type: 'spacer', flex: 1}, {text: 'Apply', subtype: 'primary', onclick: D}]).hide().on('show', function () { O.find('#r,#g,#b').value(1), t(); }); } function N (a) { a.control.value() === !0 && (la = ka / ja, ma = ja / ka); } var O, P, Q, R, S, T, U, V, W, X, Y, Z, $, _, aa, ba, ca, da, ea, fa, ga, ha, ia, ja, ka, la, ma, na = new j(), oa = function (a) { return a.toBlob(); }; S = J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {text: 'Apply', subtype: 'primary', onclick: y}]).hide().on('show hide', function (a) { V.toggleCropRect(a.type === 'show'); }).on('show', t), T = J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {type: 'textbox', name: 'w', label: 'Width', size: 4, onkeyup: q}, {type: 'textbox', name: 'h', label: 'Height', size: 4, onkeyup: q}, {type: 'checkbox', name: 'constrain', text: 'Constrain proportions', checked: !0, onchange: N}, {type: 'spacer', flex: 1}, {text: 'Apply', subtype: 'primary', onclick: 'submit'}]).hide().on('submit', function (a) { var c = parseInt(O.find('#w').value(), 10), d = parseInt(O.find('#h').value(), 10); a.preventDefault(), A(b.resize, c, d)(), B(); }).on('show', t), U = J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {icon: 'fliph', tooltip: 'Flip horizontally', onclick: z(b.flip, 'h')}, {icon: 'flipv', tooltip: 'Flip vertically', onclick: z(b.flip, 'v')}, {icon: 'rotateleft', tooltip: 'Rotate counterclockwise', onclick: z(b.rotate, -90)}, {icon: 'rotateright', tooltip: 'Rotate clockwise', onclick: z(b.rotate, 90)}, {type: 'spacer', flex: 1}, {text: 'Apply', subtype: 'primary', onclick: D}]).hide().on('show', t), Y = K('Invert', b.invert), ea = K('Sharpen', b.sharpen), fa = K('Emboss', b.emboss), Z = L('Brightness', b.brightness, 0, -1, 1), $ = L('Hue', b.hue, 180, 0, 360), _ = L('Saturate', b.saturate, 0, -1, 1), aa = L('Contrast', b.contrast, 0, -1, 1), ba = L('Grayscale', b.grayscale, 0, 0, 1), ca = L('Sepia', b.sepia, 0, 0, 1), da = M('Colorize', b.colorize), ga = L('Gamma', b.gamma, 0, -1, 1), ha = L('Exposure', b.exposure, 1, 0, 2), Q = J([{text: 'Back', onclick: B}, {type: 'spacer', flex: 1}, {text: 'hue', icon: 'hue', onclick: v($)}, {text: 'saturate', icon: 'saturate', onclick: v(_)}, {text: 'sepia', icon: 'sepia', onclick: v(ca)}, {text: 'emboss', icon: 'emboss', onclick: v(fa)}, {text: 'exposure', icon: 'exposure', onclick: v(ha)}, {type: 'spacer', flex: 1}]).hide(), P = J([{tooltip: 'Crop', icon: 'crop', onclick: v(S)}, {tooltip: 'Resize', icon: 'resize2', onclick: v(T)}, {tooltip: 'Orientation', icon: 'orientation', onclick: v(U)}, {tooltip: 'Brightness', icon: 'sun', onclick: v(Z)}, {tooltip: 'Sharpen', icon: 'sharpen', onclick: v(ea)}, {tooltip: 'Contrast', icon: 'contrast', onclick: v(aa)}, {tooltip: 'Color levels', icon: 'drop', onclick: v(da)}, {tooltip: 'Gamma', icon: 'gamma', onclick: v(ga)}, {tooltip: 'Invert', icon: 'invert', onclick: v(Y)}]), V = k.create({flex: 1, imageSrc: h.url}), W = g.create('Container', {layout: 'flex', direction: 'column', border: '0 1 0 0', padding: 5, spacing: 5, items: [{type: 'button', icon: 'undo', tooltip: 'Undo', name: 'undo', onclick: G}, {type: 'button', icon: 'redo', tooltip: 'Redo', name: 'redo', onclick: H}, {type: 'button', icon: 'zoomin', tooltip: 'Zoom in', onclick: E}, {type: 'button', icon: 'zoomout', tooltip: 'Zoom out', onclick: F}]}), X = g.create('Container', {type: 'container', layout: 'flex', direction: 'row', align: 'stretch', flex: 1, items: [W, V]}), ia = [P, S, T, U, Q, Y, Z, $, _, aa, ba, ca, da, ea, fa, ga, ha], O = c.windowManager.open({layout: 'flex', direction: 'column', align: 'stretch', minWidth: d.min(f.DOM.getViewPort().w, 800), minHeight: d.min(f.DOM.getViewPort().h, 650), title: 'Edit image', items: ia.concat([X]), buttons: [{text: 'Save', name: 'save', subtype: 'primary', onclick: I}, {text: 'Cancel', onclick: 'close'}]}), O.on('close', function () { p(), n(na.data), na = null, R = null; }), na.add(h), s(), V.on('load', function () { ja = V.imageSize().w, ka = V.imageSize().h, la = ka / ja, ma = ja / ka, O.find('#w').value(ja), O.find('#h').value(ka); }), V.on('crop', y);
        } function p (a, b) { return new h(function (c, d) { return b.toBlob().then(function (b) { o(a, l(b), c, d); }); }); } return {edit: p};
    }), g('9', ['c', 'd', 'a', 'g', 'h', 'i', 'j', '8', 'k', 'b', 'l', 'm', 'n'], function (a, b, c, d, e, f, g, h, i, j, k, l, m) { var n = 0, o = function (a, b) { var c = a.dom.is(b, 'img:not([data-mce-object],[data-mce-placeholder])'); return c && (t(a, b) || u(a, b) || a.settings.imagetools_proxy); }, p = function (a, b) { a.notificationManager.open({text: b, type: 'error'}); }, q = function (a) { return a.selection.getNode(); }, r = function (a, b) { var c = b.match(/\/([^\/\?]+)?\.(?:jpeg|jpg|png|gif)(?:\?|$)/i); return c ? a.dom.encode(c[1]) : null; }, s = function () { return 'imagetools' + n++; }, t = function (a, b) { var c = b.src; return c.indexOf('data:') === 0 || c.indexOf('blob:') === 0 || new i(c).host === a.documentBaseURI.host; }, u = function (a, b) { return h.inArray(a.settings.imagetools_cors_hosts, new i(b.src).host) !== -1; }, v = function (a) { return a.settings.api_key || a.settings.imagetools_api_key; }, w = function (b, c) { var d, e = c.src; return u(b, c) ? l.getUrl(c.src, null) : t(b, c) ? a.imageToBlob(c) : (e = j.getProxyUrl(b), e += (e.indexOf('?') === -1 ? '?' : '&') + 'url=' + encodeURIComponent(c.src), d = v(b), l.getUrl(e, d)); }, x = function (a) { var b; return b = a.editorUpload.blobCache.getByUri(q(a).src), b ? g.resolve(b.blob()) : w(a, q(a)); }, y = function (a, b) { var c = f.setEditorTimeout(a, function () { a.editorUpload.uploadImagesAuto(); }, a.settings.images_upload_timeout || 3e4); b.set(c); }, z = function (a) { e(a.get()); }, A = function (a, b, c, d) { return b.toBlob().then(function (e) { var f, g, h, i, j; return h = a.editorUpload.blobCache, j = q(a), f = j.src, a.settings.images_reuse_filename && (i = h.getByUri(f), i ? (f = i.uri(), g = i.name()) : g = r(a, f)), i = h.create({id: s(), blob: e, base64: b.toBase64(), uri: f, name: g}), h.add(i), a.undoManager.transact(function () { function b () { a.$(j).off('load', b), a.nodeChanged(), c ? a.editorUpload.uploadImagesAuto() : (z(d), y(a, d)); }a.$(j).on('load', b), a.$(j).attr({src: i.blobUri()}).removeAttr('data-mce-src'); }), i; }); }, B = function (b, d, e) { return function () { return b._scanForImages().then(c.curry(x, b)).then(a.blobToImageResult).then(e).then(function (a) { return A(b, a, !1, d); }, function (a) { p(b, a); }); }; }, C = function (a, c, d) { return function () { return B(a, c, function (c) { var e = k.getImageSize(q(a)); return e && k.setImageSize(q(a), {w: e.h, h: e.w}), b.rotate(c, d); })(); }; }, D = function (a, c, d) { return function () { return B(a, c, function (a) { return b.flip(a, d); })(); }; }, E = function (b, e) { return function () { var f = q(b), h = k.getNaturalImageSize(f), i = function (b) { return new g(function (c) { a.blobToImage(b).then(function (a) { var e = k.getNaturalImageSize(a); h.w === e.w && h.h === e.h || k.getImageSize(f) && k.setImageSize(f, e), d.revokeObjectURL(a.src), c(b); }); }); }, j = function (b, c) { return m.edit(b, c).then(i).then(a.blobToImageResult).then(function (a) { return A(b, a, !0, e); }, function () {}); }; x(b).then(a.blobToImageResult).then(c.curry(j, b), function (a) { p(b, a); }); }; }; return {rotate: C, flip: D, editImageDialog: E, isEditableImage: o, cancelTimedUpload: z}; }), g('3', ['8', '9'], function (a, b) { var c = function (c, d) { a.each({mceImageRotateLeft: b.rotate(c, d, -90), mceImageRotateRight: b.rotate(c, d, 90), mceImageFlipVertical: b.flip(c, d, 'v'), mceImageFlipHorizontal: b.flip(c, d, 'h'), mceEditImage: b.editImageDialog(c, d)}, function (a, b) { c.addCommand(b, a); }); }; return {register: c}; }), g('4', ['9'], function (a) { var b = function (b, c, d) { b.on('NodeChange', function (e) { var f = d.get(); f && f.src !== e.element.src && (a.cancelTimedUpload(c), b.editorUpload.uploadImagesAuto(), d.set(null)), a.isEditableImage(b, e.element) && d.set(e.element); }); }; return {setup: b}; }), g('5', [], function () { var a = function (a) { a.addButton('rotateleft', {title: 'Rotate counterclockwise', cmd: 'mceImageRotateLeft'}), a.addButton('rotateright', {title: 'Rotate clockwise', cmd: 'mceImageRotateRight'}), a.addButton('flipv', {title: 'Flip vertically', cmd: 'mceImageFlipVertical'}), a.addButton('fliph', {title: 'Flip horizontally', cmd: 'mceImageFlipHorizontal'}), a.addButton('editimage', {title: 'Edit image', cmd: 'mceEditImage'}), a.addButton('imageoptions', {title: 'Image options', icon: 'options', cmd: 'mceImage'}); }; return {register: a}; }), g('6', ['a', 'b', '9'], function (a, b, c) { var d = function (d) { d.addContextToolbar(a.curry(c.isEditableImage, d), b.getToolbarItems(d)); }; return {register: d}; }), g('0', ['1', '2', '3', '4', '5', '6'], function (a, b, c, d, e, f) { return b.add('imagetools', function (b) { var g = a(0), h = a(null); c.register(b, g), e.register(b), f.register(b), d.setup(b, g, h); }), function () {}; }), d('0')();
}());
