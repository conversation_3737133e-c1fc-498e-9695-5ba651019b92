!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('6', tinymce.util.Tools.resolve), g('2', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('9', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('a', [], function () { function a (a) { return a && a.nodeType === 1 && a.contentEditable === 'false'; } function b (b, c, d, e, f) { function g (a, b) { if (b = b || 0, !a[0]) throw 'findAndReplaceDOMText cannot handle zero-length matches'; var c = a.index; if (b > 0) { var d = a[b]; if (!d) throw 'Invalid capture group'; c += a[0].indexOf(d), a[0] = d; } return [c, c + a[0].length, [a[0]]]; } function h (b) { var c; if (b.nodeType === 3) return b.data; if (o[b.nodeName] && !n[b.nodeName]) return ''; if (c = '', a(b)) return '\n'; if ((n[b.nodeName] || p[b.nodeName]) && (c += '\n'), b = b.firstChild) do c += h(b); while (b = b.nextSibling);return c; } function i (b, c, d) { var e, f, g, h, i = [], j = 0, k = b, l = c.shift(), m = 0; a:for (;;) { if ((n[k.nodeName] || p[k.nodeName] || a(k)) && j++, k.nodeType === 3 && (!f && k.length + j >= l[1] ? (f = k, h = l[1] - j) : e && i.push(k), !e && k.length + j > l[0] && (e = k, g = l[0] - j), j += k.length), e && f) { if (k = d({startNode: e, startNodeIndex: g, endNode: f, endNodeIndex: h, innerNodes: i, match: l[2], matchIndex: m}), j -= f.length - h, e = null, f = null, i = [], l = c.shift(), m++, !l) break; } else if (o[k.nodeName] && !n[k.nodeName] || !k.firstChild) { if (k.nextSibling) { k = k.nextSibling; continue; } } else if (!a(k)) { k = k.firstChild; continue; } for (;;) { if (k.nextSibling) { k = k.nextSibling; break; } if (k.parentNode === b) break a; k = k.parentNode; } } } function j (a) { var b; if (typeof a !== 'function') { var c = a.nodeType ? a : m.createElement(a); b = function (a, b) { var d = c.cloneNode(!1); return d.setAttribute('data-mce-index', b), a && d.appendChild(m.createTextNode(a)), d; }; } else b = a; return function (a) { var c, d, e, f = a.startNode, g = a.endNode, h = a.matchIndex; if (f === g) { var i = f; e = i.parentNode, a.startNodeIndex > 0 && (c = m.createTextNode(i.data.substring(0, a.startNodeIndex)), e.insertBefore(c, i)); var j = b(a.match[0], h); return e.insertBefore(j, i), a.endNodeIndex < i.length && (d = m.createTextNode(i.data.substring(a.endNodeIndex)), e.insertBefore(d, i)), i.parentNode.removeChild(i), j; }c = m.createTextNode(f.data.substring(0, a.startNodeIndex)), d = m.createTextNode(g.data.substring(a.endNodeIndex)); for (var k = b(f.data.substring(a.startNodeIndex), h), l = [], n = 0, o = a.innerNodes.length; n < o; ++n) { var p = a.innerNodes[n], q = b(p.data, h); p.parentNode.replaceChild(q, p), l.push(q); } var r = b(g.data.substring(0, a.endNodeIndex), h); return e = f.parentNode, e.insertBefore(c, f), e.insertBefore(k, f), e.removeChild(f), e = g.parentNode, e.insertBefore(r, g), e.insertBefore(d, g), e.removeChild(g), r; }; } var k, l, m, n, o, p, q = [], r = 0; if (m = c.ownerDocument, n = f.getBlockElements(), o = f.getWhiteSpaceElements(), p = f.getShortEndedElements(), l = h(c)) { if (b.global) for (;k = b.exec(l);)q.push(g(k, e)); else k = l.match(b), q.push(g(k, e)); return q.length && (r = q.length, i(c, q, j(d))), r; } } return {findAndReplaceDOMText: b}; }), g('7', ['9', 'a'], function (a, b) { var c = function (a) { var b = a.getAttribute('data-mce-index'); return typeof b === 'number' ? '' + b : b; }, d = function (a, c, d) { var e, f; return f = a.dom.create('span', {'data-mce-bogus': 1}), f.className = 'mce-match-marker', e = a.getBody(), n(a, c, !1), b.findAndReplaceDOMText(d, e, f, !1, a.schema); }, e = function (a) { var b = a.parentNode; a.firstChild && b.insertBefore(a.firstChild, a), a.parentNode.removeChild(a); }, f = function (b, d) { var e, f = []; if (e = a.toArray(b.getBody().getElementsByTagName('span')), e.length) for (var g = 0; g < e.length; g++) { var h = c(e[g]); h !== null && h.length && h === d.toString() && f.push(e[g]); } return f; }, g = function (a, b, c) { var d = b.get(), e = a.dom; c = c !== !1, c ? d++ : d--, e.removeClass(f(a, b.get()), 'mce-match-marker-selected'); var g = f(a, d); return g.length ? (e.addClass(f(a, d), 'mce-match-marker-selected'), a.selection.scrollIntoView(g[0]), d) : -1; }, h = function (a, b) { var c = b.parentNode; a.remove(b), a.isEmpty(c) && a.remove(c); }, i = function (a, b, c, e, f) { c = c.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&'), c = c.replace(/\s/g, '\\s'), c = f ? '\\b' + c + '\\b' : c; var h = d(a, b, new RegExp(c, e ? 'g' : 'gi')); return h && (b.set(-1), b.set(g(a, b, !0))), h; }, j = function (a, b) { var c = g(a, b, !0); c !== -1 && b.set(c); }, k = function (a, b) { var c = g(a, b, !1); c !== -1 && b.set(c); }, l = function (a) { var b = c(a); return b !== null && b.length > 0; }, m = function (b, d, f, g, i) { var m, n, q, r, s, t, u = d.get(); for (g = g !== !1, q = b.getBody(), n = a.grep(a.toArray(q.getElementsByTagName('span')), l), m = 0; m < n.length; m++) { var v = c(n[m]); if (r = s = parseInt(v, 10), i || r === d.get()) { for (f.length ? (n[m].firstChild.nodeValue = f, e(n[m])) : h(b.dom, n[m]); n[++m];) { if (r = parseInt(c(n[m]), 10), r !== s) { m--; break; }h(b.dom, n[m]); }g && u--; } else s > d.get() && n[m].setAttribute('data-mce-index', s - 1); } return b.undoManager.add(), d.set(u), g ? (t = o(b, d), j(b, d)) : (t = p(b, d), k(b, d)), !i && t; }, n = function (b, d, f) { var g, h, i, j; for (h = a.toArray(b.getBody().getElementsByTagName('span')), g = 0; g < h.length; g++) { var k = c(h[g]); k !== null && k.length && (k === d.get().toString() && (i || (i = h[g].firstChild), j = h[g].firstChild), e(h[g])); } if (i && j) { var l = b.dom.createRng(); return l.setStart(i, 0), l.setEnd(j, j.data.length), f !== !1 && b.selection.setRng(l), l; } }, o = function (a, b) { return f(a, b.get() + 1).length > 0; }, p = function (a, b) { return f(a, b.get() - 1).length > 0; }; return {done: n, find: i, next: j, prev: k, replace: m, hasNext: o, hasPrev: p}; }), g('3', ['7'], function (a) { var b = function (b, c) { var d = function (d) { return a.done(b, c, d); }, e = function (d, e, f) { return a.find(b, c, d, e, f); }, f = function () { return a.next(b, c); }, g = function () { return a.prev(b, c); }, h = function (d, e, f) { return a.replace(b, c, d, e, f); }; return {done: d, find: e, next: f, prev: g, replace: h}; }; return {get: b}; }), g('8', ['9', '7'], function (a, b) { var c = function (c, d) { function e () { i.statusbar.find('#next').disabled(b.hasNext(c, d) === !1), i.statusbar.find('#prev').disabled(b.hasPrev(c, d) === !1); } function f () { c.windowManager.alert('Could not find the specified string.', function () { i.find('#find')[0].focus(); }); } var g, h = {}; g = a.trim(c.selection.getContent({format: 'text'})); var i = c.windowManager.open({layout: 'flex', pack: 'center', align: 'center', onClose: function () { c.focus(), b.done(c, d); }, onSubmit: function (a) { var g, j, k, l; return a.preventDefault(), j = i.find('#case').checked(), l = i.find('#words').checked(), k = i.find('#find').value(), k.length ? h.text === k && h.caseState === j && h.wholeWord === l ? b.hasNext(c, d) ? (b.next(c, d), void e()) : void f() : (g = b.find(c, d, k, j, l), g || f(), i.statusbar.items().slice(1).disabled(g === 0), e(), void (h = {text: k, caseState: j, wholeWord: l})) : (b.done(c, d, !1), void i.statusbar.items().slice(1).disabled(!0)); }, buttons: [{text: 'Find', subtype: 'primary', onclick: function () { i.submit(); }}, {text: 'Replace', disabled: !0, onclick: function () { b.replace(c, d, i.find('#replace').value()) || (i.statusbar.items().slice(1).disabled(!0), d.set(-1), h = {}); }}, {text: 'Replace all', disabled: !0, onclick: function () { b.replace(c, d, i.find('#replace').value(), !0, !0), i.statusbar.items().slice(1).disabled(!0), h = {}; }}, {type: 'spacer', flex: 1}, {text: 'Prev', name: 'prev', disabled: !0, onclick: function () { b.prev(c, d), e(); }}, {text: 'Next', name: 'next', disabled: !0, onclick: function () { b.next(c, d), e(); }}], title: 'Find and replace', items: {type: 'form', padding: 20, labelGap: 30, spacing: 10, items: [{type: 'textbox', name: 'find', size: 40, label: 'Find', value: g}, {type: 'textbox', name: 'replace', size: 40, label: 'Replace with'}, {type: 'checkbox', name: 'case', text: 'Match case', label: ' '}, {type: 'checkbox', name: 'words', text: 'Whole words', label: ' '}]}}); }; return {open: c}; }), g('4', ['8'], function (a) { var b = function (b, c) { b.addCommand('SearchReplace', function () { a.open(b, c); }); }; return {register: b}; }), g('5', ['8'], function (a) { var b = function (b, c) { return function () { a.open(b, c); }; }, c = function (a, c) { a.addMenuItem('searchreplace', {text: 'Find and replace', shortcut: 'Meta+F', onclick: b(a, c), separator: 'before', context: 'edit'}), a.addButton('searchreplace', {tooltip: 'Find and replace', shortcut: 'Meta+F', onclick: b(a, c)}), a.shortcuts.add('Meta+F', '', b(a, c)); }; return {register: c}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return b.add('searchreplace', function (b) { var f = a(-1); return d.register(b, f), e.register(b, f), c.get(b, f); }), function () {}; }), d('0')(); }());
