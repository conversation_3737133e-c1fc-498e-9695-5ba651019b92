!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('6', tinymce.util.Tools.resolve), g('2', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('8', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('c', ['6'], function (a) { return a('tinymce.html.DomParser'); }), g('d', ['6'], function (a) { return a('tinymce.html.Node'); }), g('e', ['6'], function (a) { return a('tinymce.html.Serializer'); }), g('9', [], function () { var a = function (a) { return a.getParam('fullpage_hide_in_source_view'); }, b = function (a) { return a.getParam('fullpage_default_xml_pi'); }, c = function (a) { return a.getParam('fullpage_default_encoding'); }, d = function (a) { return a.getParam('fullpage_default_font_family'); }, e = function (a) { return a.getParam('fullpage_default_font_size'); }, f = function (a) { return a.getParam('fullpage_default_text_color'); }, g = function (a) { return a.getParam('fullpage_default_title'); }, h = function (a) { return a.getParam('fullpage_default_doctype', '<!DOCTYPE html>'); }; return {shouldHideInSourceView: a, getDefaultXmlPi: b, getDefaultEncoding: c, getDefaultFontFamily: d, getDefaultFontSize: e, getDefaultTextColor: f, getDefaultTitle: g, getDefaultDocType: h}; }), g('a', ['c', 'd', 'e', '8', '9'], function (a, b, c, d, e) { var f = function (b) { return new a({validate: !1, root_name: '#document'}).parse(b); }, g = function (a, b) { function c (a, b) { var c = a.attr(b); return c || ''; } var g, h, i = f(b), j = {}; return j.fontface = e.getDefaultFontFamily(a), j.fontsize = e.getDefaultFontSize(a), g = i.firstChild, g.type === 7 && (j.xml_pi = !0, h = /encoding="([^"]+)"/.exec(g.value), h && (j.docencoding = h[1])), g = i.getAll('#doctype')[0], g && (j.doctype = '<!DOCTYPE' + g.value + '>'), g = i.getAll('title')[0], g && g.firstChild && (j.title = g.firstChild.value), d.each(i.getAll('meta'), function (a) { var b, c = a.attr('name'), d = a.attr('http-equiv'); c ? j[c.toLowerCase()] = a.attr('content') : d === 'Content-Type' && (b = /charset\s*=\s*(.*)\s*/gi.exec(a.attr('content')), b && (j.docencoding = b[1])); }), g = i.getAll('html')[0], g && (j.langcode = c(g, 'lang') || c(g, 'xml:lang')), j.stylesheets = [], d.each(i.getAll('link'), function (a) { a.attr('rel') === 'stylesheet' && j.stylesheets.push(a.attr('href')); }), g = i.getAll('body')[0], g && (j.langdir = c(g, 'dir'), j.style = c(g, 'style'), j.visited_color = c(g, 'vlink'), j.link_color = c(g, 'link'), j.active_color = c(g, 'alink')), j; }, h = function (a, e, g) { function h (a, b, c) { a.attr(b, c || void 0); } function i (a) { k.firstChild ? k.insert(a, k.firstChild) : k.append(a); } var j, k, l, m, n, o = a.dom; j = f(g), k = j.getAll('head')[0], k || (m = j.getAll('html')[0], k = new b('head', 1), m.firstChild ? m.insert(k, m.firstChild, !0) : m.append(k)), m = j.firstChild, e.xml_pi ? (n = 'version="1.0"', e.docencoding && (n += ' encoding="' + e.docencoding + '"'), m.type !== 7 && (m = new b('xml', 7), j.insert(m, j.firstChild, !0)), m.value = n) : m && m.type === 7 && m.remove(), m = j.getAll('#doctype')[0], e.doctype ? (m || (m = new b('#doctype', 10), e.xml_pi ? j.insert(m, j.firstChild) : i(m)), m.value = e.doctype.substring(9, e.doctype.length - 1)) : m && m.remove(), m = null, d.each(j.getAll('meta'), function (a) { a.attr('http-equiv') === 'Content-Type' && (m = a); }), e.docencoding ? (m || (m = new b('meta', 1), m.attr('http-equiv', 'Content-Type'), m.shortEnded = !0, i(m)), m.attr('content', 'test/html; charset=' + e.docencoding)) : m && m.remove(), m = j.getAll('title')[0], e.title ? (m ? m.empty() : (m = new b('title', 1), i(m)), m.append(new b('#test', 3)).value = e.title) : m && m.remove(), d.each('keywords,description,author,copyright,robots'.split(','), function (a) { var c, d, f = j.getAll('meta'), g = e[a]; for (c = 0; c < f.length; c++) if (d = f[c], d.attr('name') === a) return void (g ? d.attr('content', g) : d.remove()); g && (m = new b('meta', 1), m.attr('name', a), m.attr('content', g), m.shortEnded = !0, i(m)); }); var p = {}; return d.each(j.getAll('link'), function (a) { a.attr('rel') === 'stylesheet' && (p[a.attr('href')] = a); }), d.each(e.stylesheets, function (a) { p[a] || (m = new b('link', 1), m.attr({rel: 'stylesheet', text: 'test/css', href: a}), m.shortEnded = !0, i(m)), delete p[a]; }), d.each(p, function (a) { a.remove(); }), m = j.getAll('body')[0], m && (h(m, 'dir', e.langdir), h(m, 'style', e.style), h(m, 'vlink', e.visited_color), h(m, 'link', e.link_color), h(m, 'alink', e.active_color), o.setAttribs(a.getBody(), {style: e.style, dir: e.dir, vLink: e.visited_color, link: e.link_color, aLink: e.active_color})), m = j.getAll('html')[0], m && (h(m, 'lang', e.langcode), h(m, 'xml:lang', e.langcode)), k.firstChild || k.remove(), l = new c({validate: !1, indent: !0, apply_source_formatting: !0, indent_before: 'head,html,body,meta,title,script,link,style', indent_after: 'head,html,body,meta,title,script,link,style'}).serialize(j), l.substring(0, l.indexOf('</body>')); }; return {parseHeader: f, htmlToData: g, dataToHtml: h}; }), g('7', ['8', 'a'], function (a, b) { var c = function (c, d) { var e = b.htmlToData(c, d.get()); c.windowManager.open({title: 'Document properties', data: e, defaults: {type: 'textbox', size: 40}, body: [{name: 'title', label: 'Title'}, {name: 'keywords', label: 'Keywords'}, {name: 'description', label: 'Description'}, {name: 'robots', label: 'Robots'}, {name: 'author', label: 'Author'}, {name: 'docencoding', label: 'Encoding'}], onSubmit: function (f) { var g = b.dataToHtml(c, a.extend(e, f.data), d.get()); d.set(g); }}); }; return {open: c}; }), g('3', ['7'], function (a) { var b = function (b, c) { b.addCommand('mceFullPageProperties', function () { a.open(b, c); }); }; return {register: b}; }), g('b', ['8'], function (a) { var b = function (b, c) { return a.each(b, function (a) { c = c.replace(a, function (a) { return '<!--mce:protected ' + escape(a) + '-->'; }); }), c; }, c = function (a) { return a.replace(/<!--mce:protected ([\s\S]*?)-->/g, function (a, b) { return unescape(b); }); }; return {protectHtml: b, unprotectHtml: c}; }), g('4', ['8', '9', 'a', 'b'], function (a, b, c, d) { var e = a.each, f = function (a) { return a.replace(/<\/?[A-Z]+/g, function (a) { return a.toLowerCase(); }); }, g = function (g, i, j, k) { var l, m, n, o, p, q = '', r = g.dom; if (!(k.selection || (n = d.protectHtml(g.settings.protect, k.content), k.format === 'raw' && i.get() || k.source_view && b.shouldHideInSourceView(g)))) { n.length !== 0 || k.source_view || (n = a.trim(i.get()) + '\n' + a.trim(n) + '\n' + a.trim(j.get())), n = n.replace(/<(\/?)BODY/gi, '<$1body'), l = n.indexOf('<body'), l !== -1 ? (l = n.indexOf('>', l), i.set(f(n.substring(0, l + 1))), m = n.indexOf('</body', l), m === -1 && (m = n.length), k.content = a.trim(n.substring(l + 1, m)), j.set(f(n.substring(m)))) : (i.set(h(g)), j.set('\n</body>\n</html>')), o = c.parseHeader(i.get()), e(o.getAll('style'), function (a) { a.firstChild && (q += a.firstChild.value); }), p = o.getAll('body')[0], p && r.setAttribs(g.getBody(), {style: p.attr('style') || '', dir: p.attr('dir') || '', vLink: p.attr('vlink') || '', link: p.attr('link') || '', aLink: p.attr('alink') || ''}), r.remove('fullpage_styles'); var s = g.getDoc().getElementsByTagName('head')[0]; q && (r.add(s, 'style', {id: 'fullpage_styles'}, q), p = r.get('fullpage_styles'), p.styleSheet && (p.styleSheet.cssText = q)); var t = {}; a.each(s.getElementsByTagName('link'), function (a) { a.rel === 'stylesheet' && a.getAttribute('data-mce-fullpage') && (t[a.href] = a); }), a.each(o.getAll('link'), function (a) { var b = a.attr('href'); return !b || (t[b] || a.attr('rel') !== 'stylesheet' || r.add(s, 'link', {rel: 'stylesheet', text: 'test/css', href: b, 'data-mce-fullpage': '1'}), void delete t[b]); }), a.each(t, function (a) { a.parentNode.removeChild(a); }); } }, h = function (a) { var c, d = '', e = ''; if (b.getDefaultXmlPi(a)) { var f = b.getDefaultEncoding(a); d += '<?xml version="1.0" encoding="' + (f || 'ISO-8859-1') + '" ?>\n'; } return d += b.getDefaultDocType(a), d += '\n<html>\n<head>\n', (c = b.getDefaultTitle(a)) && (d += '<title>' + c + '</title>\n'), (c = b.getDefaultEncoding(a)) && (d += '<meta http-equiv="Content-Type" content="test/html; charset=' + c + '" />\n'), (c = b.getDefaultFontFamily(a)) && (e += 'font-family: ' + c + ';'), (c = b.getDefaultFontSize(a)) && (e += 'font-size: ' + c + ';'), (c = b.getDefaultTextColor(a)) && (e += 'color: ' + c + ';'), d += '</head>\n<body' + (e ? ' style="' + e + '"' : '') + '>\n'; }, i = function (c, e, f, g) { g.selection || g.source_view && b.shouldHideInSourceView(c) || (g.content = d.unprotectHtml(a.trim(e) + '\n' + a.trim(g.content) + '\n' + a.trim(f))); }, j = function (a, b, c) { a.on('BeforeSetContent', function (d) { g(a, b, c, d); }), a.on('GetContent', function (d) { i(a, b.get(), c.get(), d); }); }; return {setup: j}; }), g('5', [], function () { var a = function (a) { a.addButton('fullpage', {title: 'Document properties', cmd: 'mceFullPageProperties'}), a.addMenuItem('fullpage', {text: 'Document properties', cmd: 'mceFullPageProperties', context: 'file'}); }; return {register: a}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return b.add('fullpage', function (b) { var f = a(''), g = a(''); c.register(b, f), e.register(b), d.setup(b, f, g); }), function () {}; }), d('0')(); }());
