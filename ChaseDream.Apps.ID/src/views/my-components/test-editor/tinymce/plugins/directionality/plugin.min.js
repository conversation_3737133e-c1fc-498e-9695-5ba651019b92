!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('4', tinymce.util.Tools.resolve), g('1', ['4'], function (a) { return a('tinymce.PluginManager'); }), g('6', ['4'], function (a) { return a('tinymce.util.Tools'); }), g('5', ['6'], function (a) { var b = function (b, c) { var d, e = b.dom, f = b.selection.getSelectedBlocks(); f.length && (d = e.getAttrib(f[0], 'dir'), a.each(f, function (a) { e.getParent(a.parentNode, '*[dir="' + c + '"]', e.getRoot()) || e.setAttrib(a, 'dir', d !== c ? c : null); }), b.nodeChanged()); }; return {setDir: b}; }), g('2', ['5'], function (a) { var b = function (b) { b.addCommand('mceDirectionLTR', function () { a.setDir(b, 'ltr'); }), b.addCommand('mceDirectionRTL', function () { a.setDir(b, 'rtl'); }); }; return {register: b}; }), g('3', ['6'], function (a) { var b = function (b) { var c = []; return a.each('h1 h2 h3 h4 h5 h6 div p'.split(' '), function (a) { c.push(a + '[dir=' + b + ']'); }), c.join(','); }, c = function (a) { a.addButton('ltr', {title: 'Left to right', cmd: 'mceDirectionLTR', stateSelector: b('ltr')}), a.addButton('rtl', {title: 'Right to left', cmd: 'mceDirectionRTL', stateSelector: b('rtl')}); }; return {register: c}; }), g('0', ['1', '2', '3'], function (a, b, c) { return a.add('directionality', function (a) { b.register(a), c.register(a); }), function () {}; }), d('0')(); }());
