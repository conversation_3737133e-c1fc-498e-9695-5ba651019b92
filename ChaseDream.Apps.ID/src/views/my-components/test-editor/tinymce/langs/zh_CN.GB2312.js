tinymce.addI18n('zh_CN.GB2312', {
    'Cut': '\u526a\u5207',
    'Heading 5': '\u7ae0\u8282\u6807\u98985',
    'Header 2': '\u6807\u98982',
    'Your browser doesn\'t support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.': '\u4f60\u7684\u6d4f\u89c8\u5668\u4e0d\u652f\u6301\u8bbf\u95ee\u526a\u5207\u677f\uff0c\u8bf7\u4f7f\u7528Ctrl+X\/C\/V\u5feb\u6377\u952e\u66ff\u4ee3\u3002',
    'Heading 4': '\u7ae0\u8282\u6807\u98984',
    'Div': '\u5411\u540e',
    'Heading 2': '\u7ae0\u8282\u6807\u98982',
    'Paste': '\u7c98\u5e16',
    'Close': '\u5173\u95ed',
    'Font Family': '\u5b57\u4f53',
    'Pre': '\u5411\u524d',
    'Align right': '\u5c45\u4e2d\u5bf9\u9f50',
    'New document': '\u65b0\u6587\u6863',
    'Blockquote': '\u5757\u5f15\u7528',
    'Numbered list': '\u6570\u5b57\u5217\u8868',
    'Heading 1': '\u7ae0\u8282\u6807\u98981',
    'Headings': '\u7ae0\u8282\u6807\u9898',
    'Increase indent': '\u589e\u52a0\u7f29\u8fdb',
    'Formats': '\u683c\u5f0f\u5316',
    'Headers': ' \u6807\u9898',
    'Select all': '\u5168\u9009',
    'Header 3': '\u6807\u98983',
    'Blocks': '\u5757',
    'Undo': '\u64a4\u9500',
    'Strikethrough': '\u5220\u9664\u7ebf',
    'Bullet list': '\u7b26\u53f7\u5217\u8868',
    'Header 1': '\u6807\u98981',
    'Superscript': '\u4e0a\u89d2\u6807',
    'Clear formatting': '\u6e05\u9664\u683c\u5f0f',
    'Font Sizes': '\u5b57\u53f7',
    'Subscript': '\u4e0b\u89d2\u6807',
    'Header 6': '\u6807\u98986',
    'Redo': '\u91cd\u505a',
    'Paragraph': '\u6bb5\u843d',
    'Ok': '\u786e\u8ba4',
    'Bold': '\u7c97\u4f53',
    'Code': '\u4ee3\u7801',
    'Italic': '\u659c\u4f53',
    'Align center': '\u5c45\u4e2d\u5bf9\u9f50',
    'Header 5': '\u6807\u98985',
    'Heading 6': '\u7ae0\u8282\u6807\u98986',
    'Heading 3': '\u7ae0\u8282\u6807\u98983',
    'Decrease indent': '\u51cf\u5c0f\u7f29\u8fdb',
    'Header 4': '\u6807\u98984',
    'Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.': '\u7c98\u8d34\u73b0\u5728\u662f\u7eaf\u6587\u672c\u6a21\u5f0f\u3002\u5185\u5bb9\u5c06\u7c98\u8d34\u4e3a\u7eaf\u6587\u672c\u5f62\u5f0f\uff0c\b\u76f4\u5230\u60a8\u5207\u6362\u9009\u9879\u5f00\u5173\u3002',
    'Underline': '\u4e0b\u5212\u7ebf',
    'Cancel': '\u53d6\u6d88',
    'Justify': '\u4e24\u7aef\u5bf9\u9f50',
    'Inline': '\u5185\u5d4c',
    'Copy': '\u590d\u5236',
    'Align left': '\u5de6\u5bf9\u9f50',
    'Visual aids': '\u53ef\u89c6\u5316\u5de5\u5177',
    'Lower Greek': '\u5c0f\u5199\u5e0c\u814a\u5b57\u6bcd',
    'Square': '\u65b9\u5f62',
    'Default': '\u9ed8\u8ba4',
    'Lower Alpha': '\u5c0f\u5199\u5b57\u6bcd',
    'Circle': '\u5706\u5f62',
    'Disc': '\u78c1\u76d8',
    'Upper Alpha': '\u5927\u5199\u5b57\u6bcd',
    'Upper Roman': '\u5927\u5199\u7f57\u9a6c\u5b57\u6bcd',
    'Lower Roman': '\u5c0f\u5199\u7f57\u9a6c\u5b57\u6bcd',
    'Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.': '\u6807\u8bc6\u7b26\u5e94\u8be5\u4ee5\u5b57\u6bcd\u5f00\u5934\uff0c\u540e\u8ddf\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u7834\u6298\u53f7\u3001\u70b9\u3001\u5192\u53f7\u6216\u4e0b\u5212\u7ebf\u3002',
    'Name': '\u540d\u79f0',
    'Anchor': '\u951a\u70b9',
    'Id': '\u6807\u8bc6\u7b26',
    'You have unsaved changes are you sure you want to navigate away?': '\u60a8\u8fd8\u6ca1\u6709\u4fdd\u5b58\uff0c\u60a8\u786e\u5b9a\u8981\u79bb\u5f00\uff1f',
    'Restore last draft': '\u6062\u590d\u6700\u540e\u8349\u7a3f',
    'Special character': '\u7279\u6b8a\u5b57\u7b26',
    'Source code': '\u6e90\u4ee3\u7801',
    'Language': '\u8bed\u8a00',
    'Insert\/Edit code sample': '\u63d2\u5165\/\u7f16\u8f91\u4ee3\u7801\u793a\u4f8b',
    'B': 'B\u503c',
    'R': 'R\u503c',
    'G': 'G\u503c',
    'Color': '\u989c\u8272',
    'Right to left': '\u4ece\u53f3\u5230\u5de6',
    'Left to right': '\u4ece\u5de6\u5230\u53f3',
    'Emoticons': '\u8868\u60c5',
    'Robots': '\u641c\u7d22\u673a\u5668\u4eba\u7528Robots\u6587\u4ef6',
    'Document properties': '\u7b14\u8bb0\u5c5e\u6027',
    'Title': '\u6807\u9898',
    'Keywords': '\u5173\u952e\u5b57',
    'Encoding': '\u7f16\u7801',
    'Description': '\u63cf\u8ff0',
    'Author': '\u4f5c\u8005',
    'Fullscreen': '\u5168\u5c4f',
    'Horizontal line': '\u6c34\u5e73\u7ebf',
    'Horizontal space': '\u6c34\u5e73\u95f4\u8ddd',
    'Insert\/edit image': '\u63d2\u5165\/\u7f16\u8f91 \u56fe\u7247',
    'General': '\u5e38\u89c4',
    'Advanced': '\u9ad8\u7ea7\u9009\u9879',
    'Source': '\u6e90',
    'Border': '\u8fb9',
    'Constrain proportions': '\u9650\u5236\u6bd4\u4f8b',
    'Vertical space': '\u5782\u76f4\u95f4\u8ddd',
    'Image description': '\u56fe\u7247\u63cf\u8ff0',
    'Style': '\u6837\u5f0f',
    'Dimensions': '\u5c3a\u5bf8',
    'Insert image': '\u63d2\u5165\u56fe\u7247',
    'Image': '\u56fe\u7247',
    'Zoom in': '\u653e\u5927',
    'Contrast': '\u5bf9\u6bd4\u5ea6',
    'Back': '\u8fd4\u56de',
    'Gamma': '\u7070\u5ea6',
    'Flip horizontally': '\u6c34\u5e73\u7ffb\u8f6c',
    'Resize': '\u8c03\u6574\u5927\u5c0f',
    'Sharpen': '\u9510\u5316',
    'Zoom out': '\u7f29\u5c0f',
    'Image options': '\u56fe\u7247\u9009\u9879',
    'Apply': '\u5e94\u7528',
    'Brightness': '\u4eae\u5ea6',
    'Rotate clockwise': '\u987a\u65f6\u9488\u65cb\u8f6c',
    'Rotate counterclockwise': '\u9006\u65f6\u9488\u65cb\u8f6c',
    'Edit image': '\u7f16\u8f91\u56fe\u7247',
    'Color levels': '\u8272\u9636',
    'Crop': '\u526a\u88c1',
    'Orientation': '\u65b9\u5411',
    'Flip vertically': '\u5782\u76f4\u7ffb\u8f6c',
    'Invert': '\u53cd\u8272',
    'Date\/time': '\u65e5\u671f\u002f\u65f6\u95f4',
    'Insert date\/time': '\u63d2\u5165\u65e5\u671f\/\u65f6\u95f4',
    'Remove link': '\u5220\u9664\u94fe\u63a5',
    'Url': 'URL\u94fe\u63a5',
    'Text to display': '\u8981\u663e\u793a\u7684\u6587\u5b57',
    'Anchors': '\u951a\u70b9',
    'Insert link': '\u63d2\u5165\u94fe\u63a5',
    'Link': '\u94fe\u63a5',
    'New window': '\u65b0\u7a97\u53e3',
    'None': '\u65e0\u503c',
    'The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?': '\u60a8\u8f93\u5165\u7684\u7f51\u5740\u4f3c\u4e4e\u662f\u4e00\u4e2a\u5916\u90e8\u94fe\u63a5\u3002\u662f\u5426\u9700\u8981\u6dfb\u52a0HTTP:\/\/\u524d\u7f00\uff1f',
    'Paste or type a link': 'Paste or type a link',
    'Target': '\u76ee\u6807',
    'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?': '\u60a8\u8f93\u5165\u7684\u7f51\u5740\u4f3c\u4e4e\u662f\u4e00\u4e2a\u7535\u5b50\u90ae\u4ef6\u5730\u5740\u3002\u662f\u5426\u9700\u8981\u6dfb\u52a0mailto\uff1a\u524d\u7f00\uff1f',
    'Insert\/edit link': '\u63d2\u5165\/\u7f16\u8f91 \u94fe\u63a5',
    'Insert\/edit video': '\u63d2\u5165\/\u7f16\u8f91 \u89c6\u9891',
    'Media': '\u63d2\u5165\u002f\u7f16\u8f91\u5a92\u4f53',
    'Alternative source': '\u66ff\u4ee3\u6e90',
    'Paste your embed code below:': '\u4e0b\u9762\u7c98\u8d34\u60a8\u7684\u5d4c\u5165\u4ee3\u7801\uff1a',
    'Insert video': '\u63d2\u5165\u89c6\u9891',
    'Poster': '\u6d77\u62a5',
    'Insert\/edit media': '\u63d2\u5165\u002f\u7f16\u8f91\u5a92\u4f53',
    'Embed': '\u5185\u5d4c',
    'Nonbreaking space': '\u4e0d\u95f4\u65ad\u7a7a\u683c',
    'Page break': '\u5206\u9875\u7b26',
    'Paste as text': '\u4f5c\u4e3a\u6587\u672c\u7c98\u5e16',
    'Preview': '\u9884\u89c8',
    'Print': '\u6253\u5370',
    'Save': '\u4fdd\u5b58',
    'Could not find the specified string.': '\u627e\u4e0d\u5230\u6307\u5b9a\u7684\u5b57\u7b26\u4e32\u3002',
    'Replace': '\u66ff\u6362',
    'Next': '\u5148\u4e00\u4e2a',
    'Whole words': '\b\u5168\u90e8\u5b57\u7b26',
    'Find and replace': '\u67e5\u627e\u548c\u66ff\u6362',
    'Replace with': '\u7528...\u6765\u4ee3\u66ff',
    'Find': '\u67e5\u627e',
    'Replace all': '\u66ff\u6362\u5168\u90e8',
    'Match case': '\u76f8\u7b26',
    'Prev': '\u4e0a\u4e00\u4e2a',
    'Spellcheck': '\u62fc\u5199\u68c0\u67e5',
    'Finish': '\u5b8c\u6210',
    'Ignore all': '\u5168\u90e8\u5ffd\u7565',
    'Ignore': '\u5ffd\u7565',
    'Add to Dictionary': '\u52a0\u5165\u5230\u5b57\u5178',
    'Insert row before': '\u5728\u524d\u63d2\u5165\u884c',
    'Rows': '\u884c\u6570',
    'Height': '\u9ad8',
    'Paste row after': '\u5728\u540e\u7c98\u5e16\u884c',
    'Alignment': '\u5bf9\u9f50\u65b9\u5f0f',
    'Border color': '\u8fb9\u6846\u989c\u8272',
    'Column group': '\u521b\u5efa\u5217\u7ec4',
    'Row': '\u884c',
    'Insert column before': '\u5728\u524d\u63d2\u5165\u5217',
    'Split cell': '\u5206\u5272\u5355\u5143\u683c',
    'Cell padding': '\u5355\u5143\u683c\u8fb9\u8ddd',
    'Cell spacing': '\u5355\u5143\u683c\u95f4\u8ddd',
    'Row type': '\u884c\u7c7b\u578b',
    'Insert table': '\u63d2\u5165\u8868\u683c',
    'Body': '\u4f53\u90e8',
    'Caption': '\u8bf4\u660e\u6587\u5b57',
    'Footer': '\u811a\u90e8',
    'Delete row': '\u5220\u9664\u884c',
    'Paste row before': '\u5728\u524d\u7c98\u5e16\u884c',
    'Scope': '\u9002\u7528\u8303\u56f4',
    'Delete table': '\u5220\u9664\u8868\u683c',
    'H Align': '\u6c34\u5e73\u5bf9\u9f50',
    'Top': '\u4e0a',
    'Header cell': '\b\u6807\u9898\u5355\u5143',
    'Column': '\u5217',
    'Row group': '\b\u521b\u5efa\u884c\u7ec4',
    'Cell': '\u5355\u5143\u683c',
    'Middle': '\u4e2d',
    'Cell type': '\u5355\u5143\u683c\u7c7b\u578b',
    'Copy row': '\u590d\u5236\u884c',
    'Row properties': '\u884c\u5c5e\u6027',
    'Table properties': '\u8868\u683c\u5c5e\u6027',
    'Bottom': '\u4e0b',
    'V Align': '\u5782\u76f4\u5bf9\u9f50',
    'Header': '\u5934\u90e8',
    'Right': '\u53f3',
    'Insert column after': '\u5728\u540e\u63d2\u5165\u5217',
    'Cols': '\u5217\u6570',
    'Insert row after': '\u5728\u540e\u63d2\u5165\u884c',
    'Width': '\u5bbd',
    'Cell properties': '\u884c\u5c5e\u6027',
    'Left': '\u5de6',
    'Cut row': '\u526a\u5207\u884c',
    'Delete column': '\u5220\u9664\u5217',
    'Center': '\u4e2d\u95f4',
    'Merge cells': '\u5408\u5e76\u5355\u5143\u683c',
    'Insert template': '\u63d2\u5165\u6a21\u677f',
    'Templates': '\u6a21\u677f',
    'Background color': '\u80cc\u666f\u989c\u8272',
    'Custom...': '\u81ea\u5b9a\u4e49...',
    'Custom color': '\u81ea\u5b9a\u4e49\u989c\u8272',
    'No color': '\u65e0\u989c\u8272',
    'Text color': '\u6587\u5b57\u989c\u8272',
    'Table of Contents': 'Table of Contents',
    'Show blocks': '\u663e\u793a\u5757',
    'Show invisible characters': '\u663e\u793a\u4e0d\u53ef\u89c1\u5b57\u7b26',
    'Words: {0}': '\u5b57\u7b26\u6570:{0}',
    'Insert': '\u63d2\u5165',
    'File': '\u6587\u4ef6',
    'Edit': '\u7f16\u8f91',
    'Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help': '\u5bcc\u6587\u5b57\u57df\u3002\u83dc\u5355\u6309 ALT-F9 \uff0c\u5de5\u5177\u6761\u6309ALT-F10\uff0c\u5e2e\u52a9\u6309ALT-0',
    'Tools': '\u5de5\u5177',
    'View': '\u89c6\u56fe',
    'Table': '\u8868\u683c',
    'Format': '\u683c\u5f0f',
    'Template': '\u6a21\u677f',
    'Insert New Note': '\u65b0\u5efa\u7b14\u8bb0'
});
