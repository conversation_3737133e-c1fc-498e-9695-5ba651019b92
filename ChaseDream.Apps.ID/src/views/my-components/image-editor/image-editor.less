.image-editor{
    .cropper{
        box-sizing: border-box;
        border: 1px solid #c3c3c3;
        width: 100%;
        height: 100%;
        img{
            max-height: 100%;
        }
    }
    .fileinput{
        display: none;
    }
    .filelabel{
        display: block;
        padding: 6px 15px;
        background: #2d8cf0;
        display: inline-block;
        border: 1px solid #2d8cf0;
        border-radius: 4px;
        cursor: pointer;
        color: white;
        font-size: 12px;
        text-align: center;
        transition: all .2s;
        &:hover{
            background: #5cadff;
            border: 1px solid #5cadff;
            transition: all .2s;
        }
    }
    &-con1{
        height: 300px;
        &-preview-con{
            width: 100% !important;
            height: 200px !important;
            border: 1px solid #c3c3c3;
            #preview1{
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
        }
    }
    &-con2{
        height: 300px;
        p{
            font-size: 14px;
            padding: 6px;
            border-bottom: 1px solid #c3c3c3;
            b{
                display: inline-block;
                width: 80px;
            }
        }
    }
    &-con3{
        height: 300px;
        .cropper3{
            box-sizing: border-box;
            border: 1px solid #c3c3c3;
            width: 100%;
            height: 100%;
            img{
                max-height: 100%;
            }
        }
        &-btn-box{
            text-align: center;
        }
        .filelabel3{
            width: 190px;
            box-sizing: border-box;
        }
        .crop3-btn-box{
            text-align: center;
        }
        &-preview-con{
            width: 100% !important;
            height: 300px !important;
            border: 1px solid #c3c3c3;
            #preview3{
                width: 100%;
                height: 100%;
                overflow: hidden;
            }
        }
    }
}