!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('6', tinymce.util.Tools.resolve), g('2', ['6'], function (a) { return a('tinymce.PluginManager'); }), h('c', setInterval), g('d', ['6'], function (a) { return a('tinymce.util.LocalStorage'); }), g('a', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('e', [], function () { var a = function (a) { return a.fire('RestoreDraft'); }, b = function (a) { return a.fire('StoreDraft'); }, c = function (a) { return a.fire('RemoveDraft'); }; return {fireRestoreDraft: a, fireStoreDraft: b, fireRemoveDraft: c}; }), h('f', document), g('g', [], function () { var a = function (a, b) { var c = {s: 1e3, m: 6e4}; return a = /^(\d+)([ms]?)$/.exec('' + (a || b)), (a[2] ? c[a[2]] : 1) * parseInt(a, 10); }; return {parse: a}; }), g('b', ['f', 'g'], function (a, b) { var c = function (a) { return a.getParam('autosave_ask_before_unload', !0); }, d = function (b) { var c = b.getParam('autosave_prefix', 'tinymce-autosave-{path}{query}-{id}-'); return c = c.replace(/\{path\}/g, a.location.pathname), c = c.replace(/\{query\}/g, a.location.search), c = c.replace(/\{id\}/g, b.id); }, e = function (a) { return a.getParam('autosave_restore_when_empty', !1); }, f = function (a) { return b.parse(a.settings.autosave_interval, '30s'); }, g = function (a) { return b.parse(a.settings.autosave_retention, '20m'); }; return {shouldAskBeforeUnload: c, getAutoSavePrefix: d, shouldRestoreWhenEmpty: e, getAutoSaveInterval: f, getAutoSaveRetention: g}; }), g('7', ['c', 'd', 'a', 'e', 'b'], function (a, b, c, d, e) { var f = function (a, b) { var d = a.settings.forced_root_block; return b = c.trim(typeof b === 'undefined' ? a.getBody().innerHTML : b), b === '' || new RegExp('^<' + d + '[^>]*>((\xa0|&nbsp;|[ \t]|<br[^>]*>)+?|)</' + d + '>|<br>$', 'i').test(b); }, g = function (a) { var c = parseInt(b.getItem(e.getAutoSavePrefix(a) + 'time'), 10) || 0; return !((new Date()).getTime() - c > e.getAutoSaveRetention(a)) || (h(a, !1), !1); }, h = function (a, c) { var f = e.getAutoSavePrefix(a); b.removeItem(f + 'draft'), b.removeItem(f + 'time'), c !== !1 && d.fireRemoveDraft(a); }, i = function (a) { var c = e.getAutoSavePrefix(a); !f(a) && a.isDirty() && (b.setItem(c + 'draft', a.getContent({format: 'raw', no_events: !0})), b.setItem(c + 'time', (new Date()).getTime()), d.fireStoreDraft(a)); }, j = function (a) { var c = e.getAutoSavePrefix(a); g(a) && (a.setContent(b.getItem(c + 'draft'), {format: 'raw'}), d.fireRestoreDraft(a)); }, k = function (b, c) { var d = e.getAutoSaveInterval(b); c.get() || (a(function () { b.removed || i(b); }, d), c.set(!0)); }, l = function (a) { a.undoManager.transact(function () { j(a), h(a); }), a.focus(); }; return {isEmpty: f, hasDraft: g, removeDraft: h, storeDraft: i, restoreDraft: j, startStoreDraft: k, restoreLastDraft: l}; }), g('3', ['7'], function (a) { var b = function (a, b) { return function () { var c = Array.prototype.slice.call(arguments); return a.apply(null, [b].concat(c)); }; }, c = function (c) { return {hasDraft: b(a.hasDraft, c), storeDraft: b(a.storeDraft, c), restoreDraft: b(a.restoreDraft, c), removeDraft: b(a.removeDraft, c), isEmpty: b(a.isEmpty, c)}; }; return {get: c}; }), h('8', window), g('9', ['6'], function (a) { return a('tinymce.EditorManager'); }), g('4', ['8', '9', 'a', 'b'], function (a, b, c, d) { b._beforeUnloadHandler = function () { var a; return c.each(b.get(), function (b) { b.plugins.autosave && b.plugins.autosave.storeDraft(), !a && b.isDirty() && d.shouldAskBeforeUnload(b) && (a = b.translate('You have unsaved changes are you sure you want to navigate away?')); }), a; }; var e = function (c) { a.onbeforeunload = b._beforeUnloadHandler; }; return {setup: e}; }), g('5', ['7'], function (a) { var b = function (b, c) { return function (d) { var e = d.control; e.disabled(!a.hasDraft(b)), b.on('StoreDraft RestoreDraft RemoveDraft', function () { e.disabled(!a.hasDraft(b)); }), a.startStoreDraft(b, c); }; }, c = function (c, d) { c.addButton('restoredraft', {title: 'Restore last draft', onclick: function () { a.restoreLastDraft(c); }, onPostRender: b(c, d)}), c.addMenuItem('restoredraft', {text: 'Restore last draft', onclick: function () { a.restoreLastDraft(c); }, onPostRender: b(c, d), context: 'file'}); }; return {register: c}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return b.add('autosave', function (b) { var f = a(!1); return d.setup(b), e.register(b, f), c.get(b); }), function () {}; }), d('0')(); }());
