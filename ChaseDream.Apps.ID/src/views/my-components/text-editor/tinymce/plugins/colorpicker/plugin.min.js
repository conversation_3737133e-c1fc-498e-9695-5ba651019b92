!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('3', tinymce.util.Tools.resolve), g('1', ['3'], function (a) { return a('tinymce.PluginManager'); }), g('4', ['3'], function (a) { return a('tinymce.util.Color'); }), g('2', ['4'], function (a) { var b = function (a, b) { a.find('#preview')[0].getEl().style.background = b; }, c = function (c, d) { var e = new a(d), f = e.toRgb(); c.fromJSON({r: f.r, g: f.g, b: f.b, hex: e.toHex().substr(1)}), b(c, e.toHex()); }, d = function (a, d, e) { var f = a.windowManager.open({title: 'Color', items: {type: 'container', layout: 'flex', direction: 'row', align: 'stretch', padding: 5, spacing: 10, items: [{type: 'colorpicker', value: e, onchange: function () { var a = this.rgb(); f && (f.find('#r').value(a.r), f.find('#g').value(a.g), f.find('#b').value(a.b), f.find('#hex').value(this.value().substr(1)), b(f, this.value())); }}, {type: 'form', padding: 0, labelGap: 5, defaults: {type: 'textbox', size: 7, value: '0', flex: 1, spellcheck: !1, onchange: function () { var a, b, d = f.find('colorpicker')[0]; return a = this.name(), b = this.value(), a === 'hex' ? (b = '#' + b, c(f, b), void d.value(b)) : (b = {r: f.find('#r').value(), g: f.find('#g').value(), b: f.find('#b').value()}, d.value(b), void c(f, b)); }}, items: [{name: 'r', label: 'R', autofocus: 1}, {name: 'g', label: 'G'}, {name: 'b', label: 'B'}, {name: 'hex', label: '#', value: '000000'}, {name: 'preview', type: 'container', border: 1}]}]}, onSubmit: function () { d('#' + f.toJSON().hex); }}); c(f, e); }; return {open: d}; }), g('0', ['1', '2'], function (a, b) { return a.add('colorpicker', function (a) { a.settings.color_picker_callback || (a.settings.color_picker_callback = function (c, d) { b.open(a, c, d); }); }), function () {}; }), d('0')(); }());
