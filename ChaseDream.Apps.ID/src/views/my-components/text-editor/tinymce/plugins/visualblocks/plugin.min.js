!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('6', tinymce.util.Tools.resolve), g('2', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('9', [], function () { var a = function (a, b) { a.fire('VisualBlocks', {state: b}); }; return {fireVisualBlocks: a}; }), g('8', [], function () { var a = function (a) { return a.getParam('visualblocks_default_state', !1); }, b = function (a) { return a.settings.visualblocks_content_css; }; return {isEnabledByDefault: a, getContentCss: b}; }), g('b', ['6'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('c', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('a', ['b', 'c'], function (a, b) { var c = a.DOM.uniqueId(), d = function (d, e) { var f = b.toArray(d.getElementsByTagName('link')), g = b.grep(f, function (a) { return a.id === c; }); if (g.length === 0) { var h = a.DOM.create('link', {id: c, rel: 'stylesheet', href: e}); d.getElementsByTagName('head')[0].appendChild(h); } }; return {load: d}; }), g('7', ['9', '8', 'a'], function (a, b, c) { var d = function (d, e, f) { var g = d.dom, h = b.getContentCss(d); c.load(d.getDoc(), h || e + '/css/visualblocks.css'), g.toggleClass(d.getBody(), 'mce-visualblocks'), f.set(!f.get()), a.fireVisualBlocks(d, f.get()); }; return {toggleVisualBlocks: d}; }), g('3', ['7'], function (a) { var b = function (b, c, d) { b.addCommand('mceVisualBlocks', function () { a.toggleVisualBlocks(b, c, d); }); }; return {register: b}; }), g('4', ['8', '7'], function (a, b) { var c = function (c, d, e) { c.on('PreviewFormats AfterPreviewFormats', function (a) { e.get() && c.dom.toggleClass(c.getBody(), 'mce-visualblocks', a.type === 'afterpreviewformats'); }), c.on('init', function () { a.isEnabledByDefault(c) && b.toggleVisualBlocks(c, d, e); }), c.on('remove', function () { c.dom.removeClass(c.getBody(), 'mce-visualblocks'); }); }; return {setup: c}; }), g('5', ['7'], function (a) { var b = function (a, b) { return function (c) { var d = c.control; d.active(b.get()), a.on('VisualBlocks', function (a) { d.active(a.state); }); }; }, c = function (a, c) { a.addButton('visualblocks', {title: 'Show blocks', cmd: 'mceVisualBlocks', onPostRender: b(a, c)}), a.addMenuItem('visualblocks', {text: 'Show blocks', cmd: 'mceVisualBlocks', onPostRender: b(a, c), selectable: !0, context: 'view', prependToContext: !0}); }; return {register: c}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return b.add('visualblocks', function (b, f) { var g = a(!1); c.register(b, f, g), e.register(b, g), d.setup(b, f, g); }), function () {}; }), d('0')(); }());
