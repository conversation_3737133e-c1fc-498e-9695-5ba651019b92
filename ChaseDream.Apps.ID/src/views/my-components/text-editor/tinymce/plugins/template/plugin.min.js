!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('5', tinymce.util.Tools.resolve), g('1', ['5'], function (a) { return a('tinymce.PluginManager'); }), h('c', Array), h('d', Error), g('6', ['c', 'd'], function (a, b) { var c = function () {}, d = function (a, b) { return function () { return a(b.apply(null, arguments)); }; }, e = function (a) { return function () { return a; }; }, f = function (a) { return a; }, g = function (a, b) { return a === b; }, h = function (b) { for (var c = new a(arguments.length - 1), d = 1; d < arguments.length; d++)c[d - 1] = arguments[d]; return function () { for (var d = new a(arguments.length), e = 0; e < d.length; e++)d[e] = arguments[e]; var f = c.concat(d); return b.apply(null, f); }; }, i = function (a) { return function () { return !a.apply(null, arguments); }; }, j = function (a) { return function () { throw new b(a); }; }, k = function (a) { return a(); }, l = function (a) { a(); }, m = e(!1), n = e(!0); return {noop: c, compose: d, constant: e, identity: f, tripleEquals: g, curry: h, not: i, die: j, apply: k, call: l, never: m, always: n}; }), g('8', ['5'], function (a) { return a('tinymce.util.Tools'); }), g('e', ['5'], function (a) { return a('tinymce.util.XHR'); }), g('f', ['5'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('9', ['f'], function (a) { var b = function (a) { return a.getParam('template_cdate_classes', 'cdate'); }, c = function (a) { return a.getParam('template_mdate_classes', 'mdate'); }, d = function (a) { return a.getParam('template_selected_content_classes', 'selcontent'); }, e = function (a) { return a.getParam('template_preview_replace_values'); }, f = function (a) { return a.getParam('template_replace_values'); }, g = function (a) { return a.templates; }, h = function (a) { return a.getParam('template_cdate_format', a.getLang('template.cdate_format')); }, i = function (a) { return a.getParam('template_mdate_format', a.getLang('template.mdate_format')); }, j = function (a) { return a.getParam('template_popup_width', 600); }, k = function (b) { return Math.min(a.DOM.getViewPort().h, b.getParam('template_popup_height', 500)); }; return {getCreationDateClasses: b, getModificationDateClasses: c, getSelectedContentClasses: d, getPreviewReplaceValues: e, getTemplateReplaceValues: f, getTemplates: g, getCdateFormat: h, getMdateFormat: i, getDialogWidth: j, getDialogHeight: k}; }), g('a', [], function () { var a = function (a, b) { if (a = '' + a, a.length < b) for (var c = 0; c < b - a.length; c++)a = '0' + a; return a; }, b = function (b, c, d) { var e = 'Sun Mon Tue Wed Thu Fri Sat Sun'.split(' '), f = 'Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday'.split(' '), g = 'Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec'.split(' '), h = 'January February March April May June July August September October November December'.split(' '); return d = d || new Date(), c = c.replace('%D', '%m/%d/%Y'), c = c.replace('%r', '%I:%M:%S %p'), c = c.replace('%Y', '' + d.getFullYear()), c = c.replace('%y', '' + d.getYear()), c = c.replace('%m', a(d.getMonth() + 1, 2)), c = c.replace('%d', a(d.getDate(), 2)), c = c.replace('%H', '' + a(d.getHours(), 2)), c = c.replace('%M', '' + a(d.getMinutes(), 2)), c = c.replace('%S', '' + a(d.getSeconds(), 2)), c = c.replace('%I', '' + ((d.getHours() + 11) % 12 + 1)), c = c.replace('%p', '' + (d.getHours() < 12 ? 'AM' : 'PM')), c = c.replace('%B', '' + b.translate(h[d.getMonth()])), c = c.replace('%b', '' + b.translate(g[d.getMonth()])), c = c.replace('%A', '' + b.translate(f[d.getDay()])), c = c.replace('%a', '' + b.translate(e[d.getDay()])), c = c.replace('%%', '%'); }; return {getDateTime: b}; }), g('7', ['8', 'e', '9', 'a'], function (a, b, c, d) { var e = function (a, d) { return function () { var e = c.getTemplates(a); return typeof e === 'function' ? void e(d) : void (typeof e === 'string' ? b.send({url: e, success: function (a) { d(JSON.parse(a)); }}) : d(e)); }; }, f = function (b, c, d) { return a.each(d, function (a, b) { typeof a === 'function' && (a = a(b)), c = c.replace(new RegExp('\\{\\$' + b + '\\}', 'g'), a); }), c; }, g = function (b, d) { var e = b.dom, f = c.getTemplateReplaceValues(b); a.each(e.select('*', d), function (b) { a.each(f, function (a, c) { e.hasClass(b, c) && typeof f[c] === 'function' && f[c](b); }); }); }, h = function (a, b) { return new RegExp('\\b' + b + '\\b', 'g').test(a.className); }, i = function (b, e, i) { var j, k, l = b.dom, m = b.selection.getContent(); i = f(b, i, c.getTemplateReplaceValues(b)), j = l.create('div', null, i), k = l.select('.mceTmpl', j), k && k.length > 0 && (j = l.create('div', null), j.appendChild(k[0].cloneNode(!0))), a.each(l.select('*', j), function (a) { h(a, c.getCreationDateClasses(b).replace(/\s+/g, '|')) && (a.innerHTML = d.getDateTime(b, c.getCdateFormat(b))), h(a, c.getModificationDateClasses(b).replace(/\s+/g, '|')) && (a.innerHTML = d.getDateTime(b, c.getMdateFormat(b))), h(a, c.getSelectedContentClasses(b).replace(/\s+/g, '|')) && (a.innerHTML = m); }), g(b, j), b.execCommand('mceInsertContent', !1, j.innerHTML), b.addVisual(); }; return {createTemplateList: e, replaceTemplateValues: f, replaceVals: g, insertTemplate: i}; }), g('2', ['6', '7'], function (a, b) { var c = function (c) { c.addCommand('mceInsertTemplate', a.curry(b.insertTemplate, c)); }; return {register: c}; }), g('3', ['8', '9', 'a', '7'], function (a, b, c, d) { var e = function (e) { e.on('PreProcess', function (f) { var g = e.dom, h = b.getMdateFormat(e); a.each(g.select('div', f.node), function (b) { g.hasClass(b, 'mceTmpl') && (a.each(g.select('*', b), function (a) { g.hasClass(a, e.getParam('template_mdate_classes', 'mdate').replace(/\s+/g, '|')) && (a.innerHTML = c.getDateTime(e, h)); }), d.replaceVals(e, b)); }); }); }; return {setup: e}; }), g('b', ['f', '8', 'e', '9', '7'], function (a, b, c, d, e) { var f = function (a, c, f) { if (f.indexOf('<html>') === -1) { var g = ''; b.each(a.contentCSS, function (b) { g += '<link type="test/css" rel="stylesheet" href="' + a.documentBaseURI.toAbsolute(b) + '">'; }); var h = a.settings.body_class || ''; h.indexOf('=') !== -1 && (h = a.getParam('body_class', '', 'hash'), h = h[a.id] || ''), f = '<!DOCTYPE html><html><head>' + g + '</head><body class="' + h + '">' + f + '</body></html>'; }f = e.replaceTemplateValues(a, f, d.getPreviewReplaceValues(a)); var i = c.find('iframe')[0].getEl().contentWindow.document; i.open(), i.write(f), i.close(); }, g = function (a, g) { var h, i, j = []; if (!g || g.length === 0) { var k = a.translate('No templates defined.'); return void a.notificationManager.open({text: k, type: 'info'}); }b.each(g, function (a) { j.push({selected: !j.length, text: a.title, value: {url: a.url, content: a.content, description: a.description}}); }); var l = function (b) { var d = b.control.value(); d.url ? c.send({url: d.url, success: function (b) { i = b, f(a, h, i); }}) : (i = d.content, f(a, h, i)), h.find('#description')[0].text(b.control.value().description); }; h = a.windowManager.open({title: 'Insert template', layout: 'flex', direction: 'column', align: 'stretch', padding: 15, spacing: 10, items: [{type: 'form', flex: 0, padding: 0, items: [{type: 'container', label: 'Templates', items: {type: 'listbox', label: 'Templates', name: 'template', values: j, onselect: l}}]}, {type: 'label', name: 'description', label: 'Description', text: '\xa0'}, {type: 'iframe', flex: 1, border: 1}], onsubmit: function () { e.insertTemplate(a, !1, i); }, minWidth: d.getDialogWidth(a), minHeight: d.getDialogHeight(a)}), h.find('listbox')[0].fire('select'); }; return {open: g}; }), g('4', ['7', 'b'], function (a, b) { var c = function (a) { return function (c) { b.open(a, c); }; }, d = function (b) { b.addButton('template', {title: 'Insert template', onclick: a.createTemplateList(b.settings, c(b))}), b.addMenuItem('template', {text: 'Template', onclick: a.createTemplateList(b.settings, c(b)), icon: 'template', context: 'insert'}); }; return {register: d}; }), g('0', ['1', '2', '3', '4'], function (a, b, c, d) { return a.add('template', function (a) { d.register(a), b.register(a), c.setup(a); }), function () {}; }), d('0')(); }());
