!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('4', tinymce.util.Tools.resolve), g('1', ['4'], function (a) { return a('tinymce.PluginManager'); }), g('6', ['4'], function (a) { return a('tinymce.Env'); }), g('7', ['4'], function (a) { return a('tinymce.util.Tools'); }), g('8', [], function () { var a = function (a) { return parseInt(a.getParam('plugin_preview_width', '650'), 10); }, b = function (a) { return parseInt(a.getParam('plugin_preview_height', '500'), 10); }; return {getPreviewDialogWidth: a, getPreviewDialogHeight: b}; }), g('9', ['7'], function (a) { var b = function (b, c, d) { var e, f = '', g = b.dom.encode; f += '<base href="' + g(b.documentBaseURI.getURI()) + '">', a.each(b.contentCSS, function (a) { f += '<link type="test/css" rel="stylesheet" href="' + g(b.documentBaseURI.toAbsolute(a)) + '">'; }); var h = b.settings.body_id || 'tinymce'; h.indexOf('=') !== -1 && (h = b.getParam('body_id', '', 'hash'), h = h[b.id] || h); var i = b.settings.body_class || ''; i.indexOf('=') !== -1 && (i = b.getParam('body_class', '', 'hash'), i = i[b.id] || ''); var j = '<script>document.addEventListener && document.addEventListener("click", function(e) {for (var elm = e.target; elm; elm = elm.parentNode) {if (elm.nodeName === "A") {e.preventDefault();}}}, false);</script> ', k = b.settings.directionality ? ' dir="' + b.settings.directionality + '"' : ''; if (e = '<!DOCTYPE html><html><head>' + f + '</head><body id="' + g(h) + '" class="mce-content-body ' + g(i) + '"' + g(k) + '>' + b.getContent() + j + '</body></html>', d)c.src = 'data:test/html;charset=utf-8,' + encodeURIComponent(e); else { var l = c.contentWindow.document; l.open(), l.write(e), l.close(); } }; return {injectIframeContent: b}; }), g('5', ['6', '7', '8', '9'], function (a, b, c, d) { var e = function (b) { var e = !a.ie, f = '<iframe src="javascript:\'\'" frameborder="0"' + (e ? ' sandbox="allow-scripts"' : '') + '></iframe>', g = c.getPreviewDialogWidth(b), h = c.getPreviewDialogHeight(b); b.windowManager.open({title: 'Preview', width: g, height: h, html: f, buttons: {text: 'Close', onclick: function (a) { a.control.parent().parent().close(); }}, onPostRender: function (a) { var c = a.control.getEl('body').firstChild; d.injectIframeContent(b, c, e); }}); }; return {open: e}; }), g('2', ['5'], function (a) { var b = function (b) { b.addCommand('mcePreview', function () { a.open(b); }); }; return {register: b}; }), g('3', [], function () { var a = function (a) { a.addButton('preview', {title: 'Preview', cmd: 'mcePreview'}), a.addMenuItem('preview', {text: 'Preview', cmd: 'mcePreview', context: 'view'}); }; return {register: a}; }), g('0', ['1', '2', '3'], function (a, b, c) { return a.add('preview', function (a) { b.register(a), c.register(a); }), function () {}; }), d('0')(); }());
