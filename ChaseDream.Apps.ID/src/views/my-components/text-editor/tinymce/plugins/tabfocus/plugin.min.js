!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('3', tinymce.util.Tools.resolve), g('1', ['3'], function (a) { return a('tinymce.PluginManager'); }), h('4', window), g('5', ['3'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('6', ['3'], function (a) { return a('tinymce.EditorManager'); }), g('7', ['3'], function (a) { return a('tinymce.Env'); }), g('8', ['3'], function (a) { return a('tinymce.util.Delay'); }), g('9', ['3'], function (a) { return a('tinymce.util.Tools'); }), g('a', ['3'], function (a) { return a('tinymce.util.VK'); }), g('b', [], function () { var a = function (a) { return a.getParam('tabfocus_elements', ':prev,:next'); }, b = function (b) { return b.getParam('tab_focus', a(b)); }; return {getTabFocus: b}; }), g('2', ['4', '5', '6', '7', '8', '9', 'a', 'b'], function (a, b, c, d, e, f, g, h) { var i = b.DOM, j = function (a) { a.keyCode !== g.TAB || a.ctrlKey || a.altKey || a.metaKey || a.preventDefault(); }, k = function (b) { function k (j) { function k (a) { function d (a) { return a.nodeName === 'BODY' || a.type !== 'hidden' && a.style.display !== 'none' && a.style.visibility !== 'hidden' && d(a.parentNode); } function e (a) { return /INPUT|TEXTAREA|BUTTON/.test(a.tagName) && c.get(j.id) && a.tabIndex !== -1 && d(a); } if (m = i.select(':input:enabled,*[tabindex]:not(iframe)'), f.each(m, function (a, c) { if (a.id === b.id) return l = c, !1; }), a > 0) { for (o = l + 1; o < m.length; o++) if (e(m[o])) return m[o]; } else for (o = l - 1; o >= 0; o--) if (e(m[o])) return m[o]; return null; } var l, m, n, o; if (!(j.keyCode !== g.TAB || j.ctrlKey || j.altKey || j.metaKey || j.isDefaultPrevented()) && (n = f.explode(h.getTabFocus(b)), n.length === 1 && (n[1] = n[0], n[0] = ':prev'), m = j.shiftKey ? n[0] === ':prev' ? k(-1) : i.get(n[0]) : n[1] === ':next' ? k(1) : i.get(n[1]))) { var p = c.get(m.id || m.name); m.id && p ? p.focus() : e.setTimeout(function () { d.webkit || a.focus(), m.focus(); }, 10), j.preventDefault(); } }b.on('init', function () { b.inline && i.setAttrib(b.getBody(), 'tabIndex', null), b.on('keyup', j), d.gecko ? b.on('keypress keydown', k) : b.on('keydown', k); }); }; return {setup: k}; }), g('0', ['1', '2'], function (a, b) { return a.add('tabfocus', function (a) { b.setup(a); }), function () {}; }), d('0')(); }());
