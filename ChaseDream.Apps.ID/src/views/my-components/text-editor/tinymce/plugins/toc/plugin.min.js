!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('6', tinymce.util.Tools.resolve), g('1', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('8', ['6'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('9', ['6'], function (a) { return a('tinymce.util.I18n'); }), g('a', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('3', [], function () { var a = function (a) { return a.getParam('toc_class', 'mce-toc'); }, b = function (a) { var b = a.getParam('toc_header', 'h2'); return /^h[1-6]$/.test(b) ? b : 'h2'; }, c = function (a) { var b = parseInt(a.getParam('toc_depth', '3'), 10); return b >= 1 && b <= 9 ? b : 3; }; return {getTocClass: a, getTocHeader: b, getTocDepth: c}; }), g('b', [], function () { var a = function (a) { var b = 0; return function () { var c = (new Date()).getTime().toString(32); return a + c + (b++).toString(32); }; }; return {create: a}; }), g('7', ['8', '9', 'a', '3', 'b'], function (a, b, c, d, e) { var f = e.create('mcetoc_'), g = function (a) { var b, c = []; for (b = 1; b <= a; b++)c.push('h' + b); return c.join(','); }, h = function (a) { return i(a).length > 0; }, i = function (a) { var b = d.getTocClass(a), e = d.getTocHeader(a), h = g(d.getTocDepth(a)), i = a.$(h); return i.length && /^h[1-9]$/i.test(e) && (i = i.filter(function (c, d) { return !a.dom.hasClass(d.parentNode, b); })), c.map(i, function (b) { return {id: b.id ? b.id : f(), level: parseInt(b.nodeName.replace(/^H/i, ''), 10), title: a.$.text(b), element: b}; }); }, j = function (a) { var b, c = 9; for (b = 0; b < a.length; b++) if (a[b].level < c && (c = a[b].level), c === 1) return c; return c; }, k = function (b, c) { var d = '<' + b + ' contenteditable="true">', e = '</' + b + '>'; return d + a.DOM.encode(c) + e; }, l = function (a) { var b = m(a); return '<div class="' + a.dom.encode(d.getTocClass(a)) + '" contenteditable="false">' + b + '</div>'; }, m = function (a) { var c, e, f, g, h = '', l = i(a), m = j(l) - 1; if (!l.length) return ''; for (h += k(d.getTocHeader(a), b.translate('Table of Contents')), c = 0; c < l.length; c++) { if (f = l[c], f.element.id = f.id, g = l[c + 1] && l[c + 1].level, m === f.level)h += '<li>'; else for (e = m; e < f.level; e++)h += '<ul><li>'; if (h += '<a href="#' + f.id + '">' + f.title + '</a>', g !== f.level && g) for (e = f.level; e > g; e--)h += '</li></ul><li>'; else h += '</li>', g || (h += '</ul>'); m = f.level; } return h; }, n = function (a, b) { return !b.length || a.dom.getParents(b[0], '.mce-offscreen-selection').length > 0; }, o = function (a) { var b = d.getTocClass(a), c = a.$('.' + b); n(a, c) ? a.insertContent(l(a)) : p(a); }, p = function (a) { var b = d.getTocClass(a), c = a.$('.' + b); c.length && a.undoManager.transact(function () { c.html(m(a)); }); }; return {hasHeaders: h, insertToc: o, updateToc: p}; }), g('2', ['7'], function (a) { var b = function (b) { b.addCommand('mceInsertToc', function () { a.insertToc(b); }), b.addCommand('mceUpdateToc', function () { a.updateToc(b); }); }; return {register: b}; }), g('4', ['3'], function (a) { var b = function (b) { var c = b.$, d = a.getTocClass(b); b.on('PreProcess', function (a) { var b = c('.' + d, a.node); b.length && (b.removeAttr('contentEditable'), b.find('[contenteditable]').removeAttr('contentEditable')); }), b.on('SetContent', function () { var a = c('.' + d); a.length && (a.attr('contentEditable', !1), a.children(':first-child').attr('contentEditable', !0)); }); }; return {setup: b}; }), g('5', ['3', '7'], function (a, b) { var c = function (a) { return function (c) { var d = c.control; a.on('LoadContent SetContent change', function () { d.disabled(a.readonly || !b.hasHeaders(a)); }); }; }, d = function (b) { return function (c) { return c && b.dom.is(c, '.' + a.getTocClass(b)) && b.getBody().contains(c); }; }, e = function (a) { a.addButton('toc', {tooltip: 'Table of Contents', cmd: 'mceInsertToc', icon: 'toc', onPostRender: c(a)}), a.addButton('tocupdate', {tooltip: 'Update', cmd: 'mceUpdateToc', icon: 'reload'}), a.addMenuItem('toc', {text: 'Table of Contents', context: 'insert', cmd: 'mceInsertToc', onPostRender: c(a)}), a.addContextToolbar(d(a), 'tocupdate'); }; return {register: e}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return a.add('toc', function (a) { b.register(a), e.register(a), d.setup(a); }), function () {}; }), d('0')(); }());
