!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('3', tinymce.util.Tools.resolve), g('1', ['3'], function (a) { return a('tinymce.PluginManager'); }), g('4', ['3'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('5', ['3'], function (a) { return a('tinymce.util.Tools'); }), g('6', [], function () { var a = ['000000', 'Black', '993300', 'Burnt orange', '333300', 'Dark olive', '003300', 'Dark green', '003366', 'Dark azure', '000080', 'Navy Blue', '333399', 'Indigo', '333333', 'Very dark gray', '800000', 'Maroon', 'FF6600', 'Orange', '808000', 'Olive', '008000', 'Green', '008080', 'Teal', '0000FF', 'Blue', '666699', 'Grayish blue', '808080', 'Gray', 'FF0000', 'Red', 'FF9900', 'Amber', '99CC00', 'Yellow green', '339966', 'Sea green', '33CCCC', 'Turquoise', '3366FF', 'Royal blue', '800080', 'Purple', '999999', 'Medium gray', 'FF00FF', 'Magenta', 'FFCC00', 'Gold', 'FFFF00', 'Yellow', '00FF00', 'Lime', '00FFFF', 'Aqua', '00CCFF', 'Sky blue', '993366', 'Red violet', 'FFFFFF', 'White', 'FF99CC', 'Pink', 'FFCC99', 'Peach', 'FFFF99', 'Light yellow', 'CCFFCC', 'Pale green', 'CCFFFF', 'Pale cyan', '99CCFF', 'Light sky blue', 'CC99FF', 'Plum'], b = function (b) { return b.getParam('textcolor_map', a); }, c = function (a) { return a.getParam('forecolor_map', b(a)); }, d = function (a) { return a.getParam('backcolor_map', b(a)); }, e = function (a) { return a.getParam('textcolor_rows', 5); }, f = function (a) { return a.getParam('textcolor_cols', 8); }, g = function (a) { return a.getParam('forecolor_rows', e(a)); }, h = function (a) { return a.getParam('backcolor_rows', e(a)); }, i = function (a) { return a.getParam('forecolor_cols', f(a)); }, j = function (a) { return a.getParam('backcolor_cols', f(a)); }, k = function (a) { return a.getParam('color_picker_callback', null); }, l = function (a) { return typeof k(a) === 'function'; }; return {getForeColorMap: c, getBackColorMap: d, getForeColorRows: g, getBackColorRows: h, getForeColorCols: i, getBackColorCols: j, getColorPickerCallback: k, hasColorPicker: l}; }), g('7', [], function () { var a = function (a, b) { var c; return a.dom.getParents(a.selection.getStart(), function (a) { var d; (d = a.style[b === 'forecolor' ? 'color' : 'background-color']) && (c = d); }), c; }, b = function (a) { var b, c = []; for (b = 0; b < a.length; b += 2)c.push({text: a[b + 1], color: '#' + a[b]}); return c; }, c = function (a, b, c) { a.undoManager.transact(function () { a.focus(), a.formatter.apply(b, {value: c}), a.nodeChanged(); }); }, d = function (a, b) { a.undoManager.transact(function () { a.focus(), a.formatter.remove(b, {value: null}, null, !0), a.nodeChanged(); }); }; return {getCurrentColor: a, mapColors: b, applyFormat: c, removeFormat: d}; }), g('9', ['3'], function (a) { return a('tinymce.util.I18n'); }), g('8', ['4', '9', '6', '7'], function (a, b, c, d) { var e = function (c, e, f, g) { var h, i, j, k, l, m, n, o = 0, p = a.DOM.uniqueId('mcearia'), q = function (a, c) { var d = a === 'transparent'; return '<td class="mce-grid-cell' + (d ? ' mce-colorbtn-trans' : '') + '"><div id="' + p + '-' + o++ + '" data-mce-color="' + (a || '') + '" role="option" tabIndex="-1" style="' + (a ? 'background-color: ' + a : '') + '" title="' + b.translate(c) + '">' + (d ? '&#215;' : '') + '</div></td>'; }; for (h = d.mapColors(f), h.push({text: b.translate('No color'), color: 'transparent'}), j = '<table class="mce-grid mce-grid-border mce-colorbutton-grid" role="list" cellspacing="0"><tbody>', k = h.length - 1, m = 0; m < e; m++) { for (j += '<tr>', l = 0; l < c; l++)n = m * c + l, n > k ? j += '<td></td>' : (i = h[n], j += q(i.color, i.text)); j += '</tr>'; } if (g) { for (j += '<tr><td colspan="' + c + '" class="mce-custom-color-btn"><div id="' + p + '-c" class="mce-widget mce-btn mce-btn-small mce-btn-flat" role="button" tabindex="-1" aria-labelledby="' + p + '-c" style="width: 100%"><button type="button" role="presentation" tabindex="-1">' + b.translate('Custom...') + '</button></div></td></tr>', j += '<tr>', l = 0; l < c; l++)j += q('', 'Custom color'); j += '</tr>'; } return j += '</tbody></table>'; }; return {getHtml: e}; }), g('2', ['4', '5', '6', '7', '8'], function (a, b, c, d, e) { var f = function (a, b) { a.style.background = b, a.setAttribute('data-mce-color', b); }, g = function (a) { return function (b) { var c = b.control; c._color ? d.applyFormat(a, c.settings.format, c._color) : d.removeFormat(a, c.settings.format); }; }, h = function (e, g) { return function (h) { var i, j = this.parent(), k = d.getCurrentColor(e, j.settings.format), l = function (a) { j.hidePanel(), j.color(a), d.applyFormat(e, j.settings.format, a); }, m = function () { j.hidePanel(), j.resetColor(), d.removeFormat(e, j.settings.format); }; if (a.DOM.getParent(h.target, '.mce-custom-color-btn')) { j.hidePanel(); var n = c.getColorPickerCallback(e); n.call(e, function (a) { var c, d, e, h = j.panel.getEl().getElementsByTagName('table')[0]; for (c = b.map(h.rows[h.rows.length - 1].childNodes, function (a) { return a.firstChild; }), e = 0; e < c.length && (d = c[e], d.getAttribute('data-mce-color')); e++);if (e === g) for (e = 0; e < g - 1; e++)f(c[e], c[e + 1].getAttribute('data-mce-color')); f(d, a), l(a); }, k); }i = h.target.getAttribute('data-mce-color'), i ? (this.lastId && a.DOM.get(this.lastId).setAttribute('aria-selected', !1), h.target.setAttribute('aria-selected', !0), this.lastId = h.target.id, i === 'transparent' ? m() : l(i)) : i !== null && j.hidePanel(); }; }, i = function (a, b) { return function () { var d = b ? c.getForeColorCols(a) : c.getBackColorCols(a), f = b ? c.getForeColorRows(a) : c.getBackColorRows(a), g = b ? c.getForeColorMap(a) : c.getBackColorMap(a), h = c.hasColorPicker(a); return e.getHtml(d, f, g, h); }; }, j = function (a) { a.addButton('forecolor', {type: 'colorbutton', tooltip: 'Text color', format: 'forecolor', panel: {role: 'application', ariaRemember: !0, html: i(a, !0), onclick: h(a, c.getForeColorCols(a))}, onclick: g(a)}), a.addButton('backcolor', {type: 'colorbutton', tooltip: 'Background color', format: 'hilitecolor', panel: {role: 'application', ariaRemember: !0, html: i(a, !1), onclick: h(a, c.getBackColorCols(a))}, onclick: g(a)}); }; return {register: j}; }), g('0', ['1', '2'], function (a, b) { return a.add('textcolor', function (a) { b.register(a); }), function () {}; }), d('0')(); }());
