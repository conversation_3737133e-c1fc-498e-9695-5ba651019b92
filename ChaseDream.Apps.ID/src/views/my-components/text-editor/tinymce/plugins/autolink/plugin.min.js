!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('4', tinymce.util.Tools.resolve), g('1', ['4'], function (a) { return a('tinymce.Env'); }), g('2', ['4'], function (a) { return a('tinymce.PluginManager'); }), g('5', [], function () { var a = function (a) { return a.getParam('autolink_pattern', /^(https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.|(?:mailto:)?[A-Z0-9._%+\-]+@)(.+)$/i); }, b = function (a) { return a.getParam('default_link_target', ''); }; return {getAutoLinkPattern: a, getDefaultLinkTarget: b}; }), g('3', ['1', '2', '5'], function (a, b, c) { var d = function (a, b) { return a === b || a === ' ' || a.charCodeAt(0) === 160; }, e = function (a) { k(a, -1, '(', !0); }, f = function (a) { k(a, 0, '', !0); }, g = function (a) { k(a, -1, '', !1); }, h = function (a, b) { if (b < 0 && (b = 0), a.nodeType === 3) { var c = a.data.length; b > c && (b = c); } return b; }, i = function (a, b, c) { b.nodeType !== 1 || b.hasChildNodes() ? a.setStart(b, h(b, c)) : a.setStartBefore(b); }, j = function (a, b, c) { b.nodeType !== 1 || b.hasChildNodes() ? a.setEnd(b, h(b, c)) : a.setEndAfter(b); }, k = function (a, b, e) { var f, g, h, k, l, m, n, o, p, q, r = c.getAutoLinkPattern(a), s = c.getDefaultLinkTarget(a); if (a.selection.getNode().tagName !== 'A') { if (f = a.selection.getRng(!0).cloneRange(), f.startOffset < 5) { if (o = f.endContainer.previousSibling, !o) { if (!f.endContainer.firstChild || !f.endContainer.firstChild.nextSibling) return; o = f.endContainer.firstChild.nextSibling; } if (p = o.length, i(f, o, p), j(f, o, p), f.endOffset < 5) return; g = f.endOffset, k = o; } else { if (k = f.endContainer, k.nodeType !== 3 && k.firstChild) { for (;k.nodeType !== 3 && k.firstChild;)k = k.firstChild; k.nodeType === 3 && (i(f, k, 0), j(f, k, k.nodeValue.length)); }g = f.endOffset === 1 ? 2 : f.endOffset - 1 - b; }h = g; do i(f, k, g >= 2 ? g - 2 : 0), j(f, k, g >= 1 ? g - 1 : 0), g -= 1, q = f.toString(); while (q !== ' ' && q !== '' && q.charCodeAt(0) !== 160 && g - 2 >= 0 && q !== e);d(f.toString(), e) ? (i(f, k, g), j(f, k, h), g += 1) : f.startOffset === 0 ? (i(f, k, 0), j(f, k, h)) : (i(f, k, g), j(f, k, h)), m = f.toString(), m.charAt(m.length - 1) === '.' && j(f, k, h - 1), m = f.toString().trim(), n = m.match(r), n && (n[1] === 'www.' ? n[1] = 'http://www.' : /@$/.test(n[1]) && !/^mailto:/.test(n[1]) && (n[1] = 'mailto:' + n[1]), l = a.selection.getBookmark(), a.selection.setRng(f), a.execCommand('createlink', !1, n[1] + n[2]), s && a.dom.setAttrib(a.selection.getNode(), 'target', s), a.selection.moveToBookmark(l), a.nodeChanged()); } }, l = function (b) { var c; return b.on('keydown', function (a) { if (a.keyCode === 13) return g(b); }), a.ie ? void b.on('focus', function () { if (!c) { c = !0; try { b.execCommand('AutoUrlDetect', !1, !0); } catch (a) {} } }) : (b.on('keypress', function (a) { if (a.keyCode === 41) return e(b); }), void b.on('keyup', function (a) { if (a.keyCode === 32) return f(b); })); }; return {setup: l}; }), g('0', ['1', '2', '3'], function (a, b, c) { return b.add('autolink', function (a) { c.setup(a); }), function () {}; }), d('0')(); }());
