!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('5', tinymce.util.Tools.resolve), g('2', ['5'], function (a) { return a('tinymce.PluginManager'); }), g('6', ['5'], function (a) { return a('tinymce.Env'); }), g('7', ['5'], function (a) { return a('tinymce.util.Delay'); }), g('8', [], function () { var a = function (a) { return parseInt(a.getParam('autoresize_min_height', a.getElement().offsetHeight), 10); }, b = function (a) { return parseInt(a.getParam('autoresize_max_height', 0), 10); }, c = function (a) { return a.getParam('autoresize_overflow_padding', 1); }, d = function (a) { return a.getParam('autoresize_bottom_margin', 50); }, e = function (a) { return a.getParam('autoresize_on_init', !0); }; return {getAutoResizeMinHeight: a, getAutoResizeMaxHeight: b, getAutoResizeOverflowPadding: c, getAutoResizeBottomMargin: d, shouldAutoResizeOnInit: e}; }), g('4', ['6', '7', '8'], function (a, b, c) { var d = function (a) { return a.plugins.fullscreen && a.plugins.fullscreen.isFullscreen(); }, e = function (a, c, d, g, h) { b.setEditorTimeout(a, function () { f(a, c), d-- ? e(a, c, d, g, h) : h && h(); }, g); }, f = function (b, e) { var g, h, i, j, k, l, m, n, o, p, q, r, s = b.dom; if (h = b.getDoc(), h && !d(b)) { i = h.body, j = h.documentElement, k = c.getAutoResizeMinHeight(b), m = s.getStyle(i, 'margin-top', !0), n = s.getStyle(i, 'margin-bottom', !0), o = s.getStyle(i, 'padding-top', !0), p = s.getStyle(i, 'padding-bottom', !0), q = s.getStyle(i, 'border-top-width', !0), r = s.getStyle(i, 'border-bottom-width', !0), l = i.offsetHeight + parseInt(m, 10) + parseInt(n, 10) + parseInt(o, 10) + parseInt(p, 10) + parseInt(q, 10) + parseInt(r, 10), (isNaN(l) || l <= 0) && (l = a.ie ? i.scrollHeight : a.webkit && i.clientHeight === 0 ? 0 : i.offsetHeight), l > c.getAutoResizeMinHeight(b) && (k = l); var t = c.getAutoResizeMaxHeight(b); t && l > t ? (k = t, i.style.overflowY = 'auto', j.style.overflowY = 'auto') : (i.style.overflowY = 'hidden', j.style.overflowY = 'hidden', i.scrollTop = 0), k !== e.get() && (g = k - e.get(), s.setStyle(b.iframeElement, 'height', k + 'px'), e.set(k), a.webKit && g < 0 && f(b)); } }, g = function (a, b) { a.on('init', function () { var b, d, e = a.dom; b = c.getAutoResizeOverflowPadding(a), d = c.getAutoResizeBottomMargin(a), b !== !1 && e.setStyles(a.getBody(), {paddingLeft: b, paddingRight: b}), d !== !1 && e.setStyles(a.getBody(), {paddingBottom: d}); }), a.on('nodechange setcontent keyup FullscreenStateChanged', function () { f(a, b); }), c.shouldAutoResizeOnInit(a) && a.on('init', function () { e(a, b, 20, 100, function () { e(a, b, 5, 1e3); }); }); }; return {setup: g, resize: f}; }), g('3', ['4'], function (a) { var b = function (b, c) { b.addCommand('mceAutoResize', function () { a.resize(b, c); }); }; return {register: b}; }), g('0', ['1', '2', '3', '4'], function (a, b, c, d) { return b.add('autoresize', function (b) { if (!b.inline) { var e = a(0); c.register(b, e), d.setup(b, e); } }), function () {}; }), d('0')(); }());
