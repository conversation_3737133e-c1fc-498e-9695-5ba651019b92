<style lang="less">
    @import '../../../styles/common.less';
    @import 'user-edit.less';
</style>

<template>
    <div class="user-edit">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="android-create"></Icon>
                    编辑用户
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="80">
                    <FormItem prop="username" label="用户名：">
                        <Input v-model="userform.username" readonly></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem prop="role" label="角色：">
                        <RadioGroup v-model="userform.role">
                            <Radio label="1">管理员</Radio>
                            <Radio label="0">操作员</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

export default {
    name: 'user_edit',
    components: {

    },
    data () {
        return {
            userform: this.$store.state.users.currentRow,
            rules: {

            },
            access_token: this.$store.state.user.access_token,
            isAccess: true
        };
    },
    computed: {

    },
    watch: {
        '$route' (to) {
           if(to.name === 'user_edit'){
               this.userform = this.$store.state.users.currentRow;
               console.log(this.userform)
           }
        },
    },
    mounted () {
        if(parseInt(Cookies.get('uAccess')) === 1){
            this.isAccess = false
        }
        this.userform = JSON.parse(Cookies.get('currentRow'));
    },
    methods: {
        handleSubmit () {
            this.$refs.userForm.validate((valid) => {
                if (valid) {
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/user',
                        method:'PUT',
                        data: {
                            username: _this.userform.username,
                            id: _this.userform.id,
                            password: _this.userform.password,
                            role: _this.userform.role
                        },
                    }).then(function (res) {
                        //console.log(res.data);
                        if(res.data.msg === 'success'){
                            _this.$store.commit('removeTag', 'user_edit');
                            _this.$store.commit('closePage', 'user_edit');
                            _this.$router.push({
                                name: 'user_list'
                            });
                        }else{

                        }
                    }).catch(function (err) {
                        console.log(err)
                    });
                }
            });
        },
        closePage () {
            this.userform = {}
            this.$store.commit('removeTag', 'user_edit');
            this.$store.commit('closePage', 'user_edit');
            this.$router.push({
                name: 'user_list'
            });
        }

    }
};
</script>

<style>

</style>
