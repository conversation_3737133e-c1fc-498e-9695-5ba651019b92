export const editInlineColumns = [
    {
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },
    /*{
        title: 'ID',
        key: 'id',
        width: 80,
        align: 'center'
    },*/
    {
        title: '用户名',
        align: 'center',
        key: 'username',
        //width: 120,
        editable: true
    },
    /*{
        title: '密码',
        align: 'center',
        key: 'password',
        width: 150,
        editable: true
    },*/
    {
        title: '角色',
        align: 'center',
        key: 'role_name',
        width: 150,
        editable: true
    },
    {
        title: '操作',
        align: 'center',
        width: 190,
        key: 'handle',
        handle: ['edit', 'delete']
    }
];

export const editInlineData = [
    {
        username: '<PERSON><PERSON>',
        password: '1',
        role: '管理员'
    },
    {
        username: '<PERSON><PERSON>',
        password: '1',
        role: '成员'
    },
    {
        username: 'lisa',
        password: '123',
        role: '成员'
    }
];


const tableData = {
    editInlineColumns: editInlineColumns,
    editInlineData: editInlineData
};

export default tableData;
