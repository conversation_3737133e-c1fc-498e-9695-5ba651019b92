<style lang="less">
    @import '../../../styles/common.less';
    @import 'user-add.less';
</style>

<template>
    <div class="user-add">
        <Row v-if="isAccess">
            <Col span="24" class="padding-left-10">
            <Card>
                <p slot="title">
                    <Icon type="person-add"></Icon>
                    添加用户
                </p>
                <Form ref="userForm" :model="userform" :rules="rules" :label-width="90">
                    <FormItem prop="userName" label="用户名：">
                        <Input v-model="userform.userName" placeholder="请输入用户名"></Input>
                    </FormItem>
                    <FormItem prop="password" label="密码：">
                        <Input type="password" v-model="userform.password" placeholder="请输入密码"></Input>
                    </FormItem>
                    <FormItem prop="role" label="角色：">
                        <RadioGroup v-model="userform.role">
                            <Radio label="1">管理员</Radio>
                            <Radio label="0">操作员</Radio>
                        </RadioGroup>
                    </FormItem>
                    <p class="f_err_msg">{{errMsg}}</p>
                    <FormItem>
                        <Button @click="handleSubmit" type="primary">提交</Button>
                        <Button @click="closePage">取消</Button>
                    </FormItem>
                </Form>
            </Card>
            </Col>
        </Row>
        <p v-if="!isAccess">您没有权限访问此页</p>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
import util from '@/libs/util.js';

export default {
    name: 'user_add',
    components: {
       // canEditTable
    },
    data () {
        return {
            userform: {
                userName: '',
                password: '',
                role: '0'
            },
            rules: {
                userName: [
                    { required: true, message: '账号不能为空', trigger: 'blur' },
                    { type: 'string', min: 3, message: '账号不能小于3个字符', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '密码不能为空', trigger: 'blur' },
                    { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                ]
            },
            access_token: this.$store.state.user.access_token,
            errMsg: '',
            isAccess: true
        };
    },
    computed: {

    },
    watch: {
        '$route' (to) {
            if(to.name === 'user_add'){
                this.userform.userName = '';
                this.userform.password = '';
                this.userform.role = '';
            }
        },
    },
    mounted () {
        if(parseInt(Cookies.get('uAccess')) === 1){
            this.isAccess = false
        }
    },
    methods: {
        handleSubmit () {
            this.$refs.userForm.validate((valid) => {
                if (valid) {
                    var _this = this;
                    util.ajax({
                        url:'/api/v1/admin/user',
                        method:'POST',
                        data: {
                            username: _this.userform.userName,
                            password: _this.userform.password,
                            role: _this.userform.role
                        },
                    }).then(function (res) {
                        if(res.data.msg === 'success'){
                            _this.$store.commit('removeTag', 'user_add');
                            _this.$store.commit('closePage', 'user_add');
                            _this.$router.push({
                                name: 'user_list'
                            });
                        }else{
                            //_this.errMsg = res.msg;
                            _this.$Notice.error({
                                title: res.msg
                            });
                        }
                    }).catch(function (err) {
                        //_this.errMsg = err.msg;
                        _this.$Notice.error({
                            title: err.msg
                        });
                        console.log(err)
                    });
                }
            });
        },
        closePage () {
            this.userform.userName = '';
            this.userform.password = '';
            this.userform.role = '';
            this.$store.commit('removeTag', 'user_add');
            this.$router.push({
                name: 'user_list'
            });
        }
    }
};
</script>

<style>

</style>
