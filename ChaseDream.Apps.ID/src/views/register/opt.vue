<style lang="less">
    @import 'register.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 280px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-form opt">
                        <RadioGroup v-model="type" type="button" @on-change="handleSubmit" size="large">
                            <Radio label="1">
                                <div><Icon type="icon_mobile"></Icon></div>
                                <span>手机找回</span>
                            </Radio>
                            <Radio label="2">
                                <div><Icon type="icon_email"></Icon></div>
                                <span>邮箱找回</span>
                            </Radio>
                        </RadioGroup>
                        <!--<br>
                        <br>
                        <br>
                        <br>-->
                        <!--<Button @click="handleSubmit" type="success" long>下一步</Button>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="findpassword">
                <p slot="title">
                    <router-link to="/login">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    找回密码
                </p>
                <div class="form-con opt">
                    <RadioGroup v-model="type" type="button" size="large" @on-change="handleSubmit">
                        <Radio label="1">
                            <Icon type="icon_mobile"></Icon>
                            <span>通过手机找回</span>
                            <Icon type="checked" v-if="type === '1'"></Icon>
                        </Radio>
                        <Radio label="2">
                            <Icon type="icon_email"></Icon>
                            <span>通过邮箱找回</span>
                            <Icon type="checked" v-if="type === '2'"></Icon>
                        </Radio>
                    </RadioGroup>
                    <!--<br>
                    <br>
                    <br>
                    <br>
                    <Button @click="handleSubmit" type="success" long>下一步</Button>-->
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    /*import jigsaws from '@/libs/jigsaw.js';
    import initGeetest from '@/libs/gt.js';*/
    export default {
        name: 'opt',
        data () {
            return {
                form: {
                    mobile: '', //test
                    area_code: 86,
                    captcha: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isPc: true,
                type: '0'
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!this.isPc){
                //window.parent.postMessage({register_type:'register'},'*');
                var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }


            }else {

            }
        },
        methods: {
            handleSubmit (e) {
                //console.log(this.type)
                this.$router.push('/register/findpassword/'+ e);
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
