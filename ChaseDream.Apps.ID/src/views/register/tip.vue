<style lang="less">
    @import 'register.less';
    @import '../login.less';
    @import '../../libs/jigsaw.css';
</style>

<template>
    <div class="register tip" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 390px;background: #fff;">
            <!--<iframe src="https://forum.chasedream.com/member.php?mod=register" frameborder="0" style="position: absolute;left: 0;top: 0;width: 100%;height: 767px;z-index: 1;"></iframe>-->
            <div class="r tip">
                <div class="r-c" style="margin-top: 0">
                    <!--<div class="r-tab">
                        <div class="tab"><span>1</span>手机注册</div>
                        <div class="tab on"><span>2</span>绑定微信（选填）</div>
                        <div class="tab"><span>3</span>论坛昵称</div>
                    </div>-->
                    <div class="r-b r-user">
                        <div class="title">
                            <p>您的此微信已经绑定了其他帐户</p>
                        </div>
                        <div class="we-logo">
                            <img :src="'https://forum.chasedream.com/uc_server/avatar.php?uid='+ userInfo.uid +'&size=small'" alt="" @click="loginWithOther" style="cursor: pointer;">
                            <span>{{userInfo.username}}</span>
                            <p><a href="javascript:;" style="cursor: default;">点击头像直接用该帐户登录</a></p>

                        </div>
                        <div class="desc">
                            您可以：
                            <div class="item">
                            <h3>将该微信换绑到当前帐户</h3>
                            <p>一旦换绑，之前的ChaseDrea帐户将自动解除绑定。</p>
                            <Button type="success" @click="change_binding">换绑</Button>
                        </div>
                            <div class="item">
                                <h3>换一个微信绑定当前帐户</h3>
                                <p>请在微信端更换登录后点击下方重新绑定按钮</p>
                                <!--<router-link to="/register/scan?openid=">-->
                                <a href="javascript:;" @click="getqrcode" v-if="isWin">
                                    <Button type="info">重新绑定</Button>
                                </a>
                                <a href="javascript:;" @click="stap" v-if="!isWin">
                                    <Button type="info">重新绑定</Button>
                                </a>
                                <!--</router-link>-->
                            </div>
                            <div class="item">
                                <h3>跳过绑定微信登录</h3>
                                <p>未来使用手机号码+密码的方式登录</p>
                                <a href="javascript:;" @click="skip">
                                    <Button type="ghost">跳过</Button>
                                </a>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false">
                <p slot="title">
                    <router-link to="/register/wechat">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    提示
                </p>
                <div class="form-con">
                    <div class="tip_info t_tip">
                        <h3>您的此微信已经绑定了其他帐户</h3>
                        <div class="account" @click="loginWithOther">
                            <img :src="'https://forum.chasedream.com/uc_server/avatar.php?uid='+ userInfo.uid +'&size=small'" alt="">
                            <span>{{userInfo.username}}</span> <!--Evelyn_Zhang-->
                            <p><a href="javascript:;">点击头像直接用该帐户登录</a></p>
                        </div>
                        <div class="desc">
                            您可以：
                            <div class="item">
                                <h3>将该微信换绑到当前帐户</h3>
                                <p>一旦换绑，之前的ChaseDrea帐户将自动解除绑定。</p>
                                <Button type="success" @click="change_binding">换绑</Button>
                            </div>
                            <div class="item">
                                <h3>换一个微信绑定当前帐户</h3>
                                <p>请在微信端更换登录后点击下方重新绑定按钮</p>
                                <Button type="info" @click="getqrcode">重新绑定</Button>
                            </div>
                            <div class="item">
                                <h3>跳过绑定微信登录</h3>
                                <p>未来使用手机号码+密码的方式登录</p>
                                <a href="javascript:;" @click="skip">
                                    <Button type="ghost">跳过</Button>
                                </a>

                            </div>
                        </div>
                    </div>
                    <Modal class="wcLogin" v-model="wcLogin" width="360" @on-cancel="cancel">
                        <div slot="header">
                            <h3 align="center">注册/登录</h3>
                            <p align="center">
                                首次登录需要完成两步操作，以后即可自动登录
                            </p>
                        </div>
                        <div class="steps">
                            <div class="item">
                                <p>第1步：先交二维码图片临时保存到手机相册</p>
                                <img :src="wcImg" alt="" width="100%">
                            </div>
                            <div class="item">
                                <p>第2步：在微信的"扫一扫"中打开刚保存的二维码图片</p>
                                <img src="../../images/register/login_wechat_scan.png" alt="" width="100%">
                            </div>
                        </div>
                        <div slot="footer">
                            <a href="weixin://scanqrcode">
                                <Button type="success" size="large" long>二维码存好了，打开微信扫一扫</Button>
                            </a>
                        </div>
                    </Modal>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'tip',
        data () {
            return {
                access_token: this.$store.state.user.access_token,
                isPc: true,
                openid: '',
                wcImg: '',
                wcLogin: false,
                timer: '',
                userInfo: {
                    openid: '',
                    unionid: '',
                    nickname: '',
                    uid: '',
                    username: '',
                    auth: '',
                    saltkey: ''
                },
                isWin: true
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;
            this.userInfo = this.$store.state.register.unbindInfo;

            if(!!this.isPc){
                //window.parent.postMessage({register_type:'tip'},'*');
            }else {
                /*if(this.isWeiXin()){
                    this.showWXp = this.$store.state.register.showWXp;
                    this.showWX = false;
                    this.isWXlogin();
                }*/
            }
            if(window.location.href.split('/').pop() === 'security'){
                this.isWin = false
            }
        },
        methods: {
            loginWithOther(){
                //console.log(this.userInfo.auth)
                //console.log(this.userInfo.saltkey)
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/login_with_other',
                    method:'POST',
                    data: {
                        auth: this.userInfo.auth,
                        saltkey: this.userInfo.saltkey
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!_this.isPc){
                            window.parent.postMessage({register_type:'login'},'*');
                        }else {
                            window.location.href = 'https://forum.chasedream.com/forum.php';
                        }
                    }else{
                        //_this.errMsg = res.msg
                        _this.$Message.error('登录失败！');
                    }
                }).catch(function (err) {
                    console.log(err)
                    //_this.errMsg = err.msg;
                    _this.$Message.error(err.msg);
                });
            },
            /*unbinding () {
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/unbinding',
                    method:'get',
                    data: {
                        openid: this.userInfo.openid,
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){

                    }else{
                        _this.errMsg = res.msg
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },*/
            stap(){
                this.$emit('on-stap');
            },
            change_binding(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/change_binding',
                    method:'post',
                    data: {
                        openid: this.userInfo.openid,
                        unionid: this.userInfo.unionid,
                        nickname: this.userInfo.nickname
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.change_binding){
                            if(!!_this.isPc){
                                //console.log('pc')
                                window.parent.postMessage({register_type:'login'},'*');
                            }else {
                                //console.log('mobile')
                                //window.location.href = 'https://forum.chasedream.com/forum.php';
                                _this.$router.push({
                                    name: 'wechat'
                                });
                            }
                        }else {
                            _this.$Message.error('换绑失败！');
                        }
                    }else{
                        _this.$Message.error('换绑失败！');
                        //_this.errMsg = res.msg
                    }
                }).catch(function (err) {
                    console.log(err)
                    //_this.errMsg = err.msg;
                    _this.$Message.error(err.msg);
                });
            },
            getqrcode (){
                if(!!this.isPc){
                    this.$router.push({
                        name: 'scan'
                    });
                    window.parent.postMessage({register_type:'toScan'},'*');
                }else {
                    if(!!this.timer){
                        clearInterval(this.timer);
                    }

                    this.wcLogin = true;
                    var _this = this;

                    util.ajax({
                        url: "api/v1/wechat/qrcode?t=" + (new Date()).getTime(), // 加随机数防止缓存
                        method: "get",
                        dataType: "json"
                    }).then (function (data) {
                        //console.log(data);
                        if(data.data.msg === 'success'){
                            _this.wcImg = data.data.data.image;
                            var s = 0;
                            _this.timer = setInterval(function () {
                                s++;
                                if(s === 1000*120){
                                    _this.wcLogin = false;
                                    clearInterval(_this.timer);
                                }

                                util.ajax({
                                    url: "api/v1/wechat/verify_ticket?type=binding&t=" + data.data.data.ticket,
                                    method: "get",
                                    dataType: "json"
                                }).then (function (data) {
                                    console.log(data);
                                    if(data.data.msg === 'success'){
                                        if(data.data.data.openid !== ''){
                                            clearInterval(_this.timer);
                                            if(data.data.data.uid > 0 && !data.data.data.binding){
                                                _this.$store.commit('setUnbindInfo',data.data.data);
                                                _this.wcLogin = false;
                                                /*_this.$router.push({
                                                 name: 'tip'
                                                 });*/
                                            }else if(!!data.data.data.binding) {
                                                //window.location.href = 'https://forum.chasedream.com/forum.php';
                                                _this.$router.push({
                                                    name: 'wechat'
                                                });
                                            }
                                        }
                                    }else{
                                        //_this.errMsg = data.msg;
                                        _this.$Message.error(data.msg);
                                    }
                                }).catch(function (err) {
                                    console.log(err)
                                    //_this.errMsg = err.msg;
                                    _this.$Message.error(err.msg);
                                });
                            },1000);
                        }else{
                            //_this.errMsg = res.msg;
                            _this.$Message.error(res.msg);
                        }
                    }).catch(function (err) {
                        console.log(err)
                        //_this.errMsg = err.msg;
                        _this.$Message.error(err.msg);
                    });
                }
            },
            skip (){
                if(!!this.isPc){
                    window.parent.postMessage({register_type:'close'},'*');
                    this.$emit('on-stap-1');

                }else {
                    //window.location.href = 'https://forum.chasedream.com/forum.php';
                    this.$router.push({
                        name: 'wechat'
                    });
                }
            },
            /*reBinding(){
                _this.$router.push({
                    name: 'scan'
                });
                if(!!this.isPc){
                    window.parent.postMessage({register_type:'toScan'},'*');
                }
            },*/
            cancel (){
                clearInterval(this.timer);
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            },
            isWXlogin(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/check_login',
                    method:'GET',
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.login){
                            window.location.href = 'https://forum.chasedream.com/forum.php';
                        }else {
                            if(!_this.$store.state.register.openid){
                                _this.$store.commit('wx_url',util,_this);
                            }
                        }
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },
        }
    };
</script>

<style>

</style>
