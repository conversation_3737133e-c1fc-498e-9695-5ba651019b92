<style lang="less">
@import "register.less";
</style>

<template>
  <div class="register" @keydown.enter="handleSubmit">
    <div
      class="register-con"
      v-if="!!isPc"
      style="height: 280px;overflow: hidden;background: #fff;"
    >
      <div class="r">
        <div class="r-c">
          <div class="r-form">
            <Form :label-width="132">
              <FormItem label="手机号:" class="required" v-show="f_type === 1">
                <Input v-model="form.mobile" class="ipt_m"></Input>
                <span class="tip" v-if="isTipShow">{{ mobile_tip }}</span>
                <Poptip
                  title=""
                  placement="bottom-start"
                  class="pc_citys"
                  v-model="visible"
                >
                  <a
                    >+{{ form.area_code }}
                    <Icon type="chevron-down" size="10"></Icon
                  ></a>
                  <div slot="content">
                    <Tabs value="use">
                      <TabPane label="常用" name="use">
                        <div class="citys">
                          <span
                            v-for="item in area_code_use"
                            v-text="item.name + ' +' + item.area_code"
                            :key="item.index"
                            @click="getAreaCode(item.area_code)"
                            :class="
                              form.area_code === item.area_code ? 'on' : ''
                            "
                            :title="item.name + ' +' + item.area_code"
                          ></span>
                        </div>
                      </TabPane>
                      <TabPane label="ABCDEF" name="abcdef">
                        <div class="citys">
                          <span
                            v-for="item in area_code_abcdef"
                            v-text="item.name + ' +' + item.area_code"
                            :key="item.index"
                            @click="getAreaCode(item.area_code)"
                            :class="
                              form.area_code === item.area_code ? 'on' : ''
                            "
                            :title="item.name + ' +' + item.area_code"
                          ></span>
                        </div>
                      </TabPane>
                      <TabPane label="GHJ" name="ghij">
                        <div class="citys">
                          <span
                            v-for="item in area_code_ghij"
                            v-text="item.name + ' +' + item.area_code"
                            :key="item.index"
                            @click="getAreaCode(item.area_code)"
                            :class="
                              form.area_code === item.area_code ? 'on' : ''
                            "
                            :title="item.name + ' +' + item.area_code"
                          ></span>
                        </div>
                      </TabPane>
                      <TabPane label="KLMN" name="klmn">
                        <div class="citys">
                          <span
                            v-for="item in area_code_klmn"
                            v-text="item.name + ' +' + item.area_code"
                            :key="item.index"
                            @click="getAreaCode(item.area_code)"
                            :class="
                              form.area_code === item.area_code ? 'on' : ''
                            "
                            :title="item.name + ' +' + item.area_code"
                          ></span>
                        </div>
                      </TabPane>
                      <TabPane label="PQISTUVW" name="opqistuvw">
                        <div class="citys">
                          <span
                            v-for="item in area_code_opqistuvw"
                            v-text="item.name + ' +' + item.area_code"
                            :key="item.index"
                            @click="getAreaCode(item.area_code)"
                            :class="
                              form.area_code === item.area_code ? 'on' : ''
                            "
                            :title="item.name + ' +' + item.area_code"
                          ></span>
                        </div>
                      </TabPane>
                      <TabPane label="XYZ" name="xyz">
                        <div class="citys">
                          <span
                            v-for="item in area_code_xyz"
                            v-text="item.name + ' +' + item.area_code"
                            :key="item.index"
                            @click="getAreaCode(item.area_code)"
                            :class="
                              form.area_code === item.area_code ? 'on' : ''
                            "
                            :title="item.name + ' +' + item.area_code"
                          ></span>
                        </div>
                      </TabPane>
                    </Tabs>
                  </div>
                </Poptip>
              </FormItem>
              <FormItem label="邮箱:" class="required" v-show="f_type === 2">
                <Input v-model="form.email" @on-change="email_tip = ''"></Input>
                <span class="tip" v-if="isTipShow">{{ email_tip }}</span>
              </FormItem>
              <FormItem
                prop="validate_code"
                class="validate_code required"
                label="验证码:"
              >
                <Input v-model="form.captcha"></Input>
                <Button
                  type="ghost"
                  @click="getCaptcha"
                  v-if="!!isSentcode && f_type === 1"
                  >获取验证码</Button
                >
                <Button type="ghost" class="wait-btn" v-if="!isSentcode"
                  >{{ count }}秒后重发</Button
                >
                <Button
                  type="ghost"
                  id="getCaptcha"
                  @click="getCaptcha_e"
                  v-if="!!isSentcode && f_type === 2"
                  >获取验证码</Button
                >
                <span class="tip" v-if="isTipShow">{{ captcha_tip }}</span>
              </FormItem>
              <FormItem class="re-btn">
                <span
                  class="tip"
                  v-if="!!errMsg && !isTipShow"
                  style="display: block;"
                  >{{ errMsg }}</span
                >
                <Button @click="handleSubmit" type="success" v-if="f_type === 1"
                  >下一步</Button
                >
                <Button @click="handleSubmit" type="success" v-if="f_type === 2"
                  >下一步</Button
                >
                <router-link to="/register/opt">
                  <Button>上一步</Button>
                </router-link>
              </FormItem>
            </Form>
            <!--<div id="captcha-box" style=""></div>-->
          </div>
        </div>
      </div>
    </div>
    <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
      <Card :bordered="false" class="findpassword">
        <p slot="title">
          <router-link to="/register/opt">
            <Icon type="ios-arrow-thin-left" size="24"></Icon>
          </router-link>
          找回密码
        </p>
        <div class="re_tip" v-if="!!errMsg">
          <p>
            <span
              ><img src="../../images/register/icon_tips.png" alt="" />
            </span>
            {{ errMsg }}
          </p>
        </div>
        <div class="form-con">
          <Form
            ref="nameForm"
            :model="form"
            class="findpasswordForm"
            :label-width="60"
          >
            <FormItem
              prop="mobile"
              label="手机号"
              class="mobileItem"
              v-if="f_type == 1"
            >
              <div class="area_code_btn" @click="showpannel">
                +{{ form.area_code }}
                <Icon type="chevron-down" size="10"></Icon>
              </div>
              <Input v-model="form.mobile"></Input>
            </FormItem>
            <FormItem prop="email" label="邮箱" v-if="f_type == 2">
              <Input v-model="form.email" @on-change="errMsg = ''"></Input>
            </FormItem>
            <FormItem prop="validate_code" class="validate_code" label="验证码">
              <Input v-model="form.captcha"></Input>
              <Button
                type="ghost"
                @click="getCaptcha"
                v-if="!!isSentcode && f_type === 1"
                >获取验证码</Button
              >
              <Button type="ghost" class="wait-btn" v-if="!isSentcode"
                >{{ count }}秒后重发</Button
              >
              <Button
                type="ghost"
                id="getCaptcha"
                @click="getCaptcha_e"
                v-if="!!isSentcode && f_type === 2"
                >获取验证码</Button
              >
            </FormItem>
            <!--<FormItem class="captcha-item">
                            <div class="cH">
                                <div id="captcha-box"></div>
                                <div v-if="addYz" class="addYz">正在加载验证码....</div>
                            </div>
                        </FormItem>-->
            <FormItem class="re-btn">
              <Button @click="handleSubmit" type="success" long>下一步</Button>
            </FormItem>
          </Form>
          <!--<div id="captcha-box" style=""></div>-->
        </div>
      </Card>
    </div>
    <div class="area-code" v-if="!!showCodep">
      <div class="mask" @click="showCodep = false"></div>
      <div class="code-inner">
        <div class="top">
          国家/地区
          <span>代码</span>
        </div>
        <p class="used">常用</p>
        <div class="used-list">
          <Button @click="getAreaCode(86)">中国<br />+86</Button>
          <Button @click="getAreaCode(852)">香港<br />+852</Button>
          <Button @click="getAreaCode(886)">台湾<br />+886</Button>
          <Button @click="getAreaCode(853)">澳门<br />+853</Button>
          <Button @click="getAreaCode(1)">美加<br />+1</Button>
          <Button @click="getAreaCode(44)">英国<br />+44</Button>
          <Button @click="getAreaCode(33)">法国<br />+33</Button>
          <Button @click="getAreaCode(49)">德国<br />+49</Button>
          <Button @click="getAreaCode(61)">澳洲<br />+61</Button>
          <Button @click="getAreaCode(82)">韩国<br />+82</Button>
          <Button @click="getAreaCode(65)">新加坡<br />+65</Button>
          <Button @click="getAreaCode(81)">日本<br />+81</Button>
        </div>
        <div class="detail-list">
          <p>详细列表</p>
          <Menu active-name="86" @on-select="getAreaCode">
            <MenuGroup
              :title="code.letter"
              v-for="code in code_group"
              :key="code.index"
            >
              <MenuItem
                :name="list.area_code"
                v-for="list in code.data"
                :key="list.index"
              >
                {{ list.name }}
                <span>+{{ list.area_code }}</span>
              </MenuItem>
            </MenuGroup>
          </Menu>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import area from "../area_code";
import jigsaws from "@/libs/jigsaw.js";
import initGeetest from "@/libs/gt.js";
export default {
  name: "findpassword",
  data() {
    return {
      form: {
        mobile: "", //test
        area_code: 86,
        email: "",
        captcha: "",
      },
      access_token: this.$store.state.user.access_token,
      errMsg: "",
      isPc: true,
      isSentcode: true,
      count: 60,
      showCodep: false,
      code_group: [],
      isValidate: false,
      addYz: true,
      geetest_challenge: "",
      geetest_validate: "",
      geetest_seccode: "",
      isnext: false,
      area_code_use: [],
      area_code_abcdef: [],
      area_code_ghij: [],
      area_code_klmn: [],
      area_code_opqistuvw: [],
      area_code_xyz: [],
      mobile_tip: "",
      captcha_tip: "",
      email_tip: "",
      visible: false,
      isTipShow: true,
      f_type: 1,
    };
  },
  mounted() {
    this.$store.commit("isPC");
    this.isPc = this.$store.state.register.ispc;
    if (!!Cookies.get("area_code")) {
      this.form.area_code = Cookies.get("area_code");
    }
    this.f_type = parseInt(window.location.href.split("/").pop());
    if (!!this.isPc) {
      //window.parent.postMessage({register_type:'register'},'*');
      var requireds = document.getElementsByClassName("required");
      for (var i = 0; i < requireds.length; i++) {
        var l = requireds[i].getElementsByClassName("ivu-form-item-label")[0];
        l.innerHTML =
          '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
      }
      this.area_code_use = area.area_code_use;
      this.area_code_abcdef = area.area_code_abcdef;
      this.area_code_ghij = area.area_code_ghij;
      this.area_code_klmn = area.area_code_klmn;
      this.area_code_opqistuvw = area.area_code_opqistuvw;
      this.area_code_xyz = area.area_code_xyz;

      if (window.innerWidth < 760) {
        this.isTipShow = false;
      }
    } else {
    }

    this.code_group = area.area_code_group;

    if (this.f_type === 2) {
      //this.geetest();
    }
  },
  methods: {
    getCaptcha() {
      if (!this.form.mobile || !this.form.area_code) {
        this.errMsg = "请输入手机号码";
        this.mobile_tip = "请输入手机号码";
        return false;
      }
      this.show_captcha("m");
      return false;
      var dataIn = {};
      dataIn.mobile = this.form.mobile;
      dataIn.area_code = parseInt(this.form.area_code);

      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/sms/send/recovery_password",
          method: "post",
          data: dataIn,
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            _this.errMsg = "";
            _this.mobile_tip = "";
            _this.captcha_tip = "";
            _this.countdown();
            _this.$Message.info(res.data.data.message);
          } else {
            _this.errMsg = res.msg;
            if (res.msg == "验证码错误") {
              _this.captcha_tip = res.msg;
            } else {
              _this.mobile_tip = res.msg;
            }
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
          _this.captcha_tip = err.msg;
        });
    },
    getCaptcha_e(captchaObj) {
      if (!this.form.email) {
        this.errMsg = "请输入邮箱地址";
        this.email_tip = "请输入邮箱地址";
        return;
      } else if (!!this.form.email) {
        var reg = /^([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+\.[a-zA-Z]{2,5}$/;
        if (!reg.test(this.form.email)) {
          this.errMsg = "请输入正确邮箱地址";
          this.email_tip = "请输入正确邮箱地址";
          return false;
        }
      }
      if (!this.isValidate) {
        //captchaObj.verify();
        this.show_captcha("e");
      }
    },
    captchaAjax(obj) {
      if (!obj.ticket || !obj.ticket) {
        this.errMsg = "需要滑动验证通过后才能发送短信验证码";
        return false;
      }
      var dataIn = {
        email: this.form.email,
        /*geetest_challenge: this.geetest_challenge,
                    geetest_validate: this.geetest_validate,
                    geetest_seccode: this.geetest_seccode*/
        ticket: obj.ticket,
        randstr: obj.randstr,
        platform: !!this.isPc ? 1 : 2,
      };
      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/recovery_password/code",
          method: "post",
          data: dataIn,
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            _this.errMsg = "";
            _this.captcha_tip = "";
            _this.email_tip = "";
            _this.countdown();
            //_this.$Message.info(res.data.data.message);
            _this.$Message.success({
              content: "验证码已发送到邮箱，请查收!",
              top: "50%",
              duration: 3,
            });
          } else {
            _this.errMsg = res.msg;
            if (res.msg == "验证码错误") {
              _this.captcha_tip = res.msg;
            } else {
              _this.email_tip = res.msg;
            }
            //_this.geetest();
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
          _this.captcha_tip = err.msg;
          //_this.geetest();
        });
    },
    captchaAjax_m(obj) {
      if (!obj.ticket || !obj.ticket) {
        this.errMsg = "需要滑动验证通过后才能发送短信验证码";
        return false;
      }
      var dataIn = {};
      dataIn.mobile = this.form.mobile;
      dataIn.area_code = parseInt(this.form.area_code);
      dataIn.ticket = obj.ticket;
      dataIn.randstr = obj.randstr;
      dataIn.platform = !!this.isPc ? 1 : 2;

      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/sms/send/recovery_password",
          method: "post",
          data: dataIn,
        })
        .then(function(res) {
          console.log(res);
          if (res.data.msg === "success") {
            _this.errMsg = "";
            _this.mobile_tip = "";
            _this.captcha_tip = "";
            _this.countdown();
            _this.$Message.info(res.data.data.message);
          } else {
            _this.errMsg = res.msg;
            if (res.msg == "验证码错误") {
              _this.captcha_tip = res.msg;
            } else {
              _this.mobile_tip = res.msg;
            }
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
          _this.captcha_tip = err.msg;
        });
    },
    show_captcha(n) {
      var _this = this;
      TCaptchaModify();
      var captcha = new TencentCaptcha("2032354718", function(res) {
        if (n === "m") {
          _this.captchaAjax_m(res);
        } else if (n === "e") {
          _this.captchaAjax(res);
        }
      });
      captcha.show();
      function TCaptchaModify() {
        if (!!_this.isPc) {
          var dom = document.getElementById("tcaptcha_transform");
          if (!!dom && !!dom.innerHTML) {
            dom.style.top = "-75px";
            dom.style.transform = "scale(0.6,0.6)";
          } else {
            setTimeout(function() {
              TCaptchaModify();
            }, 600);
          }
        }
      }
    },
    geetest(e) {
      this.addYz = true;
      let _this = this;
      this.isValidate = false;
      _this.geetest_challenge = "";
      _this.geetest_validate = "";
      _this.geetest_seccode = "";
      util
        .ajax({
          url: "api/v1/auth/geetest/register?t=" + new Date().getTime(), // 加随机数防止缓存
          method: "get",
          dataType: "json",
        })
        .then(function(data) {
          //this.xwyz = data;
          let removeObj = document.getElementById("captcha-box"); // 获取dom元素
          if (removeObj != "underfined") {
            removeObj.innerHTML = ""; // 因为个人当中直接在页面中切换短信或者密码验证，先移除一个，不然会一直 添加
          }
          // 调用 initGeetest 进行初始化
          // 参数1：配置参数
          // 参数2：回调，回调的第一个参数验证码对象，之后可以使用它调用相应的接口
          var product = "float";
          if (!!_this.isPc) {
            product = "custom";
          }
          initGeetest(
            {
              // 以下 4 个配置参数为必须，不能缺少
              gt: data.data.data.gt,
              challenge: data.data.data.challenge,
              offline: !data.data.data.success, // 表示用户后台检测极验服务器是否宕机
              new_captcha: data.data.data.new_captcha, // 用于宕机时表示是新验证码的宕机
              product: "bind", // 产品形式，包括：float，popup
              width: "300px",
              https: true,
              next_width: "190px",
              // 更多配置参数说明请参见：http://docs.geetest.com/install/client/web-front/
            },
            function(captchaObj) {
              captchaObj
                .onReady(function() {
                  //验证码ready之后才能调用verify方法显示验证码
                })
                .onSuccess(function() {
                  //your code
                })
                .onError(function(err) {
                  // 出错啦，可以提醒用户稍后进行重试
                  // error 包含error_code、msg
                  console.log(err);
                  alert("页面出错了，页面将刷新，请重新登录！");
                  window.location.href = window.location.href;
                })
                .onClose(function() {
                  // 用户把验证关闭了，这时你可以提示用户需要把验证通过后才能进行后续流程
                  _this.errMsg = "需要把验证通过后才能获取验证码";
                  _this.captcha_tip = "需要把验证通过后才能获取验证码";
                });
              // 按钮提交事件
              /* var getCaptcha = document.getElementById('getCaptcha');
                        getCaptcha.onclick = function(){
                            _this.getCaptcha_e(captchaObj);
                        }*/
              //captchaObj.reset()    // 在切换登录的时候重置，不需要切换的忽略
              //captchaObj.appendTo('#captcha-box');     // 插入验证
              _this.addYz = false;
              captchaObj.onSuccess(() => {
                // 第一次验证成功，极验自带的回调
                _this.isValidate = true;
                _this.errMsg = "";
                _this.captcha_tip = "";
                let result = captchaObj.getValidate();
                _this.dis = false;
                _this.geetest_challenge = result.geetest_challenge; //将第一次验证获取的三个参数保存起来，方便二次验证
                _this.geetest_validate = result.geetest_validate;
                _this.geetest_seccode = result.geetest_seccode;
                _this.captchaAjax();
              });
            }
          );
        });
    },
    handleSubmit() {
      if (this.f_type === 1) {
        if (!this.form.mobile) {
          this.errMsg = "手机号码不能为空";
          this.mobile_tip = "手机号码不能为空";
          return;
        }
      } else {
        if (!this.form.email) {
          this.errMsg = "邮箱地址不能为空";
          this.email_tip = "邮箱地址不能为空";
          return;
        }
      }
      if (!this.form.captcha) {
        this.errMsg = "请输入正确的验证码";
        this.captcha_tip = "请输入正确的验证码";
        return;
      }
      this.errMsg = "";
      this.mobile_tip = "";
      this.email_tip = "";
      this.captcha_tip = "";

      var dataIn = {};
      var url = "";
      if (this.f_type === 1) {
        dataIn.mobile = this.form.mobile;
        dataIn.area_code = this.form.area_code;
        dataIn.type = "recovery_password";
        dataIn.captcha = this.form.captcha;
        url = "/api/v1/auth/verify/sms";
      } else {
        dataIn.email = this.form.email;
        dataIn.code = this.form.captcha;
        url = "/api/v1/auth/recovery_password/verify_code";
      }
      var _this = this;
      util
        .ajax({
          url: url,
          method: "post",
          data: dataIn,
        })
        .then(function(res) {
          if (res.data.msg === "success") {
            if (!res.data.data.exist && _this.f_type === 1) {
              _this.errMsg = "手机号错误";
              _this.mobile_tip = "手机号错误";
              return;
            } else {
              _this.form.token = res.data.data.token;
              _this.$store.commit("setPasswordData", _this.form);
              if (_this.f_type === 1) {
                _this.$router.push({
                  name: "findpassword2",
                });
              } else {
                _this.$router.push(
                  "/register/findpassword2?" + _this.form.token
                );
              }
            }
          } else {
            _this.errMsg = res.msg;
            _this.captcha_tip = res.msg;
            _this.form.captcha = "";
            //_this.geetest();
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
          _this.captcha_tip = err.msg;
          _this.form.captcha = "";
          //_this.geetest();
        });
    },
    countdown() {
      this.isSentcode = false;
      var _this = this;
      clearInterval(timer);
      var timer = setInterval(function() {
        if (_this.count === 1) {
          clearInterval(timer);
          _this.isSentcode = true;
        } else {
          _this.count--;
        }
      }, 1000);
    },
    showpannel() {
      this.showCodep = true;
    },
    getAreaCode(n) {
      this.form.area_code = n;
      this.showCodep = false;
      this.visible = false;
      Cookies.set("area_code", n);
    },
    isWeiXin() {
      var ua = window.navigator.userAgent.toLowerCase();
      var iswx = false;
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        if (ua.match(/wxwork/i) == "wxwork") {
          // 企业微信号
          iswx = false;
        } else {
          iswx = true;
        }
      } else {
        iswx = false;
      }
      return iswx;
    },
  },
};
</script>

<style></style>
