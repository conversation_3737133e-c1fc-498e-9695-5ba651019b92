.register{
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  background-color: #fafafa;
  overflow-y: auto;
  &-con{
    position: relative;
    height: 767px;
    &-header{
      font-size: 16px;
      font-weight: 300;
      text-align: center;
      padding: 30px 0;
    }
    .r{
      position: absolute;
      left: 0;
      top: 0px;
      right:0;
      z-index: 10;
      background: #fff;
      /*width: 100%;
      height: 420px;*/
      .r-c{
        max-width: 760px;
        width: 100%;
        margin: 20px auto;
        .ivu-btn{
          width: 96px;
          height: 24px;
          line-height: 24px;
          padding: 0;
          border-radius: 0;

          &.ivu-btn-ghost{
           /* background: #C2C2C2;*/
            color: #fff;
          }
        }
        .r-tab{
          border-bottom:1px solid #CDCDCD;
          margin-top: 20px;
          /*padding: 0 185px;
          display: flex;*/
          text-align: center;

          .tab{
            /*width: 130px;*/
            display: inline-block;
            padding: 0 35px;
            font-size: 12px;
            line-height: 48px;

            span{
              width: 16px;
              height: 16px;
              display: inline-block;
              background: #CDCDCD;
              border-radius: 50%;
              color: #fff;
              margin-right: 10px;
              vertical-align: middle;
              line-height: 16px;
            }
          }
          .tab.on{
            color: #52A5E0;
            span{
              background: #52A5E0;
            }
          }
        }
        .r-form{
          margin-top: 10px;
          .ivu-form-item{
            border-bottom:1px dotted #CDCDCD;
            padding: 6px 0;
            margin: 0;
            &:last-child{
              border: none;
            }
            &.validate_code{
              .ivu-form-item-content{
                position: relative;
                .ivu-input{
                  width: 150px;
                }
                .ivu-btn{
                  position: absolute;
                  left: 160px;
                  top: 5px;
                  height: 24px;
                  line-height: 24px;
                  border: none;
                  width: 70px;
                  padding: 0;
                  color: #fff;
                  background: #51a5df;
                  &.wait-btn{
                    background: #bfbfbf;
                  }
                }
              }
            }
            &.sub_btn{
              .ivu-form-item-content{
                margin-left: 68px !important;

              }
            }
            .ivu-form-item-label{
              font-weight: bold;
            }
            .ivu-input-wrapper{
              width: 273px;

              .ivu-input{
                width: 230px;
                height: 24px;
                padding: 2px 4px;
                line-height: 17px;
                border: 1px solid;
                border-color: #848484 #E0E0E0 #E0E0E0 #848484;
                background: #FFF url(https://forum.chasedream.com/static/image/common/px.png) repeat-x 0 0;
                font: 12px/1.5 Tahoma,'Microsoft Yahei','Simsun';
                color: #444;
                border-radius: 0;
              }
            }

            &.er{
              .ivu-input{
                border-color: #F66 #FFBDB9 #FFBDB9 #F66;
                background-color: #FDF4F4;
                background-image: url(https://forum.chasedream.com/static/image/common/px_e.png);
              }
              .tip{
                padding-left: 10px;
                color: red;
                font-weight: 700;
                font-family: Tahoma,'Microsoft Yahei','Simsun';
              }
            }
          }

        }
        .r-b{
          text-align: center;
          margin-top: 20px;
          font-size: 12px;
          color: #444;
          font-weight: bold;
          line-height: 22px;

          .title{
            margin-bottom: 10px;
          }
          .we-logo{
            margin-bottom: 50px;
            img{
              width: 40px;
              height: 40px;
              vertical-align: top;
              margin-bottom: 5px;
            }
            span{
              display: block;
            }
          }
          .wc-code{
            text-align: center;
            margin-bottom: 10px;
            img{
              width: 110px;
              height: 110px;
              vertical-align: top;
            }
            .ivu-spin{
              left: 0;
              top:0;
            }
          }
          .desc{
            color: #999;
            margin-bottom: 20px;
          }
          .btns{
            .ivu-btn{
              margin:0 10px;
            }
          }
          &.r-user{
            .we-logo{
              margin-bottom: 10px;
              img{
                border-radius: 50%;
              }
            }
            .desc{
              text-align: left;
              padding: 0 25px;
              width: 500px;
              margin:0 auto;
              color: #616161;
              .item{
                margin-bottom: 10px;
              }
              h3{
                display: inline-block;
                margin-right: 10px;
                color: #444;
              }
              p{
                /*display: inline-block;*/
                color: #999;
              }
              button{
                display: block;
              }
            }
          }
        }
      }
      &.scan{
        .r-c{
          width: 300px;
        }
      }
    }

    .form-con{
      padding: 10px 0 0;

      .validate_code {
        .ivu-input-group{
          width: 60%;
        }
        .codeImg{
          position: absolute;
          right: 0;
          top: 0;
        }
      }
      .ivu-select-selection{
        width:80px;
      }
      .ivu-poptip{
        width: 100%;
        .ivu-poptip-rel{
          width: 100%;
        }
      }

      .tip_info{
        margin-top: -20px;
        background: url("../../images/register/tip_unbound.png") no-repeat 0 -20px;
        background-size: 100%;
        padding-top: 20px;
        h3{
          text-align: center;
          font-size: 20px;
          color: #333;
        }
        .account{
          text-align: center;
        }
        .desc{
          padding:0 30px;
          margin-top: 180px;
          span{
            font-size: 14px;
            color: #333;
          }
          .item{
            padding-left: 8px;
            position: relative;
            p{
              font-size: 15px;
              line-height: 1.5;
              margin-bottom: 5px;
            }

            .ivu-btn{
              width: 100px;
              height:26px;
              line-height: 26px;
              padding: 0;
              -webkit-border-radius:0;
              -moz-border-radius:0;
              border-radius:0;
              margin-bottom: 15px;

              span{
                color: #fff;
                font-size: 12px;
              }
            }
            .ivu-btn.ivu-btn-ghost{
              span{
                color: #999;
              }
            }
          }
          .item:before{
            content: '•';
            /*margin-left: -8px;*/
            color: #ccc;
            position: absolute;
            left: 0px;
          }
        }
      }
      .tip_info.t_tip{
        background: none;
        margin-top: 0;
        .account{
          padding-top: 24px;
          img{
            width: 60px;
            height: 60px;
            vertical-align: top;
            margin-bottom: 10px;
          }
          span{
            display: block;
            font-size: 14px;
            color: #000;
            margin-bottom: 25px;
          }
          p{
            a{
              font-size: 13px;
              color: #409cdf;
            }
          }
        }
        .desc{
          margin-top: 30px;
          .item{
            h3{
              text-align: left;
              font-size: 15px;
              font-weight: normal;
              color: #333;
            }
          }
        }
      }
    }
    .modify{
      .re_tip{
        margin-bottom: 8px !important;
      }
      .mobile-num{
        font-size: 13px;
        color: #808080;
        padding: 0 15px;
      }
    }
    .login-tip{
      font-size: 10px;
      text-align: center;
      color: #c3c3c3;
    }
    .ivu-spin{
      position: absolute;
      top: 3px;
      left:15px;
    }
    .ivu-alert{
      height: 36px;
      line-height: 20px;
    }
    .r-form.opt{
      width: 260px;
      text-align: center;
      margin: 0 auto;
      .ivu-radio-group-button{
        width: 100%;
        .ivu-radio-wrapper:after,.ivu-radio-wrapper:before{
          height: 0;
        }
        .ivu-radio-wrapper{
          width: 50%;
          height: 30px;
          line-height: 30px;
          position: relative;
          border-radius: 0;
          margin-bottom: 10px;
          border: none;
          div{
            height: 60px;
            width: 60px;
            text-align: center;
            border: 1px solid #E0E0E0;
            margin: 0 auto;
            margin-bottom: 10px;

            .ivu-icon{
              width: 24px;
              height: 24px;
              background-size: 100%;
              background-repeat: no-repeat;
              vertical-align: middle;
              margin-top: 15px;
              &.ivu-icon-icon_mobile{
                background-image: url("../../images/register/icon_mobile.png");
              }
              &.ivu-icon-icon_email{
                background-image: url("../../images/register/icon_email.png");
              }
            }
            &:hover{
              border-color: #53A6E0;
              .ivu-icon{
                &.ivu-icon-icon_mobile{
                  background-image: url("../../images/register/icon_mobile_checked.png");
                }
                &.ivu-icon-icon_email{
                  background-image: url("../../images/register/icon_email_checked.png");
                }
              }
            }
          }

          &.ivu-radio-wrapper-checked {
            .ivu-icon{
              &.ivu-icon-icon_mobile{
                background-image: url("../../images/register/icon_mobile_checked.png");
              }
              &.ivu-icon-icon_email{
                background-image: url("../../images/register/icon_email_checked.png");
              }
            }
          }
        }
      }
    }
    .logoff{
      .form-con{
        width: 300px;
        margin: 20px auto;
        text-align: center;

        .logoff_info{
          h3{

          }
          img{
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 15px 0 20px;
          }
        }
        .logoff_dec{
          text-align: left;
          margin-bottom: 0;
          p{
            font-size: 12px;
            line-height: 18px;
          }
        }
        .agree{
          text-align: left;
          margin-bottom: 10px;
        }
        .ivu-btn{
          width: 96px;
          height: 24px;
          padding: 0;
          border-radius: 0;
          font-size: 12px;
        }
        .logoff_success{
          p{
            font-size: 12px;
            line-height: 18px;
            margin: 20px 0 30px;
          }
          h3{
            margin-top: 12px;
          }
        }
      }
    }
    .r .r-c .r-form .ivu-form-item{
      &.logoff_dec,&.agree{
        border:none;
      }
    }
    .security{
      .ivu-tabs-bar{
        margin-bottom: 0;
        border-bottom: 1px dashed #dddee1;
        .ivu-tabs-nav{
          margin-bottom: 10px;
          .ivu-tabs-tab{
            padding: 3px 4px;
            font-size: 12px;
            margin: 0 0 0 8px;
            line-height: 12px;
            &.ivu-tabs-tab-active{
              background: #3AA1EC;
              color: #fff;
            }
          }
          .ivu-tabs-ink-bar{
            display: none;
          }
        }
      }
    }
  }
  .wc_info{
    background: #fff;
    vertical-align: middle;
    padding: 20px;
    span{
      line-height: 24px;
      display: inline-block;
      margin-right: 50px;
      .ivu-icon-wc-icon{
        width: 24px;
        height: 24px;
        background: url("../../images/register/wc_icon.png");
        background-size: 100% 100%;
        vertical-align: bottom;
        margin-right: 5px;
      }
    }
    .ivu-input-wrapper{
      display: inline-block;
      width: 300px;
      .ivu-input{
        width: 230px;
        height: 24px;
        padding: 2px 4px;
        line-height: 17px;
        border: 1px solid;
        border-color: #848484 #E0E0E0 #E0E0E0 #848484;
        background: #FFF url(https://forum.chasedream.com/static/image/common/px.png) repeat-x 0 0;
        font: 12px/1.5 Tahoma, 'Microsoft Yahei', 'Simsun';
        color: #444;
        border-radius: 0;
      }
    }
    .wc_btns{
      padding: 20px 110px;
      .ivu-btn{
        width: 96px;
        height: 24px;
        line-height: 24px;
        padding: 0;
        font-size: 12px;
        text-align: center;
        border-radius: 0;
        margin-right: 10px;
        span{
          margin: 0;
        }
        &.ivu-btn-ghost{
          background: #DADADA;
          border: 1px solid #CCC;
          color: #EC5D63;
        }
      }
    }
  }
  .mobile_r_c{
    width:100%;
    position: static;
    transform:translateY(0);

    .ivu-card{
      border-radius:0;
      box-shadow: none;

      .ivu-card-head{
        text-align: center;
        position: relative;

        .ivu-icon{
          position: absolute;
          left:10px;
          color: #808080;
        }
        span{
          position: absolute;
          right: 10px;
          color: #808080;
        }
      }

      .ivu-card-body{
        padding:0;
        padding-top: 10px;
        background-color: #fafafa;

        .ivu-form-item{
          background-color: #fff;
          margin-bottom: 1px;
          height: 55px;
          padding: 0 10px;
        }
        .ivu-form .ivu-form-item-label{
          line-height:35px;

        }
        .ivu-form-item-content{
          padding-top: 12px;
        }

        .mobileItem{
          .ivu-form-item-content{
            padding-left: 50px;
            position: relative;

            .ivu-select{
              position: absolute;
              left: 0;

            }
          }
        }
        .validate_code{
          .ivu-form-item-content {
            padding-right: 100px;
            position: relative;

            .ivu-btn {
              position: absolute;
              right: 0;
              bottom:0;
            }
          }
        }
        .re-btn{
          margin-top: 50px;
          background: none;
          .ivu-form-item-content{
            margin-left: 0 !important;
            padding: 0;

            button{
              height: 43px;
              border-radius: 0;
            }
          }
        }
        .re_tip{
          font-size: 12px;
          line-height: 17px;
          color: #ed554c;
          margin: 0 15px;
          background: #f6ccca;
          padding: 10px;

          img{
            width: 12px;
            height: 12px;
          }
          a{
            color: #ed554c;
            text-decoration: underline;
          }
        }
        .nicknameForm .ivu-form-item{
          padding: 0;
          background: none;
        }
      }
    }
    .binding_info{
      padding: 100px 10px;
        .title{
          font-size: 15px;
          color: #4c4c4c;
          position: relative;
          height: 15px;

          .line{
            height: 1px;
            margin: 0 81px;
            background-color: #d9d9d9;
          }
          p{
            position: absolute;
            left: 50%;
            top:-10px;
            width: 140px;
            margin-left: -70px;
            background-color: #fff;
          }
        }
      .we-logo{
        text-align: center;
        margin: 40px 0 20px;
        img{
          width: 40px;
          height: 40px;
          vertical-align: top;
        }
        span{
          display: block;
          font-size: 14px;
          color: #4d4d4d;
          margin-top: 10px;
        }
      }
      p{
        text-align: center;
      }
      .desc{
        font-size: 12px;
        line-height: 18px;
        color: #808080;
      }
    }
    &.binding-con{
      .ivu-card-body {
        background-color: #fff;
      }
    }
    .opt,.security,.wechat{
      width: 100%;
      .ivu-radio-group-button{
        width: 100%;
        .ivu-radio-wrapper{
          width: 100%;
          height: 50px;
          line-height: 50px;
          position: relative;
          border-radius: 0;
          border: none;
          .ivu-icon{
            width: 16px;
            height: 16px;
            background-size: 100%;
            background-repeat: no-repeat;
            vertical-align: middle;
            &.ivu-icon-icon_mobile{
              background-image: url("../../images/register/icon_mobile.png");
            }
            &.ivu-icon-icon_email{
              background-image: url("../../images/register/icon_email.png");
            }
            &.ivu-icon-checked{
              background-image: url("../../images/register/choice.png");
              position: absolute;
              right: 20px;
              top: 15px;
            }
            &.ivu-icon-wc-icon{
              width: 24px;
              height: 24px;
              background: url("../../images/register/wc_icon.png");
              background-size: 100% 100%;
            }
          }
          span.r_t{
            font-size: 12px;
            float: right;
            color: #808080;
            .ivu-icon{
              width:21px;
              height: 21px;
              text-align: right;
            }
            span{
              color: #3AA2EC;
            }
          }
          &.ivu-radio-wrapper-checked {
            border-color: #dddee1;
            color: #495060;
            box-shadow: none;
          }
        }
      }
    }
  }
  .ivu-btn-long{
  height: 43px;
  font-size: 16px;
  margin-bottom: 15px;
  border-radius: 0;
  }
}
.area-code{
position: fixed;
top:140px;
left: 0;
bottom: 0;
background-color: #fff;

.mask{
position: fixed;
width: 100%;
height: 100%;
top: 0;
bottom: 0;
left: 0;
background: #000;
opacity: 0.5;
z-index: -1;
}
.code-inner{
z-index: 999;
background: #fff;
padding:10px 15px;
}
.top{
margin-bottom: 10px;
font-size: 16px;
color: #4D4D4D;
span{
  float: right;
}
}
.used{
font-size: 15px;
color: #808080;
margin-bottom: 7px;
}
.used-list{
display: flex;
flex-wrap: wrap;
justify-content:space-between;
.ivu-btn{
  width: 83px;
  margin-bottom: 5px;

}
}

.detail-list p{
font-size: 15px;
color: #808080;
margin-top: 10px;
}

.ivu-menu{
width: 100% !important;
height:260px;
overflow-y: scroll;
}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item{
border-bottom: 1px solid #dbdbdb;
font-size: 14px;
color: #4D4D4D;
padding: 10px 0;
span{
  float: right;
  font-size: 12px;
}
}
.ivu-menu-vertical .ivu-menu-item-group-title{
font-size: 12px;
color: #3AA1EC;
padding-left: 0px;
}
}
.register.binding,.register.binding .mobile_r_c .ivu-card .ivu-card-body{
background-color: #fff;
}
.mobile_r_c{
.ivu-form{
.ivu-form-item-label{
  font-size: 16px;
}
.area_code_btn{
  position: absolute;
  left: 0;
  font-size: 14px;
  .ivu-icon{
    margin-left: 5px;
  }
}
.ivu-input{
  border:none;
  border-radius: 0;
  box-shadow: none;
  font-size: 16px;

}
.validate_code{
  margin-bottom: 10px !important;
  button{
    width: 100px;
    height:35px;
    border:none;
    font-size: 13px;
    -webkit-border-radius:0;
    -moz-border-radius:0;
    border-radius:0;
    background-color: #3aa1ec;
    color: #fff;
  }
  button.wait-btn{
    border:1px solid #d9d9d9;
    background-color: #fff;
    color: #bfbfbf;
  }
}
}
.nickname {
.ivu-card-body{
  padding-top: 0;
  .re_tip{
    margin-bottom: 10px !important;
  }
  h3{
    font-size: 15px;
    color: #4d4d4d;
    font-weight: normal;
    padding: 5px 15px;
  }
  .ivu-form-item-content{
    padding-top: 0 !important;
  }
  .nicknameForm {
    .ivu-form-item{
      height: auto !important;
      .tip{
        font-size: 13px;
        color: #999;
        padding: 0 15px;
      }
      .cH{
        padding: 15px;

        .geetest_holder.geetest_wind{
          width: 100% !important;
        }
      }
      .ivu-checkbox-wrapper{
        font-size: 14px;
        padding: 0 15px;
      }
    }
    .ivu-form-item.re-btn{
      padding: 0 15px;
    }
    .ivu-input{
      height: 55px;
      font-size: 16px;
      padding: 5px 15px;
    }
  }
}
}

}

#captcha{
padding-bottom: 5px;
}
.l_err_msg{
color: #ed3f14;
font-size: 12px;
margin-bottom: 10px;
}
.agreement-pop{
position: fixed;
left: 0;
top: 0;
bottom: 0;
width: 100%;
height: 100%;
overflow-y: scroll;
z-index: 999;
padding-bottom: 10px;
background: #fff;

.ivu-icon{
position: absolute;
left: 10px;
top: 10px;
color: #ddd;
}
.ivu-btn{
height: 43px;
font-size: 15px;
border-radius: 0;
}
}
.captcha-item .ivu-form-item-content{
margin-left: 0 !important;
padding-top: 6px !important;

.geetest_holder.geetest_wind{
width: 100% !important;
}
}
.pc_citys {
position: absolute;
left: 5px;
top: 2px;

.ivu-poptip-rel{
a{
  color: #333;
}
}
.ivu-poptip-popper{
width: 450px;

.ivu-poptip-body{
  height: 200px;
  overflow: auto;
  overflow-y: scroll;
}
.ivu-tabs-nav .ivu-tabs-tab{
  padding: 8px 4px;
}
.citys{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  span{
    width: 25%;
    display: inline-block;
    cursor:pointer;
    font-size: 12px;
    line-height: 24px;
    color: #444;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &.on{
      color: #2d8cf0;
    }
  }
}
}
}
.register-con .r .r-c .r-form .ivu-form-item .ivu-input-wrapper.ipt_m .ivu-input{
padding-left: 50px;
}
.register-con .r .r-c .r-form .ivu-form-item .tip{
color: red;
a{
color: #52A5E0;
text-decoration: underline;
}
}
.register-con{
&.tip_unbind{
height: 260px;
width: 300px;
background: #fff;
padding: 10px 0 0 50px;
box-sizing: border-box;

h3{
  margin-bottom: 15px;
}
span{
  font-size: 14px;
  line-height: 22px;
}
p{
  font-size: 14px;
  line-height: 22px;
}
.ivu-btn{
  width: 96px;
  height: 24px;
  line-height: 24px;
  padding: 0;
  border-radius: 0;
  margin: 4px 0;
}
}
}
.register-con .r.scan,.register-con .r.tip{
width: 430px;

.r-c{
margin: 15px auto;
width: 350px;

.r-b{
  margin-top: 0;
  .title{
    margin: 0;
  }
}
.ivu-btn.ivu-btn-ghost{
  background: #fff;
  color: #333;
  &:hover{
    border-color: #e9ebf0;
    background: #e9ebf0;
  }
}
}
}
.logoff_modal{
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal{
    width:300px !important;
    top: 0;
  }
  .ivu-modal-header{
    border-bottom:none;
    .ivu-modal-header-inner{
      text-align: center;
      font-size: 16px;
    }
  }
  .ivu-modal-body{
    font-size: 14px;
    p{
      text-align: center;
    }
  }
  .ivu-modal-footer{
    padding: 12px 15px;
    border-top: none;
  }
  .ivu-modal-footer .ivu-btn{
    width: 128px;
    height: 44px;
    border-radius: 0;
    font-size: 16px;
    box-shadow: none;
    &.ivu-btn-ghost{
      color: #F15454;
      background: #EBEBEB;
      border:none;
    }
    &.ivu-btn-long{
      width: 100%;
      margin: 10px 0 0;
    }
  }
}
.logoff_page{
  bottom: 0;
  top: 0;
  position: absolute;
  .logoff_info{
    height: 177px;
    margin-top: -20px;
    text-align: center;
    padding-top: 20px;

    img{
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }
    h3{
      font-size: 16px;
      color: #000;
      line-height: 30px;
      text-align: center;
      margin: 15px 0;
    }
  }
  .agree .ivu-form-item-content .ivu-checkbox-wrapper{
    font-size: 15px;
  }
}

.register .mobile_r_c .ivu-card .ivu-card-body .ivu-form-item.logoff_dec{
  padding: 60px 20px 20px;
  height: auto;
  margin-bottom: 0;
  p{
    font-size: 15px;
    line-height: 24px;
  }
}
.register .mobile_r_c .ivu-card .ivu-card-body .ivu-form-item.agree{
  margin-bottom: 0;
  padding-left: 20px;
}
.logoff_success{
  width: 300px;
  text-align: center;
  padding: 20px 15px;
  .ivu-modal{
    width: 300px !important;
    margin: auto !important;
  }
  img{
    width: 40px;
    height: 40px;
  }
  h3{
    text-align: center;
    font-size: 16px;
    line-height: 16px;
    height: 16px;
    color: #000;
    margin: 20px 0 15px;
  }
  p{
    text-align: left;
    font-size: 15px;
    line-height: 24px;
    color: #333;
  }
}
.findpassword{
  .findpasswordForm{
    p.tip{
      line-height: 32px;
      font-size: 12px;
      padding: 0 20px;
      color: #F15454;
      min-height: 10px;
    }
  }
}
#tcaptcha_transform{
  /*transform:scale(0.7,0.7);
  top: auto;*/
}


