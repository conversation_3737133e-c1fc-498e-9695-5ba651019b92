<style lang="less">
    @import 'register.less';
    @import '../login.less';
    @import '../../libs/jigsaw.css';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 420px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-b">
                        <div class="title">
                            <p>是否绑定微信登录</p>
                        </div>
                        <div class="we-logo">
                            <img src="../../images/register/icon_weixin.png" alt="">
                            <span>微信</span>
                        </div>
                        <p class="desc">绑定微信账号后，可以直接使用微信授权登录ChaseDream。<br>不用记密码，安全又方便。</p>
                        <Form>
                            <FormItem class="btns">
                                    <Button @click="binding" type="success">绑定</Button>
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c binding-con" v-if="!isPc" style="width: 100%;background: #fff;">
            <Card :bordered="false" style="background-color: #fff;">
                <p slot="title">
                    <router-link to="/register/wechat">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    绑定微信
                </p>
                <div class="form-con">
                    <div class="binding_info">
                        <div class="title">
                            <div class="line"></div>
                            <p>是否绑定微信登录</p>
                        </div>
                        <div class="we-logo">
                            <img src="../../images/register/icon_weixin.png" alt="">
                            <span>微信</span>
                        </div>
                        <p class="desc">绑定微信账号后，可以直接使用微信授权登录ChaseDream。<br>不用记密码，安全又方便。</p>
                    </div>
                    <Form>
                        <FormItem>
                            <Button @click="getqrcode" type="success" long>绑定</Button>
                        </FormItem>
                    </Form>
                    <Modal class="wcLogin" v-model="wcLogin" width="360" @on-cancel="cancel">
                        <div slot="header">
                            <h3 align="center">注册/登录</h3>
                            <p align="center">
                                首次登录需要完成两步操作，以后即可自动登录
                            </p>
                        </div>
                        <div class="steps">
                            <div class="item">
                                <p>第1步：先交二维码图片临时保存到手机相册</p>
                                <img :src="wcImg" alt="" width="100%">
                            </div>
                            <div class="item">
                                <p>第2步：在微信的"扫一扫"中打开刚保存的二维码图片</p>
                                <img src="../../images/register/login_wechat_scan.png" alt="" width="100%">
                            </div>
                        </div>
                        <div slot="footer">
                            <a href="weixin://scanqrcode">
                                <Button type="success" size="large" long>二维码存好了，打开微信扫一扫</Button>
                            </a>
                        </div>
                    </Modal>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'bind_tip',
        data () {
            return {
                access_token: this.$store.state.user.access_token,
                isPc: true,
                wcLogin: false,
                wcImg: '',
                timer: '',
                isWin: true
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            /*if(!!this.$store.state.register.openid){
                this.openid = this.$store.state.register.openid;
            }else {
                this.$router.push({
                    name: 'register'
                });
            }*/
            if(window.location.href.split('/').pop() === 'security'){
                this.isWin = false
            }
        },
        methods: {
            getqrcode (){
                if(!!this.timer){
                    clearInterval(this.timer);
                }

                this.wcLogin = true;
                var _this = this;

                util.ajax({
                    url: "api/v1/wechat/qrcode?t=" + (new Date()).getTime(), // 加随机数防止缓存
                    method: "get",
                    dataType: "json"
                }).then (function (data) {
                    //console.log(data);
                    if(data.data.msg === 'success'){
                        _this.wcImg = data.data.data.image;
                        var s = 0;
                        _this.timer = setInterval(function () {
                            s++;
                            if(s === 1000*120){
                                _this.wcLogin = false;
                                clearInterval(_this.timer);
                            }

                            util.ajax({
                                url: "api/v1/wechat/verify_ticket?type=binding&t=" + data.data.data.ticket,
                                method: "get",
                                dataType: "json"
                            }).then (function (data) {
                                console.log(data);
                                if(data.data.msg === 'success'){
                                    if(data.data.data.openid !== ''){
                                        clearInterval(_this.timer);
                                        if(data.data.data.uid > 0 && !data.data.data.binding){
                                            _this.$store.commit('setUnbindInfo',data.data.data);
                                            if(!!_this.isPc){
                                                //_this.$emit('on-stap');
                                            }else {
                                                _this.$router.push({
                                                    name: 'tip'
                                                });
                                            }
                                        }else if(!!data.data.data.binding) {
                                            //window.location.href = 'https://forum.chasedream.com/forum.php';

                                            _this.$router.push({
                                                name: 'wechat'
                                            });
                                        }
                                    }
                                }else{
                                    _this.errMsg = data.msg;
                                }
                            }).catch(function (err) {
                                console.log(err)
                                _this.errMsg = err.msg;
                            });
                        },1000);
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });

            },
            cancel (){
                clearInterval(this.timer);
            },
            binding (){
                this.$emit('on-stap');
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
