<style lang="less">
    @import 'register.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 420px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c" style="margin: 0">
                    <div class="r-form security" style="margin: 0">
                        <Tabs :value="nav_name" @on-click="change_page">
                            <TabPane label="更换手机号" name="change_mobile">
                                <change-mobile v-if="nav_name === 'change_mobile'"></change-mobile>
                            </TabPane>
                            <TabPane label="更换邮箱" name="change_email">
                                <change-email v-if="nav_name === 'change_email'"></change-email>
                            </TabPane>
                            <TabPane label="修改密码" name="change_password">
                                <change-password v-if="nav_name === 'change_password'"></change-password>
                            </TabPane>
                            <TabPane label="微信绑定" name="wechat">
                                <wechat v-if="nav_name === 'wechat'"></wechat>
                            </TabPane>
                            <TabPane label="账号注销" name="logoff">
                                <logoff v-if="nav_name === 'logoff'"></logoff>
                            </TabPane>
                        </Tabs>
                        <!--<div>
                            <change-mobile></change-mobile>
                            <change-email></change-email>
                            <change-password></change-password>
                            <wechat></wechat>
                            <logoff></logoff>
                        </div>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="">
                <p slot="title">
                    <a :href="setPage">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </a>
                    账户与安全
                </p>
                <div class="form-con security">
                    <RadioGroup v-model="type" type="button" size="large" @on-change="handleSubmit">
                        <Radio label="1">
                            <span>手机号</span>
                            <span class="r_t">
                                {{mobile}}
                                <Icon type="ios-arrow-right" size="20"></Icon>
                            </span>
                        </Radio>
                        <Radio label="2">
                            <span>密码</span>
                            <span class="r_t">
                                修改
                                <Icon type="ios-arrow-right" size="20"></Icon>
                            </span>
                        </Radio>
                        <Radio label="3">
                            <span>微信绑定</span>
                            <span class="r_t">
                                绑定、解绑
                                <Icon type="ios-arrow-right" size="20"></Icon>
                            </span>
                        </Radio>
                        <Radio label="4">
                            <span>账号注销</span>
                            <span class="r_t">
                                注销后账号将无法恢复，请谨慎操作
                                <Icon type="ios-arrow-right" size="20"></Icon>
                            </span>
                        </Radio>
                    </RadioGroup>
                    <!--<br>
                    <br>
                    <br>
                    <br>
                    <Button @click="handleSubmit" type="success" long>下一步</Button>-->
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    import change_mobile from './modify-mobile.vue';
    import change_email from './change-email.vue'
    import change_password from './change-password.vue';
    import wechat from './wechat.vue';
    import logoff from './logoff.vue'


    export default {
        name: 'security',
        components: {
            changeMobile: change_mobile,
            changeEmail: change_email,
            changePassword: change_password,
            wechat: wechat,
            logoff: logoff
        },
        data () {
            return {
                form: {
                    mobile: '', //test
                    area_code: 86,
                    captcha: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isPc: true,
                mobile: '',
                type: '0',
                setPage: '',
                nav_name: 'change_mobile'
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!this.isPc){
                //window.parent.postMessage({register_type:'register'},'*');
                var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }


            }else {
                this.getMobile();
            }
        },
        methods: {
            getMobile (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/change_mobile1',
                    method:'get',
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.mobile = res.data.data.mobile;
                        _this.setPage = 'https://forum.chasedream.com/home.php?mod=space&uid='+res.data.data.uid+'&do=profile&mycenter=1&type=setting&mobile=2';
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },
            change_page (e){
                console.log(e)
                this.nav_name = e;
            },
            handleSubmit (e) {
                if(parseInt(e) === 1){
                    this.$router.push('/register/modify-mobile');
                }else if(parseInt(e) === 2){
                    this.$router.push('/register/change-password');
                    window.location.href = 'https://forum.chasedream.com/home.php?mod=spacecp&ac=profile&op=password';
                }else if(parseInt(e) === 3){
                    this.$router.push('/register/wechat');
                }else if(parseInt(e) === 4){
                    this.$router.push('/register/logoff');
                }

            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
