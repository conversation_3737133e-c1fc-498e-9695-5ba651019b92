<style lang="less">
    @import 'register.less';
    @import '../login.less';
    @import '../../libs/jigsaw.css';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 300px;overflow: hidden;">
            <!--<iframe src="https://forum.chasedream.com/member.php?mod=register" frameborder="0" style="position: absolute;left: 0;top: 0;width: 100%;height: 767px;z-index: 1;"></iframe>-->
            <div class="r scan">
                <div class="r-c">
                    <!--<div class="r-tab">
                        <div class="tab"><span>1</span>手机注册</div>
                        <div class="tab on"><span>2</span>绑定微信（选填）</div>
                        <div class="tab"><span>3</span>论坛昵称</div>
                    </div>-->
                    <div class="r-b">
                        <div class="title">
                            <p>打开微信扫描二维码</p>
                        </div>
                        <div class="wc-code" style="position: relative;">
                            <img :src="wcImg" alt="">
                            <Spin fix v-if="spinShow">
                                <a href="javascript:;" @click="getqrcode" v-if="!isRefresh">
                                    <Icon type="ios-refresh-empty" size=30></Icon>
                                    <div>刷新</div>
                                </a>
                                <span v-if="!!isRefresh">
                                    <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
                                </span>
                            </Spin>
                        </div>
                        <p class="desc">
                            扫描后点击关注公众号，即可完成绑定
                            <br>
                            <strong style="font-weight: bold;color: #333;">
                                绑定微信账号后，可以直接使用微信授权登录ChaseDream。<br>
                                不用记密码，安全又方便。
                            </strong>
                        </p>
                        <Form>
                            <FormItem class="btns">
                                <Button @click="closeWin" type="primary" v-if="isWin">以后提醒</Button>
                                <Button @click="closeWin(1)" type="ghost" v-if="isWin">不再提醒</Button>
                                <Button @click="stap" type="ghost" v-if="!isWin">取消</Button>

                                <!--<a href="https://forum.chasedream.com/forum.php">
                                    <Button type="ghost">跳过</Button>
                                </a>-->
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false">
                <p slot="title">
                    <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    绑定微信
                    <span>登录</span>
                </p>
                <div class="form-con">
                    <div class="binding_info">
                        <div class="title">
                            <div class="line"></div>
                            <p>是否绑定微信登录</p>
                        </div>
                        <div class="we-logo">
                            <img src="../../images/register/icon_weixin.png" alt="">
                            <span>微信</span>
                        </div>
                        <p class="desc">绑定微信账号后，可以直接使用微信授权登录ChaseDream。<br>不用记密码，安全又方便。</p>
                    </div>
                    <Form>
                        <FormItem>
                            <Button @click="getqrcode" type="success" long>绑定</Button>
                            <a href="https://forum.chasedream.com/forum.php">
                                <Button type="ghost" long>跳过</Button>
                            </a>
                        </FormItem>
                    </Form>
                    <Modal class="wcLogin" v-model="wcLogin" width="360" @on-cancel="cancel">
                        <div slot="header">
                            <h3 align="center">注册/登录</h3>
                            <p align="center">
                                首次登录需要完成两步操作，以后即可自动登录
                            </p>
                        </div>
                        <div class="steps">
                            <div class="item">
                                <p>第1步：先交二维码图片临时保存到手机相册</p>
                                <img :src="wcImg" alt="" width="100%">
                            </div>
                            <div class="item">
                                <p>第2步：在微信的"扫一扫"中打开刚保存的二维码图片</p>
                                <img src="../../images/register/login_wechat_scan.png" alt="" width="100%">
                            </div>
                        </div>
                        <div slot="footer">
                            <a href="weixin://scanqrcode">
                                <Button type="success" size="large" long>二维码存好了，打开微信扫一扫</Button>
                            </a>
                        </div>
                    </Modal>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'scan',
        data () {
            return {
                access_token: this.$store.state.user.access_token,
                isPc: true,
                wcLogin: false,
                wcImg: '',
                timer: '',
                spinShow: false,
                isRefresh: true,
                outTime: 0,
                isWin: true
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            /*if(!!this.$store.state.register.openid){
             this.openid = this.$store.state.register.openid;
             }else {
             this.$router.push({
             name: 'register'
             });
             }*/
            if(!!this.isPc){
                this.getqrcode();

            }else {

            }
            if(window.location.href.split('/').pop() === 'security'){
                this.isWin = false
            }

        },
        methods: {
            getqrcode (){
                this.spinShow = true;
                this.isRefresh = true;
                if(!!this.timer){
                    clearInterval(this.timer);
                }

                this.wcLogin = true;
                var _this = this;

                util.ajax({
                    url: "api/v1/wechat/qrcode?t=" + (new Date()).getTime(), // 加随机数防止缓存
                    method: "get",
                    dataType: "json"
                }).then (function (data) {
                    //console.log(data);
                    if(data.data.msg === 'success'){
                        _this.spinShow = false;
                        _this.isRefresh = false;
                        _this.wcImg = data.data.data.image;
                        var s = 0;
                        _this.timer = setInterval(function () {
                            s++;
                            if(s === 120){   //120
                                _this.wcLogin = false;
                                clearInterval(_this.timer);
                                _this.spinShow = true;
                            }

                            util.ajax({
                                url: "api/v1/wechat/verify_ticket?type=binding&t=" + data.data.data.ticket,
                                method: "get",
                                dataType: "json"
                            }).then (function (data) {
                                console.log(data);
                                if(data.data.msg === 'success'){
                                    if(data.data.data.openid !== ''){
                                        clearInterval(_this.timer);
                                        if(!data.data.data.binding){
                                            _this.$store.commit('setUnbindInfo',data.data.data);
                                            if(_this.isPc && !_this.isWin){
                                                _this.$emit('on-tip',4,{wc_name:data.data.data.nickname,uid:data.data.data.uid});
                                            }else {
                                                _this.$router.push({
                                                    name: 'tip'
                                                });
                                                window.parent.postMessage({register_type:'toTip'},'*');
                                            }
                                        }else if(!!data.data.data.binding) {
                                            if(_this.isPc){
                                                if(!_this.isWin){
                                                    _this.$emit('on-tip',2);
                                                }else {
                                                    window.parent.postMessage({register_type:'login'},'*');
                                                }
                                            }else {
                                                window.location.href = 'https://forum.chasedream.com/forum.php';
                                            }
                                        }
                                    }
                                }else{
                                    _this.errMsg = data.msg;
                                }
                            }).catch(function (err) {
                                console.log(err)
                                _this.errMsg = err.msg;
                            });
                        },1000);
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });

            },
            cancel (){
                clearInterval(this.timer);
            },
            stap(){
                clearInterval(this.timer);
                this.$emit('on-stap');
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            },
            closeWin (n){
                if(n === 1){
                    window.parent.postMessage({register_type:'noShow'},'*');
                }else {
                    window.parent.postMessage({register_type:'close'},'*');
                }
            }
        }
    };
</script>

<style>

</style>
