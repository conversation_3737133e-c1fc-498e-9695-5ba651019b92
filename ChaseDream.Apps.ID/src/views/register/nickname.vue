<style lang="less">
@import "register.less";
@import "../../libs/jigsaw.css";
</style>

<template>
  <div class="register">
    <div
      class="register-con"
      v-if="!!isPc"
      style="height: 400px;overflow: hidden;background: #fff;"
    >
      <!--<iframe src="https://forum.chasedream.com/member.php?mod=register" frameborder="0" style="position: absolute;left: 0;top: 0;width: 100%;height: 767px;z-index: 1;"></iframe>-->
      <div class="r">
        <div class="r-c">
          <div class="r-tab">
            <div class="tab"><span>1</span>手机注册</div>
            <!--<div class="tab"><span>2</span>绑定微信（选填）</div>-->
            <div class="tab on"><span>2</span>论坛昵称</div>
          </div>
          <div class="r-form">
            <Form :label-width="132">
              <FormItem
                label="论坛昵称:"
                :class="username_tip === '' ? 'required' : 'required er'"
              >
                <Input
                  v-model="form.username"
                  @on-blur="username_exist"
                  @on-change="
                    isname = false;
                    username_tip = '';
                  "
                ></Input>
                <div
                  class="loading"
                  v-if="name_loading"
                  style="width: 20px;height: 20px;display: inline-block;position: relative;vertical-align: middle;"
                >
                  <Spin fix style="margin-left:-20px;">
                    <Icon
                      type="load-c"
                      size="18"
                      style="animation: ani-demo-spin 1s linear infinite;"
                    ></Icon>
                  </Spin>
                </div>
                <Icon
                  type="ios-checkmark"
                  size="18"
                  style="color: #19be6b;vertical-align: middle;"
                  v-if="isname && !name_loading"
                ></Icon>
                <span class="tip">{{ username_tip }}</span>
                <p>昵称将做为您在论坛的名字，显示在您的帖子和回复中。</p>
              </FormItem>
              <FormItem
                prop="email"
                label="Email:"
                :class="email_tip === '' ? '' : 'er'"
              >
                <Input
                  v-model="form.email"
                  placeholder="Email"
                  @on-blur="email_exist"
                  @on-change="
                    isemail = false;
                    email_tip = '';
                  "
                ></Input>
                <div
                  class="loading"
                  v-if="email_loading"
                  style="width: 20px;height: 20px;display: inline-block;position: relative;vertical-align: middle;"
                >
                  <Spin fix style="margin-left:-20px;">
                    <Icon
                      type="load-c"
                      size="18"
                      style="animation: ani-demo-spin 1s linear infinite;"
                    ></Icon>
                  </Spin>
                </div>
                <Icon
                  type="ios-checkmark"
                  size="18"
                  style="color: #19be6b;vertical-align: middle;"
                  v-if="
                    isemail && !!form.email && !email_loading && !!isemail_check
                  "
                ></Icon>
                <span class="tip">{{ email_tip }}</span>
                <p>选填，作为手机变更后找回帐号的重要依据。</p>
              </FormItem>
              <!--<FormItem label="验证码:" class="required">
                                <div class="cH">
                                    <div id="captcha-box"></div>
                                    <div v-if="addYz" class="addYz">正在加载验证码....</div>
                                </div>
                                <span class="tip">{{validate_tip}}</span>
                            </FormItem>-->
              <FormItem class="sub_btn">
                <!--<div id="captcha-box"></div>-->
                <Button type="success" id="regist_btn" @click="handleSubmit"
                  >提交</Button
                >
                <!--<Checkbox v-model="form.isagree">同意 <a href="javascript:;" @click="showAgreement = true">网站服务条款</a></Checkbox>-->
              </FormItem>
            </Form>
          </div>
        </div>
      </div>
    </div>
    <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
      <Card :bordered="false" class="nickname">
        <p slot="title">
          <router-link to="/register">
            <Icon type="ios-arrow-thin-left" size="24"></Icon>
          </router-link>
          论坛昵称
          <router-link to="/login">
            <span>登录</span>
          </router-link>
        </p>
        <div class="re_tip" v-if="!!errMsg">
          <p>
            <span
              ><img src="../../images/register/icon_tips.png" alt="" />
            </span>
            {{ errMsg }}
          </p>
        </div>
        <h3>给自己的帐号添加一个ChaseDream论坛昵称</h3>
        <div class="form-con">
          <Form ref="nameForm" :model="form" class="nicknameForm">
            <FormItem
              prop="username"
              class="username"
              style="position: relative"
            >
              <Input
                v-model="form.username"
                placeholder="昵称"
                @on-blur="username_exist"
                @on-change="
                  isname = false;
                  errMsg = '';
                "
              ></Input>
              <div
                class="loading"
                v-if="name_loading"
                style="width: 20px;height: 20px;display: inline-block;position: absolute;right:10px;top:20px;vertical-align: middle;"
              >
                <Spin fix style="margin-left:-20px;">
                  <Icon
                    type="load-c"
                    size="18"
                    style="animation: ani-demo-spin 1s linear infinite;"
                  ></Icon>
                </Spin>
              </div>
              <Icon
                type="ios-checkmark"
                size="18"
                style="color: #19be6b;vertical-align: middle;position: absolute;right: 10px;top: 20px;"
                v-if="
                  isname && !!form.username && !name_loading && !!isname_check
                "
              ></Icon>
              <span class="tip"
                >昵称将做为您在论坛的名字，显示在您的帖子和回复中。</span
              >
            </FormItem>
            <FormItem prop="email" style="position: relative">
              <Input
                v-model="form.email"
                placeholder="Email"
                @on-blur="email_exist"
                @on-change="
                  isemail = false;
                  email_tip = '';
                "
              ></Input>
              <div
                class="loading"
                v-if="email_loading"
                style="width: 20px;height: 20px;display: inline-block;position: absolute;right:10px;top:20px;vertical-align: middle;"
              >
                <Spin fix style="margin-left:-20px;">
                  <Icon
                    type="load-c"
                    size="18"
                    style="animation: ani-demo-spin 1s linear infinite;"
                  ></Icon>
                </Spin>
              </div>
              <Icon
                type="ios-checkmark"
                size="18"
                style="color: #19be6b;vertical-align: middle;position: absolute;right: 10px;top: 20px;"
                v-if="
                  isemail && !!form.email && !email_loading && !!isemail_check
                "
              ></Icon>
              <span class="tip">选填，作为手机变更后找回帐号的重要依据。</span>
            </FormItem>
            <!--<FormItem>
                            <div class="cH">
                                <div id="captcha-box"></div>
                                <div v-if="addYz" class="addYz">正在加载验证码....</div>
                            </div>
                        </FormItem>-->
            <FormItem class="re-btn">
              <!--<div id="captcha-box"></div>-->
              <Button id="regist_btn" type="success" long @click="handleSubmit"
                >注册</Button
              >
            </FormItem>
            <FormItem>
              <Checkbox v-model="form.isagree"
                >已阅读并同意
                <a href="javascript:;" @click="showAgreement = true"
                  >网站服务条款</a
                ></Checkbox
              >
            </FormItem>
          </Form>
        </div>
      </Card>
    </div>
    <div class="agreement-pop" v-if="!!showAgreement">
      <Icon type="close" size="24" @click="showAgreement = false"></Icon>
      <agreement></agreement>
      <Button type="success" long @click="showAgreement = false"
        >返回注册</Button
      >
    </div>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import util from "@/libs/util.js";
import area from "../area_code";
import jigsaws from "@/libs/jigsaw.js";
import initGeetest from "@/libs/gt.js";
import agreement from "../../template/agreement.vue";
export default {
  name: "nickname",
  components: {
    agreement,
  },
  data() {
    return {
      form: {
        mobile: "", //test
        password: "", //321321
        area_code: 86,
        captcha: "",
        username: "",
        email: "",
        isagree: true,
      },
      access_token: this.$store.state.user.access_token,
      errMsg: "",
      isPc: true,
      isValidate: false,
      addYz: true,
      geetest_challenge: "",
      geetest_validate: "",
      geetest_seccode: "",
      showAgreement: false,
      isname: false,
      name_loading: false,
      isname_check: false,
      isemail: false,
      isemail_check: false,
      email_loading: false,
      username_tip: "",
      email_tip: "",
      validate_tip: "",
    };
  },
  mounted() {
    this.$store.commit("isPC");
    this.isPc = this.$store.state.register.ispc;

    if (!!this.isPc) {
      //window.parent.postMessage({register_type:'nickName'},'*');
      var requireds = document.getElementsByClassName("required");
      for (var i = 0; i < requireds.length; i++) {
        var l = requireds[i].getElementsByClassName("ivu-form-item-label")[0];
        l.innerHTML =
          '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
      }
    } else {
    }

    /*if(!this.$store.state.register.registerData.mobile){
                this.$router.push({
                    name: 'register'
                });
            }*/

    this.codes = area.area_code;
    //this.geetest();
    //console.log(this.getLength('fsdf埰fse fs D fdsaf超大规模（'));
  },
  methods: {
    geetest(e) {
      this.addYz = true;
      let _this = this;
      this.isValidate = false;
      _this.geetest_challenge = "";
      _this.geetest_validate = "";
      _this.geetest_seccode = "";
      util
        .ajax({
          url: "api/v1/auth/geetest/register?t=" + new Date().getTime(), // 加随机数防止缓存
          method: "get",
          dataType: "json",
        })
        .then(function(data) {
          //this.xwyz = data;
          let removeObj = document.getElementById("captcha-box"); // 获取dom元素
          if (!!removeObj && removeObj != "underfined") {
            removeObj.innerHTML = ""; // 因为个人当中直接在页面中切换短信或者密码验证，先移除一个，不然会一直 添加
          }
          // 调用 initGeetest 进行初始化
          // 参数1：配置参数
          // 参数2：回调，回调的第一个参数验证码对象，之后可以使用它调用相应的接口
          initGeetest(
            {
              // 以下 4 个配置参数为必须，不能缺少
              gt: data.data.data.gt,
              challenge: data.data.data.challenge,
              offline: !data.data.data.success, // 表示用户后台检测极验服务器是否宕机
              new_captcha: data.data.data.new_captcha, // 用于宕机时表示是新验证码的宕机
              product: "bind", // 产品形式，包括：float，popup
              width: "300px",
              https: true,
              // 更多配置参数说明请参见：http://docs.geetest.com/install/client/web-front/
            },
            function(captchaObj) {
              captchaObj
                .onReady(function() {
                  //验证码ready之后才能调用verify方法显示验证码
                })
                .onSuccess(function() {
                  //your code
                })
                .onError(function() {
                  // 出错啦，可以提醒用户稍后进行重试
                  // error 包含error_code、msg
                  alert("页面出错了，页面将刷新，请重新登录！");
                  window.location.href = window.location.href;
                })
                .onClose(function() {
                  // 用户把验证关闭了，这时你可以提示用户需要把验证通过后才能进行后续流程
                  _this.errMsg = "需要把验证通过后才能登录";
                });
              captchaObj.reset(); // 在切换登录的时候重置，不需要切换的忽略

              // 按钮提交事件
              /*var regist_btn = document.getElementById('regist_btn');
                        regist_btn.onclick = function(){
                            // 检测验证码是否ready, 验证码的onReady是否执行
                            //captchaObj.verify(); //显示验证码
                            // some code
                            setTimeout(function () {
                                _this.handleSubmit(captchaObj);
                            },300);
                        }*/
              //captchaObj.appendTo('#captcha-box');     // 插入验证
              _this.addYz = false;

              captchaObj.onSuccess(() => {
                // 第一次验证成功，极验自带的回调
                _this.isValidate = true;
                if (!!_this.isPc) {
                  _this.validate_tip = "";
                } else {
                  _this.errMsg = "";
                }
                let result = captchaObj.getValidate();
                _this.dis = false;
                _this.geetest_challenge = result.geetest_challenge; //将第一次验证获取的三个参数保存起来，方便二次验证
                _this.geetest_validate = result.geetest_validate;
                _this.geetest_seccode = result.geetest_seccode;
                _this.registerSubmit();
              });
            }
          );
        });
    },
    username_exist() {
      this.name_loading = true;
      this.isname_check = false;
      var _this = this;
      setTimeout(function() {
        if (!_this.form.username) {
          _this.name_loading = false;
          _this.isname = false;
          if (!!_this.isPc) {
            _this.username_tip = "昵称不能为空";
          } else {
            _this.errMsg = "昵称不能为空";
          }
        } else {
          _this.name_loading = false;
          _this.isname = false;
          /*if(_this.form.username.length > 15 || _this.form.username.length < 3){
                            if(!!_this.isPc){
                                _this.username_tip = '昵称由 3 到 15 个字符组成';
                            }else{
                                _this.errMsg = '昵称由 3 到 15 个字符组成';
                            }
                            return false
                        }*/
          if (
            new Blob([_this.form.username]).size > 15 ||
            new Blob([_this.form.username]).size < 3
          ) {
            if (!!_this.isPc) {
              _this.username_tip = "昵称由 3 到 15 个字符组成";
            } else {
              _this.errMsg = "昵称由 3 到 15 个字符组成";
            }
            return false;
          }

          util
            .ajax({
              url: "/api/v1/auth/verify/username_exist",
              method: "post",
              data: {
                username: _this.form.username,
              },
            })
            .then(function(res) {
              //console.log(res)
              _this.isname_check = true;
              if (res.data.msg === "success") {
                _this.name_loading = false;
                if (!res.data.data.exist && !res.data.data.invalid) {
                  _this.isname = true;
                  if (!!_this.isPc) {
                    _this.username_tip = "";
                  } else {
                    _this.errMsg = "";
                  }
                } else if (!!res.data.data.exist) {
                  _this.isname = false;
                  if (!!_this.isPc) {
                    _this.username_tip = "昵称已存在";
                  } else {
                    _this.errMsg = "昵称已存在";
                  }
                } else if (!!res.data.data.invalid) {
                  _this.isname = false;
                  if (!!_this.isPc) {
                    _this.username_tip = "昵称包含敏感字符";
                  } else {
                    _this.errMsg = "昵称包含敏感字符";
                  }
                }
              } else {
                _this.name_loading = false;
                _this.isname = false;
                if (!!_this.isPc) {
                  _this.username_tip = res.msg;
                } else {
                  _this.errMsg = res.msg;
                }
              }
            })
            .catch(function(err) {
              console.log(err);
              _this.errMsg = err.msg;
              _this.name_loading = false;
              _this.isname_check = true;
            });
        }
      }, 100);
    },
    email_exist() {
      this.isemail_check = false;
      this.email_loading = true;
      var _this = this;
      setTimeout(function() {
        if (!!_this.form.email) {
          var reg = /^([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
          if (!reg.test(_this.form.email)) {
            _this.isemail = false;
            _this.email_loading = false;
            if (!!_this.isPc) {
              _this.email_tip = "请输入正确邮箱地址";
            } else {
              _this.errMsg = "请输入正确邮箱地址";
            }
            _this.errMsg = "请输入正确邮箱地址";
            _this.isemail = false;
            return false;
          }
          util
            .ajax({
              url: "/api/v1/auth/verify/email_exist",
              method: "post",
              data: {
                email: _this.form.email,
              },
            })
            .then(function(res) {
              //console.log(res)
              _this.isemail_check = true;
              if (res.data.msg === "success") {
                _this.email_loading = false;
                if (!res.data.data.exist && !res.data.data.invalid) {
                  _this.isemail = true;
                  if (!!_this.isPc) {
                    _this.email_tip = "";
                  } else {
                    _this.errMsg = "";
                  }
                } else if (!!res.data.data.exist) {
                  _this.isemail = false;
                  if (!!_this.isPc) {
                    _this.email_tip = "邮箱已存在";
                  } else {
                    _this.errMsg = "邮箱已存在";
                  }
                } else if (!!res.data.data.invalid) {
                  _this.isemail = false;
                  if (!!_this.isPc) {
                    _this.email_tip = "邮箱包含敏感字符";
                  } else {
                    _this.errMsg = "邮箱包含敏感字符";
                  }
                }
              } else {
                _this.email_loading = false;
                _this.isemail = false;
                if (!!_this.isPc) {
                  _this.email_tip = res.msg;
                } else {
                  _this.errMsg = res.msg;
                }
              }
            })
            .catch(function(err) {
              console.log(err);
              _this.errMsg = err.msg;
              _this.email_loading = false;
              _this.isemail = false;
              _this.isemail_check = true;
            });
        } else {
          if (!!this.isPc) {
            _this.email_tip = "";
          } else {
            _this.errMsg = "";
          }
          _this.isemail = true;
          _this.email_loading = false;
        }
      }, 100);
    },
    handleSubmit(captchaObj) {
      if (this.form.email === "") {
        this.isemail = true;
      }
      if (!this.form.username) {
        if (!!this.isPc) {
          this.username_tip = "昵称不能为空";
        } else {
          this.errMsg = "昵称不能为空";
        }
        return;
      } else if (!!this.form.username && !this.isname) {
        if (!!this.isPc) {
          this.username_tip = "昵称已存在";
        } else {
          this.errMsg = "昵称已存在";
        }
        return;
      } else if (
        new Blob([this.form.username]).size < 3 ||
        new Blob([this.form.username]).size > 15
      ) {
        if (!!this.isPc) {
          this.username_tip = "昵称由3-15个字符组成";
        } else {
          this.errMsg = "昵称由3-15个字符组成";
        }
        return;
      } else {
        if (!!this.isPc) {
          this.username_tip = "";
        }
      }
      if (!!this.form.email && !this.isemail) {
        var reg = /^([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]*)*[a-zA-Z0-9]+\.[a-zA-Z]{2,5}$/;
        if (!reg.test(this.form.email)) {
          if (!!this.isPc) {
            this.email_tip = "请输入正确邮箱地址";
          } else {
            this.errMsg = "请输入正确邮箱地址";
          }
          return false;
        } else {
          if (!!this.isPc) {
            this.email_tip = "邮箱已存在";
          } else {
            this.errMsg = "邮箱已存在";
          }
          return;
        }
      }
      if (!this.form.isagree) {
        this.errMsg = "请阅读并同意网站服务条款";
        return;
      }
      if (!this.isValidate) {
        var n_e_timer = null;
        clearInterval(n_e_timer);
        n_e_timer = setInterval(function() {
          check_n_e();
        }, 300);
        var _this = this;
        function check_n_e() {
          if (!!_this.isname_check && !!_this.isemail_check) {
            //captchaObj.verify();
            _this.show_captcha();
            clearInterval(n_e_timer);
          } else if (
            !!_this.isname_check &&
            !_this.isemail_check &&
            !_this.form.email
          ) {
            //captchaObj.verify();
            _this.show_captcha();
            clearInterval(n_e_timer);
          } else if (
            (!_this.isname_check && !!_this.form.username) ||
            (!_this.isemail_check && !!_this.form.isemail_check)
          ) {
            check_n_e();
          }
        }
      }
    },
    registerSubmit(obj) {
      this.form.mobile = this.$store.state.register.registerData.mobile;
      this.form.area_code = this.$store.state.register.registerData.area_code;
      this.form.captcha = this.$store.state.register.registerData.captcha;
      this.form.password = this.$store.state.register.registerData.password;
      //this.form.openid = this.$store.state.register.registerData.openid;
      var dataIn = {};
      dataIn.mobile = this.form.mobile;
      dataIn.password = this.form.password;
      dataIn.area_code = this.form.area_code;
      dataIn.captcha = this.form.captcha;
      dataIn.username = this.form.username;
      dataIn.email = this.form.email;
      /*dataIn.geetest_challenge = this.geetest_challenge;
                dataIn.geetest_validate = this.geetest_validate;
                dataIn.geetest_seccode = this.geetest_seccode;*/
      dataIn.ticket = obj.ticket;
      dataIn.randstr = obj.randstr;
      /*if(!!this.$store.state.register.openid){
                    dataIn.openid = this.$store.state.register.openid;
                }*/
      if (!!this.isPc) {
        if (
          !!this.$store.state.register.openid &&
          this.$store.state.register.registerData.openid != "null"
        ) {
          dataIn.openid = this.$store.state.register.registerData.openid;
        }
        if (
          !!this.$store.state.register.registerData.unionid &&
          this.$store.state.register.registerData.unionid != "null"
        ) {
          dataIn.unionid = this.$store.state.register.registerData.unionid;
        }
        if (
          !!this.$store.state.register.registerData.nickname &&
          this.$store.state.register.registerData.nickname != "null"
        ) {
          dataIn.nickname = this.$store.state.register.registerData.nickname;
        }
        dataIn.platform = "1";
      } else {
        if (!!this.$store.state.register.openid) {
          dataIn.openid = this.$store.state.register.openid;
        }
        if (!!this.$store.state.register.unionid) {
          dataIn.unionid = this.$store.state.register.unionid;
        }
        if (!!this.$store.state.register.nickname) {
          dataIn.nickname = this.$store.state.register.nickname;
        }
        dataIn.platform = "2";
      }

      //console.log(dataIn);
      var _this = this;
      util
        .ajax({
          url: "/api/v1/auth/verify/register",
          method: "post",
          data: dataIn,
        })
        .then(function(res) {
          //console.log(res)
          if (res.data.msg === "success") {
            if (!!_this.isPc) {
              window.parent.postMessage(
                { register_type: "login_show_wx" },
                "*"
              );
              window.parent.postMessage({ register_type: "login" }, "*");
            } else {
              //if(!!dataIn.openid){
              window.location.href =
                "https://forum.chasedream.com/forum.php?login=true";
              /* }else {
                                _this.$router.push({
                                    name: 'binding'
                                });
                            }*/
            }
          } else {
            _this.$Modal.error({
              title: "消息提示",
              content: "<p>注册失败，请重新注册！</p>",
              loading: true,
              onOk: () => {
                setTimeout(() => {
                  _this.$Modal.remove();
                  _this.$router.push({
                    name: "register",
                  });
                }, 2000);
              },
            });
            /*if(!!_this.isPc){
                         _this.username_tip = res.msg;
                         }else {
                         _this.errMsg = res.msg;
                         }
                         _this.geetest();*/
          }
        })
        .catch(function(err) {
          console.log(err);
          _this.errMsg = err.msg;
          if (!_this.isPc) {
            if (err.msg.indexOf("昵称") > -1) {
              _this.username_tip = err.msg;
            } else {
              _this.email_tip = err.msg;
            }
          }
          //_this.geetest();
        });
    },
    isWeiXin() {
      var ua = window.navigator.userAgent.toLowerCase();
      var iswx = false;
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        if (ua.match(/wxwork/i) == "wxwork") {
          // 企业微信号
          iswx = false;
        } else {
          iswx = true;
        }
      } else {
        iswx = false;
      }
      return iswx;
    },
    show_captcha() {
      var _this = this;
      TCaptchaModify();
      var captcha = new TencentCaptcha("2032354718", function(res) {
        _this.registerSubmit(res);
      });
      captcha.show();
      function TCaptchaModify() {
        if (!!_this.isPc) {
          var dom = document.getElementById("tcaptcha_transform");
          if (!!dom && !!dom.innerHTML) {
            dom.style.top = "-75px";
            dom.style.transform = "scale(0.6,0.6)";
          } else {
            setTimeout(function() {
              TCaptchaModify();
            }, 600);
          }
        }
      }
    },
    getLength(str) {
      ///<summary>获得字符串实际长度，中文2，英文1</summary>
      ///<param name="str">要获得长度的字符串</param>
      var realLength = 0,
        len = str.length,
        charCode = -1;
      for (var i = 0; i < len; i++) {
        charCode = str.charCodeAt(i);
        if (charCode >= 0 && charCode <= 128) realLength += 1;
        else realLength += 2;
      }
      return realLength;
    },
  },
};
</script>

<style></style>
