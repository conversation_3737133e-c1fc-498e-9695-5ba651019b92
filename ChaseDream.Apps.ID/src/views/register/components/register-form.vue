<style lang="less">
    @import '../register.less';
    @import '../../../libs/jigsaw.css';
    @import "../../../template/pc_layout.less";
</style>

<template>
    <div class="register-form" style="overflow-y: auto">
        <div class="register-con" v-if="!!isPc" style="">
            <div class="r">
                <div class="r-c">
                    <div class="r-tab">
                        <div class="tab on"><span>1</span>手机注册</div>
                        <div class="tab"><span>2</span>绑定微信（选填）</div>
                        <div class="tab"><span>3</span>论坛昵称</div>
                    </div>
                    <div class="r-form">
                        <Form :label-width="132">
                            <FormItem label="手机号:"> <!--class="er"-->
                                <Input v-model="form.mobile"></Input>
                                <span class="tip"></span>  <!--请输入邮箱地址-->
                                <Poptip title="" placement="bottom-start">
                                    <a>+{{form.area_code}}</a>
                                    <div slot="content">
                                        <Tabs value="name1">
                                            <TabPane label="常用" name="use">

                                            </TabPane>
                                            <TabPane label="ABCDEF" name="abcdef">

                                            </TabPane>
                                            <TabPane label="GHIJ" name="ghij">

                                            </TabPane>
                                            <TabPane label="KLMN" name="klmn">

                                            </TabPane>
                                            <TabPane label="OPQISTUVW" name="opqistuvw">

                                            </TabPane>
                                            <TabPane label="XYZ" name="xyz">

                                            </TabPane>
                                        </Tabs>

                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="validate_code" class="validate_code" label="验证码:">
                                <Input v-model="form.captcha"></Input>
                                <Button type="ghost" @click="getCaptcha" v-if="!!isSentcode">获取验证码</Button>
                                <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                                <span class="tip"></span>
                            </FormItem>
                            <FormItem prop="password" label="登录密码:">
                                <Input type="password" v-model="form.password"></Input>
                                <span class="tip"></span>
                            </FormItem>
                            <FormItem prop="repassword" label="确认密码:">
                                <Input type="password" v-model="form.repassword"></Input>
                                <span class="tip"></span>
                            </FormItem>
                            <FormItem class="sub_btn">
                                <Button type="success">下一步</Button>
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="area-code" v-if="!!showCodep">
            <div class="mask" @click="showCodep = false"></div>
            <div class="code-inner">
                <div class="top">
                    国家/地区
                    <span>代码</span>
                </div>
                <p class="used">常用</p>
                <div class="used-list">
                    <Button @click="getAreaCode(86)">中国<br>+86</Button>
                    <Button @click="getAreaCode(852)">香港<br>+852</Button>
                    <Button @click="getAreaCode(886)">台湾<br>+886</Button>
                    <Button @click="getAreaCode(853)">澳门<br>+853</Button>
                    <Button @click="getAreaCode(1)">美加<br>+1</Button>
                    <Button @click="getAreaCode(44)">英国<br>+44</Button>
                    <Button @click="getAreaCode(33)">法国<br>+33</Button>
                    <Button @click="getAreaCode(49)">德国<br>+49</Button>
                    <Button @click="getAreaCode(61)">澳洲<br>+61</Button>
                    <Button @click="getAreaCode(82)">韩国<br>+82</Button>
                    <Button @click="getAreaCode(65)">新加坡<br>+65</Button>
                    <Button>日本<br>+81</Button>
                </div>
                <div class="detail-list">
                    <p>详细列表</p>
                    <Menu active-name="86" @on-select="getAreaCode">
                        <MenuGroup :title="code.letter" v-for="code in code_group" :key="code.index">
                            <MenuItem :name="list.area_code" v-for="list in code.data" :key="list.index">
                                {{list.name}}
                                <span>+{{list.area_code}}</span>
                            </MenuItem>
                        </MenuGroup>
                    </Menu>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../../area_code';
    import jigsaws from '@/libs/jigsaw.js';
    export default {
        components: {

        },
        data () {
            return {
                form: {
                    mobile: '', //test
                    password: '', //321321
                    repassword: '',
                    area_code: 86,
                    captcha: ''
                },
                rules: {
                    mobile: [
                        /*{ required: true, message: '手机号不能为空', trigger: 'blur' },
                         {type: 'number', min: 9, message: '请输入正确的手机号', trigger: 'blur' }*/
                    ],
                    /*password: [
                     { required: true, message: '密码不能为空', trigger: 'blur' },
                     { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                     ]*///,
                    /* validate_code: [
                     { required: true, message: '验证码不能为空', trigger: 'blur' },
                     ]*/
                },
                access_token: this.$store.state.user.access_token,
                bgImg: '',
                errMsg: '',
                isRegistet: false,
                codeImg: '',
                code_val: 86,
                codes: [],
                code_group: [],
                isValidate: false,
                isShowImg: false,
                isPc: true,
                isSentcode: true,
                count: 60,
                showCodep: false,
                showWXp: true
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;
            //this.showWXp = this.$store.state.register.showWXp;

            this.codes = area.area_code;
            this.code_group = area.area_code_group;

            if(!!this.isPc){
                /* var _this = this;
                 util.ajax({
                 url:'https://bird.ioliu.cn/v1?url=http://www.bing.com/HPImageArchive.aspx?format=js&idx=0&n=1',
                 method:'get',
                 headers: {
                 'Content-type': 'application/json; charset=UTF-8',
                 },
                 }).then(function (res) {
                 //console.log(res.data.images[0].url);
                 _this.bgImg= 'url(https://www.bing.com/' + res.data.images[0].url + ')';
                 }).catch(function (err) {
                 console.log(err)
                 });
                 //this.getValidate_code();
                 var _this = this;*/

                /*jigsaws.init(document.getElementById('captcha'),
                 function() {
                 //document.getElementById('msg').innerHTML = '验证成功！'
                 _this.isValidate = true;
                 document.getElementsByClassName('ivu-poptip-popper')[0].style.display = 'none'
                 document.getElementsByClassName('ivu-poptip')[0].style.display = 'none';
                 _this.errMsg = '';
                 },function () {
                 // document.getElementById('msg').innerHTML = '验证失败！'
                 _this.isValidate = false;
                 })*/
            }else {
                if(this.isWeiXin()){
                    this.showWXp = this.$store.state.register.showWXp;
                    this.isWXlogin();
                }
            }
        },
        methods: {
            getCaptcha (){
                if(!this.form.mobile || !this.form.area_code){
                    this.errMsg = '请输入手机号码';
                    this.isRegistet = false;
                    return;
                }

                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/sms/send',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: _this.form.area_code
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.errMsg = '';
                        _this.countdown();
                        _this.$Message.info(res.data.data.message);
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },
            handleSubmit () {
                if(!this.form.mobile){
                    this.errMsg = '手机号码不能为空';
                    this.isRegistet = false;
                    return;
                }else if(!this.form.captcha){
                    this.errMsg = '请输入正确的验证码';
                    this.isRegistet = false;
                    return;
                }else if(!this.form.password){
                    this.errMsg = '登录密码不能为空';
                    this.isRegistet = false;
                    return;
                }else if(!this.form.repassword){
                    this.errMsg = '确认密码不能为空';
                    this.isRegistet = false;
                    return;
                }else if(!this.checkpassword(this.form.password, this.form.repassword)){
                    return
                }

                //this.checkpassword(this.form.password, this.form.repassword);


                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/sms',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: _this.form.area_code,
                        captcha: _this.form.captcha
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.exist){
                            _this.errMsg = '';
                            _this.isRegistet = true;
                        }else{
                            _this.$store.commit('setData',_this.form);
                            _this.$router.push({
                                name: 'nickname'
                            });
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.isRegistet = false;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.isRegistet = false;
                    _this.form.captcha = '';
                });
            },
            checkpassword (pw1, pw2) {
                var minlength = 8;
                var maxlength = 20;
                var strongpw = [1,2,3];
                this.errMsg = '';
                if(!pw1 && !pw2) {
                    return;
                }
                if(pw1.length < minlength) {
                    this.errMsg = '密码太短，不得少于 '+minlength+' 个字符';
                    this.isRegistet = false;
                    return false;
                }
                if(strongpw) {
                    var strongpw_error = false, j = 0;
                    var strongpw_str = new Array();
                    for(var i in strongpw) {
                        if(strongpw[i] === 1 && !pw1.match(/\d+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '数字';
                            j++;
                        }
                        if(strongpw[i] === 2 && !pw1.match(/[a-z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '小写字母';
                            j++;
                        }
                        if(strongpw[i] === 3 && !pw1.match(/[A-Z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '大写字母';
                            j++;
                        }
                        if(strongpw[i] === 4 && !pw1.match(/[^A-Za-z0-9]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '特殊符号';
                            j++;
                        }
                    }
                    if(strongpw_error) {
                        this.errMsg = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.isRegistet = false;
                        return false;
                    }
                }

                if(pw1 != pw2) {
                    this.errMsg = '两次输入的密码不一致';
                    this.isRegistet = false;
                    return false;
                }

                return true;
            },
            countdown () {
                this.isSentcode = false;
                var _this = this;
                clearInterval(timer);
                var timer = setInterval(function () {
                    if(_this.count === 1){
                        clearInterval(timer);
                        _this.isSentcode = true;
                    }else {
                        _this.count--;
                    }
                },1000);
            },
            showpannel (){
                this.showCodep = true;
            },
            getAreaCode (n){
                this.form.area_code = n;
                this.showCodep = false;
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    iswx = true;
                }else{
                    iswx = false;
                }
                return iswx;
            },
            isWXlogin(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/check_login',
                    method:'GET',
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.login){
                            window.location.href = 'https://forum.chasedream.com/forum.php';
                        }else {
                            if(!_this.$store.state.register.openid){
                                _this.$store.commit('wx_url',util,_this);
                            }
                        }
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            }
        }
    };
</script>

<style>

</style>
