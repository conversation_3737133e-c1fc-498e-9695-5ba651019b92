<style lang="less">
    @import 'register.less';
    @import '../login.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 420px;overflow: hidden;background: #fff;">
            <div class="logoff" v-if="!!isPc">
                <div class="form-con">
                    <div v-if="!lo_success">
                        <div class="logoff_info">
                            <h3>{{username}} 账户注销</h3>
                            <img :src="'https://forum.chasedream.com/uc_server/avatar.php?uid='+ uid +'&size=middle'" alt="">
                        </div>
                        <Form>
                            <FormItem class="logoff_dec">
                                <p>您正在申请注销上述账号。一旦申请完成，该账号内的全部信息将会被进行清除或匿名化处理。根据国家法律法规的要求，部分仍将留存在备份服务器上备查，但不会对外展示。</p>
                            </FormItem>
                            <FormItem class="agree">
                                <Checkbox v-model="agree"> 是的，我需要注销我的账号。</Checkbox>
                            </FormItem>
                            <FormItem>
                                <Button disabled type="success" v-if="!agree">提交</Button>
                                <Button @click="logoff_user" type="success" v-if="!!agree">提交</Button>
                            </FormItem>
                        </Form>
                    </div>
                    <div class="logoff_success" v-if="lo_success" width="300">
                        <div class="con">
                            <img src="../../images/register/logoff_success.png" alt="">
                            <h3>提交成功</h3>
                            <p>您的账号注销申请已经提交。接下来，我们将会对您的账号信息进行删除或匿名化处理。整个注销过程将在3个工作日内完成。注销完成后，由于相关信息的清除，您将无法得到任何通知。</p>
                        </div>
                        <div slot="footer">
                            <Button type="primary" size="large" long @click="cancel">确定</Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="logoff_page">
                <p slot="title">
                    <router-link to="/register/security">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    账户注销
                    <!--<span>登录</span>-->
                </p>
                <div class="form-con">
                    <div class="logoff_info">
                        <img src="../../images/register/logoff_info.png" alt="">
                        <h3>{{username}} 账户注销</h3>
                        <img :src="'https://forum.chasedream.com/uc_server/avatar.php?uid='+ uid +'&size=middle'" alt="">
                    </div>
                    <Form>
                        <FormItem class="logoff_dec">
                            <p>您正在申请注销上述账号。一旦申请完成，该账号内的全部信息将会被进行清除或匿名化处理。根据国家法律法规的要求，部分仍将留存在备份服务器上备查，但不会对外展示。</p>
                        </FormItem>
                        <FormItem class="agree">
                            <Checkbox v-model="agree"> 是的，我需要注销我的账号。 </Checkbox>
                        </FormItem>
                        <FormItem>
                            <Button disabled type="success" long v-if="!agree">提交</Button>
                            <Button @click="logoff_user" type="success" long v-if="!!agree">提交</Button>
                            <router-link to="/register/security">
                                <Button type="ghost" long>取消 </Button>
                            </router-link>
                        </FormItem>
                    </Form>
                    <Modal class="logoff_success" v-model="lo_success" :closable="false" width="300" @on-cancel="cancel">
                        <div class="con">
                            <img src="../../images/register/logoff_success.png" alt="">
                            <h3>提交成功</h3>
                            <p>您的账号注销申请已经提交。接下来，我们将会对您的账号信息进行删除或匿名化处理。整个注销过程将在3个工作日内完成。注销完成后，由于相关信息的清除，您将无法得到任何通知。</p>
                        </div>
                        <div slot="footer">
                            <router-link to="/login">
                                <Button type="primary" size="large" long @click="cancel">确定</Button>
                            </router-link>
                        </div>
                    </Modal>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'logoff',
        data () {
            return {
                access_token: this.$store.state.user.access_token,
                isPc: true,
                wcLogin: false,
                wcImg: '',
                timer: '',
                spinShow: false,
                isRefresh: true,
                outTime: 0,

                username: '',
                uid: 0,
                agree: false,
                lo_success: false
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            /*if(!!this.$store.state.register.openid){
             this.openid = this.$store.state.register.openid;
             }else {
             this.$router.push({
             name: 'register'
             });
             }*/
            if(!!this.isPc){
               // this.getqrcode();

            }else {

            }
            this.get_user_info();
        },
        methods: {
            get_user_info(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/check_login',
                    method:'get'
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        _this.username = res.data.data.username;
                        _this.uid = res.data.data.uid;
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Message.error(res.data.msg);
                    }
                }).catch(function (err) {
                    console.log(err)
                    //_this.errMsg = err.msg;
                    _this.$Message.error(err.msg);
                    /*_this.$Message.error({
                     top: 500,
                     duration: 3,
                     content: err.msg
                     });*/
                });
            },
            getqrcode (){
                this.spinShow = true;
                this.isRefresh = true;
                if(!!this.timer){
                    clearInterval(this.timer);
                }

                this.wcLogin = true;
                var _this = this;

                util.ajax({
                    url: "api/v1/wechat/qrcode?t=" + (new Date()).getTime(), // 加随机数防止缓存
                    method: "get",
                    dataType: "json"
                }).then (function (data) {
                    //console.log(data);
                    if(data.data.msg === 'success'){
                        _this.spinShow = false;
                        _this.isRefresh = false;
                        _this.wcImg = data.data.data.image;
                        var s = 0;
                        _this.timer = setInterval(function () {
                            s++;
                            if(s === 120){   //120
                                _this.wcLogin = false;
                                clearInterval(_this.timer);
                                _this.spinShow = true;
                            }

                            util.ajax({
                                url: "api/v1/wechat/verify_ticket?type=binding&t=" + data.data.data.ticket,
                                method: "get",
                                dataType: "json"
                            }).then (function (data) {
                                console.log(data);
                                if(data.data.msg === 'success'){
                                    if(data.data.data.openid !== ''){
                                        clearInterval(_this.timer);
                                        if(!data.data.data.binding){
                                            _this.$store.commit('setUnbindInfo',data.data.data);
                                            _this.$router.push({
                                                name: 'tip'
                                            });
                                            window.parent.postMessage({register_type:'toTip'},'*');
                                        }else if(!!data.data.data.binding) {
                                            if(_this.isPc){
                                                window.parent.postMessage({register_type:'login'},'*');
                                            }else {
                                                window.location.href = 'https://forum.chasedream.com/forum.php';
                                            }
                                        }
                                    }
                                }else{
                                    _this.errMsg = data.msg;
                                }
                            }).catch(function (err) {
                                console.log(err)
                                _this.errMsg = err.msg;
                            });
                        },1000);
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });

            },
            logoff_user (){
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/account_close',
                    method:'post',
                    data: {
                        type: !!this.isPc ? 'Web 桌面端' : 'Web 移动端'
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.lo_success = true;

                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Message.error(res.data.msg);
                    }
                }).catch(function (err) {
                    console.log(err)
                    //_this.errMsg = err.msg;
                    _this.$Message.error(err.msg);
                    /*_this.$Message.error({
                        top: 500,
                        duration: 3,
                        content: err.msg
                    });*/
                });
            },
            cancel (){
               this.lo_success = false;
               if(this.isPc){
                   window.parent.postMessage({register_type: 'logout'},'*');
               }else {
                   this.$router.push({
                       name: 'login'
                   });
               }
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            },
            closeWin (n){
                if(n === 1){
                    window.parent.postMessage({register_type:'noShow'},'*');
                }else {
                    window.parent.postMessage({register_type:'close'},'*');
                }
            }
        }
    };
</script>

<style>

</style>
