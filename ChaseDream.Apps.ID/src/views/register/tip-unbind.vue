<style lang="less">
    @import 'register.less';
    @import '../../libs/jigsaw.css';
</style>

<template>
    <div class="register tip" @keydown.enter="handleSubmit">
        <div class="register-con tip_unbind" v-if="!!isPc" style="height: 250px;overflow: hidden;width: 430px;">
            <div class="tip_info">
                <h3>您的微信并未绑定ChaseDream帐户</h3>
                <div class="account">
                    <!--<img src="../../images/register/tip_unbound.png" alt="" width="100%">-->
                    <!--<span>Evelyn_<PERSON></span>
                    <p><a href="">点击头像直接用该帐户登录</a></p>-->
                </div>
                <div class="desc">
                    <span>您可以：</span>
                    <div class="item">
                        <p>与已有ChaseDream帐号进行绑定</p>
                        <router-link :to="'/login?openid=' + openid">
                        <!--<a href="javascript:;" @click="binding">-->
                            <Button type="success">绑定</Button>
                        <!--</a>-->
                        </router-link>

                    </div>
                    <div class="item">
                        <p>注册一个新的ChaseDream帐号并绑定</p>
                        <!--<router-link to="/register">-->
                        <a href="javascript:;" @click="register">
                            <Button type="info">注册</Button>
                        </a>
                        <!--</router-link>-->
                    </div>
                    <div class="item">
                        <p>退出当前微信登录，回到登录页面</p>
                        <Button type="ghost" @click="wc_exit">退出</Button>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false">
                <p slot="title">
                    <router-link to="/login">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    提示
                </p>
                <div class="form-con">
                    <div class="tip_info">
                        <h3>您的微信并未绑定ChaseDream帐户</h3>
                        <div class="account">
                            <!--<img src="../../images/register/tip_unbound.png" alt="" width="100%">-->
                            <!--<span>Evelyn_Zhang</span>
                            <p><a href="">点击头像直接用该帐户登录</a></p>-->
                        </div>
                        <div class="desc">
                            <span>您可以：</span>
                            <div class="item">
                            <p>与已有ChaseDream帐号进行绑定</p>
                            <router-link to="/login" v-if="!isWeiXin()">
                                <Button type="success">绑定</Button>
                            </router-link>
                            <router-link :to="'/login' + wc_info" v-if="isWeiXin()">
                                <Button type="success">绑定</Button>
                            </router-link>
                        </div>
                            <div class="item">
                                <p>注册一个新的ChaseDream帐号并绑定</p>
                                <router-link to="/register" v-if="!isWeiXin()">
                                    <Button type="info">注册</Button>
                                </router-link>
                                <a href="javascript:;" @click="register" v-if="isWeiXin()">
                                    <Button type="info">注册</Button>
                                </a>
                            </div>
                            <div class="item">
                                <p>退出当前微信登录，回到登录页面</p>
                                <Button type="ghost" @click="wc_exit" v-if="!isWeiXin()">退出</Button>
                                <Button type="ghost" v-if="isWeiXin()"><a href="https://forum.chasedream.com/forum.php">退出</a></Button>
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'tip_unbind',
        data () {
            return {
                access_token: this.$store.state.user.access_token,
                isPc: true,
                openid: '',
                unionid: '',
                nickname: '',
                wc_info: ''
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!this.isPc){
                this.openid = window.localStorage.getItem('openid');
                this.unionid = window.localStorage.getItem('unionid');
                this.nickname = window.localStorage.getItem('nickname');
            }else {
                if(!!this.$store.state.register.openid){
                    this.openid = this.$store.state.register.openid;
                }
                if(!!this.$store.state.register.unionid){
                    this.unionid = this.$store.state.register.unionid;
                }
                if(!!this.$store.state.register.nickname){
                    this.nickname = this.$store.state.register.nickname;
                }

                if(this.isWeiXin()){
                    /*this.showWXp = this.$store.state.register.showWXp;
                    this.showWX = false;
                    this.isWXlogin();*/
                    if(window.location.href.split('?').length > 1){
                        this.wc_info = '?' +  window.location.href.split('?')[1];
                    }
                }
            }

            /*if(!!Cookies.get('openid')){
                this.openid = Cookies.get('openid');
            }else {
                this.$router.push({
                    name: 'login'
                });
            }*/

            /*else if(window.location.href.split('openid=').length > 1){
                this.openid = window.location.href.split('openid=')[1];
                this.$store.commit('setOpenid',this.openid);
                this.$store.commit('setWXp');
            }else {

            }*/


        },
        methods: {
            wc_exit () {
                //Cookies.remove('openid');
                this.$store.commit('setOpenid','');
                this.$store.commit('setUnionid','');
                this.$store.commit('setNickname','');
                if(this.isPc){
                    window.localStorage.setItem('openid',null);
                    window.localStorage.setItem('unionid',null);
                    window.localStorage.setItem('nickname',null);
                    window.parent.postMessage({register_type: 'close'},'*');
                    // 关闭窗口
                }else {
                    this.$router.push({
                        name: 'login'
                    });
                }
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            },
            binding (){

            },
            isWXlogin(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/check_login',
                    method:'GET',
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.login){
                            window.location.href = 'https://forum.chasedream.com/forum.php';
                        }else {
                            if(!_this.$store.state.register.openid){
                                _this.$store.commit('wx_url',util,_this,'binding');
                            }
                        }
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },
            register(){
                var item={
                    type: 'register',
                    openid: this.openid,
                    unionid: this.unionid,
                    nickname: this.nickname
                }
                if(!!this.isPc){
                    window.parent.postMessage({register_type: item},'*');
                }else {
                    /*this.$router.push({
                        name: 'register'
                    });*/
                    //window.location.href =
                    this.$router.push('/register'+ this.wc_info);
                }

            }
        }
    };
</script>

<style>

</style>
