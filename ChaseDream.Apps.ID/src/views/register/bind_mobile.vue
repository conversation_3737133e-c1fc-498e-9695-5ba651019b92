<style lang="less">
    @import '../login.less';
    @import '../../libs/jigsaw.css';
    @import '../register/register.less';
</style>
<template>
    <div>
        <div class="bind-mobile" v-if="!!isPc">
            <div class="lawCon" v-if="!toBinding">
                <h3>您的账号还未绑定手机</h3>
                <p align="left">根据国家法律法规对账号实名的要求，我们需要您将此账号与您的手机号码绑定以完成实名制的验证。</p>
                <Button type="primary" @click="toBinding = true;setRegister_type('toBinding')">去绑定</Button>
                <div class="law">
                    <h4>《中华人民共和国网络安全法》第二十四条</h4>
                    <p align="left" v-if="showLaw">网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，在与用户签订协议或者确认提供服务时，应当要求用户提供真实身份信息。用户不提供真实身份信息的，网络运营者不得为其提供相关服务。</p>
                    <p align="left" style="margin-top: 10px;margin-bottom: 10px;"><a href="http://search.chinalaw.gov.cn/law/searchTitleDetail?LawID=394858&Query" target="_blank">相关链接 >  </a></p>
                </div>
                <div class="btns">
                    <span class="toOpen"v-if="!showLaw" @click="showLaw = true;setRegister_type('bind_open')"></span>
                    <span class="toClose" v-if="showLaw" @click="showLaw = false;setRegister_type('bind')"></span>
                </div>
            </div>
            <div class="bind" v-if="toBinding" style="height: 280px;">
                <h3>绑定手机</h3>
                <Form ref="loginForm" :model="bind_form" class="nicknameForm" :label-width="70" style="padding-right: 80px;">
                    <FormItem prop="mobile" label="手机号:" class="mobileItem">
                        <!--<div class="area_code_btn" @click="showpannel">+{{bind_form.area_code}} <Icon type="chevron-down" size="10"></Icon></div>-->
                        <Input v-model="bind_form.mobile" placeholder="手机号"></Input>
                        <!--<Dropdown trigger="click" :transfer="true" @on-click="getAreaCode" placement="bottom-start">
                            <a href="javascript:void(0)">
                                +{{bind_form.area_code}}
                                    <Icon type="arrow-down-b"></Icon>
                            </a>
                            <DropdownMenu slot="list" class="login_pc_code">
                                <DropdownItem v-for="list in code_group" :name="list.area_code" :key="list.index">{{list.name}}
                                    <span>+{{list.area_code}}</span></DropdownItem>
                            </DropdownMenu>
                        </Dropdown>-->
                        <Poptip title="" content="content" placement="bottom-start" class="pop_area_code" popper-class="pop_area_code" v-model="openAreaCode">
                            <Button>+{{bind_form.area_code}} <Icon type="arrow-down-b"></Icon></Button>
                            <div class="api" slot="content">
                                <Select v-model="checkedCity" filterable placeholder="搜索" @on-change="checkAreaCode" ref="store"
                                        clearable>
                                    <Option v-for="item in code_group" :value="item.name + item.area_code" :key="item.index">
                                        <span>{{ item.name }}</span>
                                        <span style="float:right;color:#ccc">+{{item.area_code}}</span>
                                    </Option>
                                </Select>
                                <div class="useCitys">
                                    <h4>常用</h4>
                                    <span @click="getAreaCode(86);openAreaCode = false">大陆<br>+86</span>
                                    <span @click="getAreaCode(852);openAreaCode = false">香港<br>+852</span>
                                    <span @click="getAreaCode(853);openAreaCode = false">澳门<br>+853</span>
                                    <span @click="getAreaCode(886);openAreaCode = false">台湾<br>+886</span>
                                    <span @click="getAreaCode(1);openAreaCode = false">美/加<br>+1</span>
                                    <span @click="getAreaCode(44);openAreaCode = false">英国<br>+44</span>
                                    <span @click="getAreaCode(33);openAreaCode = false">法国<br>+33</span>
                                    <span @click="getAreaCode(49);openAreaCode = false">德国<br>+49</span>
                                    <span @click="getAreaCode(61);openAreaCode = false">澳洲<br>+61</span>
                                    <span @click="getAreaCode(82);openAreaCode = false">韩国<br>+82</span>
                                    <span @click="getAreaCode(65);openAreaCode = false">新加坡<br>+65</span>
                                    <span @click="getAreaCode(81);openAreaCode = false">日本<br>+81</span>
                                </div>
                            </div>
                        </Poptip>
                    </FormItem>
                    <FormItem prop="validate_code" class="validate_code" label="验证码:">
                        <Input v-model="bind_form.captcha" placeholder="验证码"></Input>
                        <Button type="ghost" @click="show_captcha" v-if="!!isSentcode">获取验证码</Button>
                        <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                    </FormItem>
                    <p class="l_err_msg">{{bind_errMsg}}</p>
                    <Button type="success" @click="bingding">绑定</Button>
                </Form>
            </div>
        </div>
        <div v-if="!isPc">
            <Modal v-model="bind_mobile" width="80%" :closable="false" class-name="bind_mobile_modal" :scrollable="true" :mask-closable="false">
                <div style="text-align:center">
                    <div class="lawCon" v-if="!toBinding">
                        <h3>您的账号还未绑定手机</h3>
                        <p><img src="../../images/register/bind_mobile.png" height="110" width="78"/></p>
                        <p align="left">根据国家法律法规对账号实名的要求，我们需要您将此账号与您的手机号码绑定以完成实名制的验证。</p>
                        <Button type="primary" long @click="toBinding = true">去绑定</Button>
                        <a href="javascript:;" @click="logout" style="font-size: 16px; color: #999;margin: -10px 0 20px;display: block;">退出登录</a>
                        <div class="law">
                            <h4>《中华人民共和国网络安全法》第二十四条</h4>
                            <p align="left" v-if="showLaw">网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，在与用户签订协议或者确认提供服务时，应当要求用户提供真实身份信息。用户不提供真实身份信息的，网络运营者不得为其提供相关服务。</p>
                            <p align="left" style="margin-top: 20px;margin-bottom: 10px;"><a href="http://search.chinalaw.gov.cn/law/searchTitleDetail?LawID=394858&Query" target="_blank">相关链接 >  </a></p>
                        </div>
                        <div class="btns">
                            <span class="toOpen"v-if="!showLaw" @click="showLaw = true"></span>
                            <span class="toClose" v-if="showLaw" @click="showLaw = false"></span>
                        </div>
                    </div>
                    <div class="bind" v-if="toBinding">
                        <h3>绑定手机</h3>
                        <Form ref="loginForm" :model="bind_form" class="nicknameForm" :label-width="0">
                            <FormItem prop="mobile" label="" class="mobileItem">
                                <div class="area_code_btn" @click="showpannel">+{{bind_form.area_code}} <Icon type="chevron-down" size="10"></Icon></div>
                                <Input v-model="bind_form.mobile" placeholder="手机号"></Input>
                            </FormItem>
                            <FormItem prop="validate_code" class="validate_code" label="">
                                <Input v-model="bind_form.captcha" placeholder="验证码"></Input>
                                <Button type="ghost" @click="show_captcha" v-if="!!isSentcode">获取验证码</Button>
                                <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                            </FormItem>
                            <p class="l_err_msg">{{bind_errMsg}}</p>
                            <Button type="success" long @click="bingding">绑定</Button>
                            <a href="javascript:;" @click="logout" style="font-size: 16px; color: #999;margin: -10px 0 10px;display: block;">退出登录</a>
                        </Form>
                    </div>
                </div>
            </Modal>
            <div class="area-code" v-if="!!showCodep" style="z-index: 99999;">
                <div class="mask" @click="showCodep = false"></div>
                <div class="code-inner">
                    <div class="top">
                        国家/地区
                        <span>代码</span>
                    </div>
                    <p class="used">常用</p>
                    <div class="used-list">
                        <Button @click="getAreaCode(86)">大陆<br>+86</Button>
                        <Button @click="getAreaCode(852)">香港<br>+852</Button>
                        <Button @click="getAreaCode(853)">澳门<br>+853</Button>
                        <Button @click="getAreaCode(886)">台湾<br>+886</Button>
                        <Button @click="getAreaCode(1)">美/加<br>+1</Button>
                        <Button @click="getAreaCode(44)">英国<br>+44</Button>
                        <Button @click="getAreaCode(33)">法国<br>+33</Button>
                        <Button @click="getAreaCode(49)">德国<br>+49</Button>
                        <Button @click="getAreaCode(61)">澳洲<br>+61</Button>
                        <Button @click="getAreaCode(82)">韩国<br>+82</Button>
                        <Button @click="getAreaCode(65)">新加坡<br>+65</Button>
                        <Button @click="getAreaCode(81)">日本<br>+81</Button>
                    </div>
                    <div class="detail-list">
                        <p>详细列表</p>
                        <Menu active-name="86" @on-select="getAreaCode">
                            <MenuGroup :title="code.letter" v-for="code in code_group" :key="code.index">
                                <MenuItem :name="list.area_code" v-for="list in code.data" :key="list.index">
                                    {{list.name}}
                                <span>+{{list.area_code}}</span>
                                </MenuItem>
                            </MenuGroup>
                        </Menu>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    export default {
        components: {

        },
        data () {
            return {
                bind_form: {
                    area_code:86,
                    mobile: '',
                    captcha: ''
                },
                isPc: true,
                showLaw: false,
                toBinding: false,
                isSentcode: true,
                count: 60,
                bind_errMsg: '',
                code_group: [],
                area_code_use: [],
                area_code_abcdef: [],
                area_code_ghij: [],
                area_code_klmn: [],
                area_code_opqistuvw: [],
                area_code_xyz: [],
                openAreaCode: false,
                checkedCity: '',
                bind_mobile: true,
                showCodep: false,
                code_group: [],
            }
        },
        mounted () {
            document.body.style.background = 'none !imp';
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            console.log(Cookies.get('area_code'))
            if(!!Cookies.get('area_code')){
                this.bind_form.area_code = Cookies.get('area_code');
            }
            if(!!this.isPc){
                this.code_group = area.area_code;
            }else {
                this.code_group = area.area_code_group;
            }
            this.area_code_use = area.area_code_use;
            this.area_code_abcdef = area.area_code_abcdef;
            this.area_code_ghij = area.area_code_ghij;
            this.area_code_klmn = area.area_code_klmn;
            this.area_code_opqistuvw = area.area_code_opqistuvw;
            this.area_code_xyz = area.area_code_xyz;
        },
        methods: {
            show_captcha(){
                if(!this.bind_form.mobile || !this.bind_form.area_code){
                    this.bind_errMsg = '请输入手机号码';
                    this.isRegistet = false;
                    return false;
                }

                var _this = this;
                TCaptchaModify();
                var captcha = new TencentCaptcha('2032354718', function(res) {
                    _this.getCaptcha(res)
                });
                captcha.show();
                function TCaptchaModify(){
                    if(!!_this.isPc){
                        var dom = document.getElementById('tcaptcha_transform');
                        if(!!dom && !!dom.innerHTML){
                            dom.style.top = '-75px';
                            dom.style.transform = 'scale(0.6,0.6)';
                        }else {
                            setTimeout(function () {
                                TCaptchaModify();
                            },600)
                        }
                    }
                }
            },
            getCaptcha(obj){
                /*if(!this.bind_form.mobile || !this.bind_form.area_code){
                    this.bind_errMsg = '请输入手机号码';
                    this.isRegistet = false;
                    return;
                }*/
                if(!obj.ticket || !obj.ticket){
                    this.errMsg = '需要滑动验证通过后才能发送短信验证码';
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/sms/send/binding_mobile',
                    method:'post',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                    data: {
                        mobile: _this.bind_form.mobile,
                        area_code: parseInt(_this.bind_form.area_code),
                        ticket: obj.ticket,
                        randstr: obj.randstr,
                        platform: !!this.isPc ? 1 : 2
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.bind_errMsg = '';
                        _this.countdown();
                        _this.$Message.info(res.data.data.message);
                    }else{
                        _this.bind_errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.bind_errMsg = err.msg;
                });
            },
            countdown () {
                this.isSentcode = false;
                var _this = this;
                clearInterval(timer);
                var timer = setInterval(function () {
                    if(_this.count === 1){
                        clearInterval(timer);
                        _this.isSentcode = true;
                    }else {
                        _this.count--;
                    }
                },1000);
            },
            getAreaCode (n){
                this.bind_form.area_code = n;
                this.showCodep = false;
                Cookies.set('area_code', n);
            },
            checkAreaCode(e){
                if(!!e){
                    this.bind_form.area_code = e.replace(/[^0-9]/ig,"");
                    this.checkedCity = '';
                    this.$refs.store.clearSingleSelect(); // 清空
                    this.openAreaCode = false;
                }
            },
            bingding (){
                if(!this.bind_form.mobile){
                    this.bind_errMsg = '请输入要绑定的手机号'
                    return false;
                }
                if(!this.bind_form.captcha){
                    this.bind_errMsg = '请输入验证码'
                    return false;
                }
                var dataIn = {
                    area_code: this.bind_form.area_code,
                    mobile: this.bind_form.mobile,
                    captcha: this.bind_form.captcha,
                    name: 'binding'
                }
               // window.parent.postMessage({register_type:dataIn},'*');
                //window.parent.postMessage({register_type:'binding'},'*');

                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/binding_mobile/change',
                    method:'post',
                    data: dataIn,
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.bind_errMsg = '';
                        if(!!_this.isPc){
                            window.parent.postMessage({register_type:'binding'},'*');
                        }else {
                            window.location.href = "https://forum.chasedream.com/forum.php";
                        }
                        // _this.$Message.info(res.data.data.message);
                    }else{
                        _this.bind_errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.bind_errMsg = err.msg;
                });
            },
            checkBind (){
                var _this = this;
                util.ajax({
                    url:'https://id.chasedream.com/api/v1/auth/check_mobile_binding',
                    method:'GET',
                    params: {
                        email: this.bind_form.email
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        window.parent.postMessage({register_type:'binding'},'*');
                    }else{
                        //_this.bind_errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.bind_errMsg = err.msg;
                });
            },
            setRegister_type(type){
                window.parent.postMessage({register_type: type},'*');
            },
            showpannel (){
                this.showCodep = true;
            },
            logout (){
                console.log('logout')
                if(!!this.isPc){
                    window.parent.postMessage({register_type: 'logout'},'*');
                }else {
                    window.location.href = "https://forum.chasedream.com/forum.php?type=deleteAccount";
                }

            }
        }
    }
</script>