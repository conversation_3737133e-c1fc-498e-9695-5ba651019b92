<style lang="less">
    @import 'register.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 420px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-form">
                        <Form :label-width="132">
                            <FormItem label="原密码:" class="required">
                                <Input v-model="form.password" type="password" @on-blur="checkPassword"></Input>
                                <span class="tip" v-if="isTipShow">{{password_tip}}</span>
                            </FormItem>
                            <FormItem label="新密码:" class="required">
                                <Input v-model="form.newpassword" type="password"></Input>
                                <span class="tip" v-if="isTipShow">{{newpassword_tip}}</span>
                            </FormItem>
                            <FormItem label="确认密码:" class="required">
                                <Input v-model="form.repassword" type="password"></Input>
                                <span class="tip" v-if="isTipShow">{{repassword_tip}}</span>
                            </FormItem>
                            <FormItem class="re-btn">
                                <Button @click="handleSubmit" type="success" long>确定</Button>
                            </FormItem>
                            <!--<FormItem class="re-btn">
                                <span class="tip" v-if="!!errMsg && !isTipShow" style="display: block;">{{errMsg}}</span>
                                <Button @click="handleSubmit" type="success" v-if="f_type === 1">下一步</Button>
                                <Button @click="handleSubmit" type="success" v-if="f_type === 2">下一步</Button>
                                <router-link to="/register/opt">
                                    <Button>上一步</Button>
                                </router-link>
                            </FormItem>-->
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="findpassword">
                <p slot="title">
                    <!--<router-link to="/register/security">-->
                        <a href="https://id.chasedream.com/register/security">
                            <Icon type="ios-arrow-thin-left" size="24"></Icon>
                        </a>
                    <!--</router-link>-->
                    修改密码
                </p>
                <!--<div class="re_tip" v-if="!!errMsg">
                    <p>
                        <span><img src="../../images/register/icon_tips.png" alt="">
                        </span>
                        {{errMsg}}
                    </p>
                </div>-->
                <div class="form-con">
                    <Form ref="nameForm" :model="form" class="findpasswordForm" :label-width="0">
                        <FormItem prop="password" label="" class="passwordItem required">
                            <Input v-model="form.password" type="password" @on-blur="checkPassword" placeholder="原密码"></Input>
                        </FormItem>
                        <p class="tip" v-if="isTipShow">{{password_tip}}</p>
                        <FormItem prop="newpassword" label="" class="passwordItem required">
                            <Input v-model="form.newpassword" type="password" placeholder="新密码"></Input>
                        </FormItem>
                        <p class="tip" v-if="isTipShow">{{newpassword_tip}}</p>
                        <FormItem prop="repassword" class="passwordItem required" label="">
                            <Input v-model="form.repassword" type="password" placeholder="确认密码"></Input>
                        </FormItem>
                        <p class="tip" v-if="isTipShow">{{repassword_tip}}</p>
                        <FormItem class="re-btn">
                            <Button @click="handleSubmit" type="success" long>确定</Button>
                        </FormItem>
                    </Form>
                    <div id="captcha-box" style=""></div>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import qs from 'qs';
    import util from '@/libs/util.js';
    import area from '../area_code';
    import jigsaws from '@/libs/jigsaw.js';
    import initGeetest from '@/libs/gt.js';
    export default {
        name: 'change_password',
        data () {
            return {
                form: {
                    password: '',
                    newpassword: '',
                    repassword: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isPc: true,
                password_tip: '',
                newpassword_tip: '',
                repassword_tip: '',
                isTipShow: true,
                right_password: false
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!this.isPc){
                //window.parent.postMessage({register_type:'register'},'*');
                /*var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }*/

                if(window.innerWidth < 760){
                    this.isTipShow = false;
                }

            }else {

            }
        },
        methods: {
            checkPassword () {
                if(!this.form.password){
                    this.errMsg = '请输入登录密码';
                    return;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/change_mobile1',
                    method:'post',
                    data: {
                        password: this.form.password
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.right_password = true;
                        /*if(!!_this.isPc){
                            _this.right_password = true;
                        }else {
                            _this.$router.push({
                                name: 'modify_mobile2'
                            });
                        }*/
                    }else{
                        _this.errMsg = res.msg;
                        _this.right_password = false;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.right_password = false;
                });
            },
            handleSubmit () {
                if(!this.form.password){
                    this.errMsg = '密码不能为空';
                    this.password_tip = '密码不能为空';
                    return;
                }else {
                    this.password_tip = '';
                }
                if(!this.right_password){
                    this.errMsg = '原密码不正确';
                    this.password_tip = '原密码不正确';
                    return;
                }else {
                    this.password_tip = '';
                }
                if(!this.form.newpassword){
                    this.errMsg = '新密码不能为空';
                    this.newpassword_tip = '新密码不能为空';
                    return;
                }else {
                    this.newpassword_tip = '';
                }
                if(!this.form.repassword){
                    this.errMsg = '确认密码不能为空';
                    this.repassword_tip = '确认密码不能为空';
                    return;
                }else {
                    this.repassword_tip = '';
                }
                if(!this.checkpassword(this.form.newpassword, this.form.repassword)){
                    return
                }
                if(this.form.newpassword !== this.form.repassword){
                    this.errMsg = '新密码和确认密码不一致';
                    this.repassword_tip = '新密码和确认密码不一致';
                    return;
                }else {
                    this.repassword_tip = '';
                }
                this.errMsg = '';
                this.mobile_tip = '';
                this.email_tip = '';
                this.captcha_tip = '';

                var dataIn = {
                    type: 'change_password',
                    newpassword: this.form.newpassword,
                    newpassword2: this.form.repassword,
                    oldpassword: this.form.password
                };

                window.parent.postMessage({register_type: dataIn},'*');
                return false;
                var dataIn = {
                    newpassword: this.form.newpassword,
                    newpassword2: this.form.repassword,
                    oldpassword: this.form.password,
                    onlypassword: true,  //固定参数
                    passwordsubmit: true, //固定参数
                    pwdsubmit: true,  //固定参数
                };
                dataIn = qs.stringify(dataIn)
                //console.log(dataIn);

                var _this = this;
                util.ajax({
                    url: 'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=myprofile&mod=spacecp&ac=profile',//'/api/v1/auth/recovery_password/by_email',
                    method:'post',
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    data: dataIn,
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.$router.push({
                            name: 'security'
                        });
                    }else{
                        _this.errMsg = res.msg;
                        _this.repassword_tip = res.data.msg;
                        //_this.captcha_tip = res.msg;
                        //_this.form.captcha = '';
                        //_this.geetest();
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.repassword_tip = err.msg;
                    /*_this.captcha_tip = err.msg;
                    _this.form.captcha = '';
                    _this.geetest();*/
                });
            },
            checkpassword (pw1, pw2) {
                var minlength = 8;
                var maxlength = 32;
                var strongpw = [1,2,3];
                this.errMsg = '';
                this.password_tip = '';
                this.repassword_tip = '';
                if(!pw1 && !pw2) {
                    return;
                }
                if(pw1.length < minlength) {
                    this.errMsg = '密码太短，不得少于 '+minlength+' 个字符';
                    this.newpassword_tip = '密码太短，不得少于 '+minlength+' 个字符';
                    this.isRegistet = false;
                    return false;
                }
                /*if(strongpw) {
                    var strongpw_error = false, j = 0;
                    var strongpw_str = new Array();
                    for(var i in strongpw) {
                        if(strongpw[i] === 1 && !pw1.match(/\d+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '数字';
                            j++;
                        }
                        if(strongpw[i] === 2 && !pw1.match(/[a-z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '小写字母';
                            j++;
                        }
                        if(strongpw[i] === 3 && !pw1.match(/[A-Z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '大写字母';
                            j++;
                        }
                        if(strongpw[i] === 4 && !pw1.match(/[^A-Za-z0-9]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '特殊符号';
                            j++;
                        }
                    }
                    if(strongpw_error) {
                        this.errMsg = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.newpassword_tip = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.isRegistet = false;
                        return false;
                    }
                }*/
                /////////////////////////////////////////////////////
                //  密码规则：
                //  1     ~`@#$%^&*()-_+={}[]|:;"'<,.>?/\
                //  2     0-9
                //  3     a-z
                //  4     A-Z
                //  字符串控制在1-4中，且必须包含其中2项，长度8-32
                //  如果有这些以外的字符，就提示密码中包含非法字符
                //////////////////////////////////////////////////
                var reg1 = /^[A-Za-z0-9~`@#$%^&*\(\)\-\_\+\=\{\}\[\]\|\:\;\"\'\<\,\.\>?\/\\]{8,32}$/
                var reg2 = /^(?:\d+|[a-z]+|[A-Z]+|[~`@#$%^&*\(\)\-\_\+\=\{\}\[\]\|\:\;\"\'\<\,\.\>?\/\\]+)$/
                if(reg2.test(pw1)){
                    this.errMsg = '大写字母、小写字母、数字、特殊符号，至少包含其中的两类。';
                    this.newpassword_tip = '大写字母、小写字母、数字、特殊符号，至少包含其中的两类。'
                    this.isRegistet = false;
                    return false;
                }
                if(!reg1.test(pw1)){
                    this.errMsg = '新密码中包含非法字符。';
                    this.newpassword_tip = '新密码中包含非法字符。'
                    this.isRegistet = false;
                    return false;
                }

                if(!pw2){
                    this.errMsg = '请输入确认密码！';
                    this.repassword_tip = '请输入确认密码！';
                    this.isRegistet = false;
                    return false;
                }
                if(pw1 != pw2) {
                    this.errMsg = '两次输入的密码不一致';
                    this.repassword_tip = '两次输入的密码不一致';
                    this.isRegistet = false;
                    return false;
                }
                return true;
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
