<style lang="less">
    @import 'register.less';
    @import '../login.less';
    @import '../../libs/jigsaw.css';
</style>

<template>
    <div class="captcha">
        <div class="register-con" v-if="!!isPc" style="height: 400px;overflow: hidden;padding: 20px;">
            <Button @click="show_captcha">验 证</Button>
        </div>
        <div class="register-con mobile_r_c binding-con" v-if="!isPc" style="width: 100%;background: #fff;">
            <Card :bordered="false" style="background-color: #fff;padding: 20px;">
                <Button @click="show_captcha">验 证</Button>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'captcha',
        data () {
            return {
                access_token: this.$store.state.user.access_token,
                isPc: true,

            };
        },
        mounted () {

        },
        methods: {
            show_captcha(){
                var _this = this;
                var captcha = new TencentCaptcha('2032354718', function(res) {
                    console.log(res)
                    _this.send_captcha(res)
                });
                captcha.show();
            },
            send_captcha(obj){
                var dataIn = {
                    ticket: obj.ticket,
                    randstr: obj.randstr
                }
                var _this = this;
                util.ajax({
                    url:'api/v1/auth/tencent/verify',
                    method:'post',
                    data: dataIn,
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                         _this.$Message.success('提交成功！');
                    }else{
                        _this.$Message.error('提交失败！');
                    }
                }).catch(function (err) {
                    console.log(err)
                });
            }
        }
    };
</script>

<style>

</style>
