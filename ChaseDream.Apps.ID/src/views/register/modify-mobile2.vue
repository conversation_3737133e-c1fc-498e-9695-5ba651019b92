<style lang="less">
    @import 'register.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 280px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-form">
                        <Form :label-width="132">
                            <FormItem prop="mobile" label="手机号" class="mobileItem required">
                                <Input v-model="form.mobile" class="ipt_m"></Input>
                                <span class="tip" v-if="isTipShow">{{mobile_tip}}</span>
                                <Poptip title="" placement="bottom-start" class="pc_citys" v-model="visible">
                                    <a>+{{form.area_code}} <Icon type="chevron-down" size="10"></Icon></a>
                                    <div slot="content">
                                        <Tabs value="use">
                                            <TabPane label="常用" name="use">
                                                <div class="citys">
                                                    <span v-for="item in area_code_use" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="ABCDEF" name="abcdef">
                                                <div class="citys">
                                                    <span v-for="item in area_code_abcdef" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="GHJ" name="ghij">
                                                <div class="citys">
                                                    <span v-for="item in area_code_ghij" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="KLMN" name="klmn">
                                                <div class="citys">
                                                    <span v-for="item in area_code_klmn" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="PQISTUVW" name="opqistuvw">
                                                <div class="citys">
                                                    <span v-for="item in area_code_opqistuvw" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="XYZ" name="xyz">
                                                <div class="citys">
                                                    <span v-for="item in area_code_xyz" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                        </Tabs>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="validate_code" class="validate_code required" label="验证码">
                                <Input v-model="form.captcha"></Input>
                                <Button type="ghost" @click="getCaptcha" v-if="!!isSentcode">获取验证码</Button>
                                <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                                <span class="tip" v-if="isTipShow">{{captcha_tip}}</span>
                            </FormItem>
                            <FormItem class="re-btn">
                                <span class="tip" v-if="!!errMsg && !isTipShow" style="display: block;">{{errMsg}}</span>
                                <Button @click="handleSubmit" type="success" long>确定</Button>
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="modify">
                <p slot="title">
                    <router-link to="/register/security">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    修改手机号
                </p>
                <div class="re_tip" v-if="!!errMsg">
                    <p>
                        <span><img src="../../images/register/icon_tips.png" alt="">
                        </span>
                        {{errMsg}}
                    </p>
                </div>
                <div class="form-con">
                    <Form ref="nameForm" :model="form" class="findpasswordForm" :label-width="60">
                        <FormItem prop="mobile" label="手机号" class="mobileItem">
                            <div class="area_code_btn" @click="showpannel">+{{form.area_code}} <Icon type="chevron-down" size="10"></Icon></div>
                            <Input v-model="form.mobile"></Input>
                        </FormItem>
                        <FormItem prop="validate_code" class="validate_code" label="验证码">
                            <Input v-model="form.captcha"></Input>
                            <Button type="ghost" @click="show_captcha" v-if="!!isSentcode">获取验证码</Button>
                            <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                        </FormItem>
                        <FormItem class="re-btn">
                            <Button @click="handleSubmit" type="success" long>确定</Button>
                        </FormItem>
                    </Form>
                </div>
            </Card>
        </div>
        <div class="area-code" v-if="!!showCodep">
            <div class="mask" @click="showCodep = false"></div>
            <div class="code-inner">
                <div class="top">
                    国家/地区
                    <span>代码</span>
                </div>
                <p class="used">常用</p>
                <div class="used-list">
                    <Button @click="getAreaCode(86)">中国<br>+86</Button>
                    <Button @click="getAreaCode(852)">香港<br>+852</Button>
                    <Button @click="getAreaCode(886)">台湾<br>+886</Button>
                    <Button @click="getAreaCode(853)">澳门<br>+853</Button>
                    <Button @click="getAreaCode(1)">美加<br>+1</Button>
                    <Button @click="getAreaCode(44)">英国<br>+44</Button>
                    <Button @click="getAreaCode(33)">法国<br>+33</Button>
                    <Button @click="getAreaCode(49)">德国<br>+49</Button>
                    <Button @click="getAreaCode(61)">澳洲<br>+61</Button>
                    <Button @click="getAreaCode(82)">韩国<br>+82</Button>
                    <Button @click="getAreaCode(65)">新加坡<br>+65</Button>
                    <Button @click="getAreaCode(81)">日本<br>+81</Button>
                </div>
                <div class="detail-list">
                    <p>详细列表</p>
                    <Menu active-name="86" @on-select="getAreaCode">
                        <MenuGroup :title="code.letter" v-for="code in code_group" :key="code.index">
                            <MenuItem :name="list.area_code" v-for="list in code.data" :key="list.index">
                                {{list.name}}
                                <span>+{{list.area_code}}</span>
                            </MenuItem>
                        </MenuGroup>
                    </Menu>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    export default {
        name: 'modify_mobile2',
        data () {
            return {
                form: {
                    mobile: '', //test
                    area_code: 86,
                    captcha: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isPc: true,
                isSentcode: true,
                count: 60,
                showCodep: false,
                code_group: [],
                area_code_use: [],
                area_code_abcdef: [],
                area_code_ghij: [],
                area_code_klmn: [],
                area_code_opqistuvw: [],
                area_code_xyz: [],
                mobile_tip: '',
                captcha_tip: '',
                visible: false,
                isTipShow: true
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!Cookies.get('area_code')){
                this.form.area_code = Cookies.get('area_code');
            }
            if(!!this.isPc){
                var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }
                this.area_code_use = area.area_code_use;
                this.area_code_abcdef = area.area_code_abcdef;
                this.area_code_ghij = area.area_code_ghij;
                this.area_code_klmn = area.area_code_klmn;
                this.area_code_opqistuvw = area.area_code_opqistuvw;
                this.area_code_xyz = area.area_code_xyz;

                if(window.innerWidth < 760){
                    this.isTipShow = false;
                }
            }else {

            }

            this.code_group = area.area_code_group;


        },
        methods: {
            show_captcha(){
                if(!this.form.mobile || !this.form.area_code){
                    this.errMsg = '请输入手机号码';
                    this.mobile_tip = '请输入手机号码';
                    return false;
                }

                var _this = this;
                TCaptchaModify();
                var captcha = new TencentCaptcha('2032354718', function(res) {
                    _this.getCaptcha(res)
                });
                captcha.show();
                function TCaptchaModify(){
                    if(!!_this.isPc){
                        var dom = document.getElementById('tcaptcha_transform');
                        if(!!dom && !!dom.innerHTML){
                            dom.style.top = '-75px';
                            dom.style.transform = 'scale(0.6,0.6)';
                        }else {
                            setTimeout(function () {
                                TCaptchaModify();
                            },600)
                        }
                    }
                }
            },
            getCaptcha (obj){
                if(!obj.ticket || !obj.ticket){
                    this.errMsg = '需要滑动验证通过后才能发送短信验证码';
                    return false;
                }

                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/sms/send/change_mobile',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: _this.form.area_code,
                        ticket: obj.ticket,
                        randstr: obj.randstr,
                        platform: !!this.isPc ? 1 : 2
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.errMsg = '';
                        _this.mobile_tip = '';
                        _this.captcha_tip = '';
                        _this.countdown();
                        _this.$Message.info(res.data.data.message);
                    }else{
                        _this.errMsg = res.msg;
                        _this.captcha_tip = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.captcha_tip = err.msg;
                });
            },
            handleSubmit () {
                if(!this.form.mobile){
                    this.errMsg = '手机号码不能为空';
                    this.mobile_tip = '手机号码不能为空';
                    return;
                }else if(!this.form.captcha){
                    this.errMsg = '请输入正确的验证码';
                    this.captcha_tip = '请输入正确的验证码';
                    return;
                }

                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/change_mobile2',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: _this.form.area_code,
                        captcha: _this.form.captcha
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.$Message.info('修改成功!');
                        setTimeout(function () {
                            if(!!_this.isPc){
                                window.parent.postMessage({register_type: 'logout'},'*');
                            }else {
                                _this.$router.push({
                                    name: 'login'
                                });
                            }
                        },1000);
                    }else{
                        _this.errMsg = res.msg;
                        if(res.msg == '验证码错误'){
                            _this.captcha_tip = res.msg;
                        }else {
                            _this.mobile_tip = res.msg;
                        }
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.mobile_tip = err.msg;
                    _this.form.captcha = '';
                });
            },
            countdown () {
                this.isSentcode = false;
                var _this = this;
                clearInterval(timer);
                var timer = setInterval(function () {
                    if(_this.count === 1){
                        clearInterval(timer);
                        _this.isSentcode = true;
                    }else {
                        _this.count--;
                    }
                },1000);
            },
            showpannel (){
                this.showCodep = true;
            },
            getAreaCode (n){
                this.form.area_code = n;
                this.showCodep = false;
                this.visible = false;
                Cookies.set('area_code', n);
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
