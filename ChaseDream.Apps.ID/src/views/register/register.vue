<style lang="less">
    @import 'register.less';
    @import '../../libs/jigsaw.css';
    @import "../../template/pc_layout.less";
</style>

<template>
    <div class="register" style="overflow-y: auto">
        <div class="register-con" v-if="!!isPc" style="height: 400px;overflow: hidden;">
            <!--<iframe src="https://forum.chasedream.com/member.php?mod=register" frameborder="0" style="position: absolute;left: 0;top: 0;width: 100%;height: 767px;z-index: 1;"></iframe>-->
            <!--<pc_header></pc_header>-->
            <div class="r">
                <div class="r-c" style="min-height: 400px;background: #fff;">
                    <div class="r-tab">
                        <div class="tab on"><span>1</span>手机注册</div>
                       <!-- <div class="tab"><span>2</span>绑定微信（选填）</div>-->
                        <div class="tab"><span>2</span>论坛昵称</div>
                    </div>
                    <div class="r-form">
                        <!--<div class="re_tip" v-if=" !!errMsg || !!isRegistet">
                            <p>
                                <span><img src="../../images/register/icon_tips.png" alt=""></span>
                                <span v-if="isRegistet">此手机号码已经注册，您可以直接<router-link to="/login">登录</router-link>，或<router-link to="/register/findpassword">找回密码</router-link></span>
                                    {{errMsg}}
                            </p>
                        </div>-->
                        <Form :label-width="132">
                            <FormItem label="手机号:" class="required"> <!--class="er"-->
                                <Input v-model="form.mobile" class="ipt_m" @on-change="check_num"></Input>
                                <span class="tip">{{mobile_tip}}</span>  <!--请输入邮箱地址-->
                                <span class="tip" v-if="isRegistet">此手机号码已经注册，您可以直接<a href="javascript:;" @click="toLogin">登录</a>，或<a href="javascript:;" @click="toFindPassword">找回密码</a></span>
                                <Poptip title="" placement="bottom-start" class="pc_citys" v-model="visible">
                                    <a>+{{form.area_code}} <Icon type="chevron-down" size="10"></Icon></a>
                                    <div slot="content">
                                        <Tabs value="use">
                                            <TabPane label="常用" name="use">
                                                <div class="citys">
                                                    <span v-for="item in area_code_use" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="ABCDEF" name="abcdef">
                                                <div class="citys">
                                                    <span v-for="item in area_code_abcdef" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="GHJ" name="ghij">
                                                <div class="citys">
                                                    <span v-for="item in area_code_ghij" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="KLMN" name="klmn">
                                                <div class="citys">
                                                    <span v-for="item in area_code_klmn" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="PQISTUVW" name="opqistuvw">
                                                <div class="citys">
                                                    <span v-for="item in area_code_opqistuvw" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                            <TabPane label="XYZ" name="xyz">
                                                <div class="citys">
                                                    <span v-for="item in area_code_xyz" v-text="item.name + ' +' + item.area_code" :key="item.index" @click="getAreaCode(item.area_code)" :class="form.area_code === item.area_code ? 'on' : ''" :title="item.name + ' +' + item.area_code"></span>
                                                </div>
                                            </TabPane>
                                        </Tabs>
                                    </div>
                                </Poptip>
                            </FormItem>
                            <FormItem prop="validate_code" class="validate_code required" label="验证码:">
                                <Input v-model="form.captcha" @on-blur="check_mobile"></Input>
                                <Button type="ghost" @click="getCaptcha" v-if="!!isSentcode">获取验证码</Button>
                                <Button disabled style="background: #f7f7f7;color: #bbbec4;" v-if="!!isSentcode && !is_mc">获取验证码</Button>
                                <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                                <span class="tip">{{captcha_tip}}</span>
                            </FormItem>
                            <FormItem prop="password" label="登录密码:" class="required">
                                <Input type="password" v-model="form.password"></Input>
                                <span class="tip">{{password_tip}}</span>
                            </FormItem>
                            <FormItem prop="repassword" label="确认密码:" class="required">
                                <Input type="password" v-model="form.repassword"></Input>
                                <span class="tip">{{repassword_tip}}</span>
                            </FormItem>
                            <FormItem class="sub_btn">
                                <Button type="success" @click="handleSubmit">下一步</Button>
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" v-show="!!showWXp" style="width: 100%;">
            <Card :bordered="false">
                <p slot="title">
                    <router-link to="/login">
                        <Icon type="close-round"></Icon>
                    </router-link>
                    手机注册
                    <router-link to="/login">
                        <span>登录</span>
                    </router-link>
                </p>
                <div class="re_tip" v-if=" !!errMsg || !!isRegistet">
                    <p>
                        <span><img src="../../images/register/icon_tips.png" alt="">
                        </span>
                        <span v-if="isRegistet">此手机号码已经注册，您可以直接<router-link to="/login">登录</router-link>，或<router-link to="/register/findpassword">找回密码</router-link></span>
                        {{errMsg}}
                    </p>
                </div>
                <div class="form-con">
                    <Form ref="loginForm" :model="form" :rules="rules" :label-width="80">
                        <FormItem prop="mobile" label="手机号" class="mobileItem">
                            <!--<Select v-model="form.area_code" style="width:200px">
                                <Option :value="code.area_code" :label="code.area_code" v-for="code in codes" :key="code.index">
                                    <span>{{code.name}}</span>
                                    <span style="float:right;color:#ccc">{{code.area_code}}</span>
                                </Option>
                            </Select>-->
                            <div class="area_code_btn" @click="showpannel">+{{form.area_code}} <Icon type="chevron-down" size="10"></Icon></div>
                            <Input v-model="form.mobile" @on-change="check_num"></Input>
                        </FormItem>
                        <FormItem prop="validate_code" class="validate_code" label="验证码">
                            <Input v-model="form.captcha" @on-blur="check_mobile"></Input>
                            <Button type="ghost" @click="getCaptcha" v-if="!!isSentcode && !!is_mc">获取验证码</Button>
                            <Button disabled style="background: #f7f7f7;color: #bbbec4;" v-if="!!isSentcode && !is_mc">获取验证码</Button>
                            <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                        </FormItem>
                        <FormItem prop="password" label="登录密码">
                            <Input type="password" v-model="form.password"></Input>
                        </FormItem>
                        <FormItem prop="repassword" label="确认密码">
                            <Input type="password" v-model="form.repassword"></Input>
                        </FormItem>
                        <!--<p class="l_err_msg">{{errMsg}}</p>-->
                        <FormItem class="re-btn">
                            <Button @click="handleSubmit" type="success" long>下一步</Button>
                        </FormItem>
                        <input type="hidden" id="cd_copy" value="12345">
                    </Form>
                </div>
            </Card>
        </div>
        <div class="area-code" v-if="!!showCodep">
            <div class="mask" @click="showCodep = false"></div>
            <div class="code-inner">
                <div class="top">
                    国家/地区
                    <span>代码</span>
                </div>
                <p class="used">常用</p>
                <div class="used-list">
                    <Button @click="getAreaCode(86)">大陆<br>+86</Button>
                    <Button @click="getAreaCode(852)">香港<br>+852</Button>
                    <Button @click="getAreaCode(853)">澳门<br>+853</Button>
                    <Button @click="getAreaCode(886)">台湾<br>+886</Button>
                    <Button @click="getAreaCode(1)">美加<br>+1</Button>
                    <Button @click="getAreaCode(44)">英国<br>+44</Button>
                    <Button @click="getAreaCode(33)">法国<br>+33</Button>
                    <Button @click="getAreaCode(49)">德国<br>+49</Button>
                    <Button @click="getAreaCode(61)">澳洲<br>+61</Button>
                    <Button @click="getAreaCode(82)">韩国<br>+82</Button>
                    <Button @click="getAreaCode(65)">新加坡<br>+65</Button>
                    <Button @click="getAreaCode(81)">日本<br>+81</Button>
                </div>
                <div class="detail-list">
                    <p>详细列表</p>
                    <Menu active-name="86" @on-select="getAreaCode">
                        <MenuGroup :title="code.letter" v-for="code in code_group" :key="code.index">
                            <MenuItem :name="list.area_code" v-for="list in code.data" :key="list.index">
                                {{list.name}}
                                <span>+{{list.area_code}}</span>
                            </MenuItem>
                        </MenuGroup>
                    </Menu>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    import jigsaws from '@/libs/jigsaw.js';
    import pc_header from '../../template/pc-header.vue';
    import pc_footer from '../../template/pc-footer.vue';
    export default {
        components: {
            pc_header,
            pc_footer
        },
        data () {
            return {
                form: {
                    mobile: '', //test
                    password: '', //321321
                    repassword: '',
                    area_code: 86,
                    captcha: '',
                    openid: '',
                    unionid: '',
                    nickname: ''
                },
                rules: {
                    mobile: [
                        /*{ required: true, message: '手机号不能为空', trigger: 'blur' },
                        {type: 'number', min: 9, message: '请输入正确的手机号', trigger: 'blur' }*/
                    ],
                    /*password: [
                        { required: true, message: '密码不能为空', trigger: 'blur' },
                        { type: 'string', min: 6, message: '密码不能小于6个字符', trigger: 'blur' }
                    ]*///,
                    /* validate_code: [
                     { required: true, message: '验证码不能为空', trigger: 'blur' },
                     ]*/
                },
                access_token: this.$store.state.user.access_token,
                bgImg: '',
                errMsg: '',
                isRegistet: false,
                codeImg: '',
                code_val: 86,
                codes: [],
                code_group: [],
                area_code_use: [],
                area_code_abcdef: [],
                area_code_ghij: [],
                area_code_klmn: [],
                area_code_opqistuvw: [],
                area_code_xyz: [],
                isValidate: false,
                isShowImg: false,
                isPc: true,
                isSentcode: true,
                count: 60,
                showCodep: false,
                showWXp: true,
                visible: false,
                mobile_tip: '',
                captcha_tip: '',
                password_tip: '',
                repassword_tip: '',
                is_mc: true,
                ischecked_m: false,
                openid: '',
                unionid: '',
                nickname: ''
            };
        },
        mounted () {
            //window.parent.postMessage({refresh:'id'},'*');

            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!Cookies.get('area_code')){
                this.form.area_code = Cookies.get('area_code');
            }

            if (!!this.isPc && self.innerHeight > '400') {
                window.location.href = 'https://forum.chasedream.com/member.php?mod=register';
            }
            //this.showWXp = this.$store.state.register.showWXp;

            this.codes = area.area_code;


            if(!!this.isPc){
                window.parent.postMessage({register_type:'register'},'*');
                var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }
                this.area_code_use = area.area_code_use;
                this.area_code_abcdef = area.area_code_abcdef;
                this.area_code_ghij = area.area_code_ghij;
                this.area_code_klmn = area.area_code_klmn;
                this.area_code_opqistuvw = area.area_code_opqistuvw;
                this.area_code_xyz = area.area_code_xyz;
               /* var _this = this;
                util.ajax({
                    url:'https://bird.ioliu.cn/v1?url=http://www.bing.com/HPImageArchive.aspx?format=js&idx=0&n=1',
                    method:'get',
                    headers: {
                        'Content-type': 'application/json; charset=UTF-8',
                    },
                }).then(function (res) {
                    //console.log(res.data.images[0].url);
                    _this.bgImg= 'url(https://www.bing.com/' + res.data.images[0].url + ')';
                }).catch(function (err) {
                    console.log(err)
                });
                //this.getValidate_code();
                var _this = this;*/

                /*jigsaws.init(document.getElementById('captcha'),
                    function() {
                        //document.getElementById('msg').innerHTML = '验证成功！'
                        _this.isValidate = true;
                        document.getElementsByClassName('ivu-poptip-popper')[0].style.display = 'none'
                        document.getElementsByClassName('ivu-poptip')[0].style.display = 'none';
                        _this.errMsg = '';
                    },function () {
                        // document.getElementById('msg').innerHTML = '验证失败！'
                        _this.isValidate = false;
                    })*/
                /*window.addEventListener('message',function(e){
                    console.log(e)
                    if(!!e.data.openid){
                        this.form.openid = e.data.openid;
                    }
                },false);*/
                /*if(window.location.href.split('openid=').length > 1){
                    this.form.openid = window.location.href.split('openid=')[1];
                }*/
                this.form.openid = window.localStorage.getItem('openid');
                this.form.unionid = window.localStorage.getItem('unionid');
                this.form.nickname = window.localStorage.getItem('nickname');
                window.localStorage.setItem('openid', null);
                window.localStorage.setItem('unionid', null);
                window.localStorage.setItem('nickname', null);
                console.log(this.form)
            }else {
                this.code_group = area.area_code_group;

                if(this.isWeiXin()){
                    if(window.location.href.split('openid=').length > 1){
                        this.openid = window.location.href.split('openid=')[1].split("&")[0];
                        this.unionid = window.location.href.split('unionid=')[1].split("&")[0];
                        this.nickname = window.location.href.split('nickname=')[1];
                    }
                    if(!this.openid){
                        this.showWXp = this.$store.state.register.showWXp;
                        this.showWX = false;
                        this.isWXlogin();
                    }
                }
            }
            console.log(this.form.area_code === '86')
            if(!!this.form.area_code && this.form.area_code === '86'){
                this.is_mc = false;
            }
        },
        methods: {
            getCaptcha (){
                this.errMsg = '';
                if(!this.form.mobile || !this.form.area_code){
                    if(!!this.isPc){
                        this.mobile_tip = '请输入手机号码';
                        return;
                    }else {
                        this.errMsg = '请输入手机号码';
                        this.isRegistet = false;
                        return;
                    }
                }else {
                    if(!!this.isPc){
                        this.mobile_tip = '';
                    }
                }
                this.show_captcha();
                /*var _this = this;
                util.ajax({
                    url:'/api/v1/auth/sms/send/register',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: parseInt(_this.form.area_code)
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.errMsg = '';
                        _this.captcha_tip = '';
                        _this.countdown();
                        _this.$Message.info(res.data.data.message);
                    }else{
                        _this.errMsg = res.msg;
                        _this.captcha_tip = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.captcha_tip = err.msg;
                });*/
            },
            show_captcha(){
                var _this = this;
                TCaptchaModify();
                var captcha = new TencentCaptcha('2032354718', function(res) {
                    _this.captchaSubmit(res)
                });
                captcha.show();
                function TCaptchaModify(){
                    if(!!_this.isPc){
                        var dom = document.getElementById('tcaptcha_transform');
                        if(!!dom && !!dom.innerHTML){
                            dom.style.top = '-75px';
                            dom.style.transform = 'scale(0.6,0.6)';
                        }else {
                            setTimeout(function () {
                                TCaptchaModify();
                            },600)
                        }
                    }
                }
            },
            captchaSubmit (obj){
                if(!obj.ticket || !obj.ticket){
                    this.errMsg = '需要滑动验证通过后才能发送短信验证码';
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/sms/send/register',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: parseInt(_this.form.area_code),
                        ticket: obj.ticket,
                        randstr: obj.randstr,
                        platform: !!this.isPc ? 1 : 2
                    },
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        _this.errMsg = '';
                        _this.captcha_tip = '';
                        _this.countdown();
                        _this.$Message.info(res.data.data.message);
                    }else{
                        _this.errMsg = res.msg;
                        _this.captcha_tip = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.captcha_tip = err.msg;
                });
            },
            check_num (){
                if(parseInt(this.form.area_code) === 86){
                    if(this.form.mobile.length === 11){
                        this.is_mc = true;
                    }else {
                        this.is_mc = false;
                    }
                }else  {
                    this.is_mc = true;
                }
            },
            check_mobile (){
                if(!this.form.mobile){
                    return false;
                }
                if(!this.form.captcha){
                    return false;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/sms',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: parseInt(_this.form.area_code),
                        captcha: _this.form.captcha
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.exist){
                            _this.errMsg = '';
                            _this.captcha_tip = '';
                            _this.isRegistet = true;
                        }else{
                            //_this.$store.commit('setData',_this.form);
                            //window.parent.postMessage({register_type:'nickName'},'*');
                            /*_this.$router.push({
                                name: 'nickname'
                            });*/
                            _this.isRegistet = false;
                            _this.errMsg = '';
                            _this.captcha_tip = '';
                        }
                    }else{
                        _this.errMsg = res.data.msg;
                        _this.captcha_tip = err.msg;
                        _this.isRegistet = false;
                    }
                }).catch(function (err) {
                    console.log(err)
                    //if(err.msg === '验证码错误'){
                        _this.captcha_tip = err.msg;
                    //}
                    _this.errMsg = err.msg;
                    _this.isRegistet = false;
                    //_this.form.captcha = '';
                });
            },
            handleSubmit () {
                if(!this.form.mobile){
                    if(!!this.isPc){
                        this.mobile_tip = '手机号码不能为空';
                    }else {
                        this.errMsg = '手机号码不能为空';
                    }
                    this.isRegistet = false;
                    return;
                }else {
                    this.mobile_tip = '';
                }
                if(!this.form.captcha){
                    if(!!this.isPc){
                        this.captcha_tip = '请输入正确的验证码';
                    }else {
                        this.errMsg = '请输入正确的验证码';
                    }

                    this.isRegistet = false;
                    return;
                }else {
                    this.captcha_tip = '';
                }
                if(!this.form.password){
                    if(!!this.isPc){
                        this.password_tip = '登录密码不能为空';
                    }else {
                        this.errMsg = '登录密码不能为空';
                    }

                    this.isRegistet = false;
                    return;
                }else {
                    this.password_tip = '';
                }
                if(!this.checkpassword(this.form.password, this.form.repassword)){
                    return
                }else {
                    this.repassword_tip = '';
                    this.password_tip = '';
                }
                if(!this.form.repassword){
                    if(!!this.isPc){
                        this.repassword_tip = '确认密码不能为空';
                    }else {
                        this.errMsg = '确认密码不能为空';
                    }

                    this.isRegistet = false;
                    return;
                }else {
                    if(!!this.isPc){
                        this.repassword_tip = '';
                    }else {
                        this.errMsg = '';
                    }

                }

                //this.checkpassword(this.form.password, this.form.repassword);


                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/sms',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: parseInt(_this.form.area_code),
                        captcha: _this.form.captcha
                    },
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.exist){
                            _this.errMsg = '';
                            _this.captcha_tip = '';
                            _this.isRegistet = true;
                        }else{
                            if(_this.isWeiXin()){
                                if(!!_this.openid){
                                    _this.form.openid = _this.openid;
                                }
                                if(!!_this.unionid){
                                    _this.form.unionid = _this.unionid;
                                }
                                if(!!_this.nickname){
                                    _this.form.nickname = _this.nickname;
                                }
                                console.log(1)
                            }
                            _this.$store.commit('setData',_this.form);
                            //window.parent.postMessage({register_type:'nickName'},'*');
                            console.log(4)
                            _this.$router.push({
                                name: 'nickname'
                            });
                        }
                    }else{
                        _this.errMsg = res.data.msg;
                        _this.captcha_tip = res.data.msg;
                        _this.isRegistet = false;
                    }
                }).catch(function (err) {
                    //console.log(err)
                    //if(err.msg === '验证码错误'){
                        _this.captcha_tip = err.msg;
                    //}
                    _this.errMsg = err.msg;
                    _this.isRegistet = false;
                    //_this.form.captcha = '';
                });
            },
            checkpassword (pw1, pw2) {
                var minlength = 8;
                var maxlength = 32;
                var strongpw = [1,2,3,4];
                this.errMsg = '';
                this.repassword_tip = '';
                if(!pw1 && !pw2) {
                    return;
                }
                if(pw1.length < minlength) {
                    this.errMsg = '密码太短，不得少于 '+minlength+' 个字符';
                    this.password_tip = '密码太短，不得少于 '+minlength+' 个字符';
                    this.isRegistet = false;
                    return false;
                }
                /*if(strongpw) {
                    var strongpw_error = false, j = 0;
                    var strongpw_str = new Array();
                    for(var i in strongpw) {
                        if(strongpw[i] === 1 && !pw1.match(/\d+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '数字';
                            j++;
                        }
                        if(strongpw[i] === 2 && !pw1.match(/[a-z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '小写字母';
                            j++;
                        }
                        if(strongpw[i] === 3 && !pw1.match(/[A-Z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '大写字母';
                            j++;
                        }
                        if(strongpw[i] === 4 && !pw1.match(/[^A-Za-z0-9]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '特殊符号';
                            j++;
                        }
                    }

                    if(strongpw_error) {
                        this.errMsg = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.password_tip = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        //大写字母、小写字母、数字、特殊符号，至少包含其中的两类。
                        this.isRegistet = false;
                        return false;
                    }
                }*/
                /////////////////////////////////////////////////////
                //  密码规则：
                //  1     ~`@#$%^&*()-_+={}[]|:;"'<,.>?/\
                //  2     0-9
                //  3     a-z
                //  4     A-Z
                //  字符串控制在1-4中，且必须包含其中2项，长度8-32
                //  如果有这些以外的字符，就提示密码中包含非法字符
                //////////////////////////////////////////////////
                var reg1 = /^[A-Za-z0-9~`@#$%^&*\(\)\-\_\+\=\{\}\[\]\|\:\;\"\'\<\,\.\>?\/\\]{8,32}$/
                var reg2 = /^(?:\d+|[a-z]+|[A-Z]+|[~`@#$%^&*\(\)\-\_\+\=\{\}\[\]\|\:\;\"\'\<\,\.\>?\/\\]+)$/
                //  reg1.test(pw1)
                if(reg2.test(pw1)){
                    this.errMsg = '大写字母、小写字母、数字、特殊符号，至少包含其中的两类。';
                    this.password_tip = '大写字母、小写字母、数字、特殊符号，至少包含其中的两类。'
                    this.isRegistet = false;
                    return false;
                }
                if(!reg1.test(pw1)){
                    this.errMsg = '密码中包含非法字符。';
                    this.password_tip = '密码中包含非法字符。'
                    this.isRegistet = false;
                    return false;
                }

                if(!pw2){
                    this.errMsg = '请输入确认密码！';
                    this.repassword_tip = '请输入确认密码！';
                    this.isRegistet = false;
                    return false;
                }
                if(pw1 != pw2) {
                    this.errMsg = '两次输入的密码不一致';
                    this.repassword_tip = '两次输入的密码不一致';
                    this.isRegistet = false;
                    return false;
                }

                return true;
            },
            countdown () {
                this.isSentcode = false;
                var _this = this;
                clearInterval(timer);
                var timer = setInterval(function () {
                    if(_this.count === 1){
                        clearInterval(timer);
                        _this.isSentcode = true;
                    }else {
                        _this.count--;
                    }
                },1000);
            },
            showpannel (){
                this.showCodep = true;
            },
            getAreaCode (n){
                this.form.area_code = n;
                this.showCodep = false;
                this.visible = false;
                Cookies.set('area_code', n);
                if(parseInt(this.form.area_code) === 86){
                    if(this.form.mobile.length === 11){
                        this.is_mc = true;
                    }else {
                        this.is_mc = false;
                    }
                }else {
                    this.is_mc = true;
                }
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            },
            isWXlogin(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/wechat/check_login',
                    method:'GET',
                }).then(function (res) {
                    //console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.login){
                            window.location.href = 'https://forum.chasedream.com/forum.php';
                        }else {
                            if(!_this.$store.state.register.openid){
                                _this.$store.commit('wx_url',util,_this);
                            }
                        }
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },
            toLogin (){
                window.parent.postMessage({register_type:'login'},'*');
            },
            toFindPassword (){
                window.parent.postMessage({register_type:'findPassword'},'*');
            }
        }
    };
</script>

<style>

</style>
