<style lang="less">
    @import 'register.less';
</style>

<template>
    <div class="register">
        <div class="register-con" v-if="!!isPc" style="height: 420px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-form">
                        <p class="mobile-num" align="center" v-if="!!oldEmail">
                            <strong style="font-weight: bold;font-size: 14px;color: #333;">您当前的Email为 <span style="font-size: 15px;">{{oldEmail}}</span></strong>
                        </p>
                        <Form :label-width="132">
                            <FormItem prop="password" label="登录密码" class="passwordItem required">
                                <Input v-model="form.password" type="password" @on-blur="checkPassword"></Input>
                                <span class="tip" v-if="isTipShow">{{errMsg}}</span>
                            </FormItem>
                            <FormItem prop="email" label="新Email" class="required">
                                <Input v-model="form.email"></Input>
                                <span class="tip" v-if="isTipShow">{{email_tip}}</span>
                            </FormItem>
                            <!--<FormItem prop="validate_code" class="validate_code required" label="验证码">
                                <Input v-model="form.captcha"></Input>
                                <Button type="ghost" @click="getCaptcha" v-if="!!isSentcode">获取验证码</Button>
                                <Button type="ghost" class="wait-btn" v-if="!isSentcode">{{count}}秒后重发</Button>
                                <span class="tip" v-if="isTipShow">{{captcha_tip}}</span>
                            </FormItem>-->
                            <FormItem class="re-btn">
                                <!--<span class="tip" v-if="!!errMsg && !isTipShow" style="display: block;">{{errMsg}}</span>-->
                                <Button @click="handleSubmit" type="success" long>确定</Button>
                            </FormItem>
                            <!--<FormItem class="re-btn">
                                <span class="tip" v-if="!!errMsg && !isTipShow" style="display: block;">{{errMsg}}</span>
                                <Button @click="handleSubmit" type="success" long>下一步</Button>
                            </FormItem>-->
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <!--<Card :bordered="false" class="modify">
                <p slot="title">
                    <router-link to="/register/security">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    修改手机号
                </p>
                <div class="re_tip" v-if="!!errMsg">
                    <p>
                        <span><img src="../../images/register/icon_tips.png" alt="">
                        </span>
                        {{errMsg}}
                    </p>
                </div>
                <p class="mobile-num">您当前的手机号为{{oldMobile}}</p>
                <div class="form-con">
                    <Form ref="nameForm" :model="form" class="findpasswordForm" :label-width="80">
                        <FormItem prop="password" label="登录密码" class="passwordItem">

                            <Input v-model="form.password" type="password"></Input>
                        </FormItem>
                        <FormItem class="re-btn">
                            <Button @click="handleSubmit" type="success" long>下一步</Button>
                        </FormItem>
                    </Form>
                </div>
            </Card>-->
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    //import area from '../area_code';
    export default {
        name: 'change_email',
        data () {
            return {
                form: {
                   /* mobile: '',
                    area_code: 86,
                    captcha: '',*/
                    password: '',
                    email: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                right_password: false,
                isPc: true,
                isSentcode: true,
                count: 60,
                oldEmail: '',
                isSentcode: true,
                count: 60,
                email_tip: '',
                captcha_tip: '',
                visible: false,
                isTipShow: true
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;

            if(!!this.isPc){
                /*var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }*/
                if(window.innerWidth < 760){
                    this.isTipShow = false;
                }
                this.getEmail();
                //this.form.email = window.parent.document.getElementById('emailnew').value;

            }else {

            }
            window.addEventListener('message', function (event) {
                console.log(event.data);
                //this.oldEmail = event.data;
                this.form.email = event.data;

            })

            //this.code_group = area.area_code_group;
            //this.getEmail();
        },
        methods: {
            getEmail (){
                window.parent.postMessage({register_type: 'get_email'},'*');
            },
            /*getCaptcha (){
                if(!this.form.mobile || !this.form.area_code){
                    this.errMsg = '请输入新Email';
                    this.mobile_tip = '请输入新Email';
                    return;
                }

                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/sms/send/change_mobile',
                    method:'post',
                    data: {
                        mobile: _this.form.mobile,
                        area_code: _this.form.area_code
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.errMsg = '';
                        _this.mobile_tip = '';
                        _this.captcha_tip = '';
                        _this.countdown();
                        _this.$Message.info(res.data.data.message);
                    }else{
                        _this.errMsg = res.msg;
                        _this.captcha_tip = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.captcha_tip = err.msg;
                });
            },*/
            checkPassword () {
                if(!this.form.password){
                    this.errMsg = '请输入登录密码';
                    return;
                }
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/change_mobile1',
                    method:'post',
                    data: {
                        password: this.form.password
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!_this.isPc){
                            _this.right_password = true;
                        }else {
                            /*_this.$router.push({
                                name: 'modify_mobile2'
                            });*/
                        }
                    }else{
                        _this.errMsg = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg ? err.msg : err.msg[0].message;
                });
            },
            handleSubmit () {
                if(!this.right_password){
                    this.errMsg = '请输入正确的登录密码';
                    return;
                }
                if(!this.form.email){
                    //this.errMsg = '手机号码不能为空';
                    this.email_tip = '新Email不能为空';
                    return;
                }
                /*else if(!this.form.captcha){
                    //this.errMsg = '请输入正确的验证码';
                    this.captcha_tip = '请输入正确的验证码';
                    return;
                }*/

                var  dataIn = {
                    type: 'change_email',
                    password: this.form.password,
                    email: this.form.email
                }

                window.parent.postMessage({register_type: dataIn},'*');

                return false;

                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/verify/change_mobile2',
                    method:'post',
                    data: {
                        email: _this.form.email,
                        /*area_code: _this.form.area_code,
                        captcha: _this.form.captcha*/
                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.$Message.info('修改成功!');
                        setTimeout(function () {
                            if(!!_this.isPc){
                                window.parent.postMessage({register_type: 'logout'},'*');
                            }else {
                                _this.$router.push({
                                    name: 'login'
                                });
                            }
                        },1000);
                    }else{
                        _this.errMsg = res.msg;
                        if(res.msg == '验证码错误'){
                            _this.captcha_tip = res.msg;
                        }else {
                            _this.mobile_tip = res.msg;
                        }
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.mobile_tip = err.msg;
                    _this.form.captcha = '';
                });
            },
            countdown () {
                this.isSentcode = false;
                var _this = this;
                clearInterval(timer);
                var timer = setInterval(function () {
                    if(_this.count === 1){
                        clearInterval(timer);
                        _this.isSentcode = true;
                    }else {
                        _this.count--;
                    }
                },1000);
            },
            showpannel (){
                this.showCodep = true;
            },
            /*getAreaCode (n){
                this.form.area_code = n;
                this.showCodep = false;
                this.visible = false;
                Cookies.set('area_code', n);
            },*/
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
