<style lang="less">
    @import 'register.less';
    @import '../login.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <!--<div class="register-con" v-if="!!isPc" style="height: 280px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-form">

                    </div>
                </div>
            </div>
        </div>-->
        <div v-if="!!isPc" style="height: 420px;background: #fff;">
            <bind-tip v-if="stap === 1" @on-stap="isWechatlogin ? set_stap(2) : set_stap(3)"></bind-tip>
            <div class="wc_info" v-if="stap === 2">
                <div>
                    <span>
                        <Icon type="wc-icon" size="20"></Icon>
                        微信
                    </span>
                    <Input v-model="wechat_name" readonly></Input>
                </div>
                <div class="wc_btns">
                    <Button type="primary" size="large" @click="set_stap(3)">换绑</Button>
                    <Button type="ghost" size="large" @click="unbunding">解绑</Button>
                </div>
                <!--<span></span>
                <span class="r_t">
                                下一个天亮

                </span>-->
            </div>
            <div v-if="stap === 3" style="width: 100%;height: 420px;background: #fff;">
                <scan  @on-stap="isWechatlogin ? set_stap(2) : set_stap(1)" @on-tip="set_stap" style="width:430px;height:300px;margin: 0 auto;"></scan>
            </div>
            <tip v-if="stap === 4" @on-stap="set_stap(3)" @on-stap-1="isWechatlogin ? set_stap(2) : set_stap(1)"></tip>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="findpassword">
                <p slot="title">
                    <router-link to="/register/security">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    微信绑定
                </p>
                <div class="form-con wechat">
                    <RadioGroup v-model="type" type="button" size="large" @on-change="handleSubmit">
                        <Radio label="1" v-if="!wechat_bind">
                            <Icon type="wc-icon" size="20"></Icon>
                            <span>微信</span>
                            <span class="r_t">
                                <span>未绑定</span>
                                <Icon type="ios-arrow-right" size="20"></Icon>
                            </span>
                        </Radio>
                        <Radio label="2" v-if="!!wechat_bind">
                            <Icon type="wc-icon" size="20"></Icon>
                            <span>微信</span>
                            <span class="r_t">
                                {{wechat_name}}
                                <Icon type="ios-arrow-right" size="20"></Icon>
                            </span>
                        </Radio>
                    </RadioGroup>
                </div>
            </Card>
        </div>
        <Modal
                title="提示"
                v-model="logoff"
                width="300"
                class-name="logoff_modal"
                :mask-closable="false"
                @on-cancel="closelogoff"
        >
            <p>您当前绑定的微信是“{{wechat_name}}”</p>
            <div slot="footer">
                <Button type="primary" size="large" @click="getqrcode">换绑</Button>
                <Button type="ghost" size="large" @click="unbunding">解绑</Button>
                <Button long type="text" size="large" @click="closelogoff">取消</Button>
            </div>
        </Modal>
        <Modal
                title="换绑不成功"
                v-model="binding_false"
                width="300"
                class-name="logoff_modal"
                :mask-closable="false"
                @on-cancel="binding_false = false"
        >
            <p>换绑前后的微信账号相同。如果您仍要换绑，请切换至微信端，更换微信账号后再进行换绑。</p>
            <div slot="footer">
                <Button type="primary" size="large" long @click="binding_false = false">确定</Button>
            </div>
        </Modal>
        <Modal class="wcLogin" v-model="wcLogin" width="360" @on-cancel="cancel">
            <div slot="header">
                <h3 align="center">注册/登录</h3>
                <p align="center">
                    首次登录需要完成两步操作，以后即可自动登录
                </p>
            </div>
            <div class="steps">
                <div class="item">
                    <p>第1步：先交二维码图片临时保存到手机相册</p>
                    <img :src="wcImg" alt="" width="100%">
                </div>
                <div class="item">
                    <p>第2步：在微信的"扫一扫"中打开刚保存的二维码图片</p>
                    <img src="../../images/register/login_wechat_scan.png" alt="" width="100%">
                </div>
            </div>
            <div slot="footer">
                <a href="weixin://scanqrcode">
                    <Button type="success" size="large" long>二维码存好了，打开微信扫一扫</Button>
                </a>
            </div>
        </Modal>


        <!--<div style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 1000;">
            <div style="width: 520px;height: 208px;background: #fff;position: absolute;left: 50%;top: 50%;margin-left: -260px;margin-top: -104px;font-size: 14px;">
                <div style="padding: 10px;line-height: 16px;color: #346799;">提示信息</div>
                <div style="padding: 20px 108px 20px 118px;background: url(https://forum.chasedream.com/static/image/common/info.gif) no-repeat 70px 20px;">
                    <div style="color: #444;margin-bottom: 15px;"><strong>换绑不成功</strong></div>
                    <p style="color: #444;line-height: 22px;margin-bottom: 15px;">换绑前后的微信账号相同。如果您仍要换绑，请切换至微信端，更换微信账号后再进行换绑。</p>
                    <div><button style="width: 120px;height: 23px;line-height: 23px;padding: 0;border: none;color: #fff;background: #67D86B;">确定</button></div>
                </div>
            </div>
        </div>-->

    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    import jigsaws from '@/libs/jigsaw.js';
    import initGeetest from '@/libs/gt.js';
    import bind_tip from './bind-tip.vue';
    import scan from './scan.vue'
    import tip from './tip.vue'
    export default {
        name: 'wechat',
        components: {
            bindTip: bind_tip,
            scan: scan,
            tip: tip
        },
        data () {
            return {
                form: {

                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isPc: true,
                isValidate: false,
                password_tip: '',
                newpassword_tip: '',
                repassword_tip: '',
                isTipShow: true,
                type: '0',
                logoff: false,
                wcLogin: false,
                wcImg: '',
                timer: '',
                spinShow: false,
                isRefresh: true,
                wechat_bind: false,
                wechat_name: '',
                stap: 1,
                isWechatlogin: false,
                uid: 0,
                binding_false: false
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;
            this.check_wechat();
            this.get_user_info();

            if(!!this.isPc){
                //window.parent.postMessage({register_type:'register'},'*');
                /*var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }*/

                if(window.innerWidth < 760){
                    this.isTipShow = false;
                }

            }else {

            }
        },
        methods: {
            get_user_info(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/check_login',
                    method:'get'
                }).then(function (res) {
                    if(res.data.msg === 'success'){
                        //_this.username = res.data.data.username;
                        _this.uid = res.data.data.uid;
                    }else{
                        //_this.errMsg = res.msg;
                        _this.$Message.error(res.data.msg);
                    }
                }).catch(function (err) {
                    console.log(err)
                    //_this.errMsg = err.msg;
                    _this.$Message.error(err.msg);
                    /*_this.$Message.error({
                     top: 500,
                     duration: 3,
                     content: err.msg
                     });*/
                });
            },
            handleSubmit (e) {
                if(parseInt(e) === 1){
                    this.$router.push('/register/bind-tip');
                }else if(parseInt(e) === 2){
                    this.logoff = true;
                }
            },
            closelogoff (){
                this.logoff = false;
                this.type = '0';
            },
            checkpassword (pw1, pw2) {
                var minlength = 8;
                var maxlength = 20;
                var strongpw = [1,2,3];
                this.errMsg = '';
                this.password_tip = '';
                this.repassword_tip = '';
                if(!pw1 && !pw2) {
                    return;
                }
                if(pw1.length < minlength) {
                    this.errMsg = '密码太短，不得少于 '+minlength+' 个字符';
                    this.password_tip = '密码太短，不得少于 '+minlength+' 个字符';
                    this.isRegistet = false;
                    return false;
                }
                if(strongpw) {
                    var strongpw_error = false, j = 0;
                    var strongpw_str = new Array();
                    for(var i in strongpw) {
                        if(strongpw[i] === 1 && !pw1.match(/\d+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '数字';
                            j++;
                        }
                        if(strongpw[i] === 2 && !pw1.match(/[a-z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '小写字母';
                            j++;
                        }
                        if(strongpw[i] === 3 && !pw1.match(/[A-Z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '大写字母';
                            j++;
                        }
                        if(strongpw[i] === 4 && !pw1.match(/[^A-Za-z0-9]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '特殊符号';
                            j++;
                        }
                    }
                    if(strongpw_error) {
                        this.errMsg = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.password_tip = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.isRegistet = false;
                        return false;
                    }
                }

                if(pw1 != pw2) {
                    this.errMsg = '两次输入的密码不一致';
                    this.repassword_tip = '两次输入的密码不一致';
                    this.isRegistet = false;
                    return false;
                }
                return true;
            },
            check_wechat(){
                var _this = this;
                util.ajax({
                    url:'/api/v1/auth/check_wechat_binding',
                    method:'get',
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        if(!!res.data.data.bind){
                            _this.wechat_bind = true;
                            _this.wechat_name = res.data.data.nickname
                            _this.stap = 2;
                            _this.isWechatlogin = true;
                        }else {
                            _this.wechat_bind = false;
                            _this.stap = 1;
                            _this.isWechatlogin = false;
                        }
                    }else{
                        _this.errMsg = res.msg;
                        _this.wechat_bind = false;
                        _this.stap = 1;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.wechat_bind = false;
                });

            },
            set_stap (n,obj){
                console.log(obj);
                console.log(this.wechat_name);
                console.log(this.uid);
                if(n == 4){
                    if(!!obj && obj.wc_name == this.wechat_name && obj.uid == this.uid){
                        this.set_stap(2)
                        window.parent.postMessage({register_type:'binding_false'},'*');
                    }else {
                        this.stap = n;
                    }
                }else {
                    this.stap = n;
                    if(n === 2 || n === 1){
                        this.check_wechat();
                    }
                }
            },
            unbunding (){
                var _this = this;
                util.ajax({
                    url: "api/v1/wechat/unbinding",
                    method: "get",
                    dataType: "json"
                }).then (function (data) {
                    console.log(data);
                    if(data.data.msg === 'success'){

                        if(_this.isPc){
                            _this.check_wechat();
                            //window.parent.postMessage({register_type:'login'},'*');
                        }else {
                            //window.location.href = 'https://forum.chasedream.com/forum.php';
                            //_this.tabType = 1;
                            _this.check_wechat();
                            _this.logoff = false;
                        }
                        /*if(data.data.data.openid !== ''){
                         clearInterval(_this.timer);
                         if(!data.data.data.binding){
                         _this.$store.commit('setUnbindInfo',data.data.data);
                         _this.$router.push({
                         name: 'tip'
                         });
                         window.parent.postMessage({register_type:'toTip'},'*');
                         }else if(!!data.data.data.binding) {
                         if(_this.isPc){
                         window.parent.postMessage({register_type:'login'},'*');
                         }else {
                         window.location.href = 'https://forum.chasedream.com/forum.php';
                         }
                         }
                         }*/
                    }else{
                        _this.errMsg = data.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });
            },
            getqrcode (){
                this.closelogoff();
                this.spinShow = true;
                this.isRefresh = true;
                if(!!this.timer){
                    clearInterval(this.timer);
                }

                this.wcLogin = true;
                var _this = this;

                util.ajax({
                    url: "api/v1/wechat/qrcode?t=" + (new Date()).getTime(), // 加随机数防止缓存
                    method: "get",
                    dataType: "json"
                }).then (function (data) {
                    //console.log(data);
                    if(data.data.msg === 'success'){
                        _this.spinShow = false;
                        _this.isRefresh = false;
                        _this.wcImg = data.data.data.image;
                        var s = 0;
                        _this.timer = setInterval(function () {
                            s++;
                            if(s === 120){   //120
                                _this.wcLogin = false;
                                clearInterval(_this.timer);
                                _this.spinShow = true;
                            }

                            util.ajax({
                                url: "api/v1/wechat/verify_ticket?type=binding&t=" + data.data.data.ticket,
                                method: "get",
                                dataType: "json"
                            }).then (function (data) {
                                console.log(data);
                                if(data.data.msg === 'success'){
                                    if(data.data.data.openid !== ''){
                                        clearInterval(_this.timer);
                                        if(!data.data.data.binding){
                                            _this.$store.commit('setUnbindInfo',data.data.data);
                                            if(data.data.data.wc_name == _this.wechat_name && data.data.data.uid == _this.uid){
                                                _this.wcLogin = false;
                                                _this.cancel();
                                                _this.binding_false = true;
                                            }else {
                                                _this.$router.push({
                                                    name: 'tip'
                                                });
                                                window.parent.postMessage({register_type:'toTip'},'*');
                                            }
                                        }else if(!!data.data.data.binding) {
                                            if(_this.isPc){
                                                window.parent.postMessage({register_type:'login'},'*');
                                            }else {
                                                window.location.href = 'https://forum.chasedream.com/forum.php';
                                            }
                                        }
                                    }
                                }else{
                                    _this.errMsg = data.msg;
                                }
                            }).catch(function (err) {
                                console.log(err)
                                _this.errMsg = err.msg;
                            });
                        },1000);
                    }else{
                        _this.errMsg = data.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                });

            },
            cancel (){
                clearInterval(this.timer);
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
