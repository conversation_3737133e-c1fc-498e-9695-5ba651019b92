<style lang="less">

</style>
<template>
    <div class="check-mobile-binding">
        {{bind_errMsg}}
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        components: {

        },
        data () {
            return {
                bind_errMsg: ''
            }
        },
        mounted () {
            this.checkBind();
        },
        methods: {
            checkBind (){
                var _this = this;
                util.ajax({
                    url:'https://id.chasedream.com/api/v1/auth/check_mobile_binding',
                    method:'GET',
                    params: {

                    },
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        window.parent.postMessage({register_type:'binding'},'*');
                    }else{
                        window.parent.postMessage({register_type:'showBinding'},'*');
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.bind_errMsg = err.msg;
                });
            },
            setRegister_type(type){
                window.parent.postMessage({register_type: type},'*');
            }
        }
    }
</script>