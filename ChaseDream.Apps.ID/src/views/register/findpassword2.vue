<style lang="less">
    @import 'register.less';
</style>

<template>
    <div class="register" @keydown.enter="handleSubmit">
        <div class="register-con" v-if="!!isPc" style="height: 280px;overflow: hidden;background: #fff;">
            <div class="r">
                <div class="r-c">
                    <div class="r-form">
                        <Form :label-width="132">
                            <FormItem prop="password" label="登录密码" class="passwordItem required">
                                <Input v-model="form.password" type="password"></Input>
                                <span class="tip" v-if="isTipShow">{{password_tip}}</span>
                            </FormItem>
                            <FormItem prop="repassword" class="passwordItem required" label="确认密码">
                                <Input v-model="form.repassword" type="password"></Input>
                                <span class="tip" v-if="isTipShow">{{repassword_tip}}</span>
                            </FormItem>
                            <FormItem class="re-btn">
                                <span class="tip" v-if="!!errMsg && !isTipShow" style="display: block;">{{errMsg}}</span>
                                <Button @click="handleSubmit" type="success" long>确定</Button>
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
        <div class="register-con mobile_r_c" v-if="!isPc" style="width: 100%;">
            <Card :bordered="false" class="findpassword">
                <p slot="title">
                    <router-link to="/register/findpassword">
                        <Icon type="ios-arrow-thin-left" size="24"></Icon>
                    </router-link>
                    找回密码
                </p>
                <div class="re_tip" v-if="!!errMsg">
                    <p>
                        <span><img src="../../images/register/icon_tips.png" alt="">
                        </span>
                        {{errMsg}}
                    </p>
                </div>
                <div class="form-con">
                    <Form ref="nameForm" :model="form" class="findpasswordForm" :label-width="80">
                        <FormItem prop="password" label="登录密码" class="passwordItem">

                            <Input v-model="form.password" type="password"></Input>
                        </FormItem>
                        <FormItem prop="repassword" class="passwordItem" label="确认密码">
                            <Input v-model="form.repassword" type="password"></Input>
                        </FormItem>
                        <FormItem class="re-btn">
                            <Button @click="handleSubmit" type="success" long>确定</Button>
                        </FormItem>
                    </Form>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    import area from '../area_code';
    export default {
        name: 'findpassword2',
        data () {
            return {
                form: {
                    mobile: '', //test
                    area_code: 86,
                    captcha: '',
                    type: '',
                    password: '',
                    repassword: ''
                },
                access_token: this.$store.state.user.access_token,
                errMsg: '',
                isPc: true,
                isSentcode: true,
                count: 60,
                password_tip: '',
                repassword_tip: '',
                isTipShow: true,
                token: ''
            };
        },
        mounted () {
            this.$store.commit('isPC');
            this.isPc = this.$store.state.register.ispc;
            this.token = window.location.href.split('?')[1];
            console.log(this.token)

            if(!!this.isPc){
                var requireds = document.getElementsByClassName('required');
                for(var i=0;i<requireds.length;i++){
                    var l = requireds[i].getElementsByClassName('ivu-form-item-label')[0];
                    l.innerHTML = '<i style="color: red;font-weight: 700;">*</i>' + l.innerHTML;
                }
                if(window.innerWidth < 760){
                    this.isTipShow = false;
                }

            }else {

            }

            this.code_group = area.area_code_group;

        },
        methods: {
            handleSubmit () {
                if(!this.form.password){
                    this.errMsg = '登录密码不能为空';
                    this.password_tip = '登录密码不能为空';
                    return;
                }else if(!this.form.repassword){
                    this.errMsg = '确认密码不能为空';
                    this.repassword_tip = '确认密码不能为空';
                    return;
                }else if(!this.checkpassword(this.form.password, this.form.repassword)){
                    return
                }

                var dataIn = {};
                var url = '';
                if(!this.token){
                    url = '/api/v1/auth/verify/recovery_password'
                    dataIn.password = this.form.password;
                }else {
                    url = '/api/v1/auth/recovery_password/by_email'
                    dataIn.password = this.form.password;
                    dataIn.token = this.token;
                }

               // console.log(this.form.password)
                var _this = this;

                util.ajax({
                    url: url,
                    method:'post',
                    data: dataIn,
                }).then(function (res) {
                    console.log(res)
                    if(res.data.msg === 'success'){
                        _this.$Message.info('修改成功!');
                        setTimeout(function () {
                            if(!!_this.isPc){
                                window.parent.postMessage({register_type: 'logout'},'*');
                            }else {
                                _this.$router.push({
                                    name: 'login'
                                });
                            }
                        },1000);
                    }else{
                        _this.errMsg = res.msg;
                        _this.password_tip = res.msg;
                    }
                }).catch(function (err) {
                    console.log(err)
                    _this.errMsg = err.msg;
                    _this.password_tip = err.msg;
                });
            },
            checkpassword (pw1, pw2) {
                var minlength = 8;
                var maxlength = 20;
                var strongpw = [1,2,3];
                this.errMsg = '';
                this.password_tip = '';
                this.repassword_tip = '';
                if(!pw1 && !pw2) {
                    return;
                }
                if(pw1.length < minlength) {
                    this.errMsg = '密码太短，不得少于 '+minlength+' 个字符';
                    this.password_tip = '密码太短，不得少于 '+minlength+' 个字符';
                    this.isRegistet = false;
                    return false;
                }
                if(strongpw) {
                    var strongpw_error = false, j = 0;
                    var strongpw_str = new Array();
                    for(var i in strongpw) {
                        if(strongpw[i] === 1 && !pw1.match(/\d+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '数字';
                            j++;
                        }
                        if(strongpw[i] === 2 && !pw1.match(/[a-z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '小写字母';
                            j++;
                        }
                        if(strongpw[i] === 3 && !pw1.match(/[A-Z]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '大写字母';
                            j++;
                        }
                        if(strongpw[i] === 4 && !pw1.match(/[^A-Za-z0-9]+/g)) {
                            strongpw_error = true;
                            strongpw_str[j] = '特殊符号';
                            j++;
                        }
                    }
                    if(strongpw_error) {
                        this.errMsg = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.password_tip = '密码太弱，密码中必须包含 '+strongpw_str.join('，');
                        this.isRegistet = false;
                        return false;
                    }
                }

                if(pw1 != pw2) {
                    this.errMsg = '两次输入的密码不一致';
                    this.repassword_tip = '两次输入的密码不一致';
                    this.isRegistet = false;
                    return false;
                }
                return true;
            },
            isWeiXin(){
                var ua = window.navigator.userAgent.toLowerCase();
                var iswx = false;
                if(ua.match(/MicroMessenger/i) == 'micromessenger'){
                    if(ua.match(/wxwork/i) == 'wxwork'){ // 企业微信号
                        iswx = false;
                    }else {
                        iswx = true;
                    }
                }else{
                    iswx = false;
                }
                return iswx;
            }
        }
    };
</script>

<style>

</style>
