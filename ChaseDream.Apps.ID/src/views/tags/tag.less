.forum_tag{
    .forum_tag_pc{
        width: 800px;
        margin: 0 auto;
    }
    .forum_tag_mb{
        width: 100%;
    }
    .tag_list{
        margin-top: 10px;

        small{padding:15px 20px; }
        span{
            display: inline-block;
            margin-right: 10px;
            font-size: 14px;
            padding:0 5px;
            color: #fff;
        }
        .bg1{
            background: #d32f2f;
        }
        .bg2{
            background: #9c3dc4;
        }
        .bg3{
            background: #673ab7;
        }
        .bg4{
            background: #3f51b5;
        }
        .bg5{
            background: #2196f3;
        }
        .bg6{
            background: #00a9bb;
        }
        .bg7{
            background: #007467;
        }
        .bg8{
            background: #29892d;
        }
        .bg9{
            background: #8bc34a;
        }
        .bg10{
            background: #afb42b;
        }
        .bg11{
            background: #ffbf00;
        }
        .bg12{
            background: #f88a1a;
        }
        .bg13{
            background: #ff5722;
        }
        .bg14{
            background: #795548;
        }
        .bg15{
            background: #c2c2c2;
        }
        .barring{
            background: #c2c2c2;
            text-decoration: line-through;
        }
        .ivu-icon-ios-close-empty{
            position: absolute;
            top: -10px;
            right: -5px;
            cursor: pointer;
            font-size: 20px;
            color: red;
        }
    }
    .add-tag{
        margin-top:10px;
        /*margin-bottom: 30px;*/
        h3{
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }
        .ivu-select-selection{
            border-radius: 0;
            padding: 0 10px;
            box-shadow: none;
            input{
                height: 32px;
                line-height: 32px;
                border-radius: 0;
                font-size: 14px;
                outline: none;
                border: none;
                box-sizing: border-box;
                color: #495060;
                background-color: transparent;
                position: relative;
            }
        }
        .ivu-select-dropdown{
            top: inherit !important;
            bottom: 35px;
        }
        .ivu-btn{
            height: 34px;
            line-height: 34px;
            font-size: 14px;
            background: #fd9e79;
            color: #fff;
            border: none;
            border-radius: 0;
            padding: 0;
            width: 60px;
            &.disabled{
                background: #c7c7c7;
            }
        }
    }
}
.tag_list{
    display: inline-block;
    position: relative;
    margin-right: 20px;
    .ivu-tooltip{
        .ivu-poptip-popper{
            width: 100%;
        }
        &.hide{
            .ivu-tooltip-popper{
                display: none;
            }
        }
    }
    .ivu-poptip-content{
        max-height: 200px;
        overflow: auto;

        .ivu-poptip-inner{
            background: none;
            .ivu-poptip-body{
                padding: 0;
                ul{
                    background: #fff;
                    li{
                        list-style: none;
                        height: 40px;
                        border-bottom: 1px solid #efefef;
                        padding:0 8px 16px;
                        line-height: 40px;
                    }
                    li:hover{
                        background: #efefef;
                    }
                }
            }

        }
    }
    .ivu-poptip{
        width: 90%;
    }
    .ivu-poptip-rel{
        width: 100%;
    }
    .ivu-select-dropdown{
        max-height:300px;
    }

    .ivu-select-item{
        font-size: 14px !important;
    }
}

.forum_tag_mobile{
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    .ivu-card-bordered{
        border:none;
        box-shadow: none;
        h3{
            line-height: 40px;
        }
    }
}
.tags_all,.tags_pro{
    .tag_list{
        margin-right: 10px;
        span{
            cursor: pointer;
        }
        .bg1{
            background:none;
            color: #d32f2f;
            border:1px solid #d32f2f;
        }
        &.on .bg1{
            background:#d32f2f;
            color: #fff;
            border:1px solid #d32f2f;
        }
        .bg2{
            background:none;
            color: #9c3dc4;
            border:1px solid #9c3dc4;
        }
        &.on .bg2{
            background:#9c3dc4;
            color: #fff;
            border:1px solid #9c3dc4;
        }
        .bg3{
            background:none;
            color: #673ab7;
            border:1px solid #673ab7;
        }
        &.on .bg3{
            background:#673ab7;
            color: #fff;
            border:1px solid #673ab7;
        }
        .bg4{
            background:none;
            color: #3f51b5;
            border:1px solid #3f51b5;
        }
        &.on .bg4{
            background:#3f51b5;
            color: #fff;
            border:1px solid #3f51b5;
        }
        .bg5{
            background:none;
            color: #2196f3;
            border:1px solid #2196f3;
        }
        &.on .bg5{
            background:#2196f3;
            color: #fff;
            border:1px solid #2196f3;
        }
        .bg6{
            background:none;
            color: #00a9bb;
            border:1px solid #00a9bb;
        }
        &.on .bg6{
            background:#00a9bb;
            color: #fff;
            border:1px solid #00a9bb;
        }
        .bg7{
            background:none;
            color: #007467;
            border:1px solid #007467;
        }
        &.on .bg7{
            background:#007467;
            color: #fff;
            border:1px solid #007467;
        }
        .bg8{
            background:none;
            color: #29892d;
            border:1px solid #29892d;
        }
        &.on .bg8{
            background:#29892d;
            color: #fff;
            border:1px solid #29892d;
        }
        .bg9{
            background:none;
            color: #8bc34a;
            border:1px solid #8bc34a;
        }
        &.on .bg9{
            background:#8bc34a;
            color: #fff;
            border:1px solid #8bc34a;
        }
        .bg10{
            background:none;
            color: #afb42b;
            border:1px solid #afb42b;
        }
        &.on .bg10{
            background:#afb42b;
            color: #fff;
            border:1px solid #afb42b;
        }
        .bg11{
            background:none;
            color: #ffbf00;
            border:1px solid #ffbf00;
        }
        &.on .bg11{
            background:#ffbf00;
            color: #fff;
            border:1px solid #ffbf00;
        }
        .bg12{
            background:none;
            color: #f88a1a;
            border:1px solid #f88a1a;
        }
        &.on .bg12{
            background:#f88a1a;
            color: #fff;
            border:1px solid #f88a1a;
        }
        .bg13{
            background:none;
            color: #ff5722;
            border:1px solid #ff5722;
        }
        &.on .bg13{
            background:#ff5722;
            color: #fff;
            border:1px solid #ff5722;
        }
        .bg14{
            background:none;
            color: #795548;
            border:1px solid #795548;
        }
        &.on .bg14{
            background:#795548;
            color: #fff;
            border:1px solid #795548;
        }
        .bg15{
            background:none;
            color: #c2c2c2;
            border:1px solid #c2c2c2;
        }
        &.on .bg15{
            background:#c2c2c2;
            color: #fff;
            border:1px solid #c2c2c2;
        }
    }
}