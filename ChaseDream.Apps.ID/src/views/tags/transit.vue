<style lang="less">

</style>

<template>
    <div class="forum_tag">
        <span @click="getData">123</span>
        <!--<iframe src="http://localhost:8080/transit" frameborder="0"></iframe>-->
    </div>
</template>
<script>
    import Cookies from 'js-cookie';
    import util from '@/libs/util.js';
    export default {
        name: 'forum_tag_transit',
        data () {
            return {
                isWin: true,
                timer: null
            };
        },
        watch: {
            '$route' (to) {
                if(to.name === 'forum_tag_transit'){
                    this.isWin = true;
                    this.getData();
                }
            },
        },
        mounted () {
            this.getData();
        },
        methods: {
            getData(){
                setTimeout(function () {
                    //console.log(window.localStorage.getItem('viewTag'))
                    var viewTag = window.localStorage.getItem('viewTag');
                    if(viewTag === 'false'){
                        window.parent.postMessage({tag_type: 'viewTag'},'*');
                    }
                },600)
                setTimeout(function () {
                    var _this = this;
                    this.timer = setInterval(function () {
                        _this.isWin = window.localStorage.getItem('viewTag');
                        if(_this.isWin === 'false'){
                            window.parent.postMessage({tag_type: 'viewTag'},'*');
                        }
                    },1000)
                },1000)
            }
        }
    };
</script>
