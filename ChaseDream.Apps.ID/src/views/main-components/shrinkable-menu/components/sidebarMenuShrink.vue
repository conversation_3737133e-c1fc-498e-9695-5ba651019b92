<template>
    <div>
        <template v-for="(item, index) in menuList">
            <div style="text-align: center;" :key="index" v-if="access ===0 || item.uAccess === access">
                <Dropdown transfer v-if="(item.children.length > 1 && item.showCount > 1) || !!item.isfold" placement="right-start" :key="index" @on-click="changeMenu">
                    <Button style="width: 70px;margin-left: -5px;padding:10px 0;" type="text">
                        <Icon :size="20" :color="iconColor" :type="item.icon" v-if="!!item.icon"></Icon>
                    </Button>
                    <DropdownMenu style="width: 200px;" slot="list">
                        <template v-for="(child, i) in item.children">
                            <DropdownItem :name="child.name" :key="i" v-show="child.isShow === '1'"><Icon :type="child.icon"></Icon><span style="padding-left:10px;">{{ itemTitle(child) }}</span></DropdownItem>
                        </template>
                    </DropdownMenu>
                </Dropdown>
                <Dropdown transfer v-else placement="right-start" :key="index" @on-click="changeMenu">
                    <Button @click="changeMenu(item.children[0].name)" style="width: 70px;margin-left: -5px;padding:10px 0;" type="text" v-if="item.children.length > 0">
                        <Icon :size="20" :color="iconColor" :type="item.children[0].icon || item.icon" v-if="!!item.icon"></Icon>
                    </Button>
                    <DropdownMenu style="width: 200px;" slot="list" v-if="item.children.length > 0">
                        <DropdownItem :name="item.children[0].name" :key="'d' + index" v-show="item.children[0].isShow"><Icon :type="item.children[0].icon || item.icon"></Icon><span style="padding-left:10px;">{{ itemTitle(item.children[0]) }}</span></DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </div>
        </template>
    </div>
</template>

<script>
import Cookies from 'js-cookie';
export default {
    name: 'sidebarMenuShrink',
    props: {
        menuList: {
            type: Array
        },
        iconColor: {
            type: String,
            default: 'white'
        },
        menuTheme: {
            type: String,
            default: 'darck'
        }
    },
    data () {
        return {
            access: parseInt(Cookies.get('uAccess')),
            openNames: []
        };
    },
    mounted () {
        if(!!window.location.href.split('/')[4]){
            for(var i=0;i<this.menuList.length;i++){
                if(this.menuList[i].name === window.location.href.split('/')[4]){
                    this.openNames.push(this.menuList[i].name);
                }
                this.menuList[i].isfold = false;
                if(this.menuList[i].title === '测试菜单'){
                    this.menuList[i].isfold = true;
                }
            }
        }
    },
    methods: {
        changeMenu (active) {
            this.$emit('on-change', active);
        },
        itemTitle (item) {
            if (typeof item.title === 'object') {
                return this.$t(item.title.i18n);
            } else {
                return item.title;
            }
        }
    }
};
</script>
