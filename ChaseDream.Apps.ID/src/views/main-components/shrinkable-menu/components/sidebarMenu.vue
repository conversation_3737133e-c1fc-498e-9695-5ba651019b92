<style lang="less">
    @import '../styles/menu.less';
</style>

<template>
    <Menu ref="sideMenu" :active-name="$route.name" :open-names="openNames" :theme="menuTheme" width="auto" @on-select="changeMenu">
        <template v-for="item in menuList" v-if="access === 0 || item.uAccess === access">
            <MenuItem v-if="item.children.length >= 1 && item.showCount === 1 && !item.isfold" :name="item.children[0].name" :key="'menuitem' + item.name" v-show="item.children[0].isShow === '1'">
                <Icon :type="item.children[0].icon || item.icon" :size="iconSize" :key="'menuicon' + item.name"></Icon>
                <span class="layout-text" :key="'title' + item.name">{{ itemTitle(item.children[0]) }}</span>
            </MenuItem>

            <Submenu v-if="(item.children.length > 1 && item.showCount > 1) || !!item.isfold " :name="item.name" :key="item.name">
                <template slot="title">
                    <Icon :type="item.icon" :size="iconSize"></Icon>
                    <span class="layout-text">{{ itemTitle(item) }}</span>
                </template>
                <template v-for="child in item.children">
                    <MenuItem :name="child.name" :key="'menuitem' + child.name" v-show="child.isShow === '1'">
                        <Icon :type="child.icon" :size="iconSize" :key="'icon' + child.name"></Icon>
                        <span class="layout-text" :key="'title' + child.name">{{ itemTitle(child) }}</span>
                    </MenuItem>
                </template>
            </Submenu>
            <Submenu v-if="item.children.length === 0 || item.showCount === 0" :name="item.name" :key="item.name">
                <template slot="title">
                    <Icon :type="item.icon" :size="iconSize"></Icon>
                    <span class="layout-text">{{ itemTitle(item) }}</span>
                </template>
            </Submenu>
        </template>
    </Menu>
</template>

<script>
import Cookies from 'js-cookie';
export default {
    name: 'sidebarMenu',
    props: {
        menuList: Array,
        menuTheme: {
            type: String,
            default: 'dark'
        },
    },
    data () {
        return {
            access: parseInt(Cookies.get('uAccess')),
            iconSize: 22,
            openNames: []
        };
    },
    mounted () {
        if(!!window.location.href.split('/')[4]){
            for(var i=0;i<this.menuList.length;i++){
                //this.openNames.push(this.menuList[i].name);
                if(this.menuList[i].name === window.location.href.split('/')[4]){
                    this.openNames.push(this.menuList[i].name);
                }
                this.menuList[i].isfold = false;
                if(this.menuList[i].title === '测试菜单'){
                    this.menuList[i].isfold = true;
                }
            }
        }

       // this.openNames.push(this.menuList[this.menuList.length-1].name);
    },
    methods: {
        changeMenu (active) {
            this.$emit('on-change', active);
        },
        itemTitle (item) {
            if (typeof item.title === 'object') {
                return this.$t(item.title.i18n);
            } else {
                return item.title;
            }
        }
    },
    updated () {
        this.$nextTick(() => {
            if (this.$refs.sideMenu) {
                this.$refs.sideMenu.updateOpened();
            }
        });
    }

};
</script>
