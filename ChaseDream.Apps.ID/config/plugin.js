// config/plugin.js
const os = require('os')

/** @type Egg.EggPlugin */
exports.sequelize = {
  enable: true,
  package: 'egg-sequelize'
}

exports.lru = {
  enable: true,
  package: 'egg-lru',
}

exports.nunjucks = {
  enable: true,
  package: 'egg-view-nunjucks',
}

exports.cors = {
  enable: true,
  package: 'egg-cors',
}

exports.jwt = {
  enable: true,
  package: "egg-jwt",
}

exports.validator = {
  enable: true,
  package: 'egg-y-validator'
}

exports.sessionRedis = {
  enable: true,
  package: 'egg-session-redis',
}

exports.redis = {
  enable: true,
  package: 'egg-redis',
}

exports.wechatApi = {
  enable: true,
  package: 'egg-wechat-api',
}

exports.memcache = {
  enable: true,
  package: 'egg-memcache',
}

exports.zrole = {
  enable: true,
  package: 'egg-zrole',
}

exports.mongoose = {
  enable: true,
  package: 'egg-mongoose',
}
