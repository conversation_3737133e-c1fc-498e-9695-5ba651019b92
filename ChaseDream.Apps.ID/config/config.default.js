const fs = require('fs')
const os = require('os')
const path = require('path')
const { SequelizeAdapter } = require('casbin-sequelize-adapter')

// config/config.default.js
/* eslint valid-jsdoc: "off" */

/**
 * @param {Egg.EggAppInfo} appInfo - { baseDir, root, env, ... }
 */
module.exports = appInfo => {
    /**
   * 框架内置配置
   * @type {Egg.EggAppConfig}
   */
    const config = {}

    process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0

    if (process.env.http_proxy) {
        config.httpclient = {
            request: {
                enableProxy: true,
                rejectUnauthorized: false,
                proxy: process.env.http_proxy,
            },
        }
    }

    config.token = "authorization"
    config.keys = appInfo.name + '_1530516262298_3959'
    config.api_prefix = "/api/v1"
    config.middleware = ["error", "auth"]
    config.page_size = 20
    config.dev = {
        uid: 637,
    }

    config.mongoose = {
        client: {
            url: 'mongodb://**************:27017/site_log',
            options: {
                auth: { authSource: "admin" },
                user: 'root',
                pass: 'z#QzyhK#W3O2mvGh',
                useUnifiedTopology: true,
            },
        },
        loadModel: false,
    }

    config.redis_key = {
        event_list: 'event_list',
        forum_group: 'forum_group',
    }

    config.system_settings = {
        push1_count: 'push1_count',
        push1_www_position: 'push1_www_position',
        www_hot: 'www_hot',
    }

    config.faceplusplus = {
        url: 'https://api-cn.faceplusplus.com/facepp/v3/detect',
        api_key: '4Dk0UCx2rBhorbUDKgiD0z41SahuEojt',
        api_secret: '30qNjDpw46ulgY_EPkh5fiSzJhegCz5f',
    }

    config.mail = {
        url: 'https://connect.chasedream.com/api/v2/admin/base/open/sendmail',
        token: '0C075B60D27D5B05E2D841334DC6CB056',
        mailfrom: '<EMAIL>',
    }

    config.zhiqungj = {
        url: 'https://wapi.zhiqungj.com/login/login',
        username: '17810391160@danny',
        password: 'rJ]X)74Z32{8c+Z',
    }

    config.apple = {
        web_sign: {
            "client_id": "com.chasedream.forum.sign",
            "team_id": "5YK3LG4D3X",
            "redirect_uri": "https://id.chasedream.com/api/v1/apple/auth",
            "key_id": "46FK6Z7HGD",
            "scope": ""
        },
        key: "AuthKey_46FK6Z7HGD.p8",
        oia: {
            "applinks": {
                "apps": [],
                "details": [
                    {
                        "appID": "5YK3LG4D3X.com.chasedream",
                        "paths": ["*"]
                    }
                ]
            },
            "webcredentials": {
                "apps": ["5YK3LG4D3X.com.chasedream", "5YK3LG4D3X.com.chasedream.adhoc"]
            }
        },
    }

    config.linkedin = {
        useragent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_1_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36',
        browser: {
            width: 2560,
            height: 1660,
        },
        account: {
            username: '<EMAIL>',
            password: 'Jin-2414002',
        }
    }

    config.ip_location = {
        aliyun: {
            url: 'http://ipaddr.market.alicloudapi.com/ip_addr_search/v1?IP_ADDR=',
            APPCODE: 'de2065152e6d429da410fb4c39d143db',
        }
    }

    config.app_scan_login_timeout = 60 * 3
    config.recovery_password_code_timeout = 60 * 30
    config.reg_monitor_timeout = 60 * 60 * 24 * 3

    config.super_admin = '管理员'
    config.system_maintenance = '系统维护'
    config.upload_school_area = '/upload/school_area'
    config.upload_event_img_path = '/upload/events/event-IMG'
    config.upload_recruit_img_path = '/upload/recruits/recruit-IMG'
    config.upload_threadpost_img_path = '/upload/ThreadPost/IMG'
    config.upload_user_path = '/upload/user/member'
    config.upload_linkedin = '/upload/linkedin'
    config.upload_linkedin_screenshot = '/upload/linkedin/screenshot'
    config.upload_linkedin_school = '/upload/linkedin/school'
    config.upload_linkedin_organization = '/upload/linkedin/organization'
    config.upload_contract = '/upload/contract'
    config.upload_public_url = 'https://static.chasedream.com'
    config.forum_avatar = '/forum_avatar'
    config.forum_attachment = '/forum_attachment'

    config.contract_stamp = {
        width: 595 * 4,
        height: 841 * 4,
        stamps: [{
            name: '无拒合同',
            id: 4,
            file: '8KzfYW7ckNPk',
        }, {
            name: '无懈合同',
            id: 3,
            file: '6duYdsYsZ3vV',
        }, {
            name: '润志合同',
            id: 1,
            file: 'jYB2U2PXjfNG',
        }, {
            name: 'CD合同',
            id: 2,
            file: 'unM7W8VfTZuZ',
        }, {
            name: '启思合同',
            id: 7,
            file: 'LJQGz3uakat6',
        }, {
            name: '缘奇合同',
            id: 6,
            file: 'xyM9D8I6FId8',
        }, {
            name: '触手合同',
            id: 8,
            file: '8Kzf8W7ckOPk',
        }, {
            name: '职悟合同',
            id: 9,
            file: 'Vzr3YEvAqFX3',
        }, {
            name: '王叔合同',
            id: 10,
            file: '87GDcGKBzYUe',
        }, {
            name: '探梦合同',
            id: 11,
            file: 'Xzcm2aJd8uVN',
        }, {
            name: '测试盖章',
            id: 5,
            file: 'x8M7x9VfTZuZ',
        }]
    }

    config.department = [{
        id: 1,
        name: 'MBA'
    }, {
        id: 5,
        name: 'mba'
    }, {
        id: 2,
        name: 'Master'
    }, {
        id: 3,
        name: '网站'
    }, {
        id: 4,
        name: '开发测试'
    }, {
        id: 6,
        name: '总办'
    }, {
        id: 12,
        name: '老王'
    }, {
        id: 11,
        name: 'YY'
    }, {
        id: 7,
        name: 'Agent Sim'
    }, {
        id: 8,
        name: 'Agent FB'
    }, {
        id: 9,
        name: 'Agent CL'
    }, {
        id: 10,
        name: 'GMAT'
    }]

    config.cluster = {
        listen: {
            port: 7004,
            hostname: '127.0.0.1',
        }
    }

    config.discuz = {
        prefix: 'jcsA_d25e_',
        authkey: 'dca5995vn1beqYxI',
        home_url: 'https://forum.chasedream.com',
        domain: '.chasedream.com',
        api_base_url: 'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1',
        highlight_digest: 'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=1&modsubmit=yes&infloat=yes&inajax=1',
        delete_thread: 'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=3&modsubmit=yes&infloat=yes&inajax=1',
        stick: 'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=1&modsubmit=yes&infloat=yes&inajax=1',
        send_post: 'https://forum.chasedream.com/forum.php?mod=post&action=reply&extra=page%3D1&replysubmit=yes&infloat=yes&handlekey=fastpost&inajax=1',
        cookie_max_age: 1000 * 60 * 60 * 24 * 30,
    }

    config.geetest = {
        geetest_id: '5a3175131c50da4aefb5ae54a443b111',
        geetest_key: '4465f26368f1bebfcc23c3291075ff17'
    }

    config.jiguang = {
        identity: {
            key: '2c508fe2f4fa4dd6813dd8e6',
            secret: '4bd3a9554b55107b0fb18d3a'
        }
    }

    config.tencent = {
        secretId: 'AKIDEG2XljQqyuZw2rQij02nHUP4SZPyGH0S',
        secretKey: 'C7lDTTCeqkNjpdAQUHo32kBLErCG3aqp',
        CaptchaAppId: 2032354718,
        AppSecretKey: '0kMQ_j1gariErLT27VJkPuw**',
    }

    config.sms = {
        aliyun: {
            access_key_id: '4CpHDrbHV9TYbtWj',
            access_key_secret: '07fvKaF4i5dKtLigcj25vm9RkOzgLn',
            template_code: {
                register: {
                    local: 'SMS_164085177',
                    oversea: 'SMS_164155414',
                    oversea_template: '验证码：${code}。您正在用此手机号码注册ChaseDream帐号。有效期30分钟。如非本人操作，可忽略。',
                },
                change_mobile: {
                    local: 'SMS_165060462',
                    oversea: 'SMS_165070197',
                    oversea_template: '验证码：${code}。您正在更换ChaseDream帐号绑定的手机号码。验证码有效期30分钟。如非本人操作，可忽略。',
                },
                binding_mobile: {
                    local: 'SMS_193248941',
                    oversea: 'SMS_193234035',
                    oversea_template: '验证码：${code}。您正在用此手机号码绑定ChaseDream帐号。有效期30分钟。如非本人操作，可忽略。',
                },
                recovery_password: {
                    local: 'SMS_193239018',
                    oversea: 'SMS_193244107',
                    oversea_template: '验证码：${code}。您正在用此手机号码找回ChaseDream密码。有效期30分钟。如非本人操作，可忽略。',
                },
                login: {
                    local: '',
                    oversea: '',
                    oversea_template: '',
                },
            }
        },
        send_cloud: {
            sms_key: 'DLbMs7dJ9g5iDxASXsertr6TsSPKIkb7',
            template_code: {
                register: {
                    local: 28188,
                    oversea: 27697
                },
                change_mobile: {
                    local: 28312,
                    oversea: 28314
                },
                binding_mobile: {
                    local: 46849,
                    oversea: 46849
                },
                recovery_password: {
                    local: 46850,
                    oversea: 46851
                },
                login: {
                    local: 0,
                    oversea: 0
                },
            }
        },
        twilio: {
            from: '+12013081159',
            sid: '**********************************',
            token: 'a07351f1a8e3f8c6ec8bc6d6c7578a8e',
            template_code: {
                register: {
                    oversea: `【ChaseDream】验证码：@@captcha。您正在用此手机号码注册ChaseDream帐号。有效期30分钟。如非本人操作，可忽略。`
                },
                change_mobile: {
                    oversea: '验证码：@@captcha。您正在更换ChaseDream帐号绑定的手机号码。验证码有效期30分钟。如非本人操作，可忽略。'
                },
                binding_mobile: {
                    oversea: '【ChaseDream】验证码：@@captcha。您正在用此手机号码绑定ChaseDream帐号。有效期30分钟。如非本人操作，可忽略。'
                },
                recovery_password: {
                    oversea: '【ChaseDream】验证码：@@captcha。您正在用此手机号码找回ChaseDream密码。有效期30分钟。如非本人操作，可忽略。'
                },
                login: {
                    oversea: ''
                },
            }
        },
        azure: {
            key: '8LJzgjHGdXNfU9P7aLRWFlMBCZsPPI31eZbdGmixL+g=',
            key_name: 'full',
            account: 'ChaseDream',
            template_code: {
                register: {
                    local: 'ChaseDream-Local-Forum-UserRegister',
                },
                change_mobile: {
                    local: '',
                },
                login: {
                    local: '',
                },
            },
            template: '',
        },
        type: {
            register: 'register',
            change_mobile: 'change_mobile',
            binding_mobile: 'binding_mobile',
            recovery_password: 'recovery_password',
            login: 'login',
        },
        limit: {
            hour: 3,
            day: 5
        }
    }

    config.forum = {
        url: 'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=newthread&fid=70&topicsubmit=yes',
        typeid: 0,
    }

    config.auth = {
        ignore: [/MP_verify_*/, /cdg/, /404/, /^\/$/, /forum-tag\/*/, /register\/*/, /login/, /wechat\/*/, /apple\/*/, /event\/*/, /^\/recruit$/, /auth\/.*?/, /misc\/.*?/, /^\/console\/*/, /^\/start\/*/]
    }

    config.proxy = true

    config.jwt = {
        secret: "M7tpiQoTkqybRpif"
    }

    config.security = {
        csrf: {
            enable: false,
        },
        nosniff: {
            enable: false,
        },
        xframe: {
            enable: false,
        },
    }

    config.sequelize = {
        datasources: [{
            delegate: 'model',
            baseDir: 'model',
            database: 'site_apps_id',
            dialect: 'mysql',
            host: '**************',
            port: '3306',
            username: 'root',
            password: 'vmNNO6>FOf2CF*Hi',
            timezone: '+08:00',
            log: true,
            define: {
                freezeTableName: true,
                underscored: true,
                paranoid: true,
                charset: "utf8mb4"
            }
        }, {
            delegate: 'forumModel',
            baseDir: 'forum_model',
            database: 'forum',
            dialect: 'mysql',
            host: '**************',
            port: '3306',
            username: 'root',
            password: 'vmNNO6>FOf2CF*Hi',
            timezone: '+08:00',
            log: true,
            define: {
                freezeTableName: true,
                underscored: true,
                paranoid: true,
                charset: "utf8mb4"
            }
        }, {
            delegate: 'wwwModel',
            baseDir: 'www_model',
            database: 'Site_WWW',
            dialect: 'mysql',
            host: '**************',
            port: '3306',
            username: 'root',
            password: 'vmNNO6>FOf2CF*Hi',
            timezone: '+08:00',
            log: true,
            define: {
                freezeTableName: true,
                underscored: false,
                paranoid: true,
                charset: "utf8mb4"
            }
        }]
    }

    config.zrole = {
        useAdapter: true,
        usePolicyInit: true,
        useAutoMiddleware: false,
        model: './zrole_model.conf',
        customResponse: ctx => {
            ctx.status = 403
            ctx.body = 'Forbidden'
        },
        adapterConfig: async () => {
            const connect = await SequelizeAdapter.newAdapter({
                username: config.sequelize.datasources[0].username,
                password: config.sequelize.datasources[0].password,
                host: config.sequelize.datasources[0].host,
                database: config.sequelize.datasources[0].database,
                dialect: config.sequelize.datasources[0].dialect,
                logging: false,
            })

            return connect
        },
    }

    config.redis = {
        client: {
            host: '**************',
            port: 6379,
            password: 'C7B#cI{jKng5SpZi',
            db: 7,
        },
    }

    exports.memcache = {
        url: 'localhost:11211',
        option: {}
    }

    config.cookies = {
        sameSite: 'none',
    }

    config.session = {
        key: 'M7tpiQoTkqybRpif',
        maxAge: 20 * 60 * 1000,
        httpOnly: true,
        encrypt: true,
    }

    config.wechat_group = {
        token: 'puppet_padlocal_407d6ff0a1ff43da98684a838ef141a7',
        thumbwidth: 400,
    }

    //wechat open platform
    config.wechat_open = {
        appid: 'wx12d8ad7feec2e14a',
        secret: 'c312b0b3bc5f62c643e702cf83be60d6',
    }

    //wechat public platform
    config.wechatApi = {
        appId: 'wx912f26ad8c580ac5',
        appSecret: '7c30d86d5bfea1d9029899e13692a1a0',
        token: 'fOd9wid8Ilk0uF4I',
        encodingAESKey: '3F86ULlyPz75eU1GkKsinHEoDQzoIOQmca6O7oYrcXX',
        callback: {
            id: 'http://k1m7.free.idcfengye.com/api/v1/wechat/id_web_code',
        }
    }

    config.lru = {
        clients: {
            long: {
                max: 1000,
                maxAge: 1000 * 60 * 60,
            },
            moment: {
                max: 1000,
                maxAge: 1000,
            },
        },
        app: true,
        agent: false,
    }

    config.view = {
        defaultViewEngine: 'nunjucks',
        defaultExtension: '.html',
    }

    config.cors = {
        origin: ctx => ctx.get('origin'),
        allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
        credentials: true
    }

    config.logger = {
        level: "INFO"
    }

    config.logrotator = {
        filesRotateByHour: [],
        hourDelimiter: '-',
        filesRotateBySize: [],
        maxFileSize: 50 * 1024 * 1024,
        maxFiles: 10,
        rotateDuration: 60000,
        maxDays: 7,
    }

    config.static = {
        prefix: '/public/',
        dir: path.join(appInfo.baseDir, 'app/public'),
        dynamic: true,
        preload: false,
    }

    config.siteFile = {
        '/favicon.ico': fs.readFileSync(path.join(__dirname, '../favicon.ico')),
    }

    config.multipart = {
        mode: 'stream',
        fileSize: '500mb',
        whitelist: [
            '.jpg',
            '.jpeg',
            '.png',
            '.gif',
            '.bmp',
            '.mp4',
            '.avi',
            '.mkv',
            '.wmv',
            '.rmvb',
            '.mp3',
            '.pdf',
            '.zip',
            '.rar',
            '.txt',
            '.doc',
            '.docx',
            '.xls',
            '.xlsx',
            '.ppt',
            '.pptx',
        ],
        tmpdir: path.join(os.tmpdir(), 'egg-multipart-tmp', appInfo.name),
        cleanSchedule: {
            cron: '0 30 4 * * *',
        },
    }

    config.bodyParser = {
        jsonLimit: '300mb',
        formLimit: '300mb',
    }

    config.validator = {
        open: async ctx => 'zh-CN',
        languages: {
            'zh-CN': {
                required: '%s 必填'
            }
        },
        async formatter(ctx, error) {
            ctx.type = 'json'
            ctx.status = 400
            ctx.body = error
        }
    }

    config.aes = {
        key: 'vrUkBbN2dBRoYja2UgHndYFntrzGrQEbZLceHVVf',
        iv: 'TbpqKH8dxx9fRecwfucd'
    }

    return config
}
