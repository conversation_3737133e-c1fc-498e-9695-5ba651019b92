const { SequelizeAdapter } = require('casbin-sequelize-adapter')

module.exports = appInfo => {

    const config = exports = {}

    config.discuz = {
        prefix: 'jcsA_d25e_',
        authkey: 'dca5995vn1beqYxI',
        home_url: 'https://forum.chasedream.com',
        domain: '.chasedream.com',
        api_base_url: 'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1',
        highlight_digest: 'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=1&modsubmit=yes&infloat=yes&inajax=1',
        delete_thread: 'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=3&modsubmit=yes&infloat=yes&inajax=1',
        stick: 'https://forum.chasedream.com/forum.php?mod=topicadmin&action=moderate&optgroup=1&modsubmit=yes&infloat=yes&inajax=1',
        send_post: 'https://forum.chasedream.com/forum.php?mod=post&action=reply&extra=page%3D1&replysubmit=yes&infloat=yes&handlekey=fastpost&inajax=1',
        cookie_max_age: 1000 * 60 * 60 * 24 * 30,
    }

    config.wechat_group = {
        token: 'puppet_padlocal_3d2ee9682a7d48fbaea239a428456cf8',
        thumbwidth: 400,
    }

    //wechat public platform
    config.wechatApi = {
        appId: 'wx912f26ad8c580ac5',
        appSecret: '7c30d86d5bfea1d9029899e13692a1a0',
        token: 'fOd9wid8Ilk0uF4I',
        encodingAESKey: '3F86ULlyPz75eU1GkKsinHEoDQzoIOQmca6O7oYrcXX',
        callback: {
            id: 'https://id.chasedream.com/api/v1/wechat/id_web_code',
        }
    }

    config.sequelize = {
        datasources: [
            {
                delegate: 'model',
                baseDir: 'model',
                database: 'site_apps_id',
                dialect: 'mysql',
                host: '********',
                port: '3306',
                username: 'conn_apps_id',
                password: 'Qxfh4AsAyJ1Rit3L',
                timezone: '+08:00',
                log: false,
                define: {
                    freezeTableName: true,
                    underscored: true,
                    paranoid: true,
                    charset: "utf8mb4"
                }
            },
            {
                delegate: 'forumModel',
                baseDir: 'forum_model',
                database: 'forum',
                dialect: 'mysql',
                host: '********',
                port: '3306',
                username: 'conn_forum',
                password: 'ZqA5Kv7tsr2R24RV',
                timezone: '+08:00',
                log: false,
                define: {
                    freezeTableName: true,
                    underscored: true,
                    paranoid: true,
                    charset: "utf8mb4"
                }
            }, {
                delegate: 'wwwModel',
                baseDir: 'www_model',
                database: 'Site_WWW',
                dialect: 'mssql',
                host: '*********',
                port: '1433',
                username: 'conn_www',
                password: 'QazQMMs89KEBzN6zxgb2d',
                timezone: '+08:00',
                log: true,
                define: {
                    freezeTableName: true,
                    underscored: false,
                    paranoid: true,
                    charset: "utf8mb4"
                }
            }
        ]
    }

    config.zrole = {
        useAdapter: true,
        usePolicyInit: true,
        useAutoMiddleware: false,
        model: './zrole_model.conf',
        customResponse: ctx => {
            ctx.status = 403
            ctx.body = 'Forbidden'
        },
        adapterConfig: async () => {
            const connect = await SequelizeAdapter.newAdapter({
                username: config.sequelize.datasources[0].username,
                password: config.sequelize.datasources[0].password,
                host: config.sequelize.datasources[0].host,
                database: config.sequelize.datasources[0].database,
                dialect: config.sequelize.datasources[0].dialect,
                logging: false,
            })

            return connect
        },
    }

    config.redis = {
        client: {
            host: '*********',
            port: 6379,
            password: 'stap-^Rexat3us7uk_ab',
            db: 7,
        },
    }

    exports.memcache = {
        url: '********:11211',
        option: {}
    }

    config.mongoose = {
        client: {
            url: 'mongodb://*********:27017/site_log',
            options: {
                auth: { authSource: "admin" },
                user: 'root',
                pass: 'z#QzyhK#W3O2mvGh',
                useUnifiedTopology: true,
            },
        },
        loadModel: false,
    }

    config.logger = {
        level: "ERROR"
    }

    return config
}
