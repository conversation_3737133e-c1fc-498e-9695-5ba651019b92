<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="10">
				<el-button @click="showAddDialog" type="primary">添加</el-button>
			</el-col>
			<el-col :span="14" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<el-select
					v-model="search.status"
					placeholder="选择状态"
					style="width: 120px; margin-right: 10px;"
					clearable
					@change="refresh"
				>
					<el-option label="全部" value="" />
					<el-option label="待处理" :value="0" />
					<el-option label="已处理" :value="1" />
					<el-option label="已忽略" :value="2" />
				</el-select>
				<el-input
					placeholder="请输入URL或备注"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="状态" width="100" align="center">
						<template #default="scope">
							<el-tag :type="getStatusType(scope.row.status)">
								{{ getStatusText(scope.row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="Thread ID" width="120" align="center">
						<template #default="scope">
							{{ scope.row.threadID || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="URL" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link :href="scope.row.url" target="_blank" type="primary" v-if="scope.row.url">
								{{ scope.row.url }}
							</el-link>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column label="备注" width="200" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							{{ scope.row.remark || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.createTime ? formatter(scope.row.createTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<!-- 添加/编辑弹出框 -->
		<el-dialog
			v-model="dialogVisible"
			:title="isEdit ? '编辑等待文章' : '添加等待文章'"
			width="90%"
			:close-on-click-modal="false"
			@close="closeDialog"
			class="waiting-dialog"
			:show-close="false"
		>
			<template #header="{ close }">
				<div class="dialog-header">
					<div class="header-left">
						<div class="header-icon">
							<el-icon size="20" color="#10b981">
								<Edit v-if="isEdit" />
								<Plus v-else />
							</el-icon>
						</div>
						<div class="header-text">
							<h3>{{ isEdit ? '编辑等待文章' : '添加等待文章' }}</h3>
							<p>{{ isEdit ? '修改等待文章信息并实时预览' : '填写等待文章信息并实时预览' }}</p>
						</div>
					</div>
					<el-button @click="close" :icon="Close" circle size="small" />
				</div>
			</template>

			<div class="waiting-editor-container">
				<!-- 左侧编辑区域 -->
				<div class="editor-left">
					<div class="editor-section">
						<div class="section-header">
							<el-icon><Document /></el-icon>
							<span>基本信息</span>
						</div>
						<div class="form-container">
							<el-form :model="form" label-width="100px" :rules="rules" ref="formRef" size="default">
								<div class="form-group">
									<el-form-item label="Thread ID" :required="true" prop="threadID">
										<el-input
											v-model="form.threadID"
											placeholder="请输入Thread ID"
											:prefix-icon="Key"
											@input="updatePreview"
											clearable
										/>
									</el-form-item>

									<el-form-item label="文章URL" :required="true" prop="url">
										<el-input
											v-model="form.url"
											placeholder="请输入文章URL地址"
											:prefix-icon="Link"
											@input="updatePreview"
											clearable
										/>
									</el-form-item>

									<el-form-item label="处理状态" :required="true" prop="status">
										<el-select
											v-model="form.status"
											placeholder="请选择处理状态"
											style="width: 100%"
											@change="updatePreview"
										>
											<el-option label="待处理" :value="0">
												<div style="display: flex; align-items: center; gap: 8px;">
													<el-icon color="#e6a23c"><Clock /></el-icon>
													<span>待处理</span>
												</div>
											</el-option>
											<el-option label="已处理" :value="1">
												<div style="display: flex; align-items: center; gap: 8px;">
													<el-icon color="#67c23a"><Check /></el-icon>
													<span>已处理</span>
												</div>
											</el-option>
											<el-option label="已忽略" :value="2">
												<div style="display: flex; align-items: center; gap: 8px;">
													<el-icon color="#909399"><Close /></el-icon>
													<span>已忽略</span>
												</div>
											</el-option>
										</el-select>
									</el-form-item>
								</div>
							</el-form>
						</div>
					</div>

					<div class="editor-section">
						<div class="section-header">
							<el-icon><Memo /></el-icon>
							<span>备注信息</span>
						</div>
						<div class="form-container">
							<el-form :model="form" label-width="100px" ref="formRef2" size="default">
								<div class="form-group">
									<el-form-item label="备注内容" prop="remark">
										<el-input
											type="textarea"
											v-model="form.remark"
											placeholder="请输入备注内容..."
											:rows="8"
											@input="updatePreview"
											show-word-limit
											maxlength="1000"
											resize="vertical"
										/>
									</el-form-item>
								</div>
							</el-form>
						</div>
					</div>
				</div>

				<!-- 右侧预览区域 -->
				<div class="editor-right">
					<div class="preview-header">
						<div class="preview-title">
							<el-icon><View /></el-icon>
							<span>信息预览</span>
						</div>
						<div class="preview-status">
							<el-tag :type="getStatusType(form.status)" size="small">
								{{ getStatusText(form.status) }}
							</el-tag>
						</div>
					</div>
					<div class="preview-content">
						<div class="waiting-preview">
							<div class="waiting-header">
								<h2 class="waiting-title">等待处理文章</h2>
								<div class="waiting-meta">
									<span v-if="form.threadID" class="meta-thread">
										<el-icon><Key /></el-icon>
										Thread ID: {{ form.threadID }}
									</span>
									<span class="meta-status">
										<el-icon><Flag /></el-icon>
										状态: {{ getStatusText(form.status) }}
									</span>
								</div>
							</div>

							<div class="waiting-info" v-if="form.url">
								<div class="info-item">
									<span class="info-label">文章链接：</span>
									<el-link :href="form.url" target="_blank" type="primary" class="info-link">
										{{ form.url }}
									</el-link>
								</div>
							</div>

							<div class="waiting-body">
								<div class="content-section" v-if="form.remark">
									<h4>备注内容</h4>
									<div class="remark-text">{{ form.remark }}</div>
								</div>
								<div class="content-section" v-else>
									<div class="empty-content">
										<el-icon size="48" color="#C0C4CC"><Memo /></el-icon>
										<p>暂无备注内容，请在左侧编辑区域输入备注信息</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog" size="large">
						<el-icon><Close /></el-icon>
						取消
					</el-button>
					<el-button type="primary" @click="handleSubmit(formRef)" size="large">
						<el-icon><Check /></el-icon>
						{{ isEdit ? '保存修改' : '创建记录' }}
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import {
	Edit, Delete, Search, Plus, Close, Document, Link, Key,
	Memo, View, Check, Clock, Flag
} from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import type { FormInstance } from "element-plus";

const { service } = useCool();

const search = reactive({
	s: "",
	status: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

// 弹出框相关数据
const dialogVisible = ref(false);
const isEdit = ref(false);

const form = reactive({
	id: 0,
	threadID: "",
	url: "",
	remark: "",
	status: 0
});

const rules = reactive({
	threadID: [{ required: true, message: "请输入Thread ID", trigger: "blur" }],
	url: [{ required: true, message: "请输入URL", trigger: "blur" }],
	status: [{ required: true, message: "请选择状态", trigger: "change" }]
});

const formRef = ref<FormInstance>();

const formatter = (datetime: any) => {
	if (!datetime) return '-';
	return datelineToDate(new Date(datetime).getTime() / 1000, "YYYY-MM-DD HH:mm:ss");
};

const getStatusType = (status: number) => {
	switch (status) {
		case 0: return 'warning';  // 待处理
		case 1: return 'success';  // 已处理
		case 2: return 'info';     // 已忽略
		default: return '';
	}
};

const getStatusText = (status: number) => {
	switch (status) {
		case 0: return '待处理';
		case 1: return '已处理';
		case 2: return '已忽略';
		default: return '未知';
	}
};

// 更新预览内容（等待文章相对简单，主要是实时更新显示）
const updatePreview = () => {
	// 等待文章的预览主要是实时显示表单内容，不需要特殊处理
	// 这里可以添加一些实时验证逻辑
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	showEditDialog(row.id);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.waitingArticleDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.waitingArticlePage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 弹出框控制方法
const showAddDialog = () => {
	isEdit.value = false;
	resetForm();
	dialogVisible.value = true;
};

const showEditDialog = (id: number) => {
	isEdit.value = true;
	resetForm();
	form.id = id;
	dialogVisible.value = true;
	nextTick(() => {
		loadData();
	});
};

const closeDialog = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	Object.assign(form, {
		id: 0,
		threadID: "",
		url: "",
		remark: "",
		status: 0
	});

	// 清除表单验证
	if (formRef.value) {
		formRef.value.clearValidate();
	}
};

// 加载编辑数据
const loadData = async () => {
	service.base.common.portal
		.waitingArticleFindOne({
			id: form.id
		})
		.then((res) => {
			Object.assign(form, res);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 表单提交
const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, _fields) => {
		if (!isValid) return;

		if (isEdit.value) {
			// 编辑模式
			service.base.common.portal
				.waitingArticleUpdate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已修改!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			// 添加模式
			service.base.common.portal
				.waitingArticleCreate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已添加!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";

// 等待文章编辑器样式
.waiting-dialog {
	:deep(.el-dialog) {
		margin: 20px auto;
		max-height: calc(100vh - 40px);
		border-radius: 12px;
		box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08);
	}

	:deep(.el-dialog__header) {
		padding: 0;
		margin: 0;
	}

	:deep(.el-dialog__body) {
		padding: 0;
		height: calc(100vh - 200px);
	}

	:deep(.el-dialog__footer) {
		padding: 0;
	}
}

.dialog-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 24px;
	background: linear-gradient(135deg, #10b981 0%, #059669 100%);
	color: white;
	border-radius: 12px 12px 0 0;

	.header-left {
		display: flex;
		align-items: center;
		gap: 16px;

		.header-icon {
			width: 48px;
			height: 48px;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			backdrop-filter: blur(10px);
		}

		.header-text {
			h3 {
				margin: 0;
				font-size: 20px;
				font-weight: 600;
				color: white;
			}

			p {
				margin: 4px 0 0 0;
				font-size: 14px;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}
}

.waiting-editor-container {
	display: flex;
	height: 100%;
	background: #f8fafc;
	overflow: hidden;
}

.editor-left {
	width: 50%;
	border-right: 1px solid #e2e8f0;
	display: flex;
	flex-direction: column;
	background: white;
}

.editor-right {
	width: 50%;
	display: flex;
	flex-direction: column;
	background: #f8fafc;
}

.editor-section {
	border-bottom: 1px solid #e2e8f0;

	&:last-child {
		border-bottom: none;
		flex: 1;
	}
}

.section-header {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 16px 24px;
	background: #f1f5f9;
	border-bottom: 1px solid #e2e8f0;
	font-weight: 600;
	color: #475569;
	font-size: 14px;

	.el-icon {
		color: #10b981;
	}
}

.form-container {
	padding: 24px;
	overflow-y: auto;

	.form-group {
		margin-bottom: 24px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	:deep(.el-form-item) {
		margin-bottom: 20px;

		.el-form-item__label {
			color: #374151;
			font-weight: 500;
		}

		.el-input__wrapper {
			border-radius: 8px;
			box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
			transition: all 0.2s;

			&:hover {
				box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
			}

			&.is-focus {
				box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
			}
		}

		.el-textarea__inner {
			border-radius: 8px;
			box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
			transition: all 0.2s;

			&:hover {
				box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
			}

			&:focus {
				box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
			}
		}

		.el-select {
			.el-input__wrapper {
				&.is-focus {
					box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
				}
			}
		}
	}
}

.preview-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 24px;
	background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
	border-bottom: 1px solid #e2e8f0;

	.preview-title {
		display: flex;
		align-items: center;
		gap: 8px;
		font-weight: 600;
		color: #475569;
		font-size: 14px;

		.el-icon {
			color: #10b981;
		}
	}

	.preview-status {
		.el-tag {
			border-radius: 6px;
		}
	}
}

.preview-content {
	flex: 1;
	overflow-y: auto;
	padding: 24px;
}

.waiting-preview {
	background: white;
	border-radius: 12px;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.waiting-header {
	padding: 24px;
	border-bottom: 1px solid #e2e8f0;

	.waiting-title {
		margin: 0 0 16px 0;
		font-size: 20px;
		font-weight: 700;
		color: #1f2937;
		line-height: 1.3;
	}

	.waiting-meta {
		display: flex;
		flex-wrap: wrap;
		gap: 16px;

		span {
			display: flex;
			align-items: center;
			gap: 6px;
			font-size: 14px;
			color: #6b7280;

			.el-icon {
				color: #9ca3af;
			}
		}
	}
}

.waiting-info {
	padding: 16px 24px;
	background: #f8fafc;
	border-bottom: 1px solid #e2e8f0;

	.info-item {
		display: flex;
		align-items: center;
		gap: 8px;
		font-size: 14px;

		.info-label {
			color: #6b7280;
			font-weight: 500;
		}

		.info-link {
			font-weight: 500;
		}
	}
}

.waiting-body {
	padding: 24px;
}

.content-section {
	margin-bottom: 24px;

	&:last-child {
		margin-bottom: 0;
	}

	h4 {
		margin: 0 0 12px 0;
		font-size: 16px;
		font-weight: 600;
		color: #374151;
		padding-bottom: 8px;
		border-bottom: 2px solid #e5e7eb;
	}
}

.remark-text {
	background: #f9fafb;
	border: 1px solid #e5e7eb;
	border-radius: 8px;
	padding: 16px;
	font-size: 14px;
	line-height: 1.6;
	color: #374151;
	white-space: pre-wrap;
	min-height: 120px;
}

.empty-content {
	text-align: center;
	padding: 48px 24px;
	color: #9ca3af;

	p {
		margin: 16px 0 0 0;
		font-size: 14px;
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding: 20px 24px;
	border-top: 1px solid #e2e8f0;
	background: white;
	border-radius: 0 0 12px 12px;

	.el-button {
		border-radius: 8px;
		font-weight: 500;
		padding: 12px 24px;

		&.el-button--primary {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			border: none;
			box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.25);

			&:hover {
				background: linear-gradient(135deg, #059669 0%, #047857 100%);
				transform: translateY(-1px);
				box-shadow: 0 6px 8px -1px rgba(16, 185, 129, 0.35);
			}
		}

		&:not(.el-button--primary) {
			border: 1px solid #d1d5db;
			color: #6b7280;

			&:hover {
				border-color: #9ca3af;
				color: #374151;
				transform: translateY(-1px);
			}
		}
	}
}

// 响应式设计
@media (max-width: 1400px) {
	.waiting-editor-container {
		flex-direction: column;
		height: auto;
	}

	.editor-left, .editor-right {
		width: 100%;
	}

	.editor-left {
		border-right: none;
		border-bottom: 1px solid #e2e8f0;
		max-height: 60vh;
	}

	.editor-right {
		max-height: 40vh;
	}

	.waiting-dialog {
		:deep(.el-dialog) {
			width: 95% !important;
			max-width: none;
		}

		:deep(.el-dialog__body) {
			height: 80vh;
		}
	}
}

@media (max-width: 768px) {
	.dialog-header {
		padding: 16px 20px;

		.header-left {
			gap: 12px;

			.header-icon {
				width: 40px;
				height: 40px;
			}

			.header-text {
				h3 {
					font-size: 18px;
				}

				p {
					font-size: 13px;
				}
			}
		}
	}

	.form-container {
		padding: 16px;
	}

	.preview-content {
		padding: 16px;
	}

	.waiting-header {
		padding: 16px;

		.waiting-title {
			font-size: 18px;
		}
	}

	.waiting-body {
		padding: 16px;
	}

	.dialog-footer {
		padding: 16px 20px;

		.el-button {
			padding: 10px 20px;
		}
	}
}
</style>